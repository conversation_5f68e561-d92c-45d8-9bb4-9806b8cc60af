<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Notifications\Auth\CustomResetPasswordNotification;
use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\HasApiTokens;
use Stripe\StripeClient;
use App\Helpers\AvatarHelper;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, HasUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'google_avatar',
        'email_verified_at',
        'provider',
        'country_code',
        'phone_number',
        'avatar',
        'cumulative_balance',
        'is_admin',
        'is_active',
        'is_banned',
        'banned_at',
        'ban_reason',
        'subscription_tier',
        'preferences',
        'last_login_at',
        'trial_started_at',
        'stripe_id',
        'selected_plan_tier',
        'selected_billing_cycle',
        'uuid',
        // Email verification fields
        'email_verification_code',
        'email_verification_code_expires_at',
        'password_reset_code',
        'password_reset_code_expires_at',
        'email_verification_attempts',
        'password_reset_attempts',
        'last_verification_code_sent_at',
        'last_password_reset_code_sent_at',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'subscription_tier' => 'none',
        'is_active' => true,
        'is_admin' => false,
        'is_banned' => false,
        'email_verification_attempts' => 0,
        'password_reset_attempts' => 0,
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'cumulative_balance' => 'decimal:2',
            'is_admin' => 'boolean',
            'is_active' => 'boolean',
            'is_banned' => 'boolean',
            'banned_at' => 'datetime',
            'preferences' => 'json',
            'last_login_at' => 'datetime',
            'trial_started_at' => 'datetime',
            // Email verification casts
            'email_verification_code_expires_at' => 'datetime',
            'password_reset_code_expires_at' => 'datetime',
            'last_verification_code_sent_at' => 'datetime',
            'last_password_reset_code_sent_at' => 'datetime',
        ];
    }

    /**
     * Get the user's safe avatar URL.
     *
     * @param  string|null  $value
     * @return string
     */
    public function getAvatarAttribute($value): string
    {
        // Prioritize Google avatar if available
        if ($this->google_avatar) {
            return AvatarHelper::getSafeAvatarUrl($this->google_avatar, $this->name);
        }

        // Otherwise use regular avatar
        return AvatarHelper::getSafeAvatarUrl($value, $this->name);
    }

    /**
     * Calculate and update cumulative balance.
     * Sum of all income transactions minus sum of all expense transactions.
     *
     * @return float
     */
    public function calculateCumulativeBalance(): float
    {
        // Get total income from all income transactions
        $totalIncome = $this->transactions()
            ->where('transaction_type', 'income')
            ->sum('amount');

        // Get total expenses from all expense transactions
        $totalExpense = $this->transactions()
            ->where('transaction_type', 'expense')
            ->sum('amount');

        // Calculate cumulative balance: income - expense
        $cumulativeBalance = $totalIncome - $totalExpense;

        // Update the user's cumulative balance
        $this->cumulative_balance = $cumulativeBalance;
        $this->save();

        return $cumulativeBalance;
    }

    /**
     * Get cumulative balance without updating.
     *
     * @return float
     */
    public function getCumulativeBalance(): float
    {
        return $this->cumulative_balance ?? 0;
    }

    /**
     * Get the bins for the user.
     */
    public function bins(): HasMany
    {
        return $this->hasMany(Bin::class);
    }

    /**
     * Get the transactions for the user.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the subscriptions for the user.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the crypto wallets for the user.
     */
    public function cryptoWallets(): HasMany
    {
        return $this->hasMany(CryptoWallet::class);
    }

    /**
     * Get the subscription history for the user.
     */
    public function subscriptionHistory(): HasMany
    {
        return $this->hasMany(SubscriptionHistory::class);
    }



    /**
     * Get the reports for the user.
     */
    public function reports(): HasMany
    {
        return $this->hasMany(Report::class);
    }

    /**
     * Get the Plaid accounts for the user.
     */
    public function plaidAccounts(): HasMany
    {
        return $this->hasMany(PlaidAccount::class);
    }

    /**
     * Check if the user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->is_admin;
    }

    /**
     * Check if the user is banned.
     */
    public function isBanned(): bool
    {
        return $this->is_banned;
    }

    /**
     * Ban the user.
     */
    public function ban(?string $reason = null): void
    {
        $this->is_banned = true;
        $this->banned_at = now();
        $this->ban_reason = $reason;
        $this->save();
    }

    /**
     * Unban the user.
     */
    public function unban(): void
    {
        $this->is_banned = false;
        $this->banned_at = null;
        $this->ban_reason = null;
        $this->save();
    }

    /**
     * Get the user's status label.
     */
    public function getStatusLabel(): string
    {
        if ($this->is_banned) {
            return 'Banned';
        }

        if (!$this->is_active) {
            return 'Blocked';
        }

        return 'Active';
    }

    /**
     * Get the user's status class.
     */
    public function getStatusClass(): string
    {
        if ($this->is_banned) {
            return 'danger';
        }

        if (!$this->is_active) {
            return 'warning';
        }

        return 'success';
    }

    /**
     * Get the user's full phone number with country code.
     */
    public function getFullPhoneNumber(): ?string
    {
        if (empty($this->phone_number)) {
            return null;
        }

        if (empty($this->country_code)) {
            return $this->phone_number;
        }

        return $this->country_code . ' ' . $this->phone_number;
    }

    /**
     * Check if the user has premium features.
     */
    public function hasPremium(): bool
    {
        return $this->subscription_tier === 'premium';
    }

    /**
     * Check if the user is on trial.
     */
    public function isTrial(): bool
    {
        return $this->subscription_tier === 'trial';
    }

    /**
     * Get the user's active subscription.
     */
    public function getActiveSubscription(): ?Subscription
    {
        return $this->subscriptions()
            ->where(function ($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->where(function ($query) {
                $query->where('stripe_status', 'active')
                    ->orWhere('stripe_status', 'trialing');
            })
            ->latest()
            ->first();
    }

    /**
     * Check if the user is on a trial period.
     */
    public function onTrial(): bool
    {
        $subscription = $this->getActiveSubscription();
        return $subscription && $subscription->onTrial();
    }

    /**
     * Start the user's trial period with selected plan.
     */
    public function startTrialWithPlan(string $subscriptionTier, string $billingCycle): void
    {
        if (!$this->trial_started_at) {
            $this->trial_started_at = now();
            $this->subscription_tier = 'trial';
            $this->save();

            // Create trial subscription record with selected plan
            $this->createTrialSubscription($subscriptionTier, $billingCycle);
        }
    }

    /**
     * Check if the user's trial has expired.
     */
    public function trialExpired(): bool
    {
        if (!$this->trial_started_at) {
            return false;
        }

        $trialDays = (int) config('services.subscription.trial_days', 7);
        return $this->trial_started_at->addDays($trialDays)->isPast();
    }

    /**
     * Get the trial expiration date.
     */
    public function getTrialExpirationDate(): ?\Carbon\Carbon
    {
        if (!$this->trial_started_at) {
            return null;
        }

        $trialDays = (int) config('services.subscription.trial_days', 7);
        return $this->trial_started_at->addDays($trialDays);
    }

    /**
     * Get days remaining in trial.
     */
    public function getTrialDaysRemaining(): int
    {
        if (!$this->trial_started_at || $this->trialExpired()) {
            return 0;
        }

        $expirationDate = $this->getTrialExpirationDate();
        return max(0, now()->diffInDays($expirationDate, false));
    }

    /**
     * Create a trial subscription record.
     */
    private function createTrialSubscription(): void
    {
        $trialDays = (int) config('services.subscription.trial_days', 7);

        $this->subscriptions()->create([
            'name' => 'Free Trial',
            'stripe_id' => null,
            'stripe_status' => 'trialing',
            'stripe_price' => null,
            'subscription_tier' => 'base',
            'billing_cycle' => 'monthly',
            'price' => config('services.subscription.prices.base_monthly', 5.00),
            'currency' => 'USD',
            'features' => $this->getTrialFeatures(),
            'trial_ends_at' => $this->trial_started_at->addDays($trialDays),
        ]);
    }

    /**
     * Get features available during trial.
     */
    private function getTrialFeatures(): array
    {
        return [
            'secure_login' => true,
            'bank_account_linking' => true,
            'total_balance_view' => true,
            'balance_toggle' => true,
            'financial_bins' => true,
            'auto_categorization' => true,
            'manual_bin_editing' => true,
            'graphical_insights' => true,
            'recent_transactions' => true,
            'chatbot_access' => false,
            'notifications' => true,
            'max_sub_bin_levels' => 3,
            'crypto_scanner' => false,
            'unlimited_sub_bins' => false,
            'priority_notifications' => false,
            'advanced_ai_suggestions' => false,
        ];
    }

    /**
     * Get the notification settings for the user.
     */
    public function notificationSetting(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(NotificationSetting::class);
    }

    /**
     * Get or create notification settings for the user.
     */
    public function getNotificationSettings(): NotificationSetting
    {
        $settings = $this->notificationSetting;

        if (!$settings) {
            $settings = $this->notificationSetting()->create();
        }

        return $settings;
    }

    /**
     * Check if a specific notification type is enabled.
     */
    public function isNotificationEnabled(string $type): bool
    {
        $settings = $this->getNotificationSettings();

        return $settings->{$type} ?? true;
    }

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new CustomResetPasswordNotification($token));
    }

    /**
     * Delete user and all associated data.
     * This performs a cascading delete of all user-related records.
     *
     * @param bool $force Whether to force delete (true) or soft delete (false)
     * @return bool
     */
    public function deleteWithRelated(bool $force = false): bool
    {
        // Start a database transaction
        DB::beginTransaction();

        try {
            // Delete notification settings
            if ($notificationSetting = $this->notificationSetting) {
                $force ? $notificationSetting->forceDelete() : $notificationSetting->delete();
            }

            // Delete all crypto wallets
            $this->cryptoWallets()->get()->each(function ($wallet) use ($force) {
                $force ? $wallet->forceDelete() : $wallet->delete();
            });

            // Delete all Plaid accounts
            $this->plaidAccounts()->get()->each(function ($account) use ($force) {
                $force ? $account->forceDelete() : $account->delete();
            });


            // Delete all transactions
            $this->transactions()->get()->each(function ($transaction) use ($force) {
                $force ? $transaction->forceDelete() : $transaction->delete();
            });

            // Delete all subscriptions and subscription history
            $this->subscriptions()->get()->each(function ($subscription) use ($force) {
                // Cancel subscription in Stripe if it has a Stripe ID and is active
                if ($subscription->stripe_id &&
                    ($subscription->stripe_status === 'active' || $subscription->stripe_status === 'trialing')) {
                    try {
                        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
                        $stripe->subscriptions->cancel($subscription->stripe_id, [
                            'invoice_now' => false,
                            'prorate' => true,
                        ]);
                    } catch (\Exception $e) {
                        // Log the error but continue with deletion
                        Log::error('Error canceling Stripe subscription: ' . $e->getMessage());
                    }
                }

                // Delete subscription history
                $subscription->history()->get()->each(function ($history) use ($force) {
                    $force ? $history->forceDelete() : $history->delete();
                });

                $force ? $subscription->forceDelete() : $subscription->delete();
            });

            // Delete all bins and sub-bins
            $this->bins()->get()->each(function ($bin) use ($force) {
                // Delete sub-bins first
                $bin->subBins()->get()->each(function ($subBin) use ($force) {
                    $force ? $subBin->forceDelete() : $subBin->delete();
                });

                $force ? $bin->forceDelete() : $bin->delete();
            });

            // Delete all reports
            $this->reports()->get()->each(function ($report) use ($force) {
                // Delete the report file if it exists
                if ($report->file_path && file_exists(storage_path('app/' . $report->file_path))) {
                    unlink(storage_path('app/' . $report->file_path));
                }

                $force ? $report->forceDelete() : $report->delete();
            });

            // Delete personal access tokens
            $this->tokens()->delete();

            // Finally, delete the user
            $result = $force ? $this->forceDelete() : $this->delete();

            // Commit the transaction
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            // Rollback the transaction in case of error
            DB::rollBack();
            Log::error('Error deleting user with related data: ' . $e->getMessage());
            throw $e;
        }
    }
}
