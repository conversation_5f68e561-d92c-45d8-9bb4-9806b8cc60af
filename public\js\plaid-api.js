/**
 * PocketWatch Plaid API Integration
 * 
 * This file provides helper functions for interacting with the Plaid API
 * through the PocketWatch backend. It's designed to be used by the mobile app.
 */

class PlaidAPI {
    /**
     * Initialize the Plaid API client
     * 
     * @param {string} baseUrl - The base URL for the API
     * @param {string} token - The authentication token
     */
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.token = token;
    }

    /**
     * Set the authentication token
     * 
     * @param {string} token - The authentication token
     */
    setToken(token) {
        this.token = token;
    }

    /**
     * Make an API request
     * 
     * @param {string} endpoint - The API endpoint
     * @param {string} method - The HTTP method
     * @param {object} data - The request data
     * @returns {Promise} - The API response
     */
    async request(endpoint, method = 'GET', data = null) {
        const url = `${this.baseUrl}/${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${this.token}`
        };

        const options = {
            method,
            headers
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(url, options);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || 'API request failed');
            }

            return result;
        } catch (error) {
            console.error('API request error:', error);
            throw error;
        }
    }

    /**
     * Get a Plaid link token
     * 
     * @returns {Promise} - The link token response
     */
    async getLinkToken() {
        return this.request('plaid/link-token');
    }

    /**
     * Store Plaid account information
     * 
     * @param {object} data - The Plaid account data
     * @returns {Promise} - The API response
     */
    async storePlaidAccount(data) {
        return this.request('plaid/accounts', 'POST', data);
    }

    /**
     * Get the user's Plaid accounts
     * 
     * @returns {Promise} - The accounts response
     */
    async getPlaidAccounts() {
        return this.request('plaid/accounts');
    }

    /**
     * Set a Plaid account as the default
     * 
     * @param {number} accountId - The account ID
     * @returns {Promise} - The API response
     */
    async setDefaultAccount(accountId) {
        return this.request(`plaid/accounts/${accountId}/set-default`, 'POST');
    }

    /**
     * Toggle payment for a Plaid account
     * 
     * @param {number} accountId - The account ID
     * @returns {Promise} - The API response
     */
    async togglePayment(accountId) {
        return this.request(`plaid/accounts/${accountId}/toggle-payment`, 'POST');
    }

    /**
     * Delete a Plaid account
     * 
     * @param {number} accountId - The account ID
     * @returns {Promise} - The API response
     */
    async deleteAccount(accountId) {
        return this.request(`plaid/accounts/${accountId}`, 'DELETE');
    }

    /**
     * Get payment-enabled Plaid accounts
     * 
     * @returns {Promise} - The accounts response
     */
    async getPaymentAccounts() {
        return this.request('plaid-payment/accounts');
    }

    /**
     * Process a payment using Plaid
     * 
     * @param {object} data - The payment data
     * @returns {Promise} - The API response
     */
    async processPayment(data) {
        return this.request('plaid-payment/process', 'POST', data);
    }
}

// Example usage:
/*
const plaidApi = new PlaidAPI('https://api.example.com', 'your-token');

// Get a link token
plaidApi.getLinkToken()
    .then(response => {
        const linkToken = response.link_token;
        // Use the link token with Plaid Link
    })
    .catch(error => {
        console.error('Error getting link token:', error);
    });

// Store Plaid account information
plaidApi.storePlaidAccount({
    public_token: 'public-token',
    institution_id: 'institution-id',
    institution_name: 'Bank Name',
    accounts: [
        {
            id: 'account-id',
            name: 'Checking',
            type: 'depository',
            subtype: 'checking',
            mask: '1234'
        }
    ]
})
    .then(response => {
        console.log('Account linked:', response);
    })
    .catch(error => {
        console.error('Error linking account:', error);
    });

// Process a payment
plaidApi.processPayment({
    plaid_account_id: 1,
    subscription_tier: 'premium',
    billing_cycle: 'monthly'
})
    .then(response => {
        console.log('Payment processed:', response);
    })
    .catch(error => {
        console.error('Error processing payment:', error);
    });
*/
