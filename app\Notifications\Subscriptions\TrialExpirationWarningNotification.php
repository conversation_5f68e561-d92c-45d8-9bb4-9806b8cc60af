<?php

namespace App\Notifications\Subscriptions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TrialExpirationWarningNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private int $daysRemaining;

    /**
     * Create a new notification instance.
     */
    public function __construct(int $daysRemaining)
    {
        $this->daysRemaining = $daysRemaining;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $dayText = $this->daysRemaining === 1 ? 'day' : 'days';
        
        return (new MailMessage)
            ->subject('Your PocketWatch Trial Expires Soon')
            ->greeting("Hello {$notifiable->name}!")
            ->line("Your PocketWatch free trial expires in {$this->daysRemaining} {$dayText}.")
            ->line('To continue enjoying all the features of PocketWatch, please connect your payment method and choose a subscription plan.')
            ->action('Upgrade Now', url('/subscription/upgrade'))
            ->line('If you have any questions, feel free to contact our support team.')
            ->line('Thank you for trying PocketWatch!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'trial_expiration_warning',
            'days_remaining' => $this->daysRemaining,
            'message' => "Your trial expires in {$this->daysRemaining} " . ($this->daysRemaining === 1 ? 'day' : 'days'),
        ];
    }
}
