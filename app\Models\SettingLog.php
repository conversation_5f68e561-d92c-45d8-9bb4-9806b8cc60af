<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SettingLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'setting_key',
        'old_value',
        'new_value',
        'user_id',
        'action',
        'ip_address',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'old_value' => 'json',
        'new_value' => 'json',
    ];

    /**
     * Get the user that performed the action.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Log a setting change.
     *
     * @param string $key
     * @param mixed $oldValue
     * @param mixed $newValue
     * @param string $action
     * @return \App\Models\SettingLog
     */
    public static function logChange($key, $oldValue, $newValue, $action = 'update')
    {
        return self::create([
            'setting_key' => $key,
            'old_value' => $oldValue,
            'new_value' => $newValue,
            'user_id' => auth()->id(),
            'action' => $action,
            'ip_address' => request()->ip(),
        ]);
    }
}
