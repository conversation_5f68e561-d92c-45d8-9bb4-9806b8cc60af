<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Plaid API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the Plaid API integration.
    |
    */

    // Plaid API environment (sandbox, development, production)
    'environment' => env('PLAID_ENVIRONMENT', 'sandbox'),

    // Plaid API credentials
    'client_id' => env('PLAID_CLIENT_ID'),
    'secret' => env('PLAID_SECRET'),

    // Plaid API endpoints
    'api_host' => [
        'sandbox' => 'https://sandbox.plaid.com',
        'development' => 'https://development.plaid.com',
        'production' => 'https://production.plaid.com',
    ],

    // Plaid Link configuration
    'client_name' => env('PLAID_CLIENT_NAME', 'PocketWatch'),
    'products' => explode(',', env('PLAID_PRODUCTS', 'auth,transactions,payment_initiation')),
    'country_codes' => explode(',', env('PLAID_COUNTRY_CODES', 'US,CA')),
    'language' => env('PLAID_LANGUAGE', 'en'),

    // Plaid webhook configuration
    'webhook' => env('PLAID_WEBHOOK'),

    // Security settings
    'encryption_key' => env('PLAID_ENCRYPTION_KEY', env('APP_KEY')),
];
