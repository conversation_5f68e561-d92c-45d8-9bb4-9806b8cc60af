<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // First check if phone column exists
            if (Schema::hasColumn('users', 'phone')) {
                // Rename phone to phone_number for clarity
                $table->renameColumn('phone', 'phone_number');
            } else {
                // Add phone_number column if it doesn't exist
                $table->string('phone_number')->nullable()->after('email');
            }
            
            // Add country_code column
            $table->string('country_code', 5)->nullable()->after('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove country_code column
            $table->dropColumn('country_code');
            
            // Check if phone_number column exists
            if (Schema::hasColumn('users', 'phone_number')) {
                // Rename phone_number back to phone
                $table->renameColumn('phone_number', 'phone');
            }
        });
    }
};
