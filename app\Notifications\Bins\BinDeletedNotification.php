<?php

namespace App\Notifications\Bins;

use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class BinDeletedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The bin name.
     *
     * @var string
     */
    protected $binName;

    /**
     * The timestamp when the bin was deleted.
     *
     * @var \Illuminate\Support\Carbon
     */
    protected $timestamp;

    /**
     * Create a new notification instance.
     *
     * @param  string  $binName
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_bin_operations';

    public function __construct($binName)
    {
        $this->binName = $binName;
        $this->timestamp = now();

        $this->subject = 'Financial Bin Deleted';
        $this->title = 'Bin Deleted';
        $this->content = 'A financial bin has been deleted from your PocketWatch account. If you deleted this bin, 
                         no further action is required.';
        
        $this->detailsTitle = 'Deletion Details';
        $this->details = [
            'Bin Name' => $this->binName,
            'Date & Time' => $this->timestamp->format('F j, Y, g:i a'),
        ];
        
        $this->actionText = 'View All Bins';
        $this->actionUrl = url('/bins');
        
        $this->closing = 'If you did not delete this bin, please contact our support team immediately.';
        $this->signature = 'The PocketWatch Team';
    }
}
