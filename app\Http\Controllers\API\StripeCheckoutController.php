<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;

class StripeCheckoutController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Get packages with dynamic checkout URLs.
     */
    public function getPackagesWithCheckout(Request $request)
    {
        $user = $request->user();
        
        $packages = [
            [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'description' => 'Essential financial management tools',
                'price' => 5.00,
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'stripe_price_id' => env('STRIPE_PRICE_BASE_MONTHLY'),
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Basic insights',
                    'Hierarchical sub-bins (3 levels)',
                    'Bank account linking'
                ],
                'limits' => [
                    'max_bins' => 10,
                    'max_sub_bins_per_bin' => 10,
                    'max_nesting_depth' => 3
                ],
                'checkout_action' => 'create_checkout_session'
            ],
            [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'description' => 'Advanced financial management with premium features',
                'price' => 10.00,
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'stripe_price_id' => env('STRIPE_PRICE_PREMIUM_MONTHLY'),
                'features' => [
                    'All Base features',
                    'Unlimited hierarchical sub-bins',
                    'Crypto wallet integration',
                    'Advanced AI insights',
                    'Priority notifications',
                    'Advanced reporting'
                ],
                'limits' => [
                    'max_bins' => 'unlimited',
                    'max_sub_bins_per_bin' => 'unlimited',
                    'max_nesting_depth' => 'unlimited'
                ],
                'checkout_action' => 'create_checkout_session'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'packages' => $packages,
                'trial_days' => 7,
                'currency' => 'USD',
                'user_info' => [
                    'id' => $user->id,
                    'email' => $user->email,
                    'name' => $user->name,
                    'current_tier' => $user->subscription_tier
                ]
            ]
        ]);
    }

    /**
     * Create Stripe checkout session for a package.
     */
    public function createCheckoutSession(Request $request, $packageId)
    {
        $validator = Validator::make(['package_id' => $packageId], [
            'package_id' => 'required|string|in:base_monthly,premium_monthly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid package selected',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $packageDetails = $this->getPackageDetails($packageId);

        if (!$packageDetails) {
            return response()->json([
                'success' => false,
                'message' => 'Package not found'
            ], 404);
        }

        try {
            // Create Stripe checkout session
            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price' => $packageDetails['stripe_price_id'],
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => config('app.url') . '/payment-success?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => config('app.url') . '/payment-cancel',
                'customer_email' => $user->email,
                'client_reference_id' => $user->id,
                'subscription_data' => [
                    'trial_period_days' => 7,
                    'metadata' => [
                        'user_id' => $user->id,
                        'package_id' => $packageId,
                        'package_name' => $packageDetails['name'],
                    ],
                ],
                'metadata' => [
                    'user_id' => $user->id,
                    'package_id' => $packageId,
                    'package_name' => $packageDetails['name'],
                    'user_email' => $user->email,
                ],
                'allow_promotion_codes' => true,
                'billing_address_collection' => 'auto',
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'checkout_url' => $session->url,
                    'session_id' => $session->id,
                    'package' => $packageDetails,
                    'trial_info' => [
                        'trial_days' => 7,
                        'trial_start' => 'immediately_after_payment',
                        'auto_billing_date' => now()->addDays(7)->toDateString()
                    ]
                ],
                'message' => 'Checkout session created. Redirect user to checkout_url.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create checkout session: ' . $e->getMessage(),
                'error_code' => 'checkout_creation_failed'
            ], 500);
        }
    }

    /**
     * Handle successful payment.
     */
    public function handleSuccess(Request $request)
    {
        $sessionId = $request->query('session_id');

        if (!$sessionId) {
            return response()->json([
                'success' => false,
                'message' => 'No session ID provided'
            ], 400);
        }

        try {
            // Retrieve the checkout session
            $session = Session::retrieve($sessionId);

            if ($session->payment_status === 'paid') {
                $userId = $session->metadata->user_id;
                $packageId = $session->metadata->package_id;

                $user = User::find($userId);
                if (!$user) {
                    return response()->json([
                        'success' => false,
                        'message' => 'User not found'
                    ], 404);
                }

                // Check if subscription already exists
                $existingSubscription = Subscription::where('stripe_id', $session->subscription)->first();
                
                if (!$existingSubscription) {
                    // Create subscription record
                    $packageDetails = $this->getPackageDetails($packageId);
                    
                    $subscription = Subscription::create([
                        'user_id' => $userId,
                        'name' => $packageDetails['name'],
                        'stripe_id' => $session->subscription,
                        'stripe_status' => 'trialing',
                        'stripe_price' => $packageDetails['stripe_price_id'],
                        'subscription_tier' => $packageDetails['tier'],
                        'billing_cycle' => $packageDetails['billing_cycle'],
                        'price' => $packageDetails['price'],
                        'currency' => 'USD',
                        'features' => $packageDetails['features'],
                        'trial_ends_at' => now()->addDays(7),
                    ]);

                    // Update user
                    $user->update([
                        'subscription_tier' => 'trial',
                        'trial_started_at' => now(),
                        'stripe_id' => $session->customer,
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Payment successful! Your 7-day trial has started.',
                    'data' => [
                        'session_id' => $sessionId,
                        'subscription_id' => $session->subscription,
                        'customer_id' => $session->customer,
                        'trial_started' => true,
                        'trial_ends_at' => now()->addDays(7)->toISOString(),
                        'package_name' => $session->metadata->package_name
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not completed',
                    'payment_status' => $session->payment_status
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment success: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle cancelled payment.
     */
    public function handleCancel(Request $request)
    {
        return response()->json([
            'success' => false,
            'message' => 'Payment was cancelled by user',
            'data' => [
                'cancelled_at' => now()->toISOString(),
                'next_steps' => [
                    'view_packages' => '/api/packages-checkout',
                    'try_again' => 'Select a package to try payment again'
                ]
            ]
        ]);
    }

    /**
     * Handle Stripe webhooks.
     */
    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook.secret');

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            return response()->json(['error' => 'Invalid payload'], 400);
        } catch (SignatureVerificationException $e) {
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        // Handle the event
        switch ($event->type) {
            case 'checkout.session.completed':
                $this->handleCheckoutCompleted($event->data->object);
                break;
            case 'customer.subscription.created':
                $this->handleSubscriptionCreated($event->data->object);
                break;
            case 'customer.subscription.updated':
                $this->handleSubscriptionUpdated($event->data->object);
                break;
            case 'invoice.payment_succeeded':
                $this->handlePaymentSucceeded($event->data->object);
                break;
            case 'customer.subscription.deleted':
                $this->handleSubscriptionDeleted($event->data->object);
                break;
            default:
                \Log::info('Unhandled Stripe webhook event: ' . $event->type);
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Get package details by ID.
     */
    private function getPackageDetails($packageId)
    {
        $packages = [
            'base_monthly' => [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'tier' => 'base',
                'billing_cycle' => 'monthly',
                'price' => 5.00,
                'stripe_price_id' => env('STRIPE_PRICE_BASE_MONTHLY'),
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Basic insights',
                    'Hierarchical sub-bins (3 levels)'
                ]
            ],
            'premium_monthly' => [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'tier' => 'premium',
                'billing_cycle' => 'monthly',
                'price' => 10.00,
                'stripe_price_id' => env('STRIPE_PRICE_PREMIUM_MONTHLY'),
                'features' => [
                    'All Base features',
                    'Unlimited hierarchical sub-bins',
                    'Crypto wallet integration',
                    'Advanced AI insights'
                ]
            ]
        ];

        return $packages[$packageId] ?? null;
    }

    /**
     * Handle checkout session completed.
     */
    private function handleCheckoutCompleted($session)
    {
        \Log::info('Checkout completed for session: ' . $session->id);
        // Additional logic can be added here
    }

    /**
     * Handle subscription created.
     */
    private function handleSubscriptionCreated($subscription)
    {
        \Log::info('Subscription created: ' . $subscription->id);
        // Additional logic can be added here
    }

    /**
     * Handle subscription updated.
     */
    private function handleSubscriptionUpdated($subscription)
    {
        \Log::info('Subscription updated: ' . $subscription->id);
        
        // Update local subscription record
        $localSubscription = Subscription::where('stripe_id', $subscription->id)->first();
        if ($localSubscription) {
            $localSubscription->update([
                'stripe_status' => $subscription->status,
            ]);

            // Update user subscription tier based on status
            $user = $localSubscription->user;
            if ($subscription->status === 'active') {
                $user->update(['subscription_tier' => $localSubscription->subscription_tier]);
            } elseif ($subscription->status === 'canceled') {
                $user->update(['subscription_tier' => 'expired']);
            }
        }
    }

    /**
     * Handle payment succeeded.
     */
    private function handlePaymentSucceeded($invoice)
    {
        \Log::info('Payment succeeded for invoice: ' . $invoice->id);
        // Additional logic can be added here
    }

    /**
     * Handle subscription deleted.
     */
    private function handleSubscriptionDeleted($subscription)
    {
        \Log::info('Subscription deleted: ' . $subscription->id);
        
        // Update local subscription record
        $localSubscription = Subscription::where('stripe_id', $subscription->id)->first();
        if ($localSubscription) {
            $localSubscription->update([
                'stripe_status' => 'canceled',
            ]);

            // Update user subscription tier
            $user = $localSubscription->user;
            $user->update(['subscription_tier' => 'expired']);
        }
    }
}
