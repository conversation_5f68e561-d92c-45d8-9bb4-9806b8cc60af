<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\NotificationSettingController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\StripeWebController;
use App\Http\Controllers\PlaidController;
use App\Http\Controllers\PlaidPaymentController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Route::get('/', [App\Http\Controllers\WelcomeController::class, 'index']);
Route::get('/api-docs', [App\Http\Controllers\ApiDocController::class, 'index'])->name('api.docs');

// Email testing routes
Route::get('/test-email', [App\Http\Controllers\EmailTestController::class, 'index'])->name('test.email');
Route::post('/test-email/send', [App\Http\Controllers\EmailTestController::class, 'sendTestEmail'])->name('test.email.send');
Route::post('/test-email/status', [App\Http\Controllers\EmailTestController::class, 'checkEmailStatus'])->name('test.email.status');
Route::get('/test-email/connection', [App\Http\Controllers\EmailTestController::class, 'testConnection'])->name('test.email.connection');

// Simple Stripe Blade Routes
Route::get('/stripe/redirect', [StripeWebController::class, 'showStripeRedirect'])->name('stripe.redirect');
Route::get('/stripe/success', [StripeWebController::class, 'handleSuccess'])->name('stripe.success');
Route::get('/stripe/cancel', [StripeWebController::class, 'handleCancel'])->name('stripe.cancel');

// Disable default Auth routes and define custom ones
Auth::routes(['register' => false, 'login' => false, 'reset' => false]);

// Admin login routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/login', [App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [App\Http\Controllers\Auth\LoginController::class, 'login']);
    Route::post('/logout', [App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');
});

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// User settings routes
Route::middleware(['auth'])->group(function () {
    Route::get('/settings/notifications', [NotificationSettingController::class, 'index'])->name('settings.notifications');
    Route::put('/settings/notifications', [NotificationSettingController::class, 'update'])->name('settings.notifications.update');

    // Subscription routes
    Route::get('/subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('/subscriptions/plans', [SubscriptionController::class, 'plans'])->name('subscriptions.plans');
    Route::get('/subscriptions/checkout', [SubscriptionController::class, 'checkout'])->name('subscriptions.checkout');
    Route::post('/subscriptions/process', [SubscriptionController::class, 'process'])->name('subscriptions.process');
    Route::get('/subscriptions/success/{uuid?}', [SubscriptionController::class, 'success'])->name('subscriptions.success');
    Route::get('/subscriptions/billing-history', [SubscriptionController::class, 'billingHistory'])->name('subscriptions.billing-history');
    Route::post('/subscriptions/add-payment', [SubscriptionController::class, 'addPaymentMethod'])->name('subscriptions.add-payment');
    Route::post('/subscriptions/set-default-payment/{paymentMethodId}', [SubscriptionController::class, 'setDefaultPaymentMethod'])->name('subscriptions.set-default-payment');
    Route::delete('/subscriptions/remove-payment/{paymentMethodId}', [SubscriptionController::class, 'removePaymentMethod'])->name('subscriptions.remove-payment');
    Route::get('/subscriptions/update-billing-cycle/{cycle}', [SubscriptionController::class, 'updateBillingCycle'])->name('subscriptions.update-billing-cycle');
    Route::post('/subscriptions/{uuid}/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
    Route::get('/subscriptions/{uuid}/resume', [SubscriptionController::class, 'resume'])->name('subscriptions.resume');

    // Plaid routes
    Route::prefix('plaid')->name('plaid.')->group(function () {
        Route::get('/', [PlaidController::class, 'index'])->name('index');
        Route::get('/create', [PlaidController::class, 'create'])->name('create');
        Route::post('/store', [PlaidController::class, 'store'])->name('store');
        Route::post('/{id}/set-default', [PlaidController::class, 'setDefault'])->name('set-default');
        Route::post('/{id}/toggle-payment', [PlaidController::class, 'togglePayment'])->name('toggle-payment');
        Route::delete('/{id}', [PlaidController::class, 'destroy'])->name('destroy');
        Route::get('/test', [PlaidController::class, 'test'])->name('test');
    });

    // Plaid Payment routes
    Route::prefix('plaid-payment')->name('plaid-payment.')->group(function () {
        Route::get('/checkout', [PlaidPaymentController::class, 'showPaymentForm'])->name('checkout');
        Route::post('/process', [PlaidPaymentController::class, 'processPayment'])->name('process');
    });

    // Reports routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [App\Http\Controllers\ReportController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\ReportController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\ReportController::class, 'store'])->name('store');
        Route::get('/{report}', [App\Http\Controllers\ReportController::class, 'show'])->name('show');
        Route::delete('/{report}', [App\Http\Controllers\ReportController::class, 'destroy'])->name('destroy');
        Route::get('/{report}/download', [App\Http\Controllers\ReportController::class, 'download'])->name('download');
        Route::post('/{report}/regenerate', [App\Http\Controllers\ReportController::class, 'regenerate'])->name('regenerate');
    });
});

// Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/search', [AdminController::class, 'search'])->name('search');

    // User management
    Route::get('/users', [AdminController::class, 'users'])->name('users.index');
    Route::get('/users/create', [AdminController::class, 'createUser'])->name('users.create');
    Route::post('/users', [AdminController::class, 'storeUser'])->name('users.store');
    Route::get('/users/{uuid}', [AdminController::class, 'showUser'])->name('users.show');
    Route::post('/users/{uuid}/toggle-status', [AdminController::class, 'toggleUserStatus'])->name('users.toggle-status');
    Route::post('/users/{uuid}/block', [AdminController::class, 'blockUser'])->name('users.block');
    Route::post('/users/{uuid}/unblock', [AdminController::class, 'unblockUser'])->name('users.unblock');
    Route::post('/users/{uuid}/ban', [AdminController::class, 'banUser'])->name('users.ban');
    Route::post('/users/{uuid}/unban', [AdminController::class, 'unbanUser'])->name('users.unban');
    Route::delete('/users/{uuid}', [AdminController::class, 'destroyUser'])->name('users.destroy');

    // Transaction management
    Route::get('/transactions', [AdminController::class, 'transactions'])->name('transactions.index');

    // Bin management
    Route::get('/bins', [AdminController::class, 'bins'])->name('bins.index');
    Route::get('/bins/{id}', [AdminController::class, 'showBin'])->name('bins.show');

    // Crypto wallet management
    Route::get('/crypto-wallets', [AdminController::class, 'cryptoWallets'])->name('crypto-wallets.index');

    // Subscription management
    Route::prefix('subscriptions')->name('subscriptions.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Admin\SubscriptionController::class, 'dashboard'])->name('dashboard');
        Route::get('/', [App\Http\Controllers\Admin\SubscriptionController::class, 'index'])->name('index');
        Route::get('/packages', [App\Http\Controllers\Admin\SubscriptionController::class, 'packages'])->name('packages');
        Route::get('/create', [App\Http\Controllers\Admin\SubscriptionController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\SubscriptionController::class, 'store'])->name('store');
        Route::get('/{uuid}', [App\Http\Controllers\Admin\SubscriptionController::class, 'show'])->name('show');
        Route::get('/{uuid}/edit', [App\Http\Controllers\Admin\SubscriptionController::class, 'edit'])->name('edit');
        Route::put('/{uuid}', [App\Http\Controllers\Admin\SubscriptionController::class, 'update'])->name('update');
        Route::delete('/{uuid}', [App\Http\Controllers\Admin\SubscriptionController::class, 'destroy'])->name('destroy');
        Route::post('/{uuid}/cancel', [App\Http\Controllers\Admin\SubscriptionController::class, 'cancel'])->name('cancel');
        Route::post('/{uuid}/resume', [App\Http\Controllers\Admin\SubscriptionController::class, 'resume'])->name('resume');
        Route::post('/{uuid}/pause', [App\Http\Controllers\Admin\SubscriptionController::class, 'pause'])->name('pause');
        Route::post('/{uuid}/unpause', [App\Http\Controllers\Admin\SubscriptionController::class, 'unpause'])->name('unpause');
    });

    // Settings management
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::post('/settings/general', [SettingsController::class, 'updateGeneral'])->name('settings.update.general');
    Route::post('/settings/email', [SettingsController::class, 'updateEmail'])->name('settings.update.email');
    Route::post('/settings/payment', [SettingsController::class, 'updatePayment'])->name('settings.update.payment');
    Route::post('/settings/plaid', [SettingsController::class, 'updatePlaid'])->name('settings.update.plaid');
    Route::post('/settings/system', [SettingsController::class, 'updateSystem'])->name('settings.update.system');
    Route::post('/settings/welcome', [SettingsController::class, 'updateWelcome'])->name('settings.update.welcome');
    Route::post('/settings/reset', [SettingsController::class, 'resetSettings'])->name('settings.reset');
    Route::get('/settings/export', [SettingsController::class, 'exportSettings'])->name('settings.export');
    Route::get('/settings/import', [SettingsController::class, 'showImportForm'])->name('settings.import.form');
    Route::post('/settings/import', [SettingsController::class, 'importSettings'])->name('settings.import');
    Route::get('/settings/test', [SettingsController::class, 'showTestPage'])->name('settings.test');
    Route::post('/settings/test/email', [SettingsController::class, 'testEmail'])->name('settings.test.email');
    Route::post('/settings/test/stripe', [SettingsController::class, 'testStripe'])->name('settings.test.stripe');
    Route::post('/settings/test/plaid', [SettingsController::class, 'testPlaid'])->name('settings.test.plaid');
    Route::get('/settings/audit', [SettingsController::class, 'showAuditLog'])->name('settings.audit');
});
