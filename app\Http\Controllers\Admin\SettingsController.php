<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\TestMail;
use App\Models\Setting;
use App\Models\SettingLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Stripe;

class SettingsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display the settings page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get settings by group using the helper function
        $settings = [
            'general' => $this->arrayToAssoc(setting_group('general')),
            'email' => $this->arrayToAssoc(setting_group('email')),
            'payment' => $this->arrayToAssoc(setting_group('payment')),
            'plaid' => $this->arrayToAssoc(setting_group('plaid')),
            'system' => $this->arrayToAssoc(setting_group('system')),
            'welcome' => $this->arrayToAssoc(setting_group('welcome')),
        ];

        // Get list of timezones
        $timezones = timezone_identifiers_list();

        return view('admin.settings.index', compact('settings', 'timezones'));
    }

    /**
     * Update general settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateGeneral(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'app_name' => 'required|string|max:255',
            'app_description' => 'nullable|string|max:1000',
            'footer_text' => 'nullable|string|max:1000',
            'header_text' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'favicon' => 'nullable|image|mimes:ico,png|max:1024',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update general settings using the helper function
        setting([
            'app_name' => $request->app_name,
            'app_description' => $request->app_description,
            'footer_text' => $request->footer_text,
            'header_text' => $request->header_text
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            $oldLogo = setting('logo');
            if ($oldLogo && Storage::exists(str_replace('/storage', 'public', $oldLogo))) {
                Storage::delete(str_replace('/storage', 'public', $oldLogo));
            }

            // Store new logo
            $logo = $request->file('logo');
            $logoName = 'logo_' . time() . '.' . $logo->getClientOriginalExtension();

            // Make sure the directory exists
            if (!Storage::exists('public/settings')) {
                Storage::makeDirectory('public/settings');
            }

            // Store the file
            $logoPath = $logo->storeAs('public/settings', $logoName);

            // Get the URL with the correct path - ensure it's a full URL
            $logoUrl = asset(Storage::url($logoPath));

            // Log the file path and URL for debugging
            Log::info('Logo upload', [
                'file' => $logo->getClientOriginalName(),
                'path' => $logoPath,
                'url' => $logoUrl,
                'exists' => Storage::exists($logoPath)
            ]);

            setting(['logo' => $logoUrl]);
        }

        // Handle favicon upload
        if ($request->hasFile('favicon')) {
            // Delete old favicon if exists
            $oldFavicon = setting('favicon');
            if ($oldFavicon && Storage::exists(str_replace('/storage', 'public', $oldFavicon))) {
                Storage::delete(str_replace('/storage', 'public', $oldFavicon));
            }

            // Store new favicon
            $favicon = $request->file('favicon');
            $faviconName = 'favicon_' . time() . '.' . $favicon->getClientOriginalExtension();

            // Make sure the directory exists
            if (!Storage::exists('public/settings')) {
                Storage::makeDirectory('public/settings');
            }

            // Store the file
            $faviconPath = $favicon->storeAs('public/settings', $faviconName);

            // Get the URL with the correct path - ensure it's a full URL
            $faviconUrl = asset(Storage::url($faviconPath));

            // Log the file path and URL for debugging
            Log::info('Favicon upload', [
                'file' => $favicon->getClientOriginalName(),
                'path' => $faviconPath,
                'url' => $faviconUrl,
                'exists' => Storage::exists($faviconPath)
            ]);

            setting(['favicon' => $faviconUrl]);
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'General settings updated successfully');
    }

    /**
     * Update email settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mail_mailer' => 'required|string|in:smtp,sendmail,mailgun,ses,postmark,log,array',
            'mail_host' => 'required_if:mail_mailer,smtp|nullable|string|max:255',
            'mail_port' => 'required_if:mail_mailer,smtp|nullable|numeric',
            'mail_username' => 'required_if:mail_mailer,smtp|nullable|string|max:255',
            'mail_password' => 'nullable|string|max:255',
            'mail_encryption' => 'nullable|string|in:tls,ssl,null',
            'mail_from_address' => 'required|email|max:255',
            'mail_from_name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Prepare email settings
        $emailSettings = [
            'mail_mailer' => $request->mail_mailer,
            'mail_host' => $request->mail_host,
            'mail_port' => $request->mail_port,
            'mail_username' => $request->mail_username,
            'mail_encryption' => $request->mail_encryption,
            'mail_from_address' => $request->mail_from_address,
            'mail_from_name' => $request->mail_from_name,
        ];

        // Only update password if provided
        if ($request->filled('mail_password')) {
            $emailSettings['mail_password'] = $request->mail_password;
        }

        // Update all email settings at once
        foreach ($emailSettings as $key => $value) {
            Setting::set($key, $value, 'email', $key === 'mail_port' ? 'integer' : 'string');
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Email settings updated successfully');
    }

    /**
     * Update payment settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'stripe_key' => 'required|string|max:255',
            'stripe_secret' => 'required|string|max:255',
            'stripe_webhook_secret' => 'nullable|string|max:255',
            'stripe_price_base_monthly' => 'required|string|max:255',
            'stripe_price_premium_monthly' => 'required|string|max:255',
            'stripe_price_base_yearly' => 'nullable|string|max:255',
            'stripe_price_premium_yearly' => 'nullable|string|max:255',
            'subscription_trial_days' => 'required|integer|min:0',
            'subscription_base_monthly_price' => 'required|numeric|min:0',
            'subscription_base_yearly_price' => 'required|numeric|min:0',
            'subscription_premium_monthly_price' => 'required|numeric|min:0',
            'subscription_premium_yearly_price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update Stripe settings using the helper function
        setting([
            'stripe_key' => $request->stripe_key,
            'stripe_secret' => $request->stripe_secret,
            'stripe_webhook_secret' => $request->stripe_webhook_secret,
            'stripe_price_base_monthly' => $request->stripe_price_base_monthly,
            'stripe_price_premium_monthly' => $request->stripe_price_premium_monthly,
            'stripe_price_base_yearly' => $request->stripe_price_base_yearly,
            'stripe_price_premium_yearly' => $request->stripe_price_premium_yearly,
            'subscription_trial_days' => $request->subscription_trial_days,
            'subscription_base_monthly_price' => $request->subscription_base_monthly_price,
            'subscription_base_yearly_price' => $request->subscription_base_yearly_price,
            'subscription_premium_monthly_price' => $request->subscription_premium_monthly_price,
            'subscription_premium_yearly_price' => $request->subscription_premium_yearly_price
        ]);

        // Update the configuration values
        config([
            'services.subscription.trial_days' => $request->subscription_trial_days,
            'services.subscription.base_monthly_price' => $request->subscription_base_monthly_price,
            'services.subscription.base_yearly_price' => $request->subscription_base_yearly_price,
            'services.subscription.premium_monthly_price' => $request->subscription_premium_monthly_price,
            'services.subscription.premium_yearly_price' => $request->subscription_premium_yearly_price,
        ]);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Payment settings updated successfully');
    }

    /**
     * Update Plaid settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePlaid(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plaid_client_id' => 'required|string|max:255',
            'plaid_secret' => 'required|string|max:255',
            'plaid_environment' => 'required|string|in:sandbox,development,production',
            'plaid_client_name' => 'required|string|max:255',
            'plaid_products' => 'required|string|max:255',
            'plaid_country_codes' => 'required|string|max:255',
            'plaid_language' => 'required|string|max:10',
            'plaid_webhook' => 'nullable|url|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update Plaid settings using the helper function
        setting([
            'plaid_client_id' => $request->plaid_client_id,
            'plaid_secret' => $request->plaid_secret,
            'plaid_environment' => $request->plaid_environment,
            'plaid_client_name' => $request->plaid_client_name,
            'plaid_products' => $request->plaid_products,
            'plaid_country_codes' => $request->plaid_country_codes,
            'plaid_language' => $request->plaid_language,
            'plaid_webhook' => $request->plaid_webhook,
        ]);

        // Update the configuration values
        config([
            'plaid.client_id' => $request->plaid_client_id,
            'plaid.secret' => $request->plaid_secret,
            'plaid.environment' => $request->plaid_environment,
            'plaid.client_name' => $request->plaid_client_name,
            'plaid.products' => explode(',', $request->plaid_products),
            'plaid.country_codes' => explode(',', $request->plaid_country_codes),
            'plaid.language' => $request->plaid_language,
            'plaid.webhook' => $request->plaid_webhook,
        ]);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Plaid settings updated successfully');
    }



    /**
     * Update system settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSystem(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'timezone' => 'required|string|in:' . implode(',', timezone_identifiers_list()),
            'date_format' => 'required|string|max:50',
            'time_format' => 'required|string|max:50',
            'currency' => 'required|string|size:3',
            'currency_symbol' => 'required|string|max:10',
            'maintenance_mode' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update system settings using the helper function
        setting([
            'timezone' => $request->timezone,
            'date_format' => $request->date_format,
            'time_format' => $request->time_format,
            'currency' => $request->currency,
            'currency_symbol' => $request->currency_symbol,
            'maintenance_mode' => $request->has('maintenance_mode')
        ]);

        return redirect()->route('admin.settings.index')
            ->with('success', 'System settings updated successfully');
    }

    /**
     * Update welcome page settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateWelcome(Request $request)
    {
        // Process navbar settings
        $navbarBrand = $request->input('navbar_brand', 'PocketWatch');
        $navbarLinks = $request->input('navbar_links', []);

        // Process slider settings
        $sliders = $request->input('welcome_sliders', []);

        // Process features settings
        $featuresTitle = $request->input('features_title', 'Features');
        $features = $request->input('welcome_features', []);

        // Process testimonials settings
        $testimonialsTitle = $request->input('testimonials_title', 'Testimonials');
        $testimonials = $request->input('welcome_testimonials', []);

        // Process packages settings
        $packagesTitle = $request->input('packages_title', 'Packages');
        $packages = $request->input('welcome_packages', []);

        // Process package features (convert from text to array)
        foreach ($packages as $key => $package) {
            if (isset($package['features_text'])) {
                $featuresArray = array_filter(explode("\n", $package['features_text']), 'trim');
                $packages[$key]['features'] = $featuresArray;
                unset($packages[$key]['features_text']);
            }
        }

        // Process social links
        $socialLinks = $request->input('social_links', []);

        // Process theme colors
        $primaryColor = $request->input('primary_color', '#39FF14');
        $backgroundColor = $request->input('background_color', '#0a0a0a');
        $textColor = $request->input('text_color', '#39FF14');

        // Save all settings
        setting([
            'navbar_brand' => $navbarBrand,
            'navbar_links' => $navbarLinks,
            'welcome_sliders' => $sliders,
            'features_title' => $featuresTitle,
            'welcome_features' => $features,
            'testimonials_title' => $testimonialsTitle,
            'welcome_testimonials' => $testimonials,
            'packages_title' => $packagesTitle,
            'welcome_packages' => $packages,
            'social_links' => $socialLinks,
            'primary_color' => $primaryColor,
            'background_color' => $backgroundColor,
            'text_color' => $textColor
        ]);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Welcome page settings updated successfully');
    }

    /**
     * Convert settings collection to associative array.
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $settings
     * @return array
     */
    private function arrayToAssoc($settings)
    {
        $result = [];

        foreach ($settings as $setting) {
            $result[$setting->key] = $setting->value;
        }

        return $result;
    }

    /**
     * Reset settings to default values.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resetSettings()
    {
        // Run the settings seeder to reset to defaults
        $seeder = new \Database\Seeders\SettingsSeeder();
        $seeder->run();

        // Clear the settings cache
        setting_clear_cache();

        return redirect()->route('admin.settings.index')
            ->with('success', 'All settings have been reset to default values');
    }

    /**
     * Export settings to a JSON file.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportSettings()
    {
        // Get all settings
        $settings = Setting::all()->toArray();

        // Create a filename with timestamp
        $filename = 'settings_export_' . date('Y-m-d_H-i-s') . '.json';

        // Create a temporary file
        $tempFile = storage_path('app/temp/' . $filename);

        // Ensure the directory exists
        if (!file_exists(storage_path('app/temp'))) {
            mkdir(storage_path('app/temp'), 0755, true);
        }

        // Write the settings to the file
        file_put_contents($tempFile, json_encode($settings, JSON_PRETTY_PRINT));

        // Return the file as a download
        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/json',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Show the import settings form.
     *
     * @return \Illuminate\View\View
     */
    public function showImportForm()
    {
        return view('admin.settings.import');
    }

    /**
     * Show the test settings page.
     *
     * @return \Illuminate\View\View
     */
    public function showTestPage()
    {
        return view('admin.settings.test');
    }

    /**
     * Test email settings by sending a test email.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function testEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'test_email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.settings.test')
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Send a test email
            Mail::to($request->test_email)->send(new TestMail());

            return redirect()->route('admin.settings.test')
                ->with('success', 'Test email sent successfully to ' . $request->test_email);
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.test')
                ->with('error', 'Failed to send test email: ' . $e->getMessage());
        }
    }

    /**
     * Test Stripe settings by making a test API call.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function testStripe()
    {
        try {
            // Get Stripe API key from settings
            $stripeKey = setting('stripe_secret');

            if (empty($stripeKey)) {
                throw new \Exception('Stripe secret key is not configured');
            }

            // Set the API key
            Stripe\Stripe::setApiKey($stripeKey);

            // Make a simple API call to test the connection
            Stripe\Balance::retrieve();

            return redirect()->route('admin.settings.test')
                ->with('success', 'Stripe connection successful. Your account is active.');
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.test')
                ->with('error', 'Failed to connect to Stripe: ' . $e->getMessage());
        }
    }

    /**
     * Test Plaid settings by making a test API call.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function testPlaid()
    {
        try {
            // Get Plaid API credentials from settings
            $plaidClientId = setting('plaid_client_id');
            $plaidSecret = setting('plaid_secret');
            $plaidEnvironment = setting('plaid_environment', 'sandbox');

            if (empty($plaidClientId) || empty($plaidSecret)) {
                throw new \Exception('Plaid client ID or secret is not configured');
            }

            // Determine the base URL based on the environment
            $baseUrl = 'https://sandbox.plaid.com';
            if ($plaidEnvironment === 'development') {
                $baseUrl = 'https://development.plaid.com';
            } elseif ($plaidEnvironment === 'production') {
                $baseUrl = 'https://production.plaid.com';
            }

            // Make a simple API call to test the connection
            $response = \Illuminate\Support\Facades\Http::post($baseUrl . '/institutions/get', [
                'client_id' => $plaidClientId,
                'secret' => $plaidSecret,
                'count' => 1,
                'offset' => 0,
                'country_codes' => explode(',', setting('plaid_country_codes', 'US,CA')),
            ]);

            if (!$response->successful()) {
                throw new \Exception('API call failed: ' . $response->body());
            }

            return redirect()->route('admin.settings.test')
                ->with('success', 'Plaid connection successful. Your account is active.');
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.test')
                ->with('error', 'Failed to connect to Plaid: ' . $e->getMessage());
        }
    }


    /**
     * Show the settings audit log.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function showAuditLog(Request $request)
    {
        // Get the logs with pagination
        $query = SettingLog::with('user')->orderBy('created_at', 'desc');

        // Filter by key if provided
        if ($request->has('key') && !empty($request->key)) {
            $query->where('setting_key', 'like', '%' . $request->key . '%');
        }

        // Filter by user if provided
        if ($request->has('user_id') && !empty($request->user_id)) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by date range if provided
        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->paginate(20);

        return view('admin.settings.audit', compact('logs'));
    }

    /**
     * Import settings from a JSON file.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function importSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings_file' => 'required|file|mimes:json|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.settings.import')
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Get the uploaded file
            $file = $request->file('settings_file');

            // Read the file contents
            $contents = file_get_contents($file->getRealPath());

            // Decode the JSON
            $settings = json_decode($contents, true);

            if (!is_array($settings)) {
                throw new \Exception('Invalid settings file format');
            }

            // Begin a transaction
            DB::beginTransaction();

            // Import each setting
            foreach ($settings as $setting) {
                if (!isset($setting['key']) || !isset($setting['value'])) {
                    continue;
                }

                Setting::set(
                    $setting['key'],
                    $setting['value'],
                    $setting['group'] ?? 'general',
                    $setting['type'] ?? 'string'
                );
            }

            // Commit the transaction
            DB::commit();

            // Clear the settings cache
            setting_clear_cache();

            return redirect()->route('admin.settings.index')
                ->with('success', 'Settings imported successfully');
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();

            return redirect()->route('admin.settings.import')
                ->with('error', 'Failed to import settings: ' . $e->getMessage());
        }
    }
}
