<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Test - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2d3748;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            opacity: 0.9;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .config-info {
            background: #e2e3e5;
            border: 1px solid #d6d8db;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        .test-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 20px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📧 {{ config('app.name') }} Email Test</div>
            <p>Test email verification and password reset functionality</p>
        </div>

        <div class="config-info">
            <strong>📧 Current Email Configuration:</strong><br>
            Mail Driver: {{ config('mail.default') }}<br>
            SMTP Host: {{ config('mail.mailers.smtp.host') }}<br>
            SMTP Port: {{ config('mail.mailers.smtp.port') }}<br>
            SMTP Username: {{ config('mail.mailers.smtp.username') }}<br>
            From Address: {{ config('mail.from.address') }}<br>
            From Name: {{ config('mail.from.name') }}
        </div>

        <form id="emailTestForm">
            @csrf
            <div class="form-group">
                <label for="email">📧 Test Email Address:</label>
                <input type="email" id="email" name="email" required 
                       placeholder="Enter your @gmail.com or @live.com email">
            </div>

            <div class="form-group">
                <label for="test_type">🧪 Test Type:</label>
                <select id="test_type" name="test_type" required>
                    <option value="">Select test type</option>
                    <option value="verification">Email Verification Code</option>
                    <option value="password_reset">Password Reset Code</option>
                    <option value="simple_test">Simple Test Email</option>
                </select>
            </div>

            <div class="test-buttons">
                <button type="submit">🚀 Send Test Email</button>
                <button type="button" onclick="checkEmailStatus()">📊 Check Email Status</button>
            </div>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Sending email...</p>
        </div>

        <div class="result" id="result"></div>
    </div>

    <script>
        document.getElementById('emailTestForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const testType = document.getElementById('test_type').value;
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            if (!email || !testType) {
                showResult('Please fill in all fields', 'error');
                return;
            }
            
            loading.style.display = 'block';
            result.style.display = 'none';
            
            try {
                const response = await fetch('/test-email/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                    },
                    body: JSON.stringify({
                        email: email,
                        test_type: testType
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult(`✅ ${data.message}<br><br>📧 Details:<br>${data.details || ''}`, 'success');
                } else {
                    showResult(`❌ ${data.message}<br><br>🔍 Error: ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network error: ${error.message}`, 'error');
            } finally {
                loading.style.display = 'none';
            }
        });
        
        async function checkEmailStatus() {
            const email = document.getElementById('email').value;
            
            if (!email) {
                showResult('Please enter an email address first', 'error');
                return;
            }
            
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            loading.style.display = 'block';
            result.style.display = 'none';
            
            try {
                const response = await fetch('/test-email/status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                    },
                    body: JSON.stringify({
                        email: email
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult(`📊 Email Status:<br><br>${data.status}`, 'success');
                } else {
                    showResult(`❌ ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network error: ${error.message}`, 'error');
            } finally {
                loading.style.display = 'none';
            }
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
    </script>
</body>
</html>
