<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plaid_accounts', function (Blueprint $table) {
            $table->id();
            // $table->uuid('uuid')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('institution_id');
            $table->string('institution_name');
            $table->string('account_id');
            $table->string('account_name');
            $table->string('account_type');
            $table->string('account_subtype')->nullable();
            $table->string('account_mask')->nullable();
            $table->text('access_token');
            $table->text('item_id');
            $table->boolean('is_default')->default(false);
            $table->boolean('is_payment_enabled')->default(false);
            $table->json('metadata')->nullable();
            $table->timestamp('last_synced_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plaid_accounts');
    }
};
