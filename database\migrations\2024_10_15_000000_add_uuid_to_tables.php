<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * The tables to add UUID columns to.
     *
     * @var array
     */
    protected $tables = [
        'users',
        'bins',
        'sub_bins',
        'transactions',
        'subscriptions',
        'crypto_wallets',
        'plaid_accounts',
        'reports',
    ];

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        foreach ($this->tables as $table) {
            // Only add UUID column if table exists
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $table) {
                    $table->uuid('uuid')->after('id')->nullable()->unique();
                });
            }
        }

        // Generate UUIDs for existing records
        foreach ($this->tables as $tableName) {
            // Only process if table exists
            if (Schema::hasTable($tableName)) {
                $tableClass = $this->getModelClass($tableName);
                if (class_exists($tableClass)) {
                    $tableClass::withTrashed()->whereNull('uuid')->each(function ($record) {
                        $record->uuid = (string) Str::uuid();
                        $record->saveQuietly();
                    });
                } else {
                    // Fallback to raw DB queries if model doesn't exist
                    DB::table($tableName)->whereNull('uuid')->update([
                        'uuid' => Str::uuid(),
                    ]);
                }
            }
        }

        // Make UUID columns non-nullable after populating them
        foreach ($this->tables as $table) {
            // Only process if table exists
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $table) {
                    $table->uuid('uuid')->nullable(false)->change();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        foreach ($this->tables as $table) {
            // Only process if table exists
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $table) {
                    $table->dropColumn('uuid');
                });
            }
        }
    }

    /**
     * Get the model class for a table name.
     *
     * @param string $tableName
     * @return string
     */
    protected function getModelClass($tableName)
    {
        $modelMap = [
            'users' => \App\Models\User::class,
            'bins' => \App\Models\Bin::class,
            'sub_bins' => \App\Models\SubBin::class,
            'transactions' => \App\Models\Transaction::class,
            'subscriptions' => \App\Models\Subscription::class,
            'crypto_wallets' => \App\Models\CryptoWallet::class,
            'plaid_accounts' => \App\Models\PlaidAccount::class,
            'reports' => \App\Models\Report::class,
        ];

        return $modelMap[$tableName] ?? '';
    }
};
