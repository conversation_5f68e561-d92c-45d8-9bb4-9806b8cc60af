@extends('layouts.app')

@section('content')
<div class="d-flex">
    <!-- Sidebar -->
    @include('layouts.sidebar')

    <!-- Main Content -->
    <div class="content-wrapper">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">{{ __('Dashboard') }}</h1>
                <button class="btn sidebar-toggle d-md-none">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            @if (session('status'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('status') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100">
                        <div class="card-body">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="stat-value">0</div>
                                <div class="stat-label">Total Bins</div>
                                <div class="mt-3">
                                    <a href="#" class="text-decoration-none">
                                        <i class="fas fa-plus-circle me-1"></i>Create New
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100">
                        <div class="card-body">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <div class="stat-value">0</div>
                                <div class="stat-label">Transactions</div>
                                <div class="mt-3">
                                    <a href="#" class="text-decoration-none">
                                        <i class="fas fa-plus-circle me-1"></i>Add New
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100">
                        <div class="card-body">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                <div class="stat-value">0</div>
                                <div class="stat-label">
                                    Crypto Wallets
                                    @if(!Auth::user()->hasPremium() && !Auth::user()->onTrial())
                                    <span class="badge bg-secondary ms-1">Premium</span>
                                    @endif
                                </div>
                                <div class="mt-3">
                                    @if(Auth::user()->hasPremium() || Auth::user()->onTrial())
                                    <a href="#" class="text-decoration-none">
                                        <i class="fas fa-plus-circle me-1"></i>Connect Wallet
                                    </a>
                                    @else
                                    <a href="#" class="text-decoration-none text-muted" data-bs-toggle="tooltip" title="Requires Premium Subscription">
                                        <i class="fas fa-lock me-1"></i>Premium Feature
                                    </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100">
                        <div class="card-body">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="stat-value">
                                    @if(Auth::user()->onTrial())
                                        Trial
                                    @elseif(Auth::user()->hasPremium())
                                        Premium
                                    @else
                                        Base
                                    @endif
                                </div>
                                <div class="stat-label">Subscription</div>
                                <div class="mt-3">
                                    @if(Auth::user()->onTrial())
                                        <a href="#" class="text-decoration-none">
                                            <i class="fas fa-arrow-circle-up me-1"></i>Continue Premium
                                        </a>
                                    @elseif(!Auth::user()->hasPremium())
                                        <a href="#" class="text-decoration-none">
                                            <i class="fas fa-arrow-circle-up me-1"></i>Upgrade
                                        </a>
                                    @else
                                        <span class="text-success">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

    <div class="row">
        <div class="col-md-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold" style="color: var(--primary-color);">Recent Transactions</h6>
                    <a href="#" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>New Transaction
                    </a>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <p>No transactions found</p>
                        <a href="#" class="btn btn-outline-primary mt-2">
                            <i class="fas fa-plus me-1"></i>Add Your First Transaction
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold" style="color: var(--primary-color);">Subscription Details</h6>
                </div>
                <div class="card-body">
                    <div class="subscription-details">
                        <div class="mb-3">
                            <span class="text-muted">Current Plan:</span>
                            <span class="float-end fw-bold">
                                @if(Auth::user()->onTrial())
                                    Trial ({{ Auth::user()->subscription_tier == 'premium' ? 'Premium' : 'Base' }})
                                @else
                                    {{ ucfirst(Auth::user()->subscription_tier) }}
                                @endif
                            </span>
                        </div>
                        <div class="mb-3">
                            <span class="text-muted">Status:</span>
                            <span class="float-end">
                                @if(Auth::user()->onTrial())
                                    <span class="badge bg-info">Trial</span>
                                @else
                                    <span class="badge bg-success">Active</span>
                                @endif
                            </span>
                        </div>
                        <div class="mb-3">
                            <span class="text-muted">Price:</span>
                            <span class="float-end">
                                @if(Auth::user()->subscription_tier == 'premium')
                                    $10.00/month
                                @else
                                    $5.00/month
                                @endif
                            </span>
                        </div>
                        @if(Auth::user()->onTrial())
                        <div class="mb-3">
                            <span class="text-muted">Trial Ends:</span>
                            <span class="float-end">{{ Auth::user()->getActiveSubscription()->trial_ends_at->format('M d, Y') }}</span>
                        </div>
                        @else
                        <div class="mb-3">
                            <span class="text-muted">Next Billing:</span>
                            <span class="float-end">{{ now()->addMonth()->format('M d, Y') }}</span>
                        </div>
                        @endif
                        <hr>
                        <div class="d-grid gap-2">
                            @if(Auth::user()->onTrial())
                                <a href="#" class="btn btn-success">Continue with Premium</a>
                            @elseif(Auth::user()->subscription_tier != 'premium')
                                <a href="#" class="btn btn-success">Upgrade to Premium</a>
                            @else
                                <a href="#" class="btn btn-outline-secondary">Manage Subscription</a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold" style="color: var(--primary-color);">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-plus-circle me-2" style="color: var(--primary-color);"></i>Create New Bin
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-exchange-alt me-2" style="color: var(--primary-color);"></i>Record Transaction
                        </a>
                        @if(Auth::user()->hasPremium() || Auth::user()->onTrial())
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-wallet me-2" style="color: var(--primary-color);"></i>Connect Crypto Wallet
                        </a>
                        @else
                        <a href="#" class="list-group-item list-group-item-action disabled" data-bs-toggle="tooltip" title="Requires Premium Subscription">
                            <i class="fas fa-wallet me-2 text-muted"></i>Connect Crypto Wallet
                            <span class="badge bg-secondary float-end">Premium</span>
                        </a>
                        @endif
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-line me-2" style="color: var(--primary-color);"></i>View Reports
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog me-2" style="color: var(--primary-color);"></i>Account Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .stat-card {
        border-radius: 10px;
        padding: 1.5rem;
        height: 100%;
        background-color: white;
        position: relative;
        overflow: hidden;
    }

    .stat-card .stat-icon {
        position: absolute;
        top: 1rem;
        right: 1rem;
        font-size: 2.5rem;
        opacity: 0.2;
        color: var(--primary-color);
    }

    .stat-card .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .stat-card .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .border-left-primary {
        border-left: 4px solid var(--primary-color);
    }

    .border-left-success {
        border-left: 4px solid #28a745;
    }

    .border-left-info {
        border-left: 4px solid #17a2b8;
    }

    .border-left-warning {
        border-left: 4px solid #ffc107;
    }

    .content-wrapper {
        margin-left: 260px;
        padding: 20px;
        width: calc(100% - 260px);
        transition: all 0.3s;
    }

    @media (max-width: 767.98px) {
        .content-wrapper {
            margin-left: 0;
            width: 100%;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.user-sidebar');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }
    });
</script>
@endsection
