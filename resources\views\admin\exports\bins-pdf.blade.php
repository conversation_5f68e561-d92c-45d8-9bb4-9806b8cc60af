<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bins Financial Activity Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0;
            color: #666;
        }
        
        .bin-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .bin-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .bin-title {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            margin: 0;
        }
        
        .bin-info {
            margin: 5px 0;
            color: #666;
        }
        
        .bin-details {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .detail-item {
            text-align: center;
        }
        
        .detail-label {
            font-weight: bold;
            color: #333;
        }
        
        .detail-value {
            color: #007bff;
            font-weight: bold;
        }
        
        .sub-bins {
            padding: 15px;
        }
        
        .sub-bin-tree {
            margin-left: 0;
        }
        
        .sub-bin-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #007bff;
            background-color: #f8f9fa;
        }
        
        .sub-bin-item.depth-1 {
            margin-left: 0;
            border-left-color: #007bff;
        }
        
        .sub-bin-item.depth-2 {
            margin-left: 20px;
            border-left-color: #28a745;
        }
        
        .sub-bin-item.depth-3 {
            margin-left: 40px;
            border-left-color: #ffc107;
        }
        
        .sub-bin-item.depth-4 {
            margin-left: 60px;
            border-left-color: #dc3545;
        }
        
        .sub-bin-name {
            font-weight: bold;
            color: #333;
        }
        
        .sub-bin-details {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            font-size: 11px;
        }
        
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }
        
        .amount-positive {
            color: #28a745;
            font-weight: bold;
        }
        
        .amount-negative {
            color: #dc3545;
            font-weight: bold;
        }
        
        .no-sub-bins {
            padding: 15px;
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        .summary {
            margin-top: 30px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        
        .summary h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        
        @media print {
            body {
                margin: 0;
            }
            
            .bin-section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗂️ Bins Financial Activity Report</h1>
        <p>Generated on {{ now()->format('F j, Y \a\t g:i A') }}</p>
        <p>Total Bins: {{ $bins->count() }}</p>
    </div>

    @forelse($bins as $bin)
        <div class="bin-section">
            <div class="bin-header">
                <h2 class="bin-title">{{ $bin->name }}</h2>
                <div class="bin-info">
                    <strong>Owner:</strong> {{ $bin->user->name }} ({{ $bin->user->email }}) |
                    <strong>Type:</strong> {{ ucfirst($bin->type) }} |
                    <strong>Currency:</strong> {{ $bin->currency }} |
                    <strong>Status:</strong> 
                    <span class="{{ $bin->is_active ? 'status-active' : 'status-inactive' }}">
                        {{ $bin->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                
                <div class="bin-details">
                    <div class="detail-item">
                        <div class="detail-label">Current Amount</div>
                        <div class="detail-value amount-{{ $bin->current_amount >= 0 ? 'positive' : 'negative' }}">
                            {{ $bin->currency }} {{ number_format($bin->current_amount, 2) }}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Threshold Min</div>
                        <div class="detail-value">{{ $bin->currency }} {{ number_format($bin->threshold_min, 2) }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Threshold Max</div>
                        <div class="detail-value">
                            {{ $bin->threshold_max ? $bin->currency . ' ' . number_format($bin->threshold_max, 2) : 'Unlimited' }}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Sub-Bins</div>
                        <div class="detail-value">{{ $bin->subBins->count() }}</div>
                    </div>
                </div>
            </div>
            
            <div class="sub-bins">
                @if($bin->subBins->isNotEmpty())
                    <h4>📁 Sub-Bin Hierarchy:</h4>
                    <div class="sub-bin-tree">
                        @foreach($bin->subBins->whereNull('parent_sub_bin_id') as $subBin)
                            @include('admin.exports.partials.sub-bin-item', ['subBin' => $subBin])
                        @endforeach
                    </div>
                @else
                    <div class="no-sub-bins">
                        📭 No sub-bins found for this bin
                    </div>
                @endif
            </div>
        </div>
    @empty
        <div class="no-bins">
            <p>No bins found matching the criteria.</p>
        </div>
    @endforelse

    <div class="summary">
        <h3>📊 Summary</h3>
        <p><strong>Total Bins:</strong> {{ $bins->count() }}</p>
        <p><strong>Total Sub-Bins:</strong> {{ $bins->sum(function($bin) { return $bin->subBins->count(); }) }}</p>
        <p><strong>Active Bins:</strong> {{ $bins->where('is_active', true)->count() }}</p>
        <p><strong>Total Value:</strong> {{ $bins->first()->currency ?? 'USD' }} {{ number_format($bins->sum('current_amount'), 2) }}</p>
    </div>

    <div class="footer">
        <p>PocketWatch Admin Panel - Bins Financial Activity Report</p>
        <p>This report contains sensitive financial information. Handle with care.</p>
    </div>
</body>
</html>
