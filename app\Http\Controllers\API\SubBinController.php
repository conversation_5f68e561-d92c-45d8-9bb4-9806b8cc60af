<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Bin;
use App\Models\SubBin;
use App\Notifications\Bins\SubBinCreatedNotification;
use App\Notifications\Bins\SubBinDeletedNotification;
use App\Notifications\Bins\SubBinUpdatedNotification;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SubBinController extends Controller
{
    /**
     * The subscription service instance.
     *
     * @var \App\Services\SubscriptionService
     */
    protected $subscriptionService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\SubscriptionService  $subscriptionService
     * @return void
     */
    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $binId
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request, string $binId)
    {
        $bin = Bin::find($binId);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        $subBins = $bin->subBins;

        // Add remaining sub-bin count for this bin
        $remainingSubBins = $this->subscriptionService->getRemainingSubBinCount($request->user(), $bin->id);

        return response()->json([
            'sub_bins' => $subBins,
            'remaining_sub_bins' => $remainingSubBins,
            'max_sub_bins' => $request->user()->subscription_tier === 'premium' ? 'unlimited' : SubscriptionService::MAX_SUB_BINS_PER_BIN_BASE_TIER,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $binId
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request, string $binId)
    {
        $bin = Bin::find($binId);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        // Check if the user can create more sub-bins for this bin
        if (!$this->subscriptionService->canCreateSubBin($request->user(), $bin->id)) {
            return response()->json([
                'message' => $this->subscriptionService->getUpgradeMessage('sub-bin'),
                'error' => 'subscription_limit_reached',
                'remaining_sub_bins' => 0,
                'max_sub_bins' => SubscriptionService::MAX_SUB_BINS_PER_BIN_BASE_TIER,
                'subscription_tier' => $request->user()->subscription_tier,
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'nullable|string|in:income,expense', // Make type nullable for auto-selection
            'description' => 'nullable|string',
            'threshold_max_limit' => 'nullable|numeric|min:0', // Make optional, user can set as they want
            'threshold_max_warning' => 'nullable|numeric|lt:threshold_max_limit',
            'currency' => 'nullable|string|max:10',
            'parent_sub_bin_id' => 'nullable|exists:sub_bins,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Validate parent sub-bin if provided
        $parentSubBin = null;
        if ($request->has('parent_sub_bin_id') && $request->parent_sub_bin_id) {
            $parentSubBin = SubBin::where('bin_id', $bin->id)->find($request->parent_sub_bin_id);

            if (!$parentSubBin) {
                return response()->json([
                    'message' => 'Parent sub-bin not found or does not belong to this bin',
                ], 404);
            }

            // Check maximum nesting depth (prevent infinite nesting)
            $maxDepth = $request->user()->subscription_tier === 'premium' ? null : 3; // Premium = unlimited, Base = 3 levels
            if ($maxDepth !== null && $parentSubBin->depth_level >= $maxDepth) {
                return response()->json([
                    'message' => "Maximum nesting depth of {$maxDepth} levels reached. Upgrade to Premium for unlimited nesting.",
                    'error' => 'max_depth_reached',
                    'current_depth' => $parentSubBin->depth_level,
                    'max_depth' => $maxDepth,
                    'subscription_tier' => $request->user()->subscription_tier,
                ], 403);
            }
        }

        $subBin = new SubBin($request->all());
        $subBin->bin_id = $bin->id;
        $subBin->current_amount = 0;

        // Auto-select parent category if type not provided
        if (!$request->has('type') || !$request->type) {
            if ($parentSubBin) {
                $subBin->type = $parentSubBin->type; // Use parent sub-bin's type
            } else {
                $subBin->type = $bin->type; // Use parent bin's type
            }
        }

        // Set default threshold values if not provided
        if (!$request->has('threshold_max_limit') || $request->threshold_max_limit === null) {
            $subBin->threshold_max_limit = null; // User can set as they want, no default limit
        }

        if (!$request->has('threshold_max_warning') || $request->threshold_max_warning === null) {
            $subBin->threshold_max_warning = null; // No default warning threshold
        }

        // Set parent relationship and depth
        if ($parentSubBin) {
            $subBin->parent_sub_bin_id = $parentSubBin->id;
            $subBin->depth_level = $parentSubBin->depth_level + 1;
        } else {
            $subBin->depth_level = 1;
        }

        $subBin->save();

        // Update user's cumulative balance
        $request->user()->calculateCumulativeBalance();

        // Load relationships for the response
        $subBin->load(['bin', 'parentSubBin', 'childSubBins']);

        // Send sub-bin created notification
        $request->user()->notify(new SubBinCreatedNotification($subBin));

        // Calculate remaining sub-bins after creation
        $remainingSubBins = $this->subscriptionService->getRemainingSubBinCount($request->user(), $bin->id);

        return response()->json([
            'message' => $parentSubBin
                ? 'Nested sub-bin created successfully under ' . $parentSubBin->name
                : 'Sub-bin created successfully',
            'sub_bin' => $subBin,
            'hierarchy_info' => [
                'depth_level' => $subBin->depth_level,
                'path' => $subBin->path,
                'is_nested' => $subBin->parent_sub_bin_id !== null,
                'parent_sub_bin' => $parentSubBin ? [
                    'id' => $parentSubBin->id,
                    'name' => $parentSubBin->name,
                    'depth_level' => $parentSubBin->depth_level,
                ] : null,
                'max_depth' => $request->user()->subscription_tier === 'premium' ? 'unlimited' : 3,
            ],
            'remaining_sub_bins' => $remainingSubBins,
            'max_sub_bins' => $request->user()->subscription_tier === 'premium' ? 'unlimited' : SubscriptionService::MAX_SUB_BINS_PER_BIN_BASE_TIER,
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $binId
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, string $binId, string $id)
    {
        $bin = Bin::find($binId);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        $subBin = SubBin::where('bin_id', $binId)->find($id);

        if (!$subBin) {
            return response()->json([
                'message' => 'Sub-bin not found',
            ], 404);
        }

        // Load hierarchical relationships
        $subBin->load(['parentSubBin', 'childSubBins', 'bin']);

        return response()->json([
            'sub_bin' => $subBin,
            'hierarchy_info' => [
                'depth_level' => $subBin->depth_level,
                'path' => $subBin->path,
                'is_nested' => $subBin->parent_sub_bin_id !== null,
                'has_children' => $subBin->hasChildren(),
                'children_count' => $subBin->childSubBins->count(),
                'parent_sub_bin' => $subBin->parentSubBin ? [
                    'id' => $subBin->parentSubBin->id,
                    'name' => $subBin->parentSubBin->name,
                    'depth_level' => $subBin->parentSubBin->depth_level,
                ] : null,
                'ancestors' => $subBin->ancestors()->map(function ($ancestor) {
                    return [
                        'id' => $ancestor->id,
                        'name' => $ancestor->name,
                        'depth_level' => $ancestor->depth_level,
                    ];
                }),
            ],
            'child_sub_bins' => $subBin->childSubBins->map(function ($child) {
                return [
                    'id' => $child->id,
                    'uuid' => $child->uuid,
                    'name' => $child->name,
                    'type' => $child->type,
                    'current_amount' => $child->current_amount,
                    'depth_level' => $child->depth_level,
                    'has_children' => $child->hasChildren(),
                    'children_count' => $child->childSubBins()->count(),
                ];
            }),
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $binId
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $binId, string $id)
    {
        $bin = Bin::find($binId);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        $subBin = SubBin::where('bin_id', $binId)->find($id);

        if (!$subBin) {
            return response()->json([
                'message' => 'Sub-bin not found',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'type' => 'sometimes|required|string|in:income,expense',
            'description' => 'sometimes|nullable|string',
            'threshold_max_limit' => 'sometimes|required|numeric|min:0',
            'threshold_max_warning' => 'sometimes|nullable|numeric|lt:threshold_max_limit',
            'currency' => 'sometimes|nullable|string|max:10',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Track which fields are being updated
        $updatedFields = [];
        foreach (['name', 'type', 'description', 'threshold_max_limit', 'threshold_max_warning', 'currency', 'is_active'] as $field) {
            if ($request->has($field) && $subBin->{$field} != $request->input($field)) {
                $updatedFields[] = $field;
            }
        }

        $subBin->update($request->all());

        // Send sub-bin updated notification if fields were actually updated
        if (!empty($updatedFields)) {
            // Load the bin relationship for the notification
            $subBin->load('bin');

            $request->user()->notify(new SubBinUpdatedNotification($subBin, $updatedFields));
        }

        // Update user's cumulative balance if type or amount changed
        if (in_array('type', $updatedFields) || in_array('current_amount', $updatedFields)) {
            $request->user()->calculateCumulativeBalance();
        }

        return response()->json([
            'message' => 'Sub-bin updated successfully',
            'sub_bin' => $subBin,
        ]);
    }

    /**
     * Create a nested sub-bin under an existing sub-bin.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $binId
     * @param  string  $parentSubBinId
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeNested(Request $request, string $binId, string $parentSubBinId)
    {
        $bin = Bin::find($binId);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        $parentSubBin = SubBin::where('bin_id', $binId)->find($parentSubBinId);

        if (!$parentSubBin) {
            return response()->json([
                'message' => 'Parent sub-bin not found',
            ], 404);
        }

        // Check if the user can create more sub-bins for this bin
        if (!$this->subscriptionService->canCreateSubBin($request->user(), $bin->id)) {
            return response()->json([
                'message' => $this->subscriptionService->getUpgradeMessage('sub-bin'),
                'error' => 'subscription_limit_reached',
                'remaining_sub_bins' => 0,
                'max_sub_bins' => SubscriptionService::MAX_SUB_BINS_PER_BIN_BASE_TIER,
                'subscription_tier' => $request->user()->subscription_tier,
            ], 403);
        }

        // Check maximum nesting depth
        $maxDepth = $request->user()->subscription_tier === 'premium' ? null : 3; // Premium = unlimited, Base = 3 levels
        if ($maxDepth !== null && $parentSubBin->depth_level >= $maxDepth) {
            return response()->json([
                'message' => "Maximum nesting depth of {$maxDepth} levels reached. Upgrade to Premium for unlimited nesting.",
                'error' => 'max_depth_reached',
                'current_depth' => $parentSubBin->depth_level,
                'max_depth' => $maxDepth,
                'subscription_tier' => $request->user()->subscription_tier,
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'nullable|string|in:income,expense', // Make type nullable for auto-selection
            'description' => 'nullable|string',
            'threshold_max_limit' => 'nullable|numeric|min:0', // Make optional, user can set as they want
            'threshold_max_warning' => 'nullable|numeric|lt:threshold_max_limit',
            'currency' => 'nullable|string|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $subBin = new SubBin($request->all());
        $subBin->bin_id = $bin->id;
        $subBin->parent_sub_bin_id = $parentSubBin->id;
        $subBin->current_amount = 0;
        $subBin->depth_level = $parentSubBin->depth_level + 1;

        // Auto-select parent category if type not provided
        if (!$request->has('type') || !$request->type) {
            $subBin->type = $parentSubBin->type; // Use parent sub-bin's type
        }

        // Set default threshold values if not provided
        if (!$request->has('threshold_max_limit') || $request->threshold_max_limit === null) {
            $subBin->threshold_max_limit = null; // User can set as they want, no default limit
        }

        if (!$request->has('threshold_max_warning') || $request->threshold_max_warning === null) {
            $subBin->threshold_max_warning = null; // No default warning threshold
        }

        $subBin->save();

        // Update user's cumulative balance
        $request->user()->calculateCumulativeBalance();

        // Load relationships for the response
        $subBin->load(['bin', 'parentSubBin']);

        // Send sub-bin created notification
        $request->user()->notify(new SubBinCreatedNotification($subBin));

        return response()->json([
            'message' => "Nested sub-bin created successfully under {$parentSubBin->name}",
            'sub_bin' => $subBin,
            'hierarchy_info' => [
                'depth_level' => $subBin->depth_level,
                'path' => $subBin->path,
                'is_nested' => true,
                'parent_sub_bin' => [
                    'id' => $parentSubBin->id,
                    'name' => $parentSubBin->name,
                    'depth_level' => $parentSubBin->depth_level,
                ],
                'max_depth' => $maxDepth ?? 'unlimited',
            ],
        ], 201);
    }

    /**
     * Get the hierarchical tree structure for a bin's sub-bins.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $binId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHierarchy(Request $request, string $binId)
    {
        $bin = Bin::find($binId);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        // Get all sub-bins for this bin with their relationships
        $subBins = SubBin::where('bin_id', $binId)
            ->with(['childSubBins' => function ($query) {
                $query->with('childSubBins');
            }])
            ->whereNull('parent_sub_bin_id') // Only root level sub-bins
            ->orderBy('created_at')
            ->get();

        // Build hierarchical tree
        $tree = $this->buildSubBinTree($subBins);

        return response()->json([
            'bin' => [
                'id' => $bin->id,
                'name' => $bin->name,
                'uuid' => $bin->uuid,
            ],
            'hierarchy' => $tree,
            'total_sub_bins' => SubBin::where('bin_id', $binId)->count(),
            'max_depth_allowed' => $request->user()->subscription_tier === 'premium' ? 'unlimited' : 3,
        ]);
    }

    /**
     * Build hierarchical tree structure for sub-bins.
     */
    private function buildSubBinTree($subBins)
    {
        return $subBins->map(function ($subBin) {
            return [
                'id' => $subBin->id,
                'uuid' => $subBin->uuid,
                'name' => $subBin->name,
                'type' => $subBin->type,
                'description' => $subBin->description,
                'current_amount' => $subBin->current_amount,
                'threshold_min' => $subBin->threshold_min,
                'threshold_max' => $subBin->threshold_max,
                'currency' => $subBin->currency,
                'depth_level' => $subBin->depth_level,
                'path' => $subBin->path,
                'is_active' => $subBin->is_active,
                'created_at' => $subBin->created_at,
                'children' => $subBin->childSubBins->isNotEmpty()
                    ? $this->buildSubBinTree($subBin->childSubBins)
                    : [],
                'children_count' => $subBin->childSubBins->count(),
                'has_children' => $subBin->childSubBins->isNotEmpty(),
            ];
        });
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $binId
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, string $binId, string $id)
    {
        $bin = Bin::find($binId);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        $subBin = SubBin::where('bin_id', $binId)->find($id);

        if (!$subBin) {
            return response()->json([
                'message' => 'Sub-bin not found',
            ], 404);
        }

        // Store sub-bin name before deletion for notification
        $subBinName = $subBin->name;

        $subBin->delete();

        // Send sub-bin deleted notification
        $request->user()->notify(new SubBinDeletedNotification($subBinName, $bin));

        return response()->json([
            'message' => 'Sub-bin deleted successfully',
        ]);
    }
}
