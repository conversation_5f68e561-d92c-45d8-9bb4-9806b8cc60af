Base URL: http://pocketwatch.nexusbridgellc.com/api,,,,,
Endpoint,Method,Route Name,Authentication,Parameters,Description
/register,POST,api.auth.register,No,"name (required): User's full name
email (required): User's email address
password (required): Password (min 8 characters)
password_confirmation (required): Password confirmation
country_code (optional): Country calling code
phone_number (optional): Phone number",Register a new user. All new users start with a 7-day free trial of their chosen subscription tier.
/login,POST,api.auth.login,No,"email (required): User's email address
password (required): Password",Login and get authentication token.
/me,GET,api.auth.me,Yes,None,Get current user profile.
/logout,POST,api.auth.logout,Yes,None,Logout and invalidate token.
/profile,PUT,api.auth.update-profile,Yes,"name (optional): User's full name
email (optional): User's email address
country_code (optional): Country calling code
phone_number (optional): Phone number",Update user profile.
/password,PUT,api.auth.change-password,Yes,"current_password (required): Current password
password (required): New password (min 8 characters)
password_confirmation (required): New password confirmation",Change user password.
/avatar,POST,api.auth.update-avatar,Yes,"avatar (required): Image file (jpeg, png, jpg, gif, max 2MB)",Update user avatar.
/forgot-password,POST,api.auth.forgot-password,No,email (required): User's email address,Request password reset link.
/reset-password,POST,api.auth.reset-password,No,"email (required): User's email address
token (required): Reset token from email
password (required): New password (min 8 characters)
password_confirmation (required): New password confirmation",Reset password with token.
/bins,GET,api.bins.index,Yes,"page (optional): Page number
per_page (optional): Items per page",Get all bins.
/bins,POST,api.bins.store,Yes,"name (required): Bin name
description (optional): Bin description
threshold_min (required): Minimum threshold amount
threshold_max (optional): Maximum threshold amount
currency (optional): Currency code (default: USD)",Create a new bin.
/bins/{id},GET,api.bins.show,Yes,id (required): Bin ID,Get bin details.
/bins/{id},PUT,api.bins.update,Yes,"id (required): Bin ID
name (optional): Bin name
description (optional): Bin description
threshold_min (optional): Minimum threshold amount
threshold_max (optional): Maximum threshold amount
currency (optional): Currency code
is_active (optional): Bin status (boolean)",Update bin.
/bins/{id},DELETE,api.bins.destroy,Yes,id (required): Bin ID,Delete bin.
/bins/{binId}/sub-bins,GET,api.sub-bins.index,Yes,binId (required): Parent bin ID,Get all sub-bins for a bin.
/bins/{binId}/sub-bins,POST,api.sub-bins.store,Yes,"binId (required): Parent bin ID
name (required): Sub-bin name
description (optional): Sub-bin description
threshold_min (required): Minimum threshold amount
threshold_max (optional): Maximum threshold amount
currency (optional): Currency code (default: USD)","Create a new sub-bin. Base tier users are limited to 3 levels of sub-bins, while Premium users have unlimited sub-bin levels."
/bins/{binId}/sub-bins/{id},GET,api.sub-bins.show,Yes,"binId (required): Parent bin ID
id (required): Sub-bin ID",Get sub-bin details.
/bins/{binId}/sub-bins/{id},PUT,api.sub-bins.update,Yes,"binId (required): Parent bin ID
id (required): Sub-bin ID
name (optional): Sub-bin name
description (optional): Sub-bin description
threshold_min (optional): Minimum threshold amount
threshold_max (optional): Maximum threshold amount
currency (optional): Currency code
is_active (optional): Sub-bin status (boolean)",Update sub-bin.
/bins/{binId}/sub-bins/{id},DELETE,api.sub-bins.destroy,Yes,"binId (required): Parent bin ID
id (required): Sub-bin ID",Delete sub-bin.
/transactions,GET,api.transactions.index,Yes,"page (optional): Page number
per_page (optional): Items per page
bin_id (optional): Filter by bin ID
sub_bin_id (optional): Filter by sub-bin ID
type (optional): Filter by transaction type (income, expense, transfer)
start_date (optional): Filter by start date
end_date (optional): Filter by end date
category (optional): Filter by category",Get all transactions.
/transactions,POST,api.transactions.store,Yes,"bin_id (optional): Bin ID
sub_bin_id (optional): Sub-bin ID
transaction_type (required): Type (income, expense, transfer)
amount (required): Transaction amount
currency (optional): Currency code (default: USD)
description (optional): Transaction description
category (optional): Transaction category
payment_method (optional): Payment method
transaction_date (required): Transaction date",Create a new transaction.
/transactions/{id},GET,api.transactions.show,Yes,id (required): Transaction ID,Get transaction details.
/transactions/{id},PUT,api.transactions.update,Yes,"id (required): Transaction ID
bin_id (optional): Bin ID
sub_bin_id (optional): Sub-bin ID
transaction_type (optional): Type (income, expense, transfer)
amount (optional): Transaction amount
currency (optional): Currency code
description (optional): Transaction description
category (optional): Transaction category
payment_method (optional): Payment method
transaction_date (optional): Transaction date",Update transaction.
/transactions/{id},DELETE,api.transactions.destroy,Yes,id (required): Transaction ID,Delete transaction.
/transactions/stats,GET,api.transactions.stats,Yes,"period (optional): Period (day, week, month, year)
start_date (optional): Start date for custom period
end_date (optional): End date for custom period",Get transaction statistics.
/transactions/by-bin/{binId},GET,api.transactions.by-bin,Yes,"binId (required): Bin ID
start_date (optional): Filter by start date
end_date (optional): Filter by end date
type (optional): Filter by transaction type
page (optional): Page number
per_page (optional): Items per page",Get transactions by bin.
/transactions/by-category/{category},GET,api.transactions.by-category,Yes,"category (required): Category name
start_date (optional): Filter by start date
end_date (optional): Filter by end date
type (optional): Filter by transaction type
page (optional): Page number
per_page (optional): Items per page",Get transactions by category.
/subscriptions,GET,api.subscriptions.index,Yes,None,Get all subscriptions for the authenticated user.
/subscriptions,POST,api.subscriptions.store,Yes,"subscription_tier (required): Subscription tier (base, premium)
billing_cycle (required): Billing cycle (monthly, yearly)",Create a new subscription with 7-day free trial. Base tier is $5/month or $50/year. Premium tier is $10/month or $100/year.
/subscriptions/plans,GET,api.subscriptions.plans,Yes,None,Get available subscription plans with pricing and features.
/subscriptions/history,GET,api.subscriptions.history,Yes,"page (optional): Page number (default: 1)
per_page (optional): Items per page (default: 15)",Get subscription history for the authenticated user.
/subscriptions/{id},GET,api.subscriptions.show,Yes,id (required): Subscription ID,Get subscription details.
/subscriptions/{id},PUT,api.subscriptions.update,Yes,"id (required): Subscription ID
subscription_tier (required): New subscription tier (base, premium)",Update subscription tier (upgrade/downgrade).
/subscriptions/{id},DELETE,api.subscriptions.destroy,Yes,"id (required): Subscription ID
cancel_reason (optional): Reason for cancellation
cancel_feedback (optional): Additional feedback",Cancel subscription.
/subscriptions/{id}/change-cycle,PUT,api.subscriptions.change-cycle,Yes,"id (required): Subscription ID
billing_cycle (required): New billing cycle (monthly, yearly)",Change the billing cycle of a subscription.
/subscriptions/{id}/pause,POST,api.subscriptions.pause,Yes,"id (required): Subscription ID
pause_duration (optional): Duration in days to pause (default: 30, max: 90)",Pause a subscription.
/subscriptions/{id}/resume,POST,api.subscriptions.resume,Yes,id (required): Subscription ID,Resume a paused subscription.
/crypto-wallets,GET,api.crypto-wallets.index,Yes,None,Get all crypto wallets. Requires Premium subscription or active trial.
/crypto-wallets,POST,api.crypto-wallets.store,Yes,"wallet_address (required): Blockchain wallet address
wallet_name (optional): Custom wallet name
blockchain_network (required): Network (ethereum, binance, polygon, avalanche)",Create a new crypto wallet. Requires Premium subscription or active trial.
/crypto-wallets/{id},GET,api.crypto-wallets.show,Yes,id (required): Wallet ID,Get crypto wallet details. Requires Premium subscription or active trial.
/crypto-wallets/{id},PUT,api.crypto-wallets.update,Yes,"id (required): Wallet ID
wallet_name (optional): Custom wallet name
is_active (optional): Wallet status (boolean)",Update crypto wallet. Requires Premium subscription or active trial.
/crypto-wallets/{id},DELETE,api.crypto-wallets.destroy,Yes,id (required): Wallet ID,Delete crypto wallet. Requires Premium subscription or active trial.
/crypto-wallets/{id}/assets,GET,api.crypto-wallets.assets,Yes,id (required): Wallet ID,Get crypto wallet assets. Requires Premium subscription or active trial.
/crypto-wallets/{id}/transactions,GET,api.crypto-wallets.transactions,Yes,id (required): Wallet ID,Get crypto wallet transactions. Requires Premium subscription or active trial.
/crypto-wallets/{id}/sync,POST,api.crypto-wallets.sync,Yes,id (required): Wallet ID,Sync crypto wallet with blockchain. Requires Premium subscription or active trial.
/webhook/stripe,POST,api.webhook.stripe,No,"Stripe-Signature (header, required): Stripe webhook signature",Handle Stripe webhook events.
