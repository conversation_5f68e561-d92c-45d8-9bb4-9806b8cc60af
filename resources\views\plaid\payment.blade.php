@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>Make a Payment
                    </h5>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if ($accounts->isEmpty())
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-university fa-4x text-muted"></i>
                            </div>
                            <h5>No Payment Accounts Available</h5>
                            <p class="text-muted">You need to link a bank account and enable it for payments first.</p>
                            <a href="{{ route('plaid.index') }}" class="btn btn-primary">
                                <i class="fas fa-university me-2"></i>Manage Bank Accounts
                            </a>
                        </div>
                    @else
                        <form action="{{ route('plaid-payment.process') }}" method="POST" id="paymentForm">
                            @csrf
                            
                            <div class="mb-4">
                                <label class="form-label">Select Package</label>
                                <div class="row">
                                    @foreach ($packages as $package)
                                        <div class="col-md-6 mb-3">
                                            <div class="card h-100 package-card">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input package-radio" type="radio" name="package_id" id="package_{{ $package['id'] }}" value="{{ $package['id'] }}" data-price="{{ $package['price'] }}" {{ $loop->first ? 'checked' : '' }}>
                                                        <label class="form-check-label w-100" for="package_{{ $package['id'] }}">
                                                            <h6 class="mb-1">{{ $package['name'] }}</h6>
                                                            <div class="text-primary fw-bold">${{ number_format($package['price'], 2) }}</div>
                                                            <small class="text-muted">{{ $package['description'] }}</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="account_id" class="form-label">Select Payment Account</label>
                                <select class="form-select" id="account_id" name="account_id" required>
                                    @foreach ($accounts as $account)
                                        <option value="{{ $account->id }}" {{ $account->is_default ? 'selected' : '' }}>
                                            {{ $account->institution_name }} - {{ $account->account_name }} (••••{{ $account->account_mask }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <label for="amount" class="form-label">Payment Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control" id="amount" name="amount" value="{{ $packages[0]['price'] }}" readonly>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-lock me-2"></i>Make Secure Payment
                                </button>
                            </div>
                        </form>
                    @endif
                </div>
                
                <div class="card-footer text-center">
                    <a href="{{ route('subscriptions.index') }}" class="btn btn-link">
                        <i class="fas fa-arrow-left me-1"></i>Back to Subscriptions
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update amount when package is selected
        const packageRadios = document.querySelectorAll('.package-radio');
        const amountInput = document.getElementById('amount');
        
        packageRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    amountInput.value = this.dataset.price;
                }
            });
        });
        
        // Highlight selected package card
        const packageCards = document.querySelectorAll('.package-card');
        
        packageRadios.forEach((radio, index) => {
            radio.addEventListener('change', function() {
                packageCards.forEach(card => {
                    card.classList.remove('border-primary');
                });
                
                if (this.checked) {
                    packageCards[index].classList.add('border-primary');
                }
            });
            
            // Initialize with the checked radio
            if (radio.checked) {
                packageCards[index].classList.add('border-primary');
            }
        });
    });
</script>
@endpush

@push('styles')
<style>
    .package-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }
    
    .package-card:hover {
        border-color: #ddd;
    }
    
    .package-card.border-primary {
        border-color: var(--bs-primary);
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.1);
    }
</style>
@endpush
