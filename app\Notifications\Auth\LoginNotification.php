<?php

namespace App\Notifications\Auth;

use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class LoginNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_login';

    /**
     * The login timestamp.
     *
     * @var \Illuminate\Support\Carbon
     */
    protected $timestamp;

    /**
     * The IP address.
     *
     * @var string
     */
    protected $ipAddress;

    /**
     * The device information.
     *
     * @var string
     */
    protected $device;

    /**
     * Create a new notification instance.
     *
     * @param  string  $ipAddress
     * @param  string  $device
     * @return void
     */

    public function __construct($ipAddress, $device = null)
    {
        $this->timestamp = now();
        $this->ipAddress = $ipAddress;
        $this->device = $device ?? 'Unknown device';

        $this->subject = 'New Login to Your PocketWatch Account';
        $this->title = 'New Login Detected';
        $this->content = 'We detected a new login to your PocketWatch account. If this was you, you can ignore this email.
                         If you didn\'t log in recently, please secure your account by changing your password immediately.';

        $this->detailsTitle = 'Login Details';
        $this->details = [
            'Date & Time' => $this->timestamp->format('F j, Y, g:i a'),
            'IP Address' => $this->ipAddress,
            'Device' => $this->device,
        ];

        $this->actionText = 'Secure Your Account';
        $this->actionUrl = url('/password/reset');

        $this->closing = 'If you have any concerns, please contact our support team immediately.';
        $this->signature = 'The PocketWatch Security Team';
    }
}
