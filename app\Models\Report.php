<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Report extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'type',
        'format',
        'period_type',
        'start_date',
        'end_date',
        'filters',
        'status',
        'file_path',
        'last_generated_at',
        'schedule',
        'is_recurring',
        'next_run_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'filters' => 'json',
        'start_date' => 'date',
        'end_date' => 'date',
        'last_generated_at' => 'datetime',
        'next_run_at' => 'datetime',
        'is_recurring' => 'boolean',
    ];

    /**
     * Get the user that owns the report.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include reports of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include reports with a specific format.
     */
    public function scopeWithFormat($query, $format)
    {
        return $query->where('format', $format);
    }

    /**
     * Scope a query to only include recurring reports.
     */
    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    /**
     * Scope a query to only include reports that need to be generated.
     */
    public function scopeDue($query)
    {
        return $query->where('next_run_at', '<=', now())
            ->where('is_recurring', true);
    }
}
