@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Notification Settings') }}</div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('settings.notifications.update') }}">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <h5>{{ __('Email Notifications') }}</h5>
                            <p class="text-muted">{{ __('Choose which email notifications you want to receive.') }}</p>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_login" name="email_login" {{ $settings->email_login ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_login">
                                    {{ __('Login Notifications') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive an email when a new login is detected on your account.') }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_profile_updates" name="email_profile_updates" {{ $settings->email_profile_updates ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_profile_updates">
                                    {{ __('Profile Update Notifications') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive an email when your profile information is updated.') }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_password_changes" name="email_password_changes" {{ $settings->email_password_changes ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_password_changes">
                                    {{ __('Password Change Notifications') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive an email when your password is changed.') }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_bin_operations" name="email_bin_operations" {{ $settings->email_bin_operations ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_bin_operations">
                                    {{ __('Bin Operation Notifications') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive emails when bins or sub-bins are created, updated, or deleted.') }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_transaction_operations" name="email_transaction_operations" {{ $settings->email_transaction_operations ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_transaction_operations">
                                    {{ __('Transaction Operation Notifications') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive emails when transactions are created, updated, or deleted.') }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_subscription_updates" name="email_subscription_updates" {{ $settings->email_subscription_updates ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_subscription_updates">
                                    {{ __('Subscription Update Notifications') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive emails when your subscription is created, updated, or canceled.') }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_threshold_alerts" name="email_threshold_alerts" {{ $settings->email_threshold_alerts ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_threshold_alerts">
                                    {{ __('Threshold Alert Notifications') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive emails when your bins or sub-bins cross their threshold limits.') }}</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_renewal_reminders" name="email_renewal_reminders" {{ $settings->email_renewal_reminders ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_renewal_reminders">
                                    {{ __('Subscription Renewal Reminders') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive email reminders when your subscription is about to renew.') }}</div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_marketing" name="email_marketing" {{ $settings->email_marketing ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_marketing">
                                    {{ __('Marketing Emails') }}
                                </label>
                                <div class="text-muted small">{{ __('Receive promotional emails, tips, and updates about PocketWatch features.') }}</div>
                            </div>
                        </div>

                        <div class="mb-0">
                            <button type="submit" class="btn btn-primary">
                                {{ __('Save Settings') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
