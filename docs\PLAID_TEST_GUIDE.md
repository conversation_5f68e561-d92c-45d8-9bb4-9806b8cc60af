# 🏦 Plaid Integration Test Guide

## 📋 Overview

This comprehensive test suite verifies that your Plaid banking integration is working correctly in PocketWatch. The test page provides a complete workflow to test all aspects of Plaid connectivity.

## 🚀 Quick Start

### 1. Access the Test Page
Navigate to: `http://your-domain.com/test/plaid`

### 2. Required Environment Variables
Make sure these are set in your `.env` file:

```env
PLAID_CLIENT_ID=your_plaid_client_id
PLAID_SECRET=your_plaid_secret
PLAID_ENVIRONMENT=sandbox  # or development/production
PLAID_CLIENT_NAME=PocketWatch
PLAID_PRODUCTS=auth,transactions
PLAID_COUNTRY_CODES=US
PLAID_LANGUAGE=en
PLAID_WEBHOOK_URL=https://your-domain.com/api/webhook/plaid
```

## 🧪 Test Workflow

### Step 1: Configuration Check ✅
The page automatically displays your current Plaid configuration:
- ✅ **Client ID**: Verified if set
- ✅ **Secret**: Verified if set  
- ✅ **Environment**: Shows current environment (sandbox/development/production)
- ✅ **Additional Settings**: Client name, products, country codes

### Step 2: API Connection Test 🔌
**Purpose**: Verify basic connectivity to Plaid API
**Action**: Click "Test Connection"
**Expected Result**: 
```json
{
  "success": true,
  "message": "Successfully connected to Plaid API",
  "environment": "sandbox",
  "institutions_count": 1,
  "response_time": 0.234,
  "status_code": 200
}
```

### Step 3: Link Token Generation 🔑
**Purpose**: Generate a Plaid Link token for account linking
**Action**: Click "Generate Token"
**Expected Result**:
```json
{
  "success": true,
  "message": "Link token generated successfully",
  "link_token": "link-sandbox-12345...",
  "expiration": "2024-01-15T20:30:00.000000Z"
}
```

### Step 4: Plaid Link Integration 🔗
**Purpose**: Test the complete Plaid Link flow
**Action**: Click "Connect Bank Account"
**Process**:
1. Plaid Link modal opens
2. Select "First Platypus Bank" (sandbox)
3. Use credentials: `user_good` / `pass_good`
4. Select accounts to link
5. Complete the flow

**Expected Result**:
```json
{
  "success": true,
  "public_token": "public-sandbox-12345...",
  "metadata": {
    "institution": {
      "name": "First Platypus Bank",
      "institution_id": "ins_109508"
    },
    "accounts": [...]
  }
}
```

### Step 5: Account Information 📊
**Purpose**: Retrieve connected account information
**Action**: Click "Get Accounts" (enabled after successful linking)
**Expected Result**:
```json
{
  "success": true,
  "accounts_count": 3,
  "accounts": [
    {
      "account_id": "acc_12345...",
      "name": "Plaid Checking",
      "type": "depository",
      "subtype": "checking",
      "balances": {
        "available": 1230.50,
        "current": 1230.50
      }
    }
  ]
}
```

### Step 6: Transactions Test 💳
**Purpose**: Retrieve recent transactions (last 30 days)
**Action**: Click "Get Transactions" (enabled after successful linking)
**Expected Result**:
```json
{
  "success": true,
  "transactions_count": 25,
  "total_transactions": 156,
  "transactions": [
    {
      "transaction_id": "tx_12345...",
      "amount": 12.34,
      "name": "Starbucks",
      "date": "2024-01-15"
    }
  ]
}
```

## 🎯 Test Results Summary

The page provides a real-time summary showing:
- **Total Tests**: Number of available tests
- **Passed**: Successfully completed tests
- **Failed**: Tests that encountered errors
- **Pending**: Tests not yet run

## 🔧 Troubleshooting

### Common Issues:

#### ❌ "Failed to connect to Plaid API"
**Causes**:
- Invalid `PLAID_CLIENT_ID` or `PLAID_SECRET`
- Wrong `PLAID_ENVIRONMENT` setting
- Network connectivity issues

**Solutions**:
1. Verify credentials in Plaid Dashboard
2. Check environment setting (sandbox/development/production)
3. Ensure firewall allows outbound HTTPS connections

#### ❌ "Failed to generate link token"
**Causes**:
- Missing required configuration
- Invalid product configuration
- Webhook URL issues

**Solutions**:
1. Check all required environment variables
2. Verify `PLAID_PRODUCTS` setting
3. Ensure webhook URL is accessible (if set)

#### ❌ "User cancelled" in Link flow
**Expected Behavior**: This is normal when users close the Plaid Link modal

#### ❌ "No access token found"
**Cause**: Trying to test accounts/transactions before completing Link flow
**Solution**: Complete the "Connect Bank Account" step first

## 🏗️ Sandbox Test Credentials

For testing in sandbox environment, use these credentials:

### First Platypus Bank
- **Username**: `user_good`
- **Password**: `pass_good`
- **Result**: Successful connection with sample accounts

### Tartan Bank  
- **Username**: `user_good`
- **Password**: `pass_good`
- **Result**: Successful connection with different account types

### Error Testing
- **Username**: `user_bad`
- **Password**: `pass_bad`
- **Result**: Simulates authentication errors

## 🔄 Session Management

### Clear Test Data
Use the "Clear Session" button to:
- Remove stored access tokens
- Reset UI state
- Clear test results
- Start fresh testing cycle

## 📝 Next Steps

After successful testing:

1. **Production Setup**: Update environment variables for production
2. **Webhook Implementation**: Set up webhook endpoint for real-time updates
3. **Error Handling**: Implement proper error handling in your application
4. **User Experience**: Integrate Plaid Link into your user interface
5. **Data Storage**: Store account and transaction data in your database

## 🔒 Security Notes

- Test page stores access tokens in session temporarily
- Never expose Plaid credentials in client-side code
- Use webhook verification for production webhooks
- Implement proper access controls for sensitive endpoints

## 📞 Support

If you encounter issues:
1. Check Plaid Dashboard for API logs
2. Review Laravel logs for detailed error messages
3. Verify all environment variables are correctly set
4. Test with different sandbox credentials

---

**Happy Testing!** 🎉
