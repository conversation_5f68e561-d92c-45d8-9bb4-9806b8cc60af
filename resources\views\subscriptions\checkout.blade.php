@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="fw-bold" style="color: var(--primary-color);">Complete Your Subscription</h4>
                <a href="{{ route('subscriptions.plans') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Plans
                </a>
            </div>

            <div class="row">
                <div class="col-md-7">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0 fw-bold" style="color: var(--primary-color);">Payment Details</h5>
                        </div>
                        <div class="card-body">
                            <ul class="nav nav-tabs mb-4" id="paymentTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="card-tab" data-bs-toggle="tab" data-bs-target="#card-panel" type="button" role="tab" aria-controls="card-panel" aria-selected="true">
                                        <i class="fas fa-credit-card me-2"></i>Credit Card
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="bank-tab" data-bs-toggle="tab" data-bs-target="#bank-panel" type="button" role="tab" aria-controls="bank-panel" aria-selected="false">
                                        <i class="fas fa-university me-2"></i>Bank Account
                                    </button>
                                </li>
                            </ul>

                            <div class="tab-content" id="paymentTabsContent">
                                <!-- Credit Card Payment -->
                                <div class="tab-pane fade show active" id="card-panel" role="tabpanel" aria-labelledby="card-tab">
                                    <form id="payment-form" action="{{ route('subscriptions.process') }}" method="POST">
                                        @csrf
                                        <input type="hidden" name="subscription_tier" value="{{ $tier }}">
                                        <input type="hidden" name="billing_cycle" value="{{ $billingCycle }}">

                                        <div class="mb-4">
                                            <label for="card-element" class="form-label">Credit or debit card</label>
                                            <div id="card-element" class="form-control" style="height: 40px; padding-top: 10px;"></div>
                                            <div id="card-errors" class="text-danger mt-2" role="alert"></div>
                                        </div>

                                        <div class="mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="terms-checkbox" required>
                                                <label class="form-check-label" for="terms-checkbox">
                                                    I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms of Service</a> and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-success py-3" id="submit-button">
                                                <span id="button-text">Start Free Trial</span>
                                                <span id="spinner" class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true"></span>
                                            </button>
                                        </div>

                                        <div class="text-center mt-3">
                                            <small class="text-muted">
                                                Your 7-day free trial starts today. You won't be charged until the trial ends.
                                            </small>
                                        </div>
                                    </form>
                                </div>

                                <!-- Bank Account Payment -->
                                <div class="tab-pane fade" id="bank-panel" role="tabpanel" aria-labelledby="bank-tab">
                                    <div class="mb-4">
                                        <p class="text-muted">Pay directly from your bank account using Plaid's secure connection.</p>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <a href="{{ route('plaid-payment.checkout', ['tier' => $tier, 'billing' => $billingCycle]) }}" class="btn btn-success py-3">
                                            <i class="fas fa-university me-2"></i> Continue with Bank Account
                                        </a>
                                    </div>

                                    <div class="text-center mt-3">
                                        <small class="text-muted">
                                            Your 7-day free trial starts today. You won't be charged until the trial ends.
                                        </small>
                                    </div>

                                    <div class="mt-4">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-lock text-success me-2"></i>
                                            <span class="small">Bank-level encryption protects your information</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-shield-alt text-success me-2"></i>
                                            <span class="small">Your credentials are never stored on our servers</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span class="small">Powered by Plaid, a trusted financial service provider</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4">
                        <div class="card-body">
                            <h6 class="fw-bold mb-3">Secure Payment</h6>
                            <p class="text-muted mb-3 small">
                                Your payment information is encrypted and secure. We use Stripe and Plaid for secure payment processing.
                            </p>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-lock text-success me-2"></i>
                                <span class="small">SSL Encrypted Payment</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <img src="https://cdn.jsdelivr.net/gh/stripe/stripe-js@latest/dist/stripe-badge.png" alt="Powered by Stripe" height="30">
                                </div>
                                <div>
                                    <img src="https://plaid.com/assets/img/plaid-logo-black.svg" alt="Powered by Plaid" height="20">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-5">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0 fw-bold" style="color: var(--primary-color);">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-4">
                                <div class="subscription-icon me-3">
                                    <i class="fas fa-crown fa-2x" style="color: {{ $tier == 'premium' ? '#FFD700' : '#6c757d' }};"></i>
                                </div>
                                <div>
                                    <h5 class="mb-0">{{ ucfirst($tier) }} Plan</h5>
                                    <p class="text-muted mb-0">{{ ucfirst($billingCycle) }} billing</p>
                                </div>
                            </div>

                            <div class="order-details">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subscription:</span>
                                    <span>{{ ucfirst($tier) }} ({{ ucfirst($billingCycle) }})</span>
                                </div>

                                <div class="d-flex justify-content-between mb-2">
                                    <span>Price:</span>
                                    <span>
                                        @if($tier == 'premium')
                                            ${{ $billingCycle == 'monthly' ? '10.00' : '100.00' }}
                                        @else
                                            ${{ $billingCycle == 'monthly' ? '5.00' : '50.00' }}
                                        @endif
                                        /{{ $billingCycle == 'monthly' ? 'month' : 'year' }}
                                    </span>
                                </div>

                                <div class="d-flex justify-content-between mb-2">
                                    <span>Trial Period:</span>
                                    <span>7 days</span>
                                </div>

                                <div class="d-flex justify-content-between mb-2">
                                    <span>First Billing Date:</span>
                                    <span>{{ now()->addDays(7)->format('M d, Y') }}</span>
                                </div>

                                <hr>

                                <div class="d-flex justify-content-between mb-2 fw-bold">
                                    <span>Total Due Today:</span>
                                    <span>$0.00</span>
                                </div>

                                <div class="d-flex justify-content-between fw-bold">
                                    <span>Total After Trial:</span>
                                    <span>
                                        @if($tier == 'premium')
                                            ${{ $billingCycle == 'monthly' ? '10.00' : '100.00' }}
                                        @else
                                            ${{ $billingCycle == 'monthly' ? '5.00' : '50.00' }}
                                        @endif
                                    </span>
                                </div>
                            </div>

                            <div class="alert alert-info mt-4 mb-0">
                                <i class="fas fa-info-circle me-2"></i> You can cancel anytime before the trial ends and you won't be charged.
                            </div>
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h6 class="fw-bold mb-3">What's Included:</h6>
                            <ul class="list-unstyled mb-0">
                                @if($tier == 'premium')
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> All Base Plan features</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Unlimited sub-bin levels</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Crypto scanner</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Priority notifications</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Advanced AI suggestions</li>
                                @else
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Financial bins with thresholds</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Automatic transaction categorization</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Graphical insights</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Up to 3 levels of sub-bins</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Chatbot financial advice</li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms of Service Modal -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">Terms of Service</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Subscription Terms</h6>
                <p>By subscribing to PocketWatch, you agree to the following terms:</p>
                <ul>
                    <li>Your subscription will begin with a 7-day free trial.</li>
                    <li>After the trial period, you will be charged the subscription fee for your selected plan.</li>
                    <li>Subscriptions automatically renew until canceled.</li>
                    <li>You can cancel your subscription at any time before the end of the trial period to avoid being charged.</li>
                </ul>

                <h6>2. Billing</h6>
                <p>Billing occurs on a recurring basis according to your chosen billing cycle (monthly or yearly). You authorize us to charge your payment method on file.</p>

                <h6>3. Cancellation</h6>
                <p>You may cancel your subscription at any time through your account settings. Upon cancellation, your subscription will remain active until the end of your current billing period.</p>

                <h6>4. Refunds</h6>
                <p>Refunds are provided at our discretion and in accordance with applicable laws.</p>

                <h6>5. Changes to Terms</h6>
                <p>We reserve the right to modify these terms at any time. Changes will be effective upon posting to our website.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">I Understand</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Policy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="privacyModalLabel">Privacy Policy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Information We Collect</h6>
                <p>We collect information you provide directly to us, including personal information such as your name, email address, and payment information.</p>

                <h6>2. How We Use Your Information</h6>
                <p>We use your information to:</p>
                <ul>
                    <li>Process transactions and send related information</li>
                    <li>Provide, maintain, and improve our services</li>
                    <li>Send technical notices, updates, and support messages</li>
                    <li>Respond to your comments and questions</li>
                </ul>

                <h6>3. Payment Information</h6>
                <p>Payment information is securely processed through Stripe and Plaid. We do not store your full credit card details or bank credentials on our servers.</p>

                <h6>4. Data Security</h6>
                <p>We implement appropriate security measures to protect your personal information.</p>

                <h6>5. Data Retention</h6>
                <p>We retain your information as long as your account is active or as needed to provide services.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">I Understand</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Create a Stripe client
        const stripe = Stripe('{{ config('services.stripe.key') }}');
        const elements = stripe.elements();

        // Create an instance of the card Element
        const cardElement = elements.create('card', {
            style: {
                base: {
                    color: '#32325d',
                    fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                    fontSmoothing: 'antialiased',
                    fontSize: '16px',
                    '::placeholder': {
                        color: '#aab7c4'
                    }
                },
                invalid: {
                    color: '#fa755a',
                    iconColor: '#fa755a'
                }
            }
        });

        // Add an instance of the card Element into the `card-element` div
        cardElement.mount('#card-element');

        // Handle real-time validation errors from the card Element
        cardElement.on('change', function(event) {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
            } else {
                displayError.textContent = '';
            }
        });

        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');
        const buttonText = document.getElementById('button-text');
        const spinner = document.getElementById('spinner');

        form.addEventListener('submit', function(event) {
            event.preventDefault();

            // Check if terms checkbox is checked
            const termsCheckbox = document.getElementById('terms-checkbox');
            if (!termsCheckbox.checked) {
                const errorElement = document.getElementById('card-errors');
                errorElement.textContent = 'You must agree to the Terms of Service and Privacy Policy.';
                return;
            }

            // Disable the submit button to prevent repeated clicks
            submitButton.disabled = true;
            buttonText.textContent = 'Processing...';
            spinner.classList.remove('d-none');

            stripe.createToken(cardElement).then(function(result) {
                if (result.error) {
                    // Show error to your customer
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = result.error.message;

                    // Re-enable the submit button
                    submitButton.disabled = false;
                    buttonText.textContent = 'Start Free Trial';
                    spinner.classList.add('d-none');
                } else {
                    // Send the token to your server
                    stripeTokenHandler(result.token);
                }
            });
        });

        function stripeTokenHandler(token) {
            // Insert the token ID into the form so it gets submitted to the server
            const hiddenInput = document.createElement('input');
            hiddenInput.setAttribute('type', 'hidden');
            hiddenInput.setAttribute('name', 'stripeToken');
            hiddenInput.setAttribute('value', token.id);
            form.appendChild(hiddenInput);

            // Submit the form
            form.submit();
        }
    });
</script>
@endpush
@endsection
