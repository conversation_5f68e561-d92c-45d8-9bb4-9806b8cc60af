<?php

namespace App\Http\Controllers\Test;

use App\Http\Controllers\Controller;
use App\Services\TwilioService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Exception;

class TwilioTestController extends Controller
{
    protected $twilioService;

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;
    }

    /**
     * Show the Twilio test page
     */
    public function index()
    {
        return view('test.twilio-test');
    }

    /**
     * Test Twilio API connection
     */
    public function testConnection(): JsonResponse
    {
        try {
            $result = $this->twilioService->testConnection();
            
            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'connection_details' => $result,
                'environment_check' => $this->getEnvironmentCheck(),
                'timestamp' => now()->toISOString()
            ]);

        } catch (Exception $e) {
            Log::error('Twilio connection test failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Connection test failed',
                'error' => $e->getMessage(),
                'environment_check' => $this->getEnvironmentCheck()
            ], 500);
        }
    }

    /**
     * Test sending SMS to a specific number
     */
    public function testSMS(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string',
            'message' => 'required|string|max:1600'
        ]);

        try {
            $result = $this->twilioService->sendSMS(
                $request->input('phone_number'),
                $request->input('message')
            );

            Log::info('Test SMS sent', [
                'phone' => $request->input('phone_number'),
                'success' => $result['success'],
                'sid' => $result['sid'] ?? null
            ]);

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('Test SMS failed', [
                'phone' => $request->input('phone_number'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'SMS test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test verification code sending
     */
    public function testVerificationCode(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string'
        ]);

        try {
            $result = $this->twilioService->sendVerificationCode(
                $request->input('phone_number')
            );

            // Store phone in session for verification test
            if ($result['success']) {
                session(['test_verification_phone' => $request->input('phone_number')]);
            }

            Log::info('Test verification code sent', [
                'phone' => $request->input('phone_number'),
                'success' => $result['success'],
                'sid' => $result['sid'] ?? null
            ]);

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('Test verification code failed', [
                'phone' => $request->input('phone_number'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Verification code test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test verification code validation
     */
    public function testVerifyCode(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $phoneNumber = session('test_verification_phone');
        
        if (!$phoneNumber) {
            return response()->json([
                'success' => false,
                'message' => 'No phone number in test session. Send verification code first.',
                'error_code' => 'NO_PHONE_IN_SESSION'
            ], 400);
        }

        try {
            $result = $this->twilioService->verifyCode(
                $phoneNumber,
                $request->input('code')
            );

            Log::info('Test verification attempted', [
                'phone' => $phoneNumber,
                'success' => $result['success'],
                'status' => $result['status'] ?? null
            ]);

            // Clear session if successful
            if ($result['success']) {
                session()->forget('test_verification_phone');
            }

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('Test verification failed', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Code verification test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get SMS delivery status
     */
    public function getSMSStatus(Request $request): JsonResponse
    {
        $request->validate([
            'message_sid' => 'required|string'
        ]);

        try {
            $result = $this->twilioService->getSMSStatus(
                $request->input('message_sid')
            );

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get SMS status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test multiple phone numbers
     */
    public function testMultipleNumbers(Request $request): JsonResponse
    {
        $request->validate([
            'phone_numbers' => 'required|array|min:1|max:5',
            'phone_numbers.*' => 'required|string',
            'message' => 'required|string|max:1600'
        ]);

        $results = [];
        $phoneNumbers = $request->input('phone_numbers');
        $message = $request->input('message');

        foreach ($phoneNumbers as $phoneNumber) {
            try {
                $result = $this->twilioService->sendSMS($phoneNumber, $message);
                $results[] = [
                    'phone_number' => $phoneNumber,
                    'result' => $result
                ];

                // Small delay between messages to avoid rate limiting
                usleep(500000); // 0.5 seconds

            } catch (Exception $e) {
                $results[] = [
                    'phone_number' => $phoneNumber,
                    'result' => [
                        'success' => false,
                        'message' => 'Failed to send SMS',
                        'error' => $e->getMessage()
                    ]
                ];
            }
        }

        $successCount = count(array_filter($results, function($result) {
            return $result['result']['success'] ?? false;
        }));

        Log::info('Bulk SMS test completed', [
            'total_numbers' => count($phoneNumbers),
            'successful' => $successCount,
            'failed' => count($phoneNumbers) - $successCount
        ]);

        return response()->json([
            'success' => true,
            'message' => "Sent to {$successCount} out of " . count($phoneNumbers) . " numbers",
            'results' => $results,
            'summary' => [
                'total' => count($phoneNumbers),
                'successful' => $successCount,
                'failed' => count($phoneNumbers) - $successCount
            ]
        ]);
    }

    /**
     * Get configuration status
     */
    public function getConfigStatus(): JsonResponse
    {
        $config = $this->getEnvironmentCheck();
        $isConfigured = $config['twilio_sid'] && $config['twilio_token'] && $config['twilio_from'];

        return response()->json([
            'success' => $isConfigured,
            'message' => $isConfigured ? 'Twilio is properly configured' : 'Twilio configuration is incomplete',
            'config' => $config,
            'recommendations' => $this->getConfigRecommendations($config)
        ]);
    }

    /**
     * Clear test session data
     */
    public function clearSession(): JsonResponse
    {
        session()->forget(['test_verification_phone']);
        
        return response()->json([
            'success' => true,
            'message' => 'Test session data cleared'
        ]);
    }

    /**
     * Get environment configuration check
     */
    private function getEnvironmentCheck(): array
    {
        return [
            'twilio_sid' => !empty(config('services.twilio.sid')),
            'twilio_token' => !empty(config('services.twilio.token')),
            'twilio_from' => !empty(config('services.twilio.from')),
            'twilio_verify_sid' => !empty(config('services.twilio.verify_sid')),
            'from_number' => config('services.twilio.from', 'Not Set'),
            'verify_service' => config('services.twilio.verify_sid') ? 'Configured' : 'Not Configured',
            'sdk_installed' => class_exists('Twilio\Rest\Client'),
            'cache_configured' => config('cache.default') !== null,
            'logging_enabled' => config('logging.default') !== null
        ];
    }

    /**
     * Get configuration recommendations
     */
    private function getConfigRecommendations(array $config): array
    {
        $recommendations = [];

        if (!$config['twilio_sid']) {
            $recommendations[] = 'Set TWILIO_SID in your .env file';
        }

        if (!$config['twilio_token']) {
            $recommendations[] = 'Set TWILIO_TOKEN in your .env file';
        }

        if (!$config['twilio_from']) {
            $recommendations[] = 'Set TWILIO_FROM phone number in your .env file';
        }

        if (!$config['twilio_verify_sid']) {
            $recommendations[] = 'Consider setting up TWILIO_VERIFY_SID for enhanced security';
        }

        if (!$config['sdk_installed']) {
            $recommendations[] = 'Install Twilio SDK: composer require twilio/sdk';
        }

        if (empty($recommendations)) {
            $recommendations[] = 'Configuration looks good! Ready for testing.';
        }

        return $recommendations;
    }
}
