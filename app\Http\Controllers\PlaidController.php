<?php

namespace App\Http\Controllers;

use App\Models\PlaidAccount;
use App\Services\PlaidService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PlaidController extends Controller
{
    /**
     * The Plaid service instance.
     *
     * @var \App\Services\PlaidService
     */
    protected $plaidService;

    /**
     * Create a new controller instance.
     *
     * @param \App\Services\PlaidService $plaidService
     * @return void
     */
    public function __construct(PlaidService $plaidService)
    {
        $this->middleware('auth');
        $this->plaidService = $plaidService;
    }

    /**
     * Display a listing of the user's Plaid accounts.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $accounts = Auth::user()->plaidAccounts()->latest()->get();
        
        return view('plaid.index', compact('accounts'));
    }

    /**
     * Show the form for creating a new Plaid account.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $linkToken = $this->plaidService->createLinkToken(Auth::user());
        
        if (!$linkToken) {
            return redirect()->route('plaid.index')
                ->with('error', 'Failed to create Plaid link token. Please try again later.');
        }
        
        return view('plaid.create', [
            'linkToken' => $linkToken['link_token'],
        ]);
    }

    /**
     * Store a newly created Plaid account in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'public_token' => 'required|string',
            'metadata' => 'required|json',
        ]);

        $metadata = json_decode($request->metadata, true);
        $institution = $metadata['institution'] ?? [];
        $accounts = $metadata['accounts'] ?? [];

        // Exchange public token for access token
        $exchangeResponse = $this->plaidService->exchangePublicToken($request->public_token);
        
        if (!$exchangeResponse) {
            return redirect()->route('plaid.index')
                ->with('error', 'Failed to exchange Plaid public token. Please try again.');
        }

        $accessToken = $exchangeResponse['access_token'];
        $itemId = $exchangeResponse['item_id'];

        // Get accounts details
        $accountsResponse = $this->plaidService->getAccounts($accessToken);
        
        if (!$accountsResponse) {
            return redirect()->route('plaid.index')
                ->with('error', 'Failed to retrieve account details from Plaid. Please try again.');
        }

        $accountsData = $accountsResponse['accounts'];

        // Create Plaid accounts
        $createdAccounts = 0;

        foreach ($accountsData as $accountData) {
            $account = PlaidAccount::create([
                'user_id' => Auth::id(),
                'institution_id' => $institution['institution_id'] ?? '',
                'institution_name' => $institution['name'] ?? '',
                'account_id' => $accountData['account_id'],
                'account_name' => $accountData['name'],
                'account_type' => $accountData['type'],
                'account_subtype' => $accountData['subtype'] ?? null,
                'account_mask' => $accountData['mask'] ?? '',
                'access_token' => $accessToken,
                'item_id' => $itemId,
                'is_default' => Auth::user()->plaidAccounts()->count() === 0,
                'metadata' => [
                    'balances' => $accountData['balances'] ?? null,
                ],
            ]);

            // Sync transactions for the account
            $this->plaidService->syncTransactions($account);

            $createdAccounts++;
        }

        return redirect()->route('plaid.index')
            ->with('success', "Successfully linked {$createdAccounts} account(s) from {$institution['name']}.");
    }

    /**
     * Set the specified Plaid account as default.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function setDefault($id)
    {
        $account = PlaidAccount::where('user_id', Auth::id())->findOrFail($id);
        
        // Reset all accounts to non-default
        Auth::user()->plaidAccounts()->update(['is_default' => false]);
        
        // Set the selected account as default
        $account->update(['is_default' => true]);
        
        return redirect()->route('plaid.index')
            ->with('success', "Set {$account->account_name} as your default account.");
    }

    /**
     * Toggle payment capability for the specified Plaid account.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function togglePayment($id)
    {
        $account = PlaidAccount::where('user_id', Auth::id())->findOrFail($id);
        
        $account->update(['is_payment_enabled' => !$account->is_payment_enabled]);
        
        $status = $account->is_payment_enabled ? 'enabled' : 'disabled';
        
        return redirect()->route('plaid.index')
            ->with('success', "Payment {$status} for {$account->account_name}.");
    }

    /**
     * Remove the specified Plaid account from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $account = PlaidAccount::where('user_id', Auth::id())->findOrFail($id);
        
        // Delete the account
        $account->delete();
        
        // If this was the default account, set another one as default
        if ($account->is_default) {
            $newDefault = Auth::user()->plaidAccounts()->first();
            
            if ($newDefault) {
                $newDefault->update(['is_default' => true]);
            }
        }
        
        return redirect()->route('plaid.index')
            ->with('success', "Successfully removed {$account->account_name}.");
    }

    /**
     * Show the Plaid test page.
     *
     * @return \Illuminate\View\View
     */
    public function test()
    {
        $user = Auth::user();
        $linkToken = null;
        $error = null;

        try {
            $linkTokenData = $this->plaidService->createLinkToken($user);
            if (isset($linkTokenData['link_token'])) {
                $linkToken = $linkTokenData['link_token'];
            } else {
                $error = 'Failed to create Plaid link token. Check logs for details.';
                Log::error('Plaid test - link token creation failed', ['response' => $linkTokenData]);
            }
        } catch (\Exception $e) {
            $error = 'An exception occurred while creating the link token: ' . $e->getMessage();
            Log::error('Plaid test - exception', ['exception' => $e]);
        }

        return view('plaid.test', compact('linkToken', 'error'));
    }

    /**
     * Handle Plaid webhook.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function handleWebhook(Request $request)
    {
        Log::info('Plaid webhook received', $request->all());
        
        $webhookType = $request->input('webhook_type');
        $webhookCode = $request->input('webhook_code');
        $itemId = $request->input('item_id');
        
        // Handle different webhook types
        switch ($webhookType) {
            case 'TRANSACTIONS':
                $this->handleTransactionsWebhook($webhookCode, $itemId);
                break;
                
            case 'ITEM':
                $this->handleItemWebhook($webhookCode, $itemId);
                break;
        }
        
        return response()->json(['status' => 'success']);
    }
    
    /**
     * Handle transactions webhook.
     *
     * @param  string  $webhookCode
     * @param  string  $itemId
     * @return void
     */
    protected function handleTransactionsWebhook($webhookCode, $itemId)
    {
        // Find accounts with this item ID
        $accounts = PlaidAccount::where('item_id', $itemId)->get();
        
        if ($accounts->isEmpty()) {
            Log::warning('No accounts found for Plaid item ID', ['item_id' => $itemId]);
            return;
        }
        
        foreach ($accounts as $account) {
            // Sync transactions for the account
            $this->plaidService->syncTransactions($account);
        }
    }
    
    /**
     * Handle item webhook.
     *
     * @param  string  $webhookCode
     * @param  string  $itemId
     * @return void
     */
    protected function handleItemWebhook($webhookCode, $itemId)
    {
        // Handle item errors or status changes
        if ($webhookCode === 'ERROR') {
            Log::error('Plaid item error', ['item_id' => $itemId]);
        }
    }
}
