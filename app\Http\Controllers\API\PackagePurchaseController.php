<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Stripe\Stripe;
use Stripe\Customer;
use Stripe\PaymentMethod;
use Stripe\Subscription as StripeSubscription;

class PackagePurchaseController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Get available packages for purchase.
     */
    public function getPackages()
    {
        $packages = [
            [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'description' => 'Essential financial management tools',
                'price' => config('services.subscription.base_monthly_price', 5.00),
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'stripe_price_id' => env('STRIPE_PRICE_BASE_MONTHLY'),
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Basic insights',
                    'Hierarchical sub-bins (3 levels)',
                    'Bank account linking'
                ],
                'limits' => [
                    'max_bins' => 10,
                    'max_sub_bins_per_bin' => 10,
                    'max_nesting_depth' => 3
                ]
            ],
            [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'description' => 'Advanced financial management with premium features',
                'price' => config('services.subscription.premium_monthly_price', 10.00),
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'stripe_price_id' => env('STRIPE_PRICE_PREMIUM_MONTHLY'),
                'features' => [
                    'All Base features',
                    'Unlimited hierarchical sub-bins',
                    'Crypto wallet integration',
                    'Advanced AI insights',
                    'Priority notifications',
                    'Advanced reporting'
                ],
                'limits' => [
                    'max_bins' => 'unlimited',
                    'max_sub_bins_per_bin' => 'unlimited',
                    'max_nesting_depth' => 'unlimited'
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'packages' => $packages,
                'trial_days' => 7,
                'currency' => 'USD'
            ]
        ]);
    }

    /**
     * Purchase a package with Stripe.
     */
    public function purchasePackage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_id' => 'required|string|in:base_monthly,premium_monthly',
            'payment_method_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $packageId = $request->package_id;
        $paymentMethodId = $request->payment_method_id;

        try {
            // Get package details
            $packageDetails = $this->getPackageDetails($packageId);
            if (!$packageDetails) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid package selected'
                ], 400);
            }

            // Create or get Stripe customer
            $stripeCustomer = $this->createOrGetStripeCustomer($user);

            // Attach payment method to customer
            $paymentMethod = PaymentMethod::retrieve($paymentMethodId);
            $paymentMethod->attach(['customer' => $stripeCustomer->id]);

            // Set as default payment method
            Customer::update($stripeCustomer->id, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId,
                ],
            ]);

            // Create Stripe subscription with 7-day trial
            $stripeSubscription = StripeSubscription::create([
                'customer' => $stripeCustomer->id,
                'items' => [
                    ['price' => $packageDetails['stripe_price_id']],
                ],
                'trial_period_days' => 7,
                'payment_behavior' => 'default_incomplete',
                'payment_settings' => [
                    'save_default_payment_method' => 'on_subscription',
                ],
                'expand' => ['latest_invoice.payment_intent'],
            ]);

            // Cancel any existing subscription
            $this->cancelExistingSubscription($user);

            // Create local subscription record
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'name' => $packageDetails['name'],
                'stripe_id' => $stripeSubscription->id,
                'stripe_status' => $stripeSubscription->status,
                'stripe_price' => $packageDetails['stripe_price_id'],
                'subscription_tier' => $packageDetails['tier'],
                'billing_cycle' => $packageDetails['billing_cycle'],
                'price' => $packageDetails['price'],
                'currency' => 'USD',
                'features' => $packageDetails['features'],
                'trial_ends_at' => now()->addDays(7),
            ]);

            // Update user
            $user->update([
                'subscription_tier' => 'trial',
                'trial_started_at' => now(),
                'stripe_id' => $stripeCustomer->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Package purchased successfully! Your 7-day trial has started.',
                'data' => [
                    'subscription' => $subscription,
                    'package' => $packageDetails,
                    'trial_info' => [
                        'trial_started' => true,
                        'trial_days_remaining' => 7,
                        'trial_ends_at' => now()->addDays(7)->toISOString(),
                        'auto_billing_date' => now()->addDays(7)->toISOString()
                    ],
                    'stripe_info' => [
                        'subscription_id' => $stripeSubscription->id,
                        'customer_id' => $stripeCustomer->id,
                        'status' => $stripeSubscription->status
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to purchase package: ' . $e->getMessage(),
                'error_code' => 'purchase_failed'
            ], 500);
        }
    }

    /**
     * Test purchase without Stripe (for development).
     */
    public function purchasePackageTest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_id' => 'required|string|in:base_monthly,premium_monthly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $packageId = $request->package_id;

        try {
            // Get package details
            $packageDetails = $this->getPackageDetails($packageId);

            // Cancel any existing subscription
            $this->cancelExistingSubscription($user);

            // Create test subscription record
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'name' => $packageDetails['name'] . ' (TEST)',
                'stripe_id' => 'test_sub_' . uniqid(),
                'stripe_status' => 'trialing',
                'stripe_price' => 'test_price_' . $packageId,
                'subscription_tier' => $packageDetails['tier'],
                'billing_cycle' => $packageDetails['billing_cycle'],
                'price' => $packageDetails['price'],
                'currency' => 'USD',
                'features' => $packageDetails['features'],
                'trial_ends_at' => now()->addDays(7),
            ]);

            // Update user
            $user->update([
                'subscription_tier' => 'trial',
                'trial_started_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'TEST MODE: Package purchased successfully! Your 7-day trial has started.',
                'data' => [
                    'subscription' => $subscription,
                    'package' => $packageDetails,
                    'trial_info' => [
                        'trial_started' => true,
                        'trial_days_remaining' => 7,
                        'trial_ends_at' => now()->addDays(7)->toISOString(),
                        'test_mode' => true
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to purchase package: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get package details by ID.
     */
    private function getPackageDetails($packageId)
    {
        $packages = [
            'base_monthly' => [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'tier' => 'base',
                'billing_cycle' => 'monthly',
                'price' => config('services.subscription.base_monthly_price', 9.99),
                'stripe_price_id' => env('STRIPE_PRICE_BASE_MONTHLY'),
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Basic insights',
                    'Hierarchical sub-bins (3 levels)'
                ]
            ],
            'premium_monthly' => [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'tier' => 'premium',
                'billing_cycle' => 'monthly',
                'price' => config('services.subscription.premium_monthly_price', 19.99),
                'stripe_price_id' => env('STRIPE_PRICE_PREMIUM_MONTHLY'),
                'features' => [
                    'All Base features',
                    'Unlimited hierarchical sub-bins',
                    'Crypto wallet integration',
                    'Advanced AI insights'
                ]
            ]
        ];

        return $packages[$packageId] ?? null;
    }

    /**
     * Create or get Stripe customer.
     */
    private function createOrGetStripeCustomer($user)
    {
        if ($user->stripe_id) {
            try {
                return Customer::retrieve($user->stripe_id);
            } catch (\Exception $e) {
                // Customer doesn't exist, create new one
            }
        }

        $customer = Customer::create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        $user->update(['stripe_id' => $customer->id]);

        return $customer;
    }

    /**
     * Cancel existing subscription.
     */
    private function cancelExistingSubscription($user)
    {
        $existingSubscription = Subscription::where('user_id', $user->id)
            ->whereIn('stripe_status', ['active', 'trialing'])
            ->first();

        if ($existingSubscription) {
            $existingSubscription->update(['stripe_status' => 'canceled']);
        }
    }
}
