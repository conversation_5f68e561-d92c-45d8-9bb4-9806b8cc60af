# 🗂️ Bin Management API Documentation

## 🔐 Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer {your_access_token}
```

## 📊 Base URL
```
https://your-domain.com/api
```

## 🎯 Key Features
- **Simplified Categories**: Only `income` and `expense` types
- **Smart Sub-Bin Inheritance**: Sub-bins auto-inherit parent category
- **Max Limits**: Uses maximum limits instead of minimum limits
- **Cumulative Balance**: Automatic calculation and updates
- **Enhanced Validation**: Comprehensive error handling and validation

---

## 📋 **GET** `/bins` - List All Bins

**Description**: Retrieve all bins for the authenticated user with pagination and filtering.

**Query Parameters**:
- `page` (integer, optional): Page number (default: 1)
- `per_page` (integer, optional): Items per page (default: 10, max: 100)
- `type` (string, optional): Filter by type (`income`, `expense`)
- `is_active` (boolean, optional): Filter by active status
- `search` (string, optional): Search in names and descriptions

**Response Example**:
```json
{
  "data": [
    {
      "id": 1,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "name": "Emergency Fund",
      "type": "income",
      "description": "Emergency savings account",
      "threshold_max_limit": "10000.00",
      "threshold_max_warning": "8000.00",
      "current_amount": "5000.00",
      "currency": "USD",
      "is_active": true,
      "sub_bins_count": 3,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 10,
    "total": 25,
    "last_page": 3
  },
  "user_cumulative_balance": "15000.00"
}
```

---

## ➕ **POST** `/bins` - Create New Bin

**Description**: Create a new bin for the authenticated user.

**Request Body**:
```json
{
  "name": "Emergency Fund",
  "type": "income",
  "description": "Emergency savings account",
  "threshold_max_limit": 10000.00,
  "threshold_max_warning": 8000.00,
  "currency": "USD"
}
```

**Validation Rules**:
- `name`: **required**, string, max 255 characters
- `type`: **required**, string, must be `income` or `expense`
- `description`: optional, string
- `threshold_max_limit`: **required**, numeric, minimum 0
- `threshold_max_warning`: optional, numeric, must be less than `threshold_max_limit`
- `currency`: optional, string, max 10 characters

**Response (201 Created)**:
```json
{
  "message": "Bin created successfully",
  "bin": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "Emergency Fund",
    "type": "income",
    "description": "Emergency savings account",
    "threshold_max_limit": "10000.00",
    "threshold_max_warning": "8000.00",
    "current_amount": "0.00",
    "currency": "USD",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  },
  "remaining_bins": 47,
  "user_cumulative_balance": "15000.00"
}
```

---

## 👁️ **GET** `/bins/{id}` - Get Single Bin

**Description**: Retrieve details of a specific bin with sub-bins.

**Path Parameters**:
- `id` (integer): Bin ID

**Response Example**:
```json
{
  "bin": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "Emergency Fund",
    "type": "income",
    "description": "Emergency savings account",
    "threshold_max_limit": "10000.00",
    "threshold_max_warning": "8000.00",
    "current_amount": "5000.00",
    "currency": "USD",
    "is_active": true,
    "sub_bins": [
      {
        "id": 1,
        "name": "Medical Emergency",
        "type": "income",
        "current_amount": "2000.00",
        "depth_level": 1,
        "parent_sub_bin_id": null
      },
      {
        "id": 2,
        "name": "Car Emergency",
        "type": "income",
        "current_amount": "1500.00",
        "depth_level": 1,
        "parent_sub_bin_id": null
      }
    ],
    "transactions_count": 15,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

---

## ✏️ **PUT** `/bins/{id}` - Update Bin

**Description**: Update an existing bin.

**Path Parameters**:
- `id` (integer): Bin ID

**Request Body (all fields optional)**:
```json
{
  "name": "Updated Emergency Fund",
  "type": "income",
  "description": "Updated description",
  "threshold_max_limit": 12000.00,
  "threshold_max_warning": 10000.00,
  "currency": "USD",
  "is_active": true
}
```

**Validation Rules**:
- `name`: optional, string, max 255 characters
- `type`: optional, string, must be `income` or `expense`
- `description`: optional, string
- `threshold_max_limit`: optional, numeric, minimum 0
- `threshold_max_warning`: optional, numeric, must be less than `threshold_max_limit`
- `currency`: optional, string, max 10 characters
- `is_active`: optional, boolean

**Response Example**:
```json
{
  "message": "Bin updated successfully",
  "bin": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "Updated Emergency Fund",
    "type": "income",
    "description": "Updated description",
    "threshold_max_limit": "12000.00",
    "threshold_max_warning": "10000.00",
    "current_amount": "5000.00",
    "currency": "USD",
    "is_active": true,
    "updated_at": "2024-01-01T12:00:00.000000Z"
  },
  "user_cumulative_balance": "15000.00"
}
```

---

## 🗑️ **DELETE** `/bins/{id}` - Delete Bin

**Description**: Delete a bin. The bin must not have any sub-bins or transactions.

**Path Parameters**:
- `id` (integer): Bin ID

**Success Response (200 OK)**:
```json
{
  "message": "Bin deleted successfully",
  "deleted_bin": {
    "name": "Emergency Fund",
    "type": "income",
    "amount": "5000.00"
  },
  "user_cumulative_balance": "10000.00"
}
```

**Error Responses**:

**404 Not Found** - Bin not found:
```json
{
  "message": "Bin not found"
}
```

**422 Unprocessable Entity** - Bin has sub-bins:
```json
{
  "message": "Cannot delete bin with existing sub-bins",
  "error": "Please delete all sub-bins first before deleting the bin",
  "sub_bins_count": 3
}
```

**422 Unprocessable Entity** - Bin has transactions:
```json
{
  "message": "Cannot delete bin with existing transactions",
  "error": "Please delete all transactions first before deleting the bin",
  "transactions_count": 15
}
```

**Important Notes**:
- ⚠️ **Bin must be empty**: No sub-bins or transactions allowed
- 🔄 **Auto-recalculation**: User's cumulative balance is updated after deletion
- 📧 **Notification**: User receives notification about bin deletion
- 🔒 **Security**: Only bin owner can delete their bins

---

## 🎯 **Key Changes from Previous Version**

### **✅ Updated Field Names**:
- `threshold_min` → `threshold_max_limit` (maximum limit)
- `threshold_max` → `threshold_max_warning` (warning threshold)

### **✅ Simplified Categories**:
- `expenditure` → `expense`
- Only `income` and `expense` allowed

### **✅ Enhanced Responses**:
- All responses include `user_cumulative_balance`
- Create/update responses include remaining limits
- Delete responses include deleted bin details

### **✅ Improved Validation**:
- Better error messages and codes
- Comprehensive validation rules
- Detailed error responses for delete operations

### **✅ Automatic Features**:
- Cumulative balance auto-calculation
- Notification system integration
- Subscription limit checking

---

## 🔄 **Cumulative Balance Calculation**

The `user_cumulative_balance` is automatically calculated as:
```
Cumulative Balance = Sum of all Income Bins - Sum of all Expense Bins
                   + Sum of all Income Sub-Bins - Sum of all Expense Sub-Bins
```

**Auto-updates when**:
- Creating new bins
- Updating bin types or amounts
- Deleting bins
- Creating/updating/deleting transactions

---

## 📱 **Usage Examples**

### **Create Income Bin**:
```bash
curl -X POST https://your-domain.com/api/bins \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Salary",
    "type": "income",
    "threshold_max_limit": 5000.00,
    "threshold_max_warning": 4000.00
  }'
```

### **Create Expense Bin**:
```bash
curl -X POST https://your-domain.com/api/bins \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Monthly Expenses",
    "type": "expense",
    "threshold_max_limit": 3000.00,
    "threshold_max_warning": 2500.00
  }'
```

### **Delete Bin**:
```bash
curl -X DELETE https://your-domain.com/api/bins/1 \
  -H "Authorization: Bearer {token}"
```

---

## 🚀 **Next Steps**

After implementing the bin delete API:
1. **Test all endpoints** with the new structure
2. **Update mobile app** to use new field names
3. **Implement sub-bin APIs** with parent category inheritance
4. **Test cumulative balance** calculation accuracy
5. **Verify notification system** for deletions

**The bin management system is now complete with enhanced delete functionality!** 🎉
