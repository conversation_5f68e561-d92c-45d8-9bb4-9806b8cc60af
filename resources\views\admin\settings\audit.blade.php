@extends('admin.layouts.app')

@section('content')
{{-- <div class="admin-content"> --}}
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Settings Audit Log</h1>
            <div class="breadcrumb">
                <span class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></span>
                <span class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Settings</a></span>
                <span class="breadcrumb-item active">Audit Log</span>
            </div>
        </div>

        <!-- Filter Form -->
        <div class="card mb-4">
            <div class="card-body">
                <form action="{{ route('admin.settings.audit') }}" method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="key" class="form-label">Setting Key</label>
                        <input type="text" class="form-control" id="key" name="key" value="{{ request('key') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="user_id" class="form-label">User</label>
                        <select class="form-select" id="user_id" name="user_id">
                            <option value="">All Users</option>
                            @foreach(\App\Models\User::where('is_admin', true)->get() as $user)
                                <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">Date From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">Date To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                        <a href="{{ route('admin.settings.audit') }}" class="btn btn-secondary">
                            <i class="fas fa-undo me-2"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Audit Log Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Setting Key</th>
                                <th>Action</th>
                                <th>Old Value</th>
                                <th>New Value</th>
                                <th>User</th>
                                <th>IP Address</th>
                                <th>Date/Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($logs as $log)
                                <tr>
                                    <td>{{ $log->id }}</td>
                                    <td>{{ $log->setting_key }}</td>
                                    <td>
                                        @if($log->action == 'update')
                                            <span class="badge bg-primary">Update</span>
                                        @elseif($log->action == 'create')
                                            <span class="badge bg-success">Create</span>
                                        @elseif($log->action == 'delete')
                                            <span class="badge bg-danger">Delete</span>
                                        @else
                                            <span class="badge bg-secondary">{{ $log->action }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if(is_array($log->old_value) || is_object($log->old_value))
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#valueModal" data-value="{{ json_encode($log->old_value) }}">
                                                View
                                            </button>
                                        @else
                                            {{ $log->old_value ?? 'N/A' }}
                                        @endif
                                    </td>
                                    <td>
                                        @if(is_array($log->new_value) || is_object($log->new_value))
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#valueModal" data-value="{{ json_encode($log->new_value) }}">
                                                View
                                            </button>
                                        @else
                                            {{ $log->new_value ?? 'N/A' }}
                                        @endif
                                    </td>
                                    <td>{{ $log->user ? $log->user->name : 'System' }}</td>
                                    <td>{{ $log->ip_address }}</td>
                                    <td>{{ $log->created_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center">No audit logs found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $logs->withQueryString()->links() }}
                </div>
            </div>
        </div>

        <div class="mt-3">
            <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Settings
            </a>
        </div>
    </div>
</div>

<!-- Value Modal -->
<div class="modal fade" id="valueModal" tabindex="-1" aria-labelledby="valueModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="valueModalLabel">Setting Value</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre id="valueContent" class="bg-light p-3 rounded"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const valueModal = document.getElementById('valueModal');
        if (valueModal) {
            valueModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const value = button.getAttribute('data-value');
                const valueContent = document.getElementById('valueContent');
                
                try {
                    const formattedValue = JSON.stringify(JSON.parse(value), null, 2);
                    valueContent.textContent = formattedValue;
                } catch (e) {
                    valueContent.textContent = value;
                }
            });
        }
    });
</script>
@endpush
@endsection
