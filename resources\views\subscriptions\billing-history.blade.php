@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="fw-bold" style="color: var(--primary-color);">Billing History</h4>
                <a href="{{ route('subscriptions.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Subscription
                </a>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-body">
                    @if(count($invoices) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Invoice</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoices as $invoice)
                                        <tr>
                                            <td>{{ \Carbon\Carbon::createFromTimestamp($invoice->created)->format('M d, Y') }}</td>
                                            <td>
                                                {{ ucfirst(Auth::user()->subscription_tier) }} Plan - 
                                                {{ ucfirst(Auth::user()->getActiveSubscription()->billing_cycle) }}
                                            </td>
                                            <td>${{ number_format($invoice->amount_paid / 100, 2) }}</td>
                                            <td>
                                                @if($invoice->paid)
                                                    <span class="badge bg-success">Paid</span>
                                                @elseif($invoice->status === 'open')
                                                    <span class="badge bg-warning">Pending</span>
                                                @else
                                                    <span class="badge bg-danger">Failed</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ $invoice->hosted_invoice_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-file-invoice me-1"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-receipt fa-4x text-muted"></i>
                            </div>
                            <h5 class="mb-3">No Billing History</h5>
                            <p class="text-muted">You don't have any billing history yet. Invoices will appear here once your subscription is active.</p>
                        </div>
                    @endif
                </div>
            </div>
            
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold" style="color: var(--primary-color);">Payment Methods</h5>
                </div>
                <div class="card-body">
                    @if(count($paymentMethods) > 0)
                        <div class="row">
                            @foreach($paymentMethods as $method)
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                @php
                                                    $cardBrands = [
                                                        'visa' => 'fab fa-cc-visa',
                                                        'mastercard' => 'fab fa-cc-mastercard',
                                                        'amex' => 'fab fa-cc-amex',
                                                        'discover' => 'fab fa-cc-discover',
                                                        'diners' => 'fab fa-cc-diners-club',
                                                        'jcb' => 'fab fa-cc-jcb',
                                                    ];
                                                    
                                                    $iconClass = $cardBrands[$method->card->brand] ?? 'far fa-credit-card';
                                                @endphp
                                                
                                                <div class="me-3">
                                                    <i class="{{ $iconClass }} fa-2x"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">{{ ucfirst($method->card->brand) }} •••• {{ $method->card->last4 }}</h6>
                                                    <p class="text-muted mb-0">Expires {{ $method->card->exp_month }}/{{ $method->card->exp_year }}</p>
                                                </div>
                                            </div>
                                            
                                            <div class="mt-3 d-flex justify-content-between align-items-center">
                                                @if($method->id === $defaultPaymentMethod->id)
                                                    <span class="badge bg-success">Default</span>
                                                @else
                                                    <form action="{{ route('subscriptions.set-default-payment', $method->id) }}" method="POST">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-outline-primary">Set as Default</button>
                                                    </form>
                                                @endif
                                                
                                                <form action="{{ route('subscriptions.remove-payment', $method->id) }}" method="POST">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" {{ $method->id === $defaultPaymentMethod->id ? 'disabled' : '' }}>
                                                        Remove
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No payment methods found.</p>
                        </div>
                    @endif
                    
                    <div class="mt-3">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPaymentMethodModal">
                            <i class="fas fa-plus me-2"></i> Add Payment Method
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Payment Method Modal -->
<div class="modal fade" id="addPaymentMethodModal" tabindex="-1" aria-labelledby="addPaymentMethodModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPaymentMethodModalLabel">Add Payment Method</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="payment-form" action="{{ route('subscriptions.add-payment') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="card-element" class="form-label">Credit or debit card</label>
                        <div id="card-element" class="form-control" style="height: 40px; padding-top: 10px;"></div>
                        <div id="card-errors" class="text-danger mt-2" role="alert"></div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="set-as-default" name="set_as_default" checked>
                        <label class="form-check-label" for="set-as-default">
                            Set as default payment method
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success" id="submit-button">
                            <span id="button-text">Add Payment Method</span>
                            <span id="spinner" class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Create a Stripe client
        const stripe = Stripe('{{ config('services.stripe.key') }}');
        const elements = stripe.elements();
        
        // Create an instance of the card Element
        const cardElement = elements.create('card', {
            style: {
                base: {
                    color: '#32325d',
                    fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                    fontSmoothing: 'antialiased',
                    fontSize: '16px',
                    '::placeholder': {
                        color: '#aab7c4'
                    }
                },
                invalid: {
                    color: '#fa755a',
                    iconColor: '#fa755a'
                }
            }
        });
        
        // Add an instance of the card Element into the `card-element` div
        cardElement.mount('#card-element');
        
        // Handle real-time validation errors from the card Element
        cardElement.on('change', function(event) {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
            } else {
                displayError.textContent = '';
            }
        });
        
        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');
        const buttonText = document.getElementById('button-text');
        const spinner = document.getElementById('spinner');
        
        form.addEventListener('submit', function(event) {
            event.preventDefault();
            
            // Disable the submit button to prevent repeated clicks
            submitButton.disabled = true;
            buttonText.textContent = 'Processing...';
            spinner.classList.remove('d-none');
            
            stripe.createToken(cardElement).then(function(result) {
                if (result.error) {
                    // Show error to your customer
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = result.error.message;
                    
                    // Re-enable the submit button
                    submitButton.disabled = false;
                    buttonText.textContent = 'Add Payment Method';
                    spinner.classList.add('d-none');
                } else {
                    // Send the token to your server
                    stripeTokenHandler(result.token);
                }
            });
        });
        
        function stripeTokenHandler(token) {
            // Insert the token ID into the form so it gets submitted to the server
            const hiddenInput = document.createElement('input');
            hiddenInput.setAttribute('type', 'hidden');
            hiddenInput.setAttribute('name', 'stripeToken');
            hiddenInput.setAttribute('value', token.id);
            form.appendChild(hiddenInput);
            
            // Submit the form
            form.submit();
        }
    });
</script>
@endpush
@endsection
