# PocketWatch API - Simple Documentation

Base URL: `https://your-domain.com/api`

Authentication: Add token to request header
```
Authorization: Bearer {your_token}
```

## Authentication Endpoints

### POST `/register`
Register a new user
- `name` (required): User's full name
- `email` (required): User's email address
- `password` (required): Password (min 8 characters)
- `password_confirmation` (required): Password confirmation
- `country_code` (optional): Country calling code (e.g., "1" for US)
- `phone_number` (optional): Phone number

### POST `/login`
Login and get authentication token
- `email` (required): User's email address
- `password` (required): User's password

### GET `/me`
Get current user profile
- No parameters required

### POST `/logout`
Logout and invalidate token
- No parameters required

### PUT `/profile`
Update user profile
- `name` (optional): User's full name
- `email` (optional): User's email address
- `country_code` (optional): Country calling code (e.g., "1" for US)
- `phone_number` (optional): Phone number

### PUT `/password`
Change user password
- `current_password` (required): Current password
- `password` (required): New password (min 8 characters)
- `password_confirmation` (required): New password confirmation

### POST `/avatar`
Update user avatar
- `avatar` (required): Image file (jpeg, png, jpg, gif, max 2MB)

### POST `/forgot-password`
Request password reset link
- `email` (required): User's email address

### POST `/reset-password`
Reset password with token
- `token` (required): Reset token from email
- `email` (required): User's email address
- `password` (required): New password (min 8 characters)
- `password_confirmation` (required): New password confirmation

## Bin Endpoints

### GET `/bins`
Get all bins
- `page` (optional): Page number
- `per_page` (optional): Items per page

### POST `/bins`
Create a new bin
- `name` (required): Bin name
- `description` (optional): Bin description
- `threshold_min` (required): Minimum threshold amount
- `threshold_max` (optional): Maximum threshold amount
- `currency` (optional): Currency code (default: USD)

### GET `/bins/{id}`
Get bin details
- URL parameter: `id` (required): Bin ID

### PUT `/bins/{id}`
Update bin
- URL parameter: `id` (required): Bin ID
- `name` (optional): Bin name
- `description` (optional): Bin description
- `threshold_min` (optional): Minimum threshold amount
- `threshold_max` (optional): Maximum threshold amount
- `currency` (optional): Currency code
- `is_active` (optional): Bin status (boolean)

### DELETE `/bins/{id}`
Delete bin
- URL parameter: `id` (required): Bin ID

## Sub-Bin Endpoints

### GET `/bins/{binId}/sub-bins`
Get all sub-bins for a bin
- URL parameter: `binId` (required): Parent bin ID

### POST `/bins/{binId}/sub-bins`
Create a new sub-bin
- URL parameter: `binId` (required): Parent bin ID
- `name` (required): Sub-bin name
- `description` (optional): Sub-bin description
- `threshold_min` (required): Minimum threshold amount
- `threshold_max` (optional): Maximum threshold amount
- `currency` (optional): Currency code (default: USD)

### GET `/bins/{binId}/sub-bins/{id}`
Get sub-bin details
- URL parameters:
  - `binId` (required): Parent bin ID
  - `id` (required): Sub-bin ID

### PUT `/bins/{binId}/sub-bins/{id}`
Update sub-bin
- URL parameters:
  - `binId` (required): Parent bin ID
  - `id` (required): Sub-bin ID
- `name` (optional): Sub-bin name
- `description` (optional): Sub-bin description
- `threshold_min` (optional): Minimum threshold amount
- `threshold_max` (optional): Maximum threshold amount
- `currency` (optional): Currency code
- `is_active` (optional): Sub-bin status (boolean)

### DELETE `/bins/{binId}/sub-bins/{id}`
Delete sub-bin
- URL parameters:
  - `binId` (required): Parent bin ID
  - `id` (required): Sub-bin ID

## Transaction Endpoints

### GET `/transactions`
Get all transactions
- `page` (optional): Page number
- `per_page` (optional): Items per page
- `bin_id` (optional): Filter by bin ID
- `sub_bin_id` (optional): Filter by sub-bin ID
- `type` (optional): Filter by transaction type (income, expense, transfer)
- `start_date` (optional): Filter by start date
- `end_date` (optional): Filter by end date
- `category` (optional): Filter by category

### POST `/transactions`
Create a new transaction
- `bin_id` (optional): Bin ID
- `sub_bin_id` (optional): Sub-bin ID
- `transaction_type` (required): Type (income, expense, transfer)
- `amount` (required): Transaction amount
- `currency` (optional): Currency code (default: USD)
- `description` (optional): Transaction description
- `category` (optional): Transaction category
- `payment_method` (optional): Payment method
- `transaction_date` (required): Transaction date

### GET `/transactions/{id}`
Get transaction details
- URL parameter: `id` (required): Transaction ID

### PUT `/transactions/{id}`
Update transaction
- URL parameter: `id` (required): Transaction ID
- `bin_id` (optional): Bin ID
- `sub_bin_id` (optional): Sub-bin ID
- `transaction_type` (optional): Type (income, expense, transfer)
- `amount` (optional): Transaction amount
- `currency` (optional): Currency code
- `description` (optional): Transaction description
- `category` (optional): Transaction category
- `payment_method` (optional): Payment method
- `transaction_date` (optional): Transaction date

### DELETE `/transactions/{id}`
Delete transaction
- URL parameter: `id` (required): Transaction ID

### GET `/transactions/stats`
Get transaction statistics
- `period` (optional): Period (day, week, month, year)
- `start_date` (optional): Start date for custom period
- `end_date` (optional): End date for custom period

### GET `/transactions/by-bin/{binId}`
Get transactions by bin
- URL parameter: `binId` (required): Bin ID
- `start_date` (optional): Filter by start date
- `end_date` (optional): Filter by end date
- `type` (optional): Filter by transaction type
- `page` (optional): Page number
- `per_page` (optional): Items per page

### GET `/transactions/by-category/{category}`
Get transactions by category
- URL parameter: `category` (required): Category name
- `start_date` (optional): Filter by start date
- `end_date` (optional): Filter by end date
- `type` (optional): Filter by transaction type
- `page` (optional): Page number
- `per_page` (optional): Items per page

## Subscription Endpoints

### GET `/subscriptions`
Get all subscriptions
- No parameters required

### POST `/subscriptions`
Create a new subscription (includes 7-day free trial)
- `subscription_tier` (required): Subscription tier (base, premium)
- `billing_cycle` (required): Billing cycle (monthly, yearly)

#### Subscription Tiers
- **Base Tier ($5/month or $50/year)**
  - Secure user registration and login
  - Link bank accounts via Plaid
  - View total balance on dashboard
  - Hide/Show balance with eye toggle
  - Create financial bins with thresholds
  - Automatic transaction categorization
  - Edit bins manually
  - View graphical insights
  - View recent transactions carousel
  - Chat with Binnit chatbot
  - Notification system
  - 3 Levels of Sub-Bins maximum

- **Premium Tier ($10/month or $100/year)**
  - All Base Tier features
  - Unlimited Sub-Bins
  - Crypto Scanner Page
  - Priority notifications
  - Advanced Binnit AI suggestions

### GET `/subscriptions/{id}`
Get subscription details
- URL parameter: `id` (required): Subscription ID

### PUT `/subscriptions/{id}`
Update subscription
- URL parameter: `id` (required): Subscription ID
- `subscription_tier` (required): New subscription tier

### DELETE `/subscriptions/{id}`
Cancel subscription
- URL parameter: `id` (required): Subscription ID

### GET `/subscriptions/plans`
Get available subscription plans
- No parameters required

## Crypto Wallet Endpoints

### GET `/crypto-wallets`
Get all crypto wallets
- No parameters required

### POST `/crypto-wallets`
Create a new crypto wallet
- `wallet_address` (required): Blockchain wallet address
- `wallet_name` (optional): Custom wallet name
- `blockchain_network` (required): Network (ethereum, binance, polygon, avalanche)

### GET `/crypto-wallets/{id}`
Get crypto wallet details
- URL parameter: `id` (required): Wallet ID

### PUT `/crypto-wallets/{id}`
Update crypto wallet
- URL parameter: `id` (required): Wallet ID
- `wallet_name` (optional): Custom wallet name
- `is_active` (optional): Wallet status (boolean)

### DELETE `/crypto-wallets/{id}`
Delete crypto wallet
- URL parameter: `id` (required): Wallet ID

### GET `/crypto-wallets/{id}/assets`
Get crypto wallet assets
- URL parameter: `id` (required): Wallet ID

### GET `/crypto-wallets/{id}/transactions`
Get crypto wallet transactions
- URL parameter: `id` (required): Wallet ID

### POST `/crypto-wallets/{id}/sync`
Sync crypto wallet with blockchain
- URL parameter: `id` (required): Wallet ID

## Webhook Endpoints

### POST `/webhook/stripe`
Handle Stripe webhook events
- Header: `Stripe-Signature` (required): Stripe webhook signature
