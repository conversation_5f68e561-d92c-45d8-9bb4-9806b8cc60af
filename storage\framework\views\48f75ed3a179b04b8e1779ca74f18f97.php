<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Verification Test - PocketWatch</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .status-success { border-left-color: #28a745; }
        .status-error { border-left-color: #dc3545; }
        .status-warning { border-left-color: #ffc107; }
        .verification-step {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border: 2px solid #e9ecef;
        }
        .verification-step.active {
            border-color: #007bff;
            background: #e7f3ff;
        }
        .verification-step.completed {
            border-color: #28a745;
            background: #d4edda;
        }
        .phone-input {
            font-size: 18px;
            padding: 12px;
            border-radius: 8px;
        }
        .code-input {
            font-size: 24px;
            text-align: center;
            letter-spacing: 8px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .config-item {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
        }
        .result-box {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary">
                        <i class="fas fa-mobile-alt"></i> Phone Verification Test
                    </h1>
                    <p class="lead text-muted">Test SMS verification with Twilio integration</p>
                </div>
            </div>
        </div>

        <!-- Configuration Check -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cog"></i> Twilio Configuration</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Environment Variables:</h6>
                                <div class="config-item">
                                    <strong>TWILIO_SID:</strong> 
                                    <span class="text-success"><?php echo e(config('services.twilio.sid') ? '✓ Set' : '✗ Missing'); ?></span>
                                </div>
                                <div class="config-item">
                                    <strong>TWILIO_TOKEN:</strong> 
                                    <span class="text-success"><?php echo e(config('services.twilio.token') ? '✓ Set' : '✗ Missing'); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Additional Settings:</h6>
                                <div class="config-item">
                                    <strong>FROM_NUMBER:</strong> <?php echo e(config('services.twilio.from', 'Not Set')); ?>

                                </div>
                                <div class="config-item">
                                    <strong>VERIFY_SID:</strong> <?php echo e(config('services.twilio.verify_sid') ? '✓ Set' : '✗ Not Set (will use SMS)'); ?>

                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary" onclick="testConnection()">
                                <i class="fas fa-plug"></i> Test Connection
                            </button>
                            <div id="connection-result" class="result-box" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Phone Verification Flow -->
        <div class="row">
            <div class="col-md-8 mx-auto">
                <!-- Step 1: Enter Phone Number -->
                <div class="verification-step active" id="step-1">
                    <h4><i class="fas fa-phone"></i> Step 1: Enter Phone Number</h4>
                    <p class="text-muted">Enter your phone number to receive a verification code</p>
                    
                    <div class="mb-3">
                        <label for="phone-number" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control phone-input" id="phone-number" 
                               placeholder="+1234567890" value="+1">
                        <div class="form-text">Include country code (e.g., +1 for US)</div>
                    </div>
                    
                    <button class="btn btn-primary btn-lg" onclick="sendVerificationCode()" id="send-code-btn">
                        <i class="fas fa-paper-plane"></i> Send Verification Code
                    </button>
                    
                    <div id="send-result" class="result-box" style="display: none;"></div>
                </div>

                <!-- Step 2: Enter Verification Code -->
                <div class="verification-step" id="step-2">
                    <h4><i class="fas fa-key"></i> Step 2: Enter Verification Code</h4>
                    <p class="text-muted">Enter the 6-digit code sent to your phone</p>
                    
                    <div class="mb-3">
                        <label for="verification-code" class="form-label">Verification Code</label>
                        <input type="text" class="form-control code-input" id="verification-code" 
                               placeholder="123456" maxlength="6">
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button class="btn btn-success btn-lg" onclick="verifyCode()" id="verify-btn" disabled>
                            <i class="fas fa-check"></i> Verify Code
                        </button>
                        <button class="btn btn-outline-secondary" onclick="resendCode()" id="resend-btn" disabled>
                            <i class="fas fa-redo"></i> Resend Code
                        </button>
                    </div>
                    
                    <div id="verify-result" class="result-box" style="display: none;"></div>
                </div>

                <!-- Step 3: Verification Complete -->
                <div class="verification-step" id="step-3">
                    <h4><i class="fas fa-check-circle text-success"></i> Step 3: Verification Complete</h4>
                    <p class="text-success">Your phone number has been successfully verified!</p>
                    
                    <div class="alert alert-success">
                        <h6><i class="fas fa-info-circle"></i> Verification Details:</h6>
                        <div id="verification-details"></div>
                    </div>
                    
                    <button class="btn btn-primary" onclick="resetVerification()">
                        <i class="fas fa-refresh"></i> Test Another Number
                    </button>
                </div>
            </div>
        </div>

        <!-- Additional Tests -->
        <div class="row mt-5">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-sms"></i> Send Custom SMS</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="custom-phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="custom-phone" placeholder="+1234567890">
                        </div>
                        <div class="mb-3">
                            <label for="custom-message" class="form-label">Message</label>
                            <textarea class="form-control" id="custom-message" rows="3" 
                                      placeholder="Your custom message here..." maxlength="1600"></textarea>
                            <div class="form-text">Max 1600 characters</div>
                        </div>
                        <button class="btn btn-info" onclick="sendCustomSMS()">
                            <i class="fas fa-paper-plane"></i> Send SMS
                        </button>
                        <div id="custom-sms-result" class="result-box" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> Verification Status</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary" onclick="checkStatus()">
                            <i class="fas fa-search"></i> Check Status
                        </button>
                        <div id="status-result" class="result-box" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        let currentStep = 1;
        let verificationPhone = null;

        // Test Twilio connection
        async function testConnection() {
            try {
                const response = await fetch('/api/phone/test-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const data = await response.json();
                displayResult('connection-result', data);
                
            } catch (error) {
                displayResult('connection-result', { error: error.message });
            }
        }

        // Send verification code
        async function sendVerificationCode() {
            const phoneNumber = document.getElementById('phone-number').value.trim();
            
            if (!phoneNumber || phoneNumber === '+1') {
                alert('Please enter a valid phone number');
                return;
            }

            const btn = document.getElementById('send-code-btn');
            btn.disabled = true;
            btn.innerHTML = '<div class="loading"></div> Sending...';

            try {
                const response = await fetch('/api/phone/send-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ phone_number: phoneNumber })
                });
                
                const data = await response.json();
                displayResult('send-result', data);
                
                if (data.success) {
                    verificationPhone = phoneNumber;
                    moveToStep(2);
                    document.getElementById('verify-btn').disabled = false;
                    document.getElementById('resend-btn').disabled = false;
                }
                
            } catch (error) {
                displayResult('send-result', { error: error.message });
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Verification Code';
            }
        }

        // Verify code
        async function verifyCode() {
            const code = document.getElementById('verification-code').value.trim();
            
            if (!code || code.length < 4) {
                alert('Please enter the verification code');
                return;
            }

            const btn = document.getElementById('verify-btn');
            btn.disabled = true;
            btn.innerHTML = '<div class="loading"></div> Verifying...';

            try {
                const response = await fetch('/api/phone/verify-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ 
                        phone_number: verificationPhone,
                        code: code 
                    })
                });
                
                const data = await response.json();
                displayResult('verify-result', data);
                
                if (data.success) {
                    moveToStep(3);
                    document.getElementById('verification-details').innerHTML = `
                        <strong>Phone:</strong> ${data.phone_number}<br>
                        <strong>Verified At:</strong> ${data.verified_at}<br>
                        <strong>Status:</strong> ${data.status}
                    `;
                }
                
            } catch (error) {
                displayResult('verify-result', { error: error.message });
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check"></i> Verify Code';
            }
        }

        // Resend code
        async function resendCode() {
            const btn = document.getElementById('resend-btn');
            btn.disabled = true;
            btn.innerHTML = '<div class="loading"></div> Resending...';

            try {
                const response = await fetch('/api/phone/resend-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const data = await response.json();
                displayResult('send-result', data);
                
            } catch (error) {
                displayResult('send-result', { error: error.message });
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-redo"></i> Resend Code';
            }
        }

        // Send custom SMS
        async function sendCustomSMS() {
            const phone = document.getElementById('custom-phone').value.trim();
            const message = document.getElementById('custom-message').value.trim();
            
            if (!phone || !message) {
                alert('Please enter both phone number and message');
                return;
            }

            try {
                const response = await fetch('/api/phone/send-sms', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ 
                        phone_number: phone,
                        message: message 
                    })
                });
                
                const data = await response.json();
                displayResult('custom-sms-result', data);
                
            } catch (error) {
                displayResult('custom-sms-result', { error: error.message });
            }
        }

        // Check verification status
        async function checkStatus() {
            try {
                const response = await fetch('/api/phone/status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ 
                        phone_number: verificationPhone 
                    })
                });
                
                const data = await response.json();
                displayResult('status-result', data);
                
            } catch (error) {
                displayResult('status-result', { error: error.message });
            }
        }

        // Reset verification flow
        function resetVerification() {
            currentStep = 1;
            verificationPhone = null;
            document.getElementById('phone-number').value = '+1';
            document.getElementById('verification-code').value = '';
            document.getElementById('verify-btn').disabled = true;
            document.getElementById('resend-btn').disabled = true;
            
            // Hide all result boxes
            document.querySelectorAll('.result-box').forEach(box => {
                box.style.display = 'none';
            });
            
            moveToStep(1);
        }

        // Move to specific step
        function moveToStep(step) {
            currentStep = step;
            
            // Update step classes
            for (let i = 1; i <= 3; i++) {
                const stepElement = document.getElementById(`step-${i}`);
                stepElement.classList.remove('active', 'completed');
                
                if (i < step) {
                    stepElement.classList.add('completed');
                } else if (i === step) {
                    stepElement.classList.add('active');
                }
            }
        }

        // Display result in formatted box
        function displayResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        // Auto-format phone number input
        document.getElementById('phone-number').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d+]/g, '');
            if (!value.startsWith('+')) {
                value = '+' + value;
            }
            e.target.value = value;
        });

        // Auto-format verification code input
        document.getElementById('verification-code').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^\d]/g, '');
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\PocketWatch\resources\views/test/phone-verification.blade.php ENDPATH**/ ?>