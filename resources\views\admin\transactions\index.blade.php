@extends('admin.layouts.app')

@push('styles')
<style>
    .bg-light-color {
        background-color: var(--light-color) !important;
    }

    .table-hover tbody tr:hover {
        background-color: var(--light-color) !important;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .pagination .page-link {
        color: var(--primary-color);
    }

    .pagination .page-link:hover {
        color: var(--dark-color);
    }
</style>
@endpush

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-exchange-alt me-2 text-primary"></i>Transactions
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}" class="text-decoration-none">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Transactions</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.transactions.index', array_merge(request()->all(), ['export' => 'csv'])) }}" class="btn btn-sm btn-outline-primary me-2">
                <i class="fas fa-download me-1"></i>Export CSV
            </a>
            <a href="#" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>New Transaction
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>Filters
            </h6>
            @if(request()->anyFilled(['search', 'user_id', 'type', 'start_date', 'end_date']))
                <a href="{{ route('admin.transactions.index') }}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear Filters
                </a>
            @endif
        </div>
        <div class="card-body">
            <form action="{{ route('admin.transactions.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="search" class="form-label">
                            <i class="fas fa-search me-1 text-secondary"></i>Search
                        </label>
                        <div class="input-group shadow-sm">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ request('search') }}" placeholder="Description, Amount, Category...">
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="user_id" class="form-label">
                            <i class="fas fa-user me-1 text-secondary"></i>User
                        </label>
                        <select class="form-select shadow-sm" id="user_id" name="user_id">
                            <option value="">All Users</option>
                            @foreach(\App\Models\User::where('is_admin', false)->orderBy('name')->get() as $user)
                                <option value="{{ $user->uuid }}" {{ request('user_id') == $user->uuid ? 'selected' : '' }}>
                                    {{ $user->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="type" class="form-label">
                            <i class="fas fa-exchange-alt me-1 text-secondary"></i>Type
                        </label>
                        <select class="form-select shadow-sm" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="income" {{ request('type') == 'income' ? 'selected' : '' }}>
                                <i class="fas fa-arrow-down"></i> Income
                            </option>
                            <option value="expense" {{ request('type') == 'expense' ? 'selected' : '' }}>
                                <i class="fas fa-arrow-up"></i> Expense
                            </option>
                            <option value="transfer" {{ request('type') == 'transfer' ? 'selected' : '' }}>
                                <i class="fas fa-exchange-alt"></i> Transfer
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="start_date" class="form-label">
                            <i class="far fa-calendar-alt me-1 text-secondary"></i>Start Date
                        </label>
                        <div class="input-group shadow-sm">
                            <span class="input-group-text bg-light">
                                <i class="far fa-calendar"></i>
                            </span>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request('start_date') }}">
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="end_date" class="form-label">
                            <i class="far fa-calendar-alt me-1 text-secondary"></i>End Date
                        </label>
                        <div class="input-group shadow-sm">
                            <span class="input-group-text bg-light">
                                <i class="far fa-calendar"></i>
                            </span>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request('end_date') }}">
                        </div>
                    </div>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100 shadow-sm">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">All Transactions</h6>
            <div>
                <span class="badge bg-primary rounded-pill">Total: {{ $transactions->total() }}</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-striped align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-center">#</th>
                            <th scope="col">User</th>
                            <th scope="col">Type</th>
                            <th scope="col">Amount</th>
                            <th scope="col">Bin</th>
                            <th scope="col">Category</th>
                            <th scope="col">Date</th>
                            <th scope="col">Status</th>
                            <th scope="col">Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($transactions as $transaction)
                            <tr>
                                <td class="text-center fw-bold">{{ $loop->iteration + ($transactions->perPage() * ($transactions->currentPage() - 1)) }}</td>
                                <td>
                                    <a href="{{ route('admin.users.show', $transaction->user->uuid) }}" class="text-decoration-none">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-light-color d-flex align-items-center justify-content-center me-2"
                                                 style="width: 32px; height: 32px; border-radius: 50%;">
                                                <span class="fw-bold text-primary">{{ substr($transaction->user->name, 0, 1) }}</span>
                                            </div>
                                            <span>{{ $transaction->user->name }}</span>
                                        </div>
                                    </a>
                                </td>
                                <td>
                                    @if($transaction->transaction_type == 'income')
                                        <span class="badge bg-success rounded-pill">
                                            <i class="fas fa-arrow-down me-1"></i>Income
                                        </span>
                                    @elseif($transaction->transaction_type == 'expense')
                                        <span class="badge bg-danger rounded-pill">
                                            <i class="fas fa-arrow-up me-1"></i>Expense
                                        </span>
                                    @else
                                        <span class="badge bg-info rounded-pill">
                                            <i class="fas fa-exchange-alt me-1"></i>Transfer
                                        </span>
                                    @endif
                                </td>
                                <td class="fw-bold {{ $transaction->transaction_type == 'income' ? 'text-success' : ($transaction->transaction_type == 'expense' ? 'text-danger' : 'text-info') }}">
                                    ${{ number_format($transaction->amount, 2) }}
                                </td>
                                <td>
                                    @if($transaction->bin)
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-box me-1 text-secondary"></i>
                                            <span>{{ $transaction->bin->name }}</span>
                                        </div>
                                        @if($transaction->sub_bin_id)
                                            <small class="text-muted d-block">
                                                <i class="fas fa-level-down-alt ms-2 me-1"></i>{{ $transaction->subBin->name }}
                                            </small>
                                        @endif
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    @if($transaction->category)
                                        <span class="badge bg-secondary rounded-pill">
                                            <i class="fas fa-tag me-1"></i>{{ $transaction->category }}
                                        </span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="far fa-calendar-alt me-2 text-secondary"></i>
                                        <span>{{ $transaction->transaction_date->format('M d, Y') }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success rounded-pill">
                                        <i class="fas fa-check-circle me-1"></i>{{ ucfirst($transaction->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="far fa-comment-alt me-2 text-secondary"></i>
                                        <span class="text-truncate" style="max-width: 150px;" title="{{ $transaction->description }}">
                                            {{ $transaction->description ?: 'N/A' }}
                                        </span>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No transactions found</h5>
                                        <p class="text-muted">Try adjusting your search filters</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="text-muted">
                    Showing {{ $transactions->firstItem() ?? 0 }} to {{ $transactions->lastItem() ?? 0 }} of {{ $transactions->total() }} entries
                </div>
                <div>
                    {{ $transactions->withQueryString()->links() }}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effect to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.cursor = 'pointer';
                this.classList.add('bg-light-color');
            });

            row.addEventListener('mouseleave', function() {
                this.classList.remove('bg-light-color');
            });

            // Optional: Add click event to show transaction details
            row.addEventListener('click', function() {
                const transactionId = this.querySelector('td:first-child').textContent.trim();
                // You can implement a modal or redirect to transaction details page
                // window.location.href = `/admin/transactions/${transactionId}`;
            });
        });

        // Date range validation
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');

        if (startDateInput && endDateInput) {
            startDateInput.addEventListener('change', function() {
                if (endDateInput.value && this.value > endDateInput.value) {
                    alert('Start date cannot be after end date');
                    this.value = '';
                }
            });

            endDateInput.addEventListener('change', function() {
                if (startDateInput.value && this.value < startDateInput.value) {
                    alert('End date cannot be before start date');
                    this.value = '';
                }
            });
        }
    });
</script>
@endpush
