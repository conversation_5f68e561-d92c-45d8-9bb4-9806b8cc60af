@extends('admin.layouts.app')

@section('title', 'Subscriptions')
@section('subtitle', 'Manage user subscriptions')

@section('breadcrumbs')
    <li class="breadcrumb-item">Financial</li>
@endsection

@section('actions')
    <div class="btn-group">
        <a href="{{ route('admin.subscriptions.create') }}" class="btn btn-success">
            <i class="fas fa-plus me-1"></i> Create Subscription
        </a>
        <a href="{{ route('admin.subscriptions.packages') }}" class="btn btn-info">
            <i class="fas fa-tags me-1"></i> Packages
        </a>
        <a href="{{ route('admin.subscriptions.dashboard') }}" class="btn btn-primary">
            <i class="fas fa-chart-pie me-1"></i> Dashboard
        </a>
    </div>
@endsection

@section('content')

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
            <button class="btn btn-sm btn-link text-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="true" aria-controls="filtersCollapse">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="card-body collapse show" id="filtersCollapse">
            <form action="{{ route('admin.subscriptions.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="user_id" class="form-label">User</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">All Users</option>
                        @foreach($users as $user)
                            <option value="{{ $user->uuid }}" {{ request('user_id') == $user->uuid ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        @foreach($statusOptions as $status)
                            <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                {{ ucfirst($status) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="tier" class="form-label">Tier</label>
                    <select class="form-select" id="tier" name="tier">
                        <option value="">All Tiers</option>
                        @foreach($tierOptions as $tier)
                            <option value="{{ $tier }}" {{ request('tier') == $tier ? 'selected' : '' }}>
                                {{ ucfirst($tier) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="billing_cycle" class="form-label">Billing Cycle</label>
                    <select class="form-select" id="billing_cycle" name="billing_cycle">
                        <option value="">All Cycles</option>
                        @foreach($billingCycleOptions as $cycle)
                            <option value="{{ $cycle }}" {{ request('billing_cycle') == $cycle ? 'selected' : '' }}>
                                {{ ucfirst($cycle) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" placeholder="Name or Stripe ID" value="{{ request('search') }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-12 mt-3">
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-1"></i> Apply Filters
                        </button>
                        <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Admin Note -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Note:</strong> Admin users do not require subscriptions. Only regular users are shown in this list.
    </div>

    <!-- Subscriptions Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">All Subscriptions</h6>
            <div>
                <span class="badge bg-primary">Total: {{ $subscriptions->total() }}</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Plan</th>
                            <th>Status</th>
                            <th>Price</th>
                            <th>Billing Cycle</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($subscriptions as $subscription)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2">
                                            {{ substr($subscription->user->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <a href="{{ route('admin.users.show', $subscription->user->uuid) }}" class="text-decoration-none">
                                                {{ $subscription->user->name }}
                                            </a>
                                            <div class="small text-muted">{{ $subscription->user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge {{ $subscription->subscription_tier == 'premium' ? 'bg-success' : 'bg-secondary' }}">
                                        {{ ucfirst($subscription->subscription_tier) }}
                                    </span>
                                </td>
                                <td>
                                    @php
                                        $statusColors = [
                                            'active' => 'success',
                                            'trialing' => 'info',
                                            'canceled' => 'danger',
                                            'incomplete' => 'warning',
                                            'past_due' => 'danger',
                                            'unpaid' => 'danger',
                                            'incomplete_expired' => 'danger',
                                            'paused' => 'warning',
                                            'pending' => 'secondary'
                                        ];
                                        $color = $statusColors[$subscription->stripe_status] ?? 'secondary';
                                    @endphp
                                    <span class="badge bg-{{ $color }}">
                                        {{ ucfirst($subscription->stripe_status) }}
                                    </span>

                                    @if($subscription->onTrial())
                                        <span class="badge badge-soft-info ms-1">Trial</span>
                                    @endif
                                </td>
                                <td>${{ number_format($subscription->price, 2) }}</td>
                                <td>{{ ucfirst($subscription->billing_cycle) }}</td>
                                <td>{{ $subscription->created_at->format('M d, Y') }}</td>
                                <td>
                                    @if($subscription->ends_at)
                                        <span class="{{ $subscription->ends_at->isPast() ? 'text-danger' : 'text-warning' }}">
                                            {{ $subscription->ends_at->format('M d, Y') }}
                                        </span>
                                    @elseif($subscription->stripe_status == 'canceled')
                                        <span class="text-muted">Pending cancellation</span>
                                    @elseif($subscription->onTrial())
                                        <span class="text-info">
                                            Trial ends: {{ $subscription->trial_ends_at->format('M d, Y') }}
                                        </span>
                                    @else
                                        <span class="text-success">Active</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton{{ $subscription->uuid }}" data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="dropdownMenuButton{{ $subscription->uuid }}">
                                            <li>
                                                <a class="dropdown-item" href="{{ route('admin.subscriptions.show', $subscription->uuid) }}">
                                                    <i class="fas fa-eye me-2 text-primary"></i> View Details
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ route('admin.subscriptions.edit', $subscription->uuid) }}">
                                                    <i class="fas fa-edit me-2 text-info"></i> Edit
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>

                                            @if($subscription->stripe_status === 'active' || $subscription->stripe_status === 'trialing')
                                                <li>
                                                    <button class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#cancelModal{{ $subscription->uuid }}">
                                                        <i class="fas fa-ban me-2 text-danger"></i> Cancel
                                                    </button>
                                                </li>
                                                <li>
                                                    <button class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#pauseModal{{ $subscription->uuid }}">
                                                        <i class="fas fa-pause me-2 text-warning"></i> Pause
                                                    </button>
                                                </li>
                                            @endif

                                            @if($subscription->stripe_status === 'canceled' && $subscription->ends_at && $subscription->ends_at->isFuture())
                                                <li>
                                                    <button class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#resumeModal{{ $subscription->uuid }}">
                                                        <i class="fas fa-play me-2 text-success"></i> Resume
                                                    </button>
                                                </li>
                                            @endif

                                            @if($subscription->stripe_status === 'paused')
                                                <li>
                                                    <button class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#unpauseModal{{ $subscription->uuid }}">
                                                        <i class="fas fa-play me-2 text-success"></i> Unpause
                                                    </button>
                                                </li>
                                            @endif

                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item text-danger" type="button" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $subscription->uuid }}">
                                                    <i class="fas fa-trash-alt me-2"></i> Delete
                                                </button>
                                            </li>
                                        </ul>
                                    </div>

                                    <!-- Cancel Modal -->
                                    <div class="modal fade" id="cancelModal{{ $subscription->uuid }}" tabindex="-1" aria-labelledby="cancelModalLabel{{ $subscription->uuid }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <form action="{{ route('admin.subscriptions.cancel', $subscription->uuid) }}" method="POST">
                                                    @csrf
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="cancelModalLabel{{ $subscription->uuid }}">Cancel Subscription</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to cancel this subscription?</p>
                                                        <p class="text-muted">The subscription will remain active until the end of the current billing period.</p>

                                                        <div class="mb-3">
                                                            <label for="cancel_reason" class="form-label">Cancellation Reason</label>
                                                            <input type="text" class="form-control" id="cancel_reason" name="cancel_reason" placeholder="Optional">
                                                        </div>

                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="checkbox" id="send_notification{{ $subscription->uuid }}" name="send_notification" value="1" checked>
                                                            <label class="form-check-label" for="send_notification{{ $subscription->uuid }}">
                                                                Send notification to user
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-danger">Cancel Subscription</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Resume Modal -->
                                    <div class="modal fade" id="resumeModal{{ $subscription->uuid }}" tabindex="-1" aria-labelledby="resumeModalLabel{{ $subscription->uuid }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <form action="{{ route('admin.subscriptions.resume', $subscription->uuid) }}" method="POST">
                                                    @csrf
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="resumeModalLabel{{ $subscription->uuid }}">Resume Subscription</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to resume this subscription?</p>
                                                        <p class="text-muted">The subscription will continue with the current billing cycle.</p>

                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="checkbox" id="send_notification_resume{{ $subscription->uuid }}" name="send_notification" value="1" checked>
                                                            <label class="form-check-label" for="send_notification_resume{{ $subscription->uuid }}">
                                                                Send notification to user
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-success">Resume Subscription</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Pause Modal -->
                                    <div class="modal fade" id="pauseModal{{ $subscription->uuid }}" tabindex="-1" aria-labelledby="pauseModalLabel{{ $subscription->uuid }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <form action="{{ route('admin.subscriptions.pause', $subscription->uuid) }}" method="POST">
                                                    @csrf
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="pauseModalLabel{{ $subscription->uuid }}">Pause Subscription</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to pause this subscription?</p>
                                                        <p class="text-muted">The subscription will be paused immediately and no further charges will be made until unpaused.</p>

                                                        <div class="mb-3">
                                                            <label for="pause_reason" class="form-label">Pause Reason</label>
                                                            <input type="text" class="form-control" id="pause_reason" name="pause_reason" placeholder="Optional">
                                                        </div>

                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="checkbox" id="send_notification_pause{{ $subscription->uuid }}" name="send_notification" value="1" checked>
                                                            <label class="form-check-label" for="send_notification_pause{{ $subscription->uuid }}">
                                                                Send notification to user
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-warning">Pause Subscription</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Unpause Modal -->
                                    <div class="modal fade" id="unpauseModal{{ $subscription->uuid }}" tabindex="-1" aria-labelledby="unpauseModalLabel{{ $subscription->uuid }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <form action="{{ route('admin.subscriptions.unpause', $subscription->uuid) }}" method="POST">
                                                    @csrf
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="unpauseModalLabel{{ $subscription->uuid }}">Unpause Subscription</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to unpause this subscription?</p>
                                                        <p class="text-muted">The subscription will be reactivated immediately and billing will resume.</p>

                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="checkbox" id="send_notification_unpause{{ $subscription->uuid }}" name="send_notification" value="1" checked>
                                                            <label class="form-check-label" for="send_notification_unpause{{ $subscription->uuid }}">
                                                                Send notification to user
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-success">Unpause Subscription</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal{{ $subscription->uuid }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $subscription->uuid }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <form action="{{ route('admin.subscriptions.destroy', $subscription->uuid) }}" method="POST">
                                                    @csrf
                                                    @method('DELETE')
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel{{ $subscription->uuid }}">Delete Subscription</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="alert alert-danger">
                                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                                            <strong>Warning:</strong> This action cannot be undone.
                                                        </div>
                                                        <p>Are you sure you want to permanently delete this subscription?</p>
                                                        <p class="text-muted">This will cancel the subscription in Stripe and remove it from the database.</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-danger">Delete Permanently</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No subscriptions found</h5>
                                        <p class="text-muted">Try adjusting your filters or create a new subscription</p>
                                        <a href="{{ route('admin.subscriptions.create') }}" class="btn btn-primary mt-2">
                                            <i class="fas fa-plus me-1"></i> Create Subscription
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="text-muted">
                    Showing {{ $subscriptions->firstItem() ?? 0 }} to {{ $subscriptions->lastItem() ?? 0 }} of {{ $subscriptions->total() }} subscriptions
                </div>
                <div>
                    {{ $subscriptions->withQueryString()->links() }}
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Stats -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Monthly Recurring Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($subscriptions->where('stripe_status', 'active')->sum('price'), 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Active Subscriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $subscriptions->where('stripe_status', 'active')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Premium Subscribers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $subscriptions->where('subscription_tier', 'premium')->where('stripe_status', 'active')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Trial Subscriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $subscriptions->where('stripe_status', 'trialing')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Initialize select2 for user dropdown
    $(document).ready(function() {
        // Add confirmation for delete actions
        $('.delete-subscription').on('click', function(e) {
            if (!confirm('Are you sure you want to delete this subscription? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
</script>
@endpush
