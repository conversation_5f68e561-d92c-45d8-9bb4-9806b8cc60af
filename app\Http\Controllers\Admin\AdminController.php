<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\IdObfuscator;
use App\Http\Controllers\Controller;
use App\Models\Bin;
use App\Models\CryptoWallet;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display the admin dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        // Get counts for dashboard (excluding admin users)
        $totalUsers = User::where('is_admin', false)->count();
        $activeUsers = User::where('is_admin', false)->where('is_active', true)->count();
        $premiumUsers = User::where('is_admin', false)->where('subscription_tier', 'premium')->count();
        $totalTransactions = Transaction::count();
        $totalBins = Bin::count();
        $totalWallets = CryptoWallet::count();

        // Get recent transactions with pagination
        $recentTransactions = Transaction::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Get revenue data
        $revenue = Subscription::where('stripe_status', 'active')
            ->sum('price');

        // Get monthly signups (excluding admin users)
        $monthlySignups = User::select(
            DB::raw('MONTH(created_at) as month'),
            DB::raw('COUNT(*) as count')
        )
            ->where('is_admin', false)
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        // Fill in missing months with zero
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($monthlySignups[$i])) {
                $monthlySignups[$i] = 0;
            }
        }
        ksort($monthlySignups);

        return view('admin.dashboard', compact(
            'totalUsers',
            'activeUsers',
            'premiumUsers',
            'totalTransactions',
            'totalBins',
            'totalWallets',
            'recentTransactions',
            'revenue',
            'monthlySignups'
        ));
    }

    /**
     * Display a list of users.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Exclude admin users
        $query->where('is_admin', false);

        // Filter by status if provided
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by subscription tier if provided
        if ($request->filled('tier')) {
            $query->where('subscription_tier', $request->tier);
        }

        // Search by name, email, or phone number if provided
        if ($request->filled('search')) {
            $search = trim($request->search);
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_number', 'like', "%{$search}%")
                    ->orWhere('country_code', 'like', "%{$search}%");
            });
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Get per page value (default 5, allow 10, 20, 50, 100)
        $perPage = $request->get('per_page', 5);
        $allowedPerPage = [5, 10, 20, 50, 100];
        if (!in_array($perPage, $allowedPerPage)) {
            $perPage = 5;
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $allowedSortFields = ['created_at', 'name', 'email', 'last_login_at'];
        $allowedSortOrders = ['asc', 'desc'];

        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }

        if (!in_array($sortOrder, $allowedSortOrders)) {
            $sortOrder = 'desc';
        }

        $query->orderBy($sortBy, $sortOrder);

        $users = $query->paginate($perPage)->withQueryString();

        return view('admin.users.index', compact('users'));
    }

    /**
     * Display a specific user.
     *
     * @param  string  $uuid
     * @return \Illuminate\View\View
     */
    public function showUser($uuid)
    {
        // Try to find by UUID first
        $user = User::with(['bins', 'transactions', 'subscriptions', 'cryptoWallets'])
            ->where('uuid', $uuid)
            ->first();

        // If not found by UUID, try to deobfuscate the ID
        if (!$user) {
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'user');
            if ($deobfuscatedId) {
                $user = User::with(['bins', 'transactions', 'subscriptions', 'cryptoWallets'])
                    ->find($deobfuscatedId);
            }
        }

        // If still not found, try with the original ID (for backward compatibility)
        if (!$user) {
            $user = User::with(['bins', 'transactions', 'subscriptions', 'cryptoWallets'])
                ->find($uuid);
        }

        if (!$user) {
            abort(404, 'User not found');
        }

        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for creating a new user.
     *
     * @return \Illuminate\View\View
     */
    public function createUser()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeUser(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'country_code' => 'nullable|string|max:5',
            'phone_number' => 'nullable|string|max:20',
            'is_admin' => 'boolean',
            'is_active' => 'boolean',
            'subscription_tier' => 'required|in:trial,base,premium',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'country_code' => $validated['country_code'] ?? null,
            'phone_number' => $validated['phone_number'] ?? null,
            'is_admin' => $request->has('is_admin'),
            'is_active' => $request->has('is_active'),
            'subscription_tier' => $validated['subscription_tier'],
        ]);

        return redirect()->route('admin.users.show', $user->uuid)
            ->with('success', 'User created successfully');
    }

    /**
     * Toggle user active status.
     *
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleUserStatus($uuid)
    {
        $user = $this->findUserBySecureId($uuid);
        $user->is_active = !$user->is_active;
        $user->save();

        return redirect()->back()
            ->with('success', 'User status updated successfully');
    }

    /**
     * Block a user.
     *
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function blockUser($uuid)
    {
        $user = $this->findUserBySecureId($uuid);
        $user->is_active = false;
        $user->save();

        return redirect()->back()
            ->with('success', 'User blocked successfully');
    }

    /**
     * Unblock a user.
     *
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function unblockUser($uuid)
    {
        $user = $this->findUserBySecureId($uuid);
        $user->is_active = true;
        $user->save();

        return redirect()->back()
            ->with('success', 'User unblocked successfully');
    }

    /**
     * Ban a user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function banUser(Request $request, $uuid)
    {
        $user = $this->findUserBySecureId($uuid);
        $user->ban($request->input('reason'));

        return redirect()->back()
            ->with('success', 'User banned successfully');
    }

    /**
     * Unban a user.
     *
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function unbanUser($uuid)
    {
        $user = $this->findUserBySecureId($uuid);
        $user->unban();

        return redirect()->back()
            ->with('success', 'User unbanned successfully');
    }

    /**
     * Find a user by secure ID (UUID or obfuscated ID).
     *
     * @param  string  $uuid
     * @return \App\Models\User
     */
    protected function findUserBySecureId($uuid)
    {
        // Try to find by UUID first
        $user = User::where('uuid', $uuid)->first();

        // If not found by UUID, try to deobfuscate the ID
        if (!$user) {
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'user');
            if ($deobfuscatedId) {
                $user = User::find($deobfuscatedId);
            }
        }

        // If still not found, try with the original ID (for backward compatibility)
        if (!$user) {
            $user = User::find($uuid);
        }

        if (!$user) {
            abort(404, 'User not found');
        }

        return $user;
    }

    /**
     * Delete a user and all associated data.
     *
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyUser($uuid)
    {
        $user = $this->findUserBySecureId($uuid);

        // Prevent deleting yourself
        if (Auth::id() === $user->id) {
            return redirect()->back()
                ->with('error', 'You cannot delete your own account');
        }

        try {
            // Delete user and all related data
            $user->deleteWithRelated();

            return redirect()->route('admin.users.index')
                ->with('success', 'User and all associated data deleted successfully');
        } catch (\Exception $e) {
            Log::error('Failed to delete user: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Failed to delete user. Please try again or contact support.');
        }
    }

    /**
     * Display a list of transactions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function transactions(Request $request)
    {
        $query = Transaction::with(['user', 'bin', 'subBin']);

        // Filter by user if provided
        if ($request->has('user_id') && $request->user_id) {
            $user = \App\Models\User::where('uuid', $request->user_id)->first();
            if ($user) {
                $query->where('user_id', $user->id);
            } else {
                // Try to deobfuscate or fallback to original ID for backward compatibility
                $deobfuscatedId = \App\Helpers\IdObfuscator::deobfuscate($request->user_id, 'user');
                if ($deobfuscatedId) {
                    $query->where('user_id', $deobfuscatedId);
                } else {
                    $query->where('user_id', $request->user_id);
                }
            }
        }

        // Filter by type if provided
        if ($request->has('type') && $request->type) {
            $query->where('transaction_type', $request->type);
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->whereDate('transaction_date', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->whereDate('transaction_date', '<=', $request->end_date);
        }

        // Search by description, amount, or category
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('amount', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Sort by date (default: newest first)
        $query->orderBy('transaction_date', 'desc');

        // Handle export request
        if ($request->has('export') && $request->export === 'csv') {
            return $this->exportTransactionsToCSV($query);
        }

        $transactions = $query->paginate(20);

        return view('admin.transactions.index', compact('transactions'));
    }

    /**
     * Display a list of bins.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function bins(Request $request)
    {
        $query = Bin::with(['user', 'subBins' => function($q) {
            $q->whereNull('parent_sub_bin_id')->with('descendants');
        }]);

        // Filter by user if provided
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Search by name if provided
        if ($request->filled('search')) {
            $query->where('name', 'like', "%{$request->search}%");
        }

        // Handle export request
        if ($request->has('export')) {
            // If specific bin ID is provided, export only that bin
            if ($request->has('bin_id')) {
                $query->where('id', $request->bin_id);
            }

            if ($request->export === 'csv') {
                return $this->exportBinsToCSV($query);
            } elseif ($request->export === 'pdf') {
                return $this->exportBinsToPDF($query);
            }
        }

        // Get per page value (default 5, allow 5, 10, 20, 50, 100)
        $perPage = $request->get('per_page', 5);
        $allowedPerPage = [5, 10, 20, 50, 100];
        if (!in_array($perPage, $allowedPerPage)) {
            $perPage = 5;
        }

        $bins = $query->orderBy('created_at', 'desc')->paginate($perPage)->withQueryString();

        return view('admin.bins.index', compact('bins'));
    }

    /**
     * Show bin details.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showBin(Request $request, $id)
    {
        $bin = Bin::with(['user', 'subBins.transactions', 'subBins.parentSubBin'])->findOrFail($id);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'bin' => [
                    'id' => $bin->id,
                    'name' => $bin->name,
                    'type' => $bin->type,
                    'description' => $bin->description,
                    'current_amount' => $bin->current_amount,
                    'threshold_min' => $bin->threshold_min,
                    'threshold_max' => $bin->threshold_max,
                    'currency' => $bin->currency,
                    'is_active' => $bin->is_active,
                    'created_at' => $bin->created_at->format('M d, Y \a\t g:i A'),
                    'updated_at' => $bin->updated_at->format('M d, Y \a\t g:i A'),
                    'user' => [
                        'name' => $bin->user->name,
                        'email' => $bin->user->email,
                    ],
                    'sub_bins_count' => $bin->subBins->count(),
                    'total_transactions' => $bin->subBins->sum(function($subBin) {
                        return $subBin->transactions->count();
                    })
                ]
            ]);
        }

        return redirect()->route('admin.bins.index');
    }

    /**
     * Display a list of crypto wallets.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function cryptoWallets(Request $request)
    {
        $query = CryptoWallet::with('user');

        // Filter by user if provided
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by blockchain network if provided
        if ($request->has('network')) {
            $query->where('blockchain_network', $request->network);
        }

        $wallets = $query->paginate(15);

        return view('admin.crypto-wallets.index', compact('wallets'));
    }

    /**
     * Display a list of subscriptions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function subscriptions(Request $request)
    {
        $query = Subscription::with('user');

        // Filter by user if provided
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('stripe_status', $request->status);
        }

        // Filter by tier if provided
        if ($request->has('tier')) {
            $query->where('subscription_tier', $request->tier);
        }

        $subscriptions = $query->paginate(15);

        return view('admin.subscriptions.index', compact('subscriptions'));
    }

    /**
     * Export transactions to CSV file.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    private function exportTransactionsToCSV($query)
    {
        $fileName = 'transactions_' . date('Y-m-d_His') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $columns = [
            'ID', 'User', 'Email', 'Type', 'Amount', 'Bin', 'Sub-Bin',
            'Category', 'Date', 'Status', 'Description', 'Created At'
        ];

        $callback = function() use ($query, $columns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $columns);

            // Get all transactions without pagination
            $transactions = $query->get();

            foreach ($transactions as $transaction) {
                $row = [
                    $transaction->id,
                    $transaction->user->name,
                    $transaction->user->email,
                    ucfirst($transaction->transaction_type),
                    number_format($transaction->amount, 2),
                    $transaction->bin ? $transaction->bin->name : 'N/A',
                    $transaction->subBin ? $transaction->subBin->name : 'N/A',
                    $transaction->category ?: 'N/A',
                    $transaction->transaction_date->format('Y-m-d'),
                    ucfirst($transaction->status),
                    $transaction->description ?: 'N/A',
                    $transaction->created_at->format('Y-m-d H:i:s')
                ];

                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export bins to CSV file.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    private function exportBinsToCSV($query)
    {
        $fileName = 'bins_financial_activity_' . date('Y-m-d_His') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $columns = [
            'Bin ID', 'Bin Name', 'User Name', 'User Email', 'Bin Type', 'Current Amount',
            'Threshold Min', 'Threshold Max', 'Currency', 'Status', 'Sub-Bin ID', 'Sub-Bin Name',
            'Sub-Bin Type', 'Sub-Bin Amount', 'Sub-Bin Depth', 'Sub-Bin Path', 'Sub-Bin Parent',
            'Transaction Count', 'Created Date'
        ];

        $callback = function() use ($query, $columns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $columns);

            // Get all bins with their sub-bins and transactions
            $bins = $query->with(['subBins.transactions', 'subBins.parentSubBin'])->get();

            foreach ($bins as $bin) {
                // If bin has no sub-bins, export just the bin
                if ($bin->subBins->isEmpty()) {
                    $row = [
                        $bin->id,
                        $bin->name,
                        $bin->user->name,
                        $bin->user->email,
                        ucfirst($bin->type),
                        number_format($bin->current_amount, 2),
                        number_format($bin->threshold_min, 2),
                        $bin->threshold_max ? number_format($bin->threshold_max, 2) : 'Unlimited',
                        $bin->currency,
                        $bin->is_active ? 'Active' : 'Inactive',
                        'N/A', // Sub-Bin ID
                        'N/A', // Sub-Bin Name
                        'N/A', // Sub-Bin Type
                        'N/A', // Sub-Bin Amount
                        'N/A', // Sub-Bin Depth
                        'N/A', // Sub-Bin Path
                        'N/A', // Sub-Bin Parent
                        0, // Transaction Count
                        $bin->created_at->format('Y-m-d H:i:s')
                    ];
                    fputcsv($file, $row);
                } else {
                    // Export bin with each sub-bin as separate rows
                    $this->exportSubBinsRecursively($file, $bin, $bin->subBins);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Recursively export sub-bins to CSV.
     */
    private function exportSubBinsRecursively($file, $bin, $subBins)
    {
        foreach ($subBins as $subBin) {
            $row = [
                $bin->id,
                $bin->name,
                $bin->user->name,
                $bin->user->email,
                ucfirst($bin->type),
                number_format($bin->current_amount, 2),
                number_format($bin->threshold_min, 2),
                $bin->threshold_max ? number_format($bin->threshold_max, 2) : 'Unlimited',
                $bin->currency,
                $bin->is_active ? 'Active' : 'Inactive',
                $subBin->id,
                $subBin->name,
                ucfirst($subBin->type),
                number_format($subBin->current_amount, 2),
                $subBin->depth_level,
                $subBin->path ?: 'N/A',
                $subBin->parentSubBin ? $subBin->parentSubBin->name : 'Direct Child',
                $subBin->transactions->count(),
                $subBin->created_at->format('Y-m-d H:i:s')
            ];
            fputcsv($file, $row);

            // Recursively export children
            if ($subBin->childSubBins && $subBin->childSubBins->isNotEmpty()) {
                $this->exportSubBinsRecursively($file, $bin, $subBin->childSubBins);
            }
        }
    }

    /**
     * Export bins to PDF file.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Http\Response
     */
    private function exportBinsToPDF($query)
    {
        $bins = $query->with(['subBins.transactions', 'subBins.parentSubBin'])->get();

        $html = view('admin.exports.bins-pdf', compact('bins'))->render();

        // For now, return HTML version. You can integrate with libraries like DomPDF or wkhtmltopdf
        return response($html, 200, [
            'Content-Type' => 'text/html',
            'Content-Disposition' => 'attachment; filename="bins_financial_activity_' . date('Y-m-d_His') . '.html"'
        ]);
    }

    /**
     * Search across the admin panel.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function search(Request $request)
    {
        $query = $request->input('query');

        if (empty($query)) {
            return redirect()->route('admin.dashboard')
                ->with('info', 'Please enter a search term');
        }

        // Search users (excluding admins)
        $users = User::where('is_admin', false)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('phone_number', 'like', "%{$query}%");
            })
            ->limit(5)
            ->get();

        // Search transactions
        $transactions = Transaction::where('description', 'like', "%{$query}%")
            ->orWhere('amount', 'like', "%{$query}%")
            ->orWhere('transaction_type', 'like', "%{$query}%")
            ->with('user')
            ->limit(5)
            ->get();

        // Search bins
        $bins = Bin::where('name', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->with('user')
            ->limit(5)
            ->get();

        // Search subscriptions
        $subscriptions = Subscription::where('name', 'like', "%{$query}%")
            ->orWhere('subscription_tier', 'like', "%{$query}%")
            ->orWhere('billing_cycle', 'like', "%{$query}%")
            ->with('user')
            ->limit(5)
            ->get();

        return view('admin.search', compact('query', 'users', 'transactions', 'bins', 'subscriptions'));
    }
}
