<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MoralisService
{
    /**
     * The Moralis API base URL.
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * The Moralis API key.
     *
     * @var string
     */
    protected $apiKey;

    /**
     * Create a new Moralis service instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->baseUrl = config('services.moralis.url', 'https://deep-index.moralis.io/api/v2');
        $this->apiKey = config('services.moralis.key');
    }

    /**
     * Get token balances for the given wallet address.
     *
     * @param string $walletAddress
     * @param string $chain
     * @return array|null
     */
    public function getTokenBalances(string $walletAddress, string $chain = 'eth'): ?array
    {
        try {
            $response = Http::withHeaders([
                'X-API-Key' => $this->apiKey,
            ])->get("{$this->baseUrl}/{$walletAddress}/erc20", [
                'chain' => $this->getChainId($chain),
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Failed to get token balances from Moralis', [
                'wallet_address' => $walletAddress,
                'chain' => $chain,
                'error' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception when getting token balances from Moralis', [
                'wallet_address' => $walletAddress,
                'chain' => $chain,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get transactions for the given wallet address.
     *
     * @param string $walletAddress
     * @param string $chain
     * @param int $limit
     * @return array|null
     */
    public function getTransactions(string $walletAddress, string $chain = 'eth', int $limit = 10): ?array
    {
        try {
            $response = Http::withHeaders([
                'X-API-Key' => $this->apiKey,
            ])->get("{$this->baseUrl}/{$walletAddress}", [
                'chain' => $this->getChainId($chain),
                'limit' => $limit,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Failed to get transactions from Moralis', [
                'wallet_address' => $walletAddress,
                'chain' => $chain,
                'error' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception when getting transactions from Moralis', [
                'wallet_address' => $walletAddress,
                'chain' => $chain,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get the chain ID for the given blockchain network.
     *
     * @param string $network
     * @return string
     */
    public function getChainId(string $network): string
    {
        return match ($network) {
            'ethereum' => 'eth',
            'binance' => 'bsc',
            'polygon' => 'polygon',
            'avalanche' => 'avalanche',
            default => 'eth',
        };
    }
}
