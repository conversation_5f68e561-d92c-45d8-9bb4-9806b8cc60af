<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'email_login',
        'email_profile_updates',
        'email_password_changes',
        'email_bin_operations',
        'email_transaction_operations',
        'email_subscription_updates',
        'email_threshold_alerts',
        'email_renewal_reminders',
        'email_marketing',
        'email_registration',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_login' => 'boolean',
        'email_profile_updates' => 'boolean',
        'email_password_changes' => 'boolean',
        'email_bin_operations' => 'boolean',
        'email_transaction_operations' => 'boolean',
        'email_subscription_updates' => 'boolean',
        'email_threshold_alerts' => 'boolean',
        'email_renewal_reminders' => 'boolean',
        'email_marketing' => 'boolean',
        'email_registration' => 'boolean',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'email_login' => true,
        'email_profile_updates' => true,
        'email_password_changes' => true,
        'email_bin_operations' => true,
        'email_transaction_operations' => true,
        'email_subscription_updates' => true,
        'email_threshold_alerts' => true,
        'email_renewal_reminders' => true,
        'email_marketing' => false,
        'email_registration' => true,
    ];

    /**
     * Get the user that owns the notification settings.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
