<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch - Stripe Payment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .payment-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .payment-details {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            text-align: left;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.1rem;
            color: #667eea;
        }
        
        .detail-label {
            color: #666;
        }
        
        .detail-value {
            font-weight: 500;
            color: #333;
        }
        
        .stripe-button {
            background: #635bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 20px 0;
            transition: background 0.3s ease;
        }
        
        .stripe-button:hover {
            background: #5a54d9;
        }
        
        .cancel-link {
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
            margin-top: 15px;
            display: inline-block;
        }
        
        .cancel-link:hover {
            color: #333;
            text-decoration: underline;
        }
        
        .security-note {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #155724;
        }
        
        .trial-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-size: 0.9rem;
            color: #856404;
        }
        
        .loading {
            display: none;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="logo">🎯 PocketWatch</div>
        <h2>Complete Your Purchase</h2>
        <p style="color: #666; margin: 15px 0;">You're about to purchase a subscription plan</p>
        
        <div class="payment-details">
            <div class="detail-row">
                <span class="detail-label">Plan:</span>
                <span class="detail-value">{{ $planName }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Billing:</span>
                <span class="detail-value">Monthly</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Trial Period:</span>
                <span class="detail-value">7 Days Free</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">User ID:</span>
                <span class="detail-value">#{{ $userId }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Plan ID:</span>
                <span class="detail-value">{{ $planId }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Total Amount:</span>
                <span class="detail-value">${{ number_format($price, 2) }} {{ $currency }}</span>
            </div>
        </div>
        
        <div class="trial-info">
            <strong>🎉 7-Day Free Trial</strong><br>
            Start your free trial today. You won't be charged until {{ date('M j, Y', strtotime('+7 days')) }}.
            Cancel anytime during the trial period.
        </div>
        
        <!-- Stripe Payment Form -->
        <form action="https://checkout.stripe.com/pay" method="POST" id="stripe-form">
            <input type="hidden" name="amount" value="{{ $price * 100 }}">
            <input type="hidden" name="currency" value="{{ strtolower($currency) }}">
            <input type="hidden" name="description" value="{{ $planName }} - Monthly Subscription">
            <input type="hidden" name="success_url" value="{{ route('stripe.success', ['user_id' => $userId, 'plan_id' => $planId, 'price' => $price]) }}">
            <input type="hidden" name="cancel_url" value="{{ route('stripe.cancel', ['user_id' => $userId, 'plan_id' => $planId]) }}">
            <input type="hidden" name="customer_email" value="{{ $userEmail ?? '' }}">
            <input type="hidden" name="billing_address_collection" value="auto">
            <input type="hidden" name="payment_method_types[]" value="card">
            
            <!-- Custom metadata -->
            <input type="hidden" name="metadata[user_id]" value="{{ $userId }}">
            <input type="hidden" name="metadata[plan_id]" value="{{ $planId }}">
            <input type="hidden" name="metadata[plan_name]" value="{{ $planName }}">
            <input type="hidden" name="metadata[trial_days]" value="7">
            
            <button type="submit" class="stripe-button" onclick="showLoading()">
                🔒 Pay with Stripe - ${{ number_format($price, 2) }}
            </button>
        </form>
        
        <div class="loading" id="loading">
            Processing payment... Please wait.
        </div>
        
        <a href="{{ route('stripe.cancel', ['user_id' => $userId, 'plan_id' => $planId]) }}" class="cancel-link">
            Cancel and return to packages
        </a>
        
        <div class="security-note">
            <strong>🔒 Secure Payment</strong><br>
            Your payment is processed securely by Stripe. We never store your credit card information.
        </div>
    </div>

    <script>
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.querySelector('.stripe-button').style.opacity = '0.7';
            document.querySelector('.stripe-button').disabled = true;
        }
        
        // Auto-submit form after 3 seconds for demo purposes
        // Remove this in production
        setTimeout(function() {
            console.log('Payment form ready');
            console.log('User ID: {{ $userId }}');
            console.log('Plan ID: {{ $planId }}');
            console.log('Price: ${{ $price }}');
        }, 1000);
        
        // Handle form submission
        document.getElementById('stripe-form').addEventListener('submit', function(e) {
            // You can add additional validation here
            console.log('Submitting payment for:', {
                userId: '{{ $userId }}',
                planId: '{{ $planId }}',
                price: '{{ $price }}',
                currency: '{{ $currency }}'
            });
        });
    </script>
</body>
</html>
