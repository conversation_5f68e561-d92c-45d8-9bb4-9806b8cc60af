#!/bin/bash

# Twilio SMS Integration Setup Script for PocketWatch
# This script helps set up Twilio SMS integration

echo "🚀 Setting up Twilio SMS Integration for PocketWatch..."
echo "=================================================="

# Check if composer is available
if ! command -v composer &> /dev/null; then
    echo "❌ Composer is not installed. Please install Composer first."
    exit 1
fi

# Check if we're in a Laravel project
if [ ! -f "artisan" ]; then
    echo "❌ This doesn't appear to be a Laravel project. Please run this script from your Laravel root directory."
    exit 1
fi

echo "📦 Installing Twilio SDK..."
composer require twilio/sdk

if [ $? -eq 0 ]; then
    echo "✅ Twilio SDK installed successfully!"
else
    echo "❌ Failed to install Twilio SDK. Please check your composer setup."
    exit 1
fi

echo ""
echo "🔧 Running database migrations..."
php artisan migrate

if [ $? -eq 0 ]; then
    echo "✅ Database migrations completed successfully!"
else
    echo "❌ Database migrations failed. Please check your database connection."
    exit 1
fi

echo ""
echo "🔑 Environment Configuration"
echo "============================"
echo ""
echo "Please add the following to your .env file:"
echo ""
echo "# Twilio Configuration"
echo "TWILIO_SID=your_twilio_account_sid"
echo "TWILIO_TOKEN=your_twilio_auth_token"
echo "TWILIO_FROM=your_twilio_phone_number"
echo "TWILIO_VERIFY_SID=your_twilio_verify_service_sid  # Optional but recommended"
echo ""

echo "📋 Next Steps:"
echo "=============="
echo "1. Sign up at https://console.twilio.com/"
echo "2. Get your Account SID and Auth Token"
echo "3. Buy a phone number from Twilio"
echo "4. (Optional) Create a Verify Service for enhanced security"
echo "5. Update your .env file with the credentials above"
echo "6. Test your setup at: http://your-domain.com/test/phone"
echo ""

echo "🧪 Available Test Pages:"
echo "========================"
echo "• Phone Verification: http://your-domain.com/test/phone"
echo "• Twilio Integration: http://your-domain.com/test/twilio"
echo ""

echo "📚 Documentation:"
echo "=================="
echo "• Setup Guide: docs/TWILIO_SETUP_GUIDE.md"
echo "• API Documentation: Available in your application"
echo ""

echo "✅ Twilio SMS integration setup completed!"
echo "🎉 You're ready to start sending SMS messages!"
echo ""
echo "💡 Pro Tip: Use the test pages to verify everything is working before going live."
