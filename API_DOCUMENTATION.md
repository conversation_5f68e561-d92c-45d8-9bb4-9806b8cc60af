# PocketWatch API Documentation

This document provides detailed information about all API endpoints available in the PocketWatch application. Use this as a reference when integrating with the Flutter mobile app.

## Base URL

```
https://your-domain.com/api
```

## Authentication

Most endpoints require authentication using Laravel Sanctum. Include the token in the Authorization header:

```
Authorization: Bearer {your_token}
```

## Response Format

All responses are in JSON format. Successful responses typically include:

```json
{
  "message": "Success message",
  "data": { ... }
}
```

Error responses include:

```json
{
  "message": "Error message",
  "errors": { ... }
}
```

## API Endpoints

### Authentication

#### Register

- **URL**: `/register`
- **Method**: `POST`
- **Name**: `api.auth.register`
- **Authentication**: No
- **Parameters**:
  - `name` (string, required): User's full name
  - `email` (string, required): User's email address
  - `password` (string, required): User's password (min 8 characters)
  - `password_confirmation` (string, required): Password confirmation
- **Response**:
  - `user`: User object
  - `token`: Authentication token

#### Login

- **URL**: `/login`
- **Method**: `POST`
- **Name**: `api.auth.login`
- **Authentication**: No
- **Parameters**:
  - `email` (string, required): User's email address
  - `password` (string, required): User's password
- **Response**:
  - `user`: User object
  - `token`: Authentication token

#### Get Current User

- **URL**: `/me`
- **Method**: `GET`
- **Name**: `api.auth.me`
- **Authentication**: Yes
- **Response**:
  - `user`: User object

#### Logout

- **URL**: `/logout`
- **Method**: `POST`
- **Name**: `api.auth.logout`
- **Authentication**: Yes
- **Response**:
  - `message`: Logout confirmation

#### Update Profile

- **URL**: `/profile`
- **Method**: `PUT`
- **Name**: `api.auth.update-profile`
- **Authentication**: Yes
- **Parameters**:
  - `name` (string, optional): User's full name
  - `email` (string, optional): User's email address
  - `phone` (string, optional): User's phone number
  - `preferences` (json, optional): User preferences
- **Response**:
  - `user`: Updated user object
  - `message`: Success message

#### Change Password

- **URL**: `/password`
- **Method**: `PUT`
- **Name**: `api.auth.change-password`
- **Authentication**: Yes
- **Parameters**:
  - `current_password` (string, required): Current password
  - `password` (string, required): New password (min 8 characters)
  - `password_confirmation` (string, required): New password confirmation
- **Response**:
  - `message`: Success message

#### Update Avatar

- **URL**: `/avatar`
- **Method**: `POST`
- **Name**: `api.auth.update-avatar`
- **Authentication**: Yes
- **Parameters**:
  - `avatar` (file, required): Image file (jpeg, png, jpg, gif, max 2MB)
- **Response**:
  - `user`: Updated user object
  - `avatar_url`: URL of the uploaded avatar
  - `message`: Success message

#### Forgot Password

- **URL**: `/forgot-password`
- **Method**: `POST`
- **Name**: `api.auth.forgot-password`
- **Authentication**: No
- **Parameters**:
  - `email` (string, required): User's email address
- **Response**:
  - `message`: Success message

#### Reset Password

- **URL**: `/reset-password`
- **Method**: `POST`
- **Name**: `api.auth.reset-password`
- **Authentication**: No
- **Parameters**:
  - `token` (string, required): Reset token from email
  - `email` (string, required): User's email address
  - `password` (string, required): New password (min 8 characters)
  - `password_confirmation` (string, required): New password confirmation
- **Response**:
  - `message`: Success message

### Bins

#### Get All Bins

- **URL**: `/bins`
- **Method**: `GET`
- **Name**: `api.bins.index`
- **Authentication**: Yes
- **Query Parameters**:
  - `page` (integer, optional): Page number for pagination
  - `per_page` (integer, optional): Items per page
  - `sort_by` (string, optional): Field to sort by
  - `sort_dir` (string, optional): Sort direction (asc/desc)
- **Response**:
  - `bins`: Array of bin objects
  - `pagination`: Pagination information

#### Create Bin

- **URL**: `/bins`
- **Method**: `POST`
- **Name**: `api.bins.store`
- **Authentication**: Yes
- **Parameters**:
  - `name` (string, required): Bin name
  - `type` (string, required): Bin type (income, expense)
  - `description` (string, optional): Bin description
  - `threshold_max_limit` (numeric, required): Maximum threshold limit
  - `threshold_max_warning` (numeric, optional): Warning threshold (must be less than max_limit)
  - `currency` (string, optional): Currency code (default: USD)
- **Response**:
  - `bin`: Created bin object
  - `remaining_bins`: Number of remaining bins user can create
  - `user_cumulative_balance`: User's updated cumulative balance
  - `message`: Success message

#### Get Bin Details

- **URL**: `/bins/{id}`
- **Method**: `GET`
- **Name**: `api.bins.show`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Bin ID
- **Response**:
  - `bin`: Bin object with sub-bins

#### Update Bin

- **URL**: `/bins/{id}`
- **Method**: `PUT`
- **Name**: `api.bins.update`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Bin ID
- **Parameters**:
  - `name` (string, optional): Bin name
  - `type` (string, optional): Bin type (income, expense)
  - `description` (string, optional): Bin description
  - `threshold_max_limit` (numeric, optional): Maximum threshold limit
  - `threshold_max_warning` (numeric, optional): Warning threshold (must be less than max_limit)
  - `currency` (string, optional): Currency code
  - `is_active` (boolean, optional): Bin status
- **Response**:
  - `bin`: Updated bin object
  - `user_cumulative_balance`: User's updated cumulative balance
  - `message`: Success message

#### Delete Bin

- **URL**: `/bins/{id}`
- **Method**: `DELETE`
- **Name**: `api.bins.destroy`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Bin ID
- **Response** (Success - 200):
  - `message`: Success message
  - `deleted_bin`: Object containing deleted bin details (name, type, amount)
  - `user_cumulative_balance`: User's updated cumulative balance
- **Error Responses**:
  - **404 Not Found**: Bin not found or doesn't belong to user
  - **422 Unprocessable Entity**: Bin has sub-bins or transactions
    - `message`: Error message
    - `error`: Detailed error description
    - `sub_bins_count` or `transactions_count`: Number of related items
- **Notes**:
  - Bin must not have any sub-bins or transactions before deletion
  - User's cumulative balance is automatically recalculated after deletion

### Sub-Bins

#### Get All Sub-Bins for a Bin

- **URL**: `/bins/{binId}/sub-bins`
- **Method**: `GET`
- **Name**: `api.sub-bins.index`
- **Authentication**: Yes
- **URL Parameters**:
  - `binId` (integer, required): Parent bin ID
- **Response**:
  - `sub_bins`: Array of sub-bin objects

#### Create Sub-Bin

- **URL**: `/bins/{binId}/sub-bins`
- **Method**: `POST`
- **Name**: `api.sub-bins.store`
- **Authentication**: Yes
- **URL Parameters**:
  - `binId` (integer, required): Parent bin ID
- **Parameters**:
  - `name` (string, required): Sub-bin name
  - `type` (string, optional): Sub-bin type (income, expense) - Auto-selected from parent if not provided
  - `description` (string, optional): Sub-bin description
  - `threshold_max_limit` (numeric, required): Maximum threshold limit
  - `threshold_max_warning` (numeric, optional): Warning threshold (must be less than max_limit)
  - `currency` (string, optional): Currency code (default: USD)
  - `parent_sub_bin_id` (integer, optional): Parent sub-bin ID for nesting
- **Response**:
  - `sub_bin`: Created sub-bin object
  - `remaining_sub_bins`: Number of remaining sub-bins user can create
  - `user_cumulative_balance`: User's updated cumulative balance
  - `message`: Success message
- **Notes**:
  - If `type` is not provided, it will automatically inherit from parent bin or parent sub-bin
  - `parent_category_inherited` field in response indicates if type was auto-selected

#### Get Sub-Bin Details

- **URL**: `/bins/{binId}/sub-bins/{id}`
- **Method**: `GET`
- **Name**: `api.sub-bins.show`
- **Authentication**: Yes
- **URL Parameters**:
  - `binId` (integer, required): Parent bin ID
  - `id` (integer, required): Sub-bin ID
- **Response**:
  - `sub_bin`: Sub-bin object

#### Update Sub-Bin

- **URL**: `/bins/{binId}/sub-bins/{id}`
- **Method**: `PUT`
- **Name**: `api.sub-bins.update`
- **Authentication**: Yes
- **URL Parameters**:
  - `binId` (integer, required): Parent bin ID
  - `id` (integer, required): Sub-bin ID
- **Parameters**:
  - `name` (string, optional): Sub-bin name
  - `type` (string, optional): Sub-bin type (income, expense)
  - `description` (string, optional): Sub-bin description
  - `threshold_max_limit` (numeric, optional): Maximum threshold limit
  - `threshold_max_warning` (numeric, optional): Warning threshold (must be less than max_limit)
  - `currency` (string, optional): Currency code
  - `is_active` (boolean, optional): Sub-bin status
- **Response**:
  - `sub_bin`: Updated sub-bin object
  - `user_cumulative_balance`: User's updated cumulative balance
  - `message`: Success message

#### Delete Sub-Bin

- **URL**: `/bins/{binId}/sub-bins/{id}`
- **Method**: `DELETE`
- **Name**: `api.sub-bins.destroy`
- **Authentication**: Yes
- **URL Parameters**:
  - `binId` (integer, required): Parent bin ID
  - `id` (integer, required): Sub-bin ID
- **Response**:
  - `message`: Success message

### Transactions

#### Get All Transactions

- **URL**: `/transactions`
- **Method**: `GET`
- **Name**: `api.transactions.index`
- **Authentication**: Yes
- **Query Parameters**:
  - `page` (integer, optional): Page number for pagination
  - `per_page` (integer, optional): Items per page
  - `sort_by` (string, optional): Field to sort by
  - `sort_dir` (string, optional): Sort direction (asc/desc)
  - `start_date` (date, optional): Filter by start date
  - `end_date` (date, optional): Filter by end date
  - `type` (string, optional): Filter by transaction type
  - `bin_id` (integer, optional): Filter by bin ID
  - `sub_bin_id` (integer, optional): Filter by sub-bin ID
  - `category` (string, optional): Filter by category
- **Response**:
  - `transactions`: Array of transaction objects
  - `pagination`: Pagination information

#### Create Transaction

- **URL**: `/transactions`
- **Method**: `POST`
- **Name**: `api.transactions.store`
- **Authentication**: Yes
- **Parameters**:
  - `bin_id` (integer, required): Bin ID
  - `sub_bin_id` (integer, optional): Sub-bin ID
  - `transaction_type` (string, required): Type (income, expense, transfer)
  - `amount` (numeric, required): Transaction amount
  - `currency` (string, required): Currency code (default: USD)
  - `description` (string, optional): Transaction description
  - `category` (string, optional): Transaction category
  - `payment_method` (string, optional): Payment method
  - `transaction_date` (date, required): Transaction date
  - `status` (string, optional): Status (default: completed)
  - `source` (string, optional): Source (default: manual)
  - `metadata` (json, optional): Additional metadata
- **Response**:
  - `transaction`: Created transaction object
  - `message`: Success message

#### Get Transaction Details

- **URL**: `/transactions/{id}`
- **Method**: `GET`
- **Name**: `api.transactions.show`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Transaction ID
- **Response**:
  - `transaction`: Transaction object

#### Update Transaction

- **URL**: `/transactions/{id}`
- **Method**: `PUT`
- **Name**: `api.transactions.update`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Transaction ID
- **Parameters**:
  - `bin_id` (integer, optional): Bin ID
  - `sub_bin_id` (integer, optional): Sub-bin ID
  - `transaction_type` (string, optional): Type (income, expense, transfer)
  - `amount` (numeric, optional): Transaction amount
  - `currency` (string, optional): Currency code
  - `description` (string, optional): Transaction description
  - `category` (string, optional): Transaction category
  - `payment_method` (string, optional): Payment method
  - `transaction_date` (date, optional): Transaction date
  - `status` (string, optional): Status
  - `metadata` (json, optional): Additional metadata
- **Response**:
  - `transaction`: Updated transaction object
  - `message`: Success message

#### Delete Transaction

- **URL**: `/transactions/{id}`
- **Method**: `DELETE`
- **Name**: `api.transactions.destroy`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Transaction ID
- **Response**:
  - `message`: Success message

#### Get Transactions by Bin

- **URL**: `/transactions/by-bin/{binId}`
- **Method**: `GET`
- **Name**: `api.transactions.by-bin`
- **Authentication**: Yes
- **URL Parameters**:
  - `binId` (integer, required): Bin ID
- **Query Parameters**:
  - `page` (integer, optional): Page number for pagination
  - `per_page` (integer, optional): Items per page
- **Response**:
  - `transactions`: Array of transaction objects
  - `pagination`: Pagination information

#### Get Transactions by Category

- **URL**: `/transactions/by-category/{category}`
- **Method**: `GET`
- **Name**: `api.transactions.by-category`
- **Authentication**: Yes
- **URL Parameters**:
  - `category` (string, required): Category name
- **Query Parameters**:
  - `page` (integer, optional): Page number for pagination
  - `per_page` (integer, optional): Items per page
- **Response**:
  - `transactions`: Array of transaction objects
  - `pagination`: Pagination information

#### Get Transaction Statistics

- **URL**: `/transactions/stats`
- **Method**: `GET`
- **Name**: `api.transactions.stats`
- **Authentication**: Yes
- **Query Parameters**:
  - `period` (string, optional): Period (day, week, month, year)
  - `start_date` (date, optional): Start date for custom period
  - `end_date` (date, optional): End date for custom period
- **Response**:
  - `stats`: Transaction statistics
  - `income`: Total income
  - `expense`: Total expense
  - `net`: Net amount
  - `by_category`: Breakdown by category
  - `by_bin`: Breakdown by bin

### Reports

#### Get All Reports

- **URL**: `/reports`
- **Method**: `GET`
- **Name**: `api.reports.index`
- **Authentication**: Yes
- **Query Parameters**:
  - `page` (integer, optional): Page number for pagination
  - `per_page` (integer, optional): Items per page
- **Response**:
  - `reports`: Array of report objects
  - `pagination`: Pagination information

#### Create Report

- **URL**: `/reports`
- **Method**: `POST`
- **Name**: `api.reports.store`
- **Authentication**: Yes
- **Parameters**:
  - `name` (string, required): Report name
  - `type` (string, required): Report type (transaction, bin, summary)
  - `format` (string, required): Report format (csv, pdf)
  - `period_type` (string, required): Time period (daily, weekly, monthly, custom)
  - `start_date` (date, required if period_type is custom): Start date
  - `end_date` (date, required if period_type is custom): End date
  - `bin_id` (integer, optional): Filter by bin ID
  - `sub_bin_id` (integer, optional): Filter by sub-bin ID
  - `transaction_type` (string, optional): Filter by transaction type (income, expense)
  - `min_amount` (numeric, optional): Filter by minimum amount
  - `max_amount` (numeric, optional): Filter by maximum amount
  - `is_recurring` (boolean, optional): Whether the report is recurring
  - `schedule` (string, required if is_recurring is true): Schedule (daily, weekly, monthly)
- **Response**:
  - `report`: Created report object
  - `message`: Success message

#### Get Report Details

- **URL**: `/reports/{id}`
- **Method**: `GET`
- **Name**: `api.reports.show`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Report ID
- **Response**:
  - `report`: Report object

#### Delete Report

- **URL**: `/reports/{id}`
- **Method**: `DELETE`
- **Name**: `api.reports.destroy`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Report ID
- **Response**:
  - `message`: Success message

#### Download Report

- **URL**: `/reports/{id}/download`
- **Method**: `GET`
- **Name**: `api.reports.download`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Report ID
- **Response**:
  - File download (CSV or PDF)

#### Regenerate Report

- **URL**: `/reports/{id}/regenerate`
- **Method**: `POST`
- **Name**: `api.reports.regenerate`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Report ID
- **Response**:
  - `report`: Updated report object
  - `message`: Success message

### Subscriptions

#### Get All Subscriptions

- **URL**: `/subscriptions`
- **Method**: `GET`
- **Name**: `api.subscriptions.index`
- **Authentication**: Yes
- **Response**:
  - `subscriptions`: Array of subscription objects

#### Create Subscription

- **URL**: `/subscriptions`
- **Method**: `POST`
- **Name**: `api.subscriptions.store`
- **Authentication**: Yes
- **Parameters**:
  - `stripe_price` (string, required): Stripe price ID
  - `payment_method` (string, required): Stripe payment method ID
- **Response**:
  - `subscription`: Created subscription object
  - `message`: Success message

#### Get Subscription Details

- **URL**: `/subscriptions/{id}`
- **Method**: `GET`
- **Name**: `api.subscriptions.show`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Subscription ID
- **Response**:
  - `subscription`: Subscription object

#### Update Subscription

- **URL**: `/subscriptions/{id}`
- **Method**: `PUT`
- **Name**: `api.subscriptions.update`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Subscription ID
- **Parameters**:
  - `stripe_price` (string, required): New Stripe price ID
- **Response**:
  - `subscription`: Updated subscription object
  - `message`: Success message

#### Delete Subscription

- **URL**: `/subscriptions/{id}`
- **Method**: `DELETE`
- **Name**: `api.subscriptions.destroy`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Subscription ID
- **Response**:
  - `message`: Success message

#### Cancel Subscription

- **URL**: `/subscriptions/{id}/cancel`
- **Method**: `POST`
- **Name**: `api.subscriptions.cancel`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Subscription ID
- **Response**:
  - `subscription`: Updated subscription object
  - `message`: Success message

#### Resume Subscription

- **URL**: `/subscriptions/{id}/resume`
- **Method**: `POST`
- **Name**: `api.subscriptions.resume`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Subscription ID
- **Response**:
  - `subscription`: Updated subscription object
  - `message`: Success message

#### Get Subscription Plans

- **URL**: `/subscriptions/plans`
- **Method**: `GET`
- **Name**: `api.subscriptions.plans`
- **Authentication**: Yes
- **Response**:
  - `plans`: Array of available subscription plans

### Crypto Wallets

#### Get All Crypto Wallets

- **URL**: `/crypto-wallets`
- **Method**: `GET`
- **Name**: `api.crypto-wallets.index`
- **Authentication**: Yes
- **Response**:
  - `wallets`: Array of crypto wallet objects

#### Create Crypto Wallet

- **URL**: `/crypto-wallets`
- **Method**: `POST`
- **Name**: `api.crypto-wallets.store`
- **Authentication**: Yes
- **Parameters**:
  - `wallet_address` (string, required): Wallet address
  - `wallet_name` (string, required): Wallet name
  - `blockchain_network` (string, required): Network (ethereum, binance, polygon, avalanche)
  - `is_active` (boolean, optional): Wallet status (default: true)
- **Response**:
  - `wallet`: Created wallet object
  - `message`: Success message

#### Add Crypto Wallet from QR Code

- **URL**: `/crypto-wallets/qr-code`
- **Method**: `POST`
- **Name**: `api.crypto-wallets.add-from-qr`
- **Authentication**: Yes
- **Parameters**:
  - `qr_data` (string, required): QR code data (e.g., "ethereum:0x123456789abcdef..." or just "0x123456789abcdef...")
  - `wallet_name` (string, optional): Custom wallet name (if not provided, a name will be generated based on the blockchain)
- **Response**:
  - `wallet`: Created wallet object
  - `message`: Success message

#### Get Crypto Wallet Details

- **URL**: `/crypto-wallets/{id}`
- **Method**: `GET`
- **Name**: `api.crypto-wallets.show`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Wallet ID
- **Response**:
  - `wallet`: Wallet object with assets

#### Update Crypto Wallet

- **URL**: `/crypto-wallets/{id}`
- **Method**: `PUT`
- **Name**: `api.crypto-wallets.update`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Wallet ID
- **Parameters**:
  - `wallet_name` (string, optional): Wallet name
  - `is_active` (boolean, optional): Wallet status
- **Response**:
  - `wallet`: Updated wallet object
  - `message`: Success message

#### Delete Crypto Wallet

- **URL**: `/crypto-wallets/{id}`
- **Method**: `DELETE`
- **Name**: `api.crypto-wallets.destroy`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Wallet ID
- **Response**:
  - `message`: Success message

#### Sync Crypto Wallet

- **URL**: `/crypto-wallets/{id}/sync`
- **Method**: `POST`
- **Name**: `api.crypto-wallets.sync`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Wallet ID
- **Response**:
  - `wallet`: Updated wallet object with synced data
  - `message`: Success message

#### Get Crypto Wallet Assets

- **URL**: `/crypto-wallets/{id}/assets`
- **Method**: `GET`
- **Name**: `api.crypto-wallets.assets`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Wallet ID
- **Response**:
  - `assets`: Array of assets in the wallet

#### Get Crypto Wallet Transactions

- **URL**: `/crypto-wallets/{id}/transactions`
- **Method**: `GET`
- **Name**: `api.crypto-wallets.transactions`
- **Authentication**: Yes
- **URL Parameters**:
  - `id` (integer, required): Wallet ID
- **Response**:
  - `transactions`: Array of blockchain transactions

### Webhooks

#### Stripe Webhook

- **URL**: `/webhook/stripe`
- **Method**: `POST`
- **Name**: `api.webhook.stripe`
- **Authentication**: No (uses Stripe signature verification)
- **Headers**:
  - `Stripe-Signature` (string, required): Stripe webhook signature
- **Response**:
  - `message`: Success message

## Error Codes

- `400`: Bad Request - Invalid parameters
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource not found
- `422`: Unprocessable Entity - Validation errors
- `429`: Too Many Requests - Rate limit exceeded
- `500`: Internal Server Error - Server error

## API Versioning

This API is currently at version 1. All endpoints should be prefixed with `/api`.

## Rate Limiting

API requests are limited to 60 requests per minute per user or IP address. If you exceed this limit, you'll receive a 429 Too Many Requests response.

## Pagination

Endpoints that return collections of resources (like transactions) support pagination. The response includes:

```json
{
  "data": [...],
  "links": {
    "first": "https://your-domain.com/api/resource?page=1",
    "last": "https://your-domain.com/api/resource?page=5",
    "prev": null,
    "next": "https://your-domain.com/api/resource?page=2"
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 5,
    "path": "https://your-domain.com/api/resource",
    "per_page": 15,
    "to": 15,
    "total": 75
  }
}
```

## Testing the API

You can test the API using tools like Postman or cURL. Here's an example of how to make a login request using cURL:

```bash
curl -X POST https://your-domain.com/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"email":"<EMAIL>","password":"your_password"}'
```

## Flutter Integration Tips

### Best Practices

1. Use a package like `dio` or `http` for making API requests
2. Store the authentication token securely using `flutter_secure_storage`
3. Create model classes for each response type using `json_serializable` or `freezed`
4. Implement proper error handling for API responses with custom exception classes
5. Use state management solutions like Provider, Bloc, or Riverpod to manage API data
6. Implement retry logic for failed requests with exponential backoff
7. Add loading indicators during API calls to improve user experience
8. Cache responses when appropriate to reduce API calls and improve performance
9. Implement offline support by storing critical data locally
10. Use interceptors to handle common tasks like adding auth tokens and logging

### PocketWatch-Specific Tips

1. **Authentication Flow**: Implement a complete authentication flow including login, registration, password reset, and token refresh
2. **Bin Management**: Create a dedicated service for managing bins and sub-bins with proper caching
3. **Transaction Handling**: Implement optimistic updates for transactions to make the UI feel more responsive
4. **Subscription Management**: Handle subscription upgrades/downgrades with proper UI feedback
5. **Error Handling**: Create custom error handlers for specific API errors
6. **Offline Support**: Implement offline transaction creation that syncs when the device is back online
7. **Data Synchronization**: Implement a background sync service for transactions and bins
8. **Crypto Integration**: For premium users, implement the crypto wallet integration with proper error handling

### Recommended Packages

- **API Client**: `dio` with `dio_cache_interceptor`
- **State Management**: `flutter_bloc` or `riverpod`
- **Local Storage**: `hive` or `isar` for offline data
- **Authentication**: `flutter_secure_storage` for token storage
- **Forms**: `flutter_form_builder` for input forms
- **Charts**: `fl_chart` for financial visualizations
- **Notifications**: `flutter_local_notifications` for reminders
- **Connectivity**: `connectivity_plus` for network status

## Example Flutter Code

Here's a comprehensive example of how to implement API services for the PocketWatch app in Flutter:

```dart
import 'package:dio/dio.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

// Custom exception for API errors
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  ApiException({
    required this.message,
    this.statusCode,
    this.errors,
  });

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

// API service class
class PocketWatchApiService {
  static const String baseUrl = 'https://your-domain.com/api';
  final Dio _dio = Dio();
  final FlutterSecureStorage _storage = FlutterSecureStorage();

  PocketWatchApiService() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
    _dio.options.headers = {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    };

    // Add cache interceptor
    final cacheOptions = CacheOptions(
      store: MemCacheStore(),
      policy: CachePolicy.refreshForceCache,
      hitCacheOnErrorExcept: [401, 403],
      maxStale: const Duration(days: 1),
    );
    _dio.interceptors.add(DioCacheInterceptor(options: cacheOptions));

    // Add auth interceptor
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _storage.read(key: 'auth_token');
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        return handler.next(options);
      },
      onError: (DioException e, handler) {
        if (e.response?.statusCode == 401) {
          // Handle token expiration
          _storage.delete(key: 'auth_token');
          // You could trigger a logout event here
        }
        return handler.next(e);
      },
    ));
  }

  // Authentication methods
  Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    try {
      final response = await _dio.post(
        '/register',
        data: {
          'name': name,
          'email': email,
          'password': password,
          'password_confirmation': passwordConfirmation,
        },
      );

      final token = response.data['token'];
      await _storage.write(key: 'auth_token', value: token);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _dio.post(
        '/login',
        data: {
          'email': email,
          'password': password,
        },
      );

      final token = response.data['token'];
      await _storage.write(key: 'auth_token', value: token);
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<void> logout() async {
    try {
      await _dio.post('/logout');
      await _storage.delete(key: 'auth_token');
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Bin methods
  Future<List<dynamic>> getBins() async {
    try {
      final response = await _dio.get('/bins');
      return response.data['bins'];
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<Map<String, dynamic>> createBin({
    required String name,
    required String type,
    String? description,
    required double thresholdMin,
    double? thresholdMax,
    String currency = 'USD',
  }) async {
    try {
      final response = await _dio.post(
        '/bins',
        data: {
          'name': name,
          'type': type, // 'income' or 'expenditure'
          'description': description,
          'threshold_min': thresholdMin,
          'threshold_max': thresholdMax,
          'currency': currency,
        },
      );
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Transaction methods
  Future<List<dynamic>> getTransactions({
    int? binId,
    String? type,
    String? startDate,
    String? endDate,
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final response = await _dio.get(
        '/transactions',
        queryParameters: {
          if (binId != null) 'bin_id': binId,
          if (type != null) 'type': type,
          if (startDate != null) 'start_date': startDate,
          if (endDate != null) 'end_date': endDate,
          'page': page,
          'per_page': perPage,
        },
      );
      return response.data['transactions'];
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<Map<String, dynamic>> createTransaction({
    int? binId,
    int? subBinId,
    required String transactionType,
    required double amount,
    String? description,
    String? category,
    String? paymentMethod,
    required String transactionDate,
  }) async {
    try {
      final response = await _dio.post(
        '/transactions',
        data: {
          'bin_id': binId,
          'sub_bin_id': subBinId,
          'transaction_type': transactionType,
          'amount': amount,
          'description': description,
          'category': category,
          'payment_method': paymentMethod,
          'transaction_date': transactionDate,
        },
      );
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Subscription methods
  Future<Map<String, dynamic>> createSubscription({
    required String subscriptionTier,
    required String billingCycle,
  }) async {
    try {
      final response = await _dio.post(
        '/subscriptions',
        data: {
          'subscription_tier': subscriptionTier,
          'billing_cycle': billingCycle,
        },
      );
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Helper methods
  ApiException _handleDioError(DioException e) {
    if (e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout ||
        e.type == DioExceptionType.sendTimeout) {
      return ApiException(
        message: 'Connection timeout. Please check your internet connection.',
        statusCode: e.response?.statusCode,
      );
    }

    if (e.type == DioExceptionType.connectionError) {
      return ApiException(
        message: 'No internet connection. Please check your network settings.',
        statusCode: e.response?.statusCode,
      );
    }

    if (e.response != null) {
      final data = e.response!.data;
      final message = data['message'] ?? 'An error occurred';
      final errors = data['errors'] as Map<String, dynamic>?;

      return ApiException(
        message: message,
        statusCode: e.response!.statusCode,
        errors: errors,
      );
    }

    return ApiException(
      message: e.message ?? 'An unexpected error occurred',
      statusCode: e.response?.statusCode,
    );
  }
}

// Example usage in a Flutter app
class TransactionRepository {
  final PocketWatchApiService _apiService = PocketWatchApiService();

  Future<List<Transaction>> getTransactions({
    int? binId,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final data = await _apiService.getTransactions(
        binId: binId,
        type: type,
        startDate: startDate?.toIso8601String().split('T').first,
        endDate: endDate?.toIso8601String().split('T').first,
      );

      return data.map((json) => Transaction.fromJson(json)).toList();
    } catch (e) {
      // Handle or rethrow the exception
      rethrow;
    }
  }

  Future<Transaction> createTransaction({
    required TransactionType type,
    required double amount,
    required DateTime date,
    int? binId,
    int? subBinId,
    String? description,
    String? category,
  }) async {
    try {
      final data = await _apiService.createTransaction(
        transactionType: type.toString().split('.').last,
        amount: amount,
        transactionDate: date.toIso8601String().split('T').first,
        binId: binId,
        subBinId: subBinId,
        description: description,
        category: category,
      );

      return Transaction.fromJson(data['transaction']);
    } catch (e) {
      // Handle or rethrow the exception
      rethrow;
    }
  }
}

// Example model class
enum TransactionType { income, expense, transfer }

class Transaction {
  final int id;
  final int? binId;
  final int? subBinId;
  final TransactionType type;
  final double amount;
  final String? description;
  final String? category;
  final DateTime date;

  Transaction({
    required this.id,
    this.binId,
    this.subBinId,
    required this.type,
    required this.amount,
    this.description,
    this.category,
    required this.date,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      binId: json['bin_id'],
      subBinId: json['sub_bin_id'],
      type: _parseTransactionType(json['transaction_type']),
      amount: double.parse(json['amount'].toString()),
      description: json['description'],
      category: json['category'],
      date: DateTime.parse(json['transaction_date']),
    );
  }

  static TransactionType _parseTransactionType(String type) {
    switch (type) {
      case 'income':
        return TransactionType.income;
      case 'expense':
        return TransactionType.expense;
      case 'transfer':
        return TransactionType.transfer;
      default:
        return TransactionType.expense;
    }
  }
}

## Bins: Filtered by Type (Income/Expense)

### GET /api/bins/income
Returns all income bins and their income sub-bins for the authenticated user.

- **Auth required:** Yes (Bearer token)
- **Parameters:** None
- **Response Example:**
```json
{
  "bins": [
    {
      "id": 1,
      "name": "Salary Bin",
      "type": "income",
      "sub_bins": [
        { "id": 10, "name": "Bonus", "type": "income" }
      ]
    },
    ...
  ]
}
```

### GET /api/bins/expense
Returns all expense bins and their expense sub-bins for the authenticated user.

- **Auth required:** Yes (Bearer token)
- **Parameters:** None
- **Response Example:**
```json
{
  "bins": [
    {
      "id": 2,
      "name": "Groceries Bin",
      "type": "expense",
      "sub_bins": [
        { "id": 20, "name": "Supermarket", "type": "expense" }
      ]
    },
    ...
  ]
}
```

**Usage:**
- Call `/api/bins/income` when the user selects `transaction_type = income`.
- Call `/api/bins/expense` when the user selects `transaction_type = expense`.
- Use the returned bins/sub-bins to populate the bin selection in your transaction creation flow.