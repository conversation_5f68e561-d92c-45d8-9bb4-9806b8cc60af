<?php

namespace App\Notifications\Subscriptions;

use App\Models\Subscription;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class SubscriptionUpdatedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The subscription instance.
     *
     * @var \App\Models\Subscription
     */
    protected $subscription;

    /**
     * The previous tier.
     *
     * @var string
     */
    protected $previousTier;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Subscription  $subscription
     * @param  string  $previousTier
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_subscription_updates';

    public function __construct(Subscription $subscription, $previousTier = null)
    {
        $this->subscription = $subscription;
        $this->previousTier = $previousTier;

        $this->subject = 'Your PocketWatch Subscription Has Been Updated';
        $this->title = 'Subscription Updated';
        
        if ($previousTier && $previousTier !== $subscription->subscription_tier) {
            $this->content = 'Your PocketWatch subscription has been updated from ' . ucfirst($previousTier) . ' to ' . 
                            ucfirst($this->subscription->subscription_tier) . '. Your subscription changes are now active.';
        } else {
            $this->content = 'Your PocketWatch subscription has been successfully updated and the changes are now active.';
        }
        
        $this->detailsTitle = 'Updated Subscription Details';
        $this->details = [
            'Plan' => ucfirst($this->subscription->subscription_tier) . ' Tier',
            'Billing Cycle' => ucfirst($this->subscription->billing_cycle),
            'Price' => $this->formatCurrency($this->subscription->price, $this->subscription->currency),
        ];
        
        if ($this->subscription->trial_ends_at && $this->subscription->trial_ends_at->isFuture()) {
            $this->details['Free Trial Ends'] = $this->subscription->trial_ends_at->format('F j, Y');
        }
        
        $this->actionText = 'Manage Subscription';
        $this->actionUrl = url('/subscriptions');
        
        $this->closing = 'Thank you for choosing PocketWatch. We hope you enjoy all the features of your updated subscription!';
        $this->signature = 'The PocketWatch Team';
    }
    
    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);
        
        return $symbol . number_format($value, 2) . '/' . ($this->subscription->billing_cycle === 'monthly' ? 'month' : 'year');
    }
    
    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];
        
        return $symbols[$currency] ?? $currency;
    }
}
