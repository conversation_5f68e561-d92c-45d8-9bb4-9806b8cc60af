<div class="sub-bin-item depth-{{ $subBin->depth_level }}">
    <div class="sub-bin-name">
        @for($i = 1; $i < $subBin->depth_level; $i++)
            &nbsp;&nbsp;&nbsp;&nbsp;
        @endfor
        @if($subBin->depth_level > 1)
            ↳
        @endif
        {{ $subBin->name }}
        @if($subBin->parentSubBin)
            <small>(under {{ $subBin->parentSubBin->name }})</small>
        @endif
    </div>
    
    <div class="sub-bin-details">
        <div>
            <strong>Type:</strong> {{ ucfirst($subBin->type) }}
        </div>
        <div>
            <strong>Amount:</strong> 
            <span class="amount-{{ $subBin->current_amount >= 0 ? 'positive' : 'negative' }}">
                {{ $subBin->currency }} {{ number_format($subBin->current_amount, 2) }}
            </span>
        </div>
        <div>
            <strong>Threshold:</strong> 
            {{ $subBin->currency }} {{ number_format($subBin->threshold_min, 2) }}
            @if($subBin->threshold_max)
                - {{ $subBin->currency }} {{ number_format($subBin->threshold_max, 2) }}
            @else
                - ∞
            @endif
        </div>
        <div>
            <strong>Status:</strong> 
            <span class="{{ $subBin->is_active ? 'status-active' : 'status-inactive' }}">
                {{ $subBin->is_active ? 'Active' : 'Inactive' }}
            </span>
        </div>
        <div>
            <strong>Transactions:</strong> {{ $subBin->transactions->count() }}
        </div>
        <div>
            <strong>Path:</strong> {{ $subBin->path ?: 'N/A' }}
        </div>
    </div>
</div>

@if($subBin->childSubBins && $subBin->childSubBins->isNotEmpty())
    @foreach($subBin->childSubBins as $childSubBin)
        @include('admin.exports.partials.sub-bin-item', ['subBin' => $childSubBin])
    @endforeach
@endif
