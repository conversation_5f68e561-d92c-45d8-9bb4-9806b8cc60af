<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\FixNotificationSettings::class,
        Commands\RetryFailedNotifications::class,
        Commands\TestNotification::class,
        Commands\SyncCryptoWallets::class,
        Commands\CheckTrialExpirations::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Run the queue worker every minute
        $schedule->command('queue:work --stop-when-empty')->everyMinute();

        // Check for failed notifications every hour
        $schedule->command('notifications:retry-failed')->hourly();

        // Send subscription renewal reminders daily
        $schedule->command('subscriptions:send-renewal-reminders')
            ->daily()
            ->at('09:00');

        // Check for threshold alerts daily
        $schedule->command('check:threshold-alerts')
            ->daily()
            ->at('10:00');

        // Generate scheduled reports
        $schedule->command('reports:generate-scheduled')
            ->hourly();

        // Sync crypto wallets
        $schedule->command('crypto:sync-wallets')
            ->hourly();

        // Check for trial expirations
        $schedule->command('trials:check-expirations')
            ->daily()
            ->at('08:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
