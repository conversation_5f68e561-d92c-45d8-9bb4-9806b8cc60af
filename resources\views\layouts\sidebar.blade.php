<div class="user-sidebar">
    <div class="sidebar-header">
        <div class="user-info">
            @if(Auth::user()->avatar)
                <img src="{{ Auth::user()->avatar }}" alt="{{ Auth::user()->name }}" class="user-avatar">
            @else
                <div class="user-avatar-placeholder">
                    {{ substr(Auth::user()->name, 0, 1) }}
                </div>
            @endif
            <div class="user-details">
                <h6 class="user-name">{{ Auth::user()->name }}</h6>
                <span class="user-subscription">
                    @if(Auth::user()->onTrial())
                        <span class="badge bg-info">Trial</span>
                    @elseif(Auth::user()->hasPremium())
                        <span class="badge bg-success">Premium</span>
                    @else
                        <span class="badge bg-secondary">Base</span>
                    @endif
                </span>
            </div>
        </div>
    </div>

    <div class="sidebar-menu">
        <div class="menu-section">
            <h6 class="menu-heading">Main</h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('bins.*') ? 'active' : '' }}" href="#">
                        <i class="fas fa-box"></i> Bins
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('transactions.*') ? 'active' : '' }}" href="#">
                        <i class="fas fa-exchange-alt"></i> Transactions
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('insights.*') ? 'active' : '' }}" href="#">
                        <i class="fas fa-chart-line"></i> Insights
                    </a>
                </li>
            </ul>
        </div>

        <div class="menu-section">
            <h6 class="menu-heading">Features</h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('chatbot.*') ? 'active' : '' }}" href="#">
                        <i class="fas fa-robot"></i> Binnit Chatbot
                    </a>
                </li>
                @if(Auth::user()->hasPremium())
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('crypto.*') ? 'active' : '' }}" href="#">
                        <i class="fab fa-bitcoin"></i> Crypto Scanner
                        <span class="badge bg-success float-end">Premium</span>
                    </a>
                </li>
                @else
                <li class="nav-item disabled">
                    <a class="nav-link disabled" href="#" data-bs-toggle="tooltip" title="Requires Premium Subscription">
                        <i class="fab fa-bitcoin"></i> Crypto Scanner
                        <span class="badge bg-secondary float-end">Premium</span>
                    </a>
                </li>
                @endif
            </ul>
        </div>

        <div class="menu-section">
            <h6 class="menu-heading">Account</h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}" href="#">
                        <i class="fas fa-user-circle"></i> Profile
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('subscriptions.*') ? 'active' : '' }}" href="#">
                        <i class="fas fa-crown"></i> Subscription
                        @if(Auth::user()->onTrial())
                            <span class="badge bg-info float-end">Trial</span>
                        @endif
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('plaid.*') ? 'active' : '' }}" href="{{ route('plaid.index') }}">
                        <i class="fas fa-university"></i> Bank Accounts
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('settings.*') ? 'active' : '' }}" href="#">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ route('logout') }}"
                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </div>

        @if(!Auth::user()->hasPremium() && !Auth::user()->onTrial())
        <div class="upgrade-banner">
            <div class="upgrade-content">
                <h6>Upgrade to Premium</h6>
                <p>Get unlimited sub-bins, crypto analysis, and more for just $10/month!</p>
                <a href="#" class="btn btn-success btn-sm w-100">Upgrade Now</a>
            </div>
        </div>
        @elseif(Auth::user()->onTrial())
        <div class="upgrade-banner">
            <div class="upgrade-content">
                <h6>Trial Active</h6>
                <p>Your 7-day free trial ends in {{ Auth::user()->getActiveSubscription()->trial_ends_at->diffInDays(now()) }} days.</p>
                <a href="#" class="btn btn-success btn-sm w-100">Continue with Premium</a>
            </div>
        </div>
        @endif
    </div>
</div>

<style>
    .user-sidebar {
        position: fixed;
        top: 60px;
        left: 0;
        bottom: 0;
        width: 260px;
        background-color: white;
        border-right: 1px solid rgba(0, 0, 0, 0.1);
        overflow-y: auto;
        transition: all 0.3s;
        z-index: 1020;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
    }

    .sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .user-info {
        display: flex;
        align-items: center;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .user-avatar-placeholder {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
    }

    .user-name {
        margin-bottom: 0;
        font-weight: 600;
    }

    .menu-section {
        padding: 1rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .menu-heading {
        padding: 0 1.5rem;
        font-size: 0.75rem;
        text-transform: uppercase;
        color: #6c757d;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .sidebar-menu .nav-link {
        padding: 0.5rem 1.5rem;
        color: #495057;
        font-weight: 500;
        border-left: 3px solid transparent;
    }

    .sidebar-menu .nav-link i {
        width: 20px;
        margin-right: 10px;
        text-align: center;
    }

    .sidebar-menu .nav-link:hover {
        background-color: var(--light-color);
        color: var(--primary-color);
    }

    .sidebar-menu .nav-link.active {
        color: var(--primary-color);
        background-color: var(--light-color);
        border-left-color: var(--primary-color);
    }

    .sidebar-menu .nav-link.disabled {
        color: #adb5bd;
        cursor: not-allowed;
    }

    .upgrade-banner {
        margin: 1rem;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .upgrade-content {
        padding: 1rem;
        color: white;
        text-align: center;
    }

    .upgrade-content h6 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .upgrade-content p {
        font-size: 0.85rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }

    /* For mobile */
    @media (max-width: 767.98px) {
        .user-sidebar {
            transform: translateX(-100%);
        }

        .user-sidebar.show {
            transform: translateX(0);
        }
    }
</style>
