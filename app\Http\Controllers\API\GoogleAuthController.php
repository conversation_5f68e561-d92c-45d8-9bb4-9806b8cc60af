<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class GoogleAuthController extends Controller
{
    /**
     * Redirect to Google OAuth.
     */
    public function redirectToGoogle()
    {
        try {
            $redirectUrl = Socialite::driver('google')
                ->stateless()
                ->redirect()
                ->getTargetUrl();

            return response()->json([
                'success' => true,
                'redirect_url' => $redirectUrl,
                'message' => 'Redirect to Google OAuth'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate Google OAuth URL: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Google OAuth callback.
     */
    public function handleGoogleCallback(Request $request)
    {
        try {
            // Validate the authorization code
            $validator = Validator::make($request->all(), [
                'code' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authorization code is required',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get user from Google
            $googleUser = Socialite::driver('google')
                ->stateless()
                ->user();

            // Check if user already exists with this Google ID
            $user = User::where('google_id', $googleUser->getId())->first();

            if ($user) {
                // Update existing user's Google info
                $user->update([
                    'google_avatar' => $googleUser->getAvatar(),
                    'email_verified_at' => now(),
                    'last_login_at' => now(),
                ]);

                $message = 'Login successful via Google';
            } else {
                // Check if user exists with same email
                $existingUser = User::where('email', $googleUser->getEmail())->first();

                if ($existingUser) {
                    // Link Google account to existing user
                    $existingUser->update([
                        'google_id' => $googleUser->getId(),
                        'google_avatar' => $googleUser->getAvatar(),
                        'provider' => 'google',
                        'email_verified_at' => now(),
                        'last_login_at' => now(),
                    ]);

                    $user = $existingUser;
                    $message = 'Google account linked successfully';
                } else {
                    // Create new user
                    $user = User::create([
                        'name' => $googleUser->getName(),
                        'email' => $googleUser->getEmail(),
                        'google_id' => $googleUser->getId(),
                        'google_avatar' => $googleUser->getAvatar(),
                        'provider' => 'google',
                        'email_verified_at' => now(),
                        'password' => null, // No password for OAuth users
                        'subscription_tier' => 'none',
                        'is_active' => true,
                        'last_login_at' => now(),
                    ]);

                    $message = 'Account created successfully via Google';
                }
            }

            // Create authentication token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => $message,
                'user' => [
                    'id' => $user->id,
                    'uuid' => $user->uuid,
                    'name' => $user->name,
                    'email' => $user->email,
                    'avatar' => $user->google_avatar ?: $user->avatar,
                    'provider' => $user->provider,
                    'subscription_tier' => $user->subscription_tier,
                    'trial_started_at' => $user->trial_started_at,
                    'trial_expired' => $user->trialExpired(),
                    'trial_days_remaining' => $user->getTrialDaysRemaining(),
                    'email_verified_at' => $user->email_verified_at,
                    'created_at' => $user->created_at,
                ],
                'token' => $token,
                'token_type' => 'Bearer'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Google authentication failed: ' . $e->getMessage(),
                'error_code' => 'google_auth_failed'
            ], 500);
        }
    }

    /**
     * Login with Google ID Token (for mobile apps).
     */
    public function loginWithGoogleToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Google ID token is required',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Verify Google ID token
            $client = new \Google_Client(['client_id' => config('services.google.client_id')]);
            $payload = $client->verifyIdToken($request->id_token);

            if (!$payload) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Google ID token'
                ], 401);
            }

            $googleId = $payload['sub'];
            $email = $payload['email'];
            $name = $payload['name'];
            $avatar = $payload['picture'] ?? null;
            $emailVerified = $payload['email_verified'] ?? false;

            // Check if user already exists with this Google ID
            $user = User::where('google_id', $googleId)->first();

            if ($user) {
                // Update existing user
                $user->update([
                    'google_avatar' => $avatar,
                    'email_verified_at' => $emailVerified ? now() : null,
                    'last_login_at' => now(),
                ]);

                $message = 'Login successful via Google';
            } else {
                // Check if user exists with same email
                $existingUser = User::where('email', $email)->first();

                if ($existingUser) {
                    // Link Google account to existing user
                    $existingUser->update([
                        'google_id' => $googleId,
                        'google_avatar' => $avatar,
                        'provider' => 'google',
                        'email_verified_at' => $emailVerified ? now() : null,
                        'last_login_at' => now(),
                    ]);

                    $user = $existingUser;
                    $message = 'Google account linked successfully';
                } else {
                    // Create new user
                    $user = User::create([
                        'name' => $name,
                        'email' => $email,
                        'google_id' => $googleId,
                        'google_avatar' => $avatar,
                        'provider' => 'google',
                        'email_verified_at' => $emailVerified ? now() : null,
                        'password' => null,
                        'subscription_tier' => 'none',
                        'is_active' => true,
                        'last_login_at' => now(),
                    ]);

                    $message = 'Account created successfully via Google';
                }
            }

            // Create authentication token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => $message,
                'user' => [
                    'id' => $user->id,
                    'uuid' => $user->uuid,
                    'name' => $user->name,
                    'email' => $user->email,
                    'avatar' => $user->google_avatar ?: $user->avatar,
                    'provider' => $user->provider,
                    'subscription_tier' => $user->subscription_tier,
                    'trial_started_at' => $user->trial_started_at,
                    'trial_expired' => $user->trialExpired(),
                    'trial_days_remaining' => $user->getTrialDaysRemaining(),
                    'email_verified_at' => $user->email_verified_at,
                    'created_at' => $user->created_at,
                ],
                'token' => $token,
                'token_type' => 'Bearer'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Google token verification failed: ' . $e->getMessage(),
                'error_code' => 'google_token_invalid'
            ], 500);
        }
    }

    /**
     * Unlink Google account.
     */
    public function unlinkGoogle(Request $request)
    {
        $user = $request->user();

        if (!$user->google_id) {
            return response()->json([
                'success' => false,
                'message' => 'No Google account linked to this user'
            ], 400);
        }

        // Check if user has a password set (for security)
        if (!$user->password && $user->provider === 'google') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot unlink Google account. Please set a password first.',
                'action_required' => 'set_password'
            ], 400);
        }

        // Unlink Google account
        $user->update([
            'google_id' => null,
            'google_avatar' => null,
            'provider' => 'email',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Google account unlinked successfully'
        ]);
    }
}
