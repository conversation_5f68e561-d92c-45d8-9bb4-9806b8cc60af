<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CryptoWallet extends Model
{
    use HasFactory, SoftDeletes, HasUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'user_id',
        'wallet_address',
        'wallet_name',
        'blockchain_network',
        'assets',
        'total_value_usd',
        'last_synced_at',
        'ai_advice',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'assets' => 'json',
        'total_value_usd' => 'decimal:8',
        'last_synced_at' => 'datetime',
        'ai_advice' => 'json',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the crypto wallet.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Sync wallet data from external API.
     */
    public function syncWalletData()
    {
        // This will be implemented to connect to Moralis/Covalent APIs
        // For now, we'll just return a placeholder
        return true;
    }

    /**
     * Get AI advice for the wallet.
     */
    public function getAiAdvice()
    {
        // This will be implemented to connect to Binnit AI
        // For now, we'll just return a placeholder
        return [
            'advice' => 'This is a placeholder for AI advice.',
            'generated_at' => now(),
        ];
    }
}
