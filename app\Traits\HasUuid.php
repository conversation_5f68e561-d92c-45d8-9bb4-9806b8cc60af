<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

trait HasUuid
{
    /**
     * Boot the trait.
     *
     * @return void
     */
    protected static function bootHasUuid()
    {
        static::creating(function (Model $model) {
            if (! $model->uuid) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'uuid';
    }

    /**
     * Find a model by its UUID.
     *
     * @param  string  $uuid
     * @param  array  $columns
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public static function findByUuid($uuid, $columns = ['*'])
    {
        return static::where('uuid', $uuid)->first($columns);
    }

    /**
     * Find a model by its UUID or fail.
     *
     * @param  string  $uuid
     * @param  array  $columns
     * @return \Illuminate\Database\Eloquent\Model
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public static function findByUuidOrFail($uuid, $columns = ['*'])
    {
        return static::where('uuid', $uuid)->firstOrFail($columns);
    }
}
