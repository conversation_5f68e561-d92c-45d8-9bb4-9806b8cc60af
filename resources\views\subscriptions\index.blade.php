@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Subscription Status Card -->
            <div class="card subscription-card mb-4">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-bold" style="color: var(--primary-color);">Your Subscription</h5>
                    <div>
                        @if(Auth::user()->getActiveSubscription())
                            @if(Auth::user()->onTrial())
                                <span class="badge bg-info rounded-pill px-3">Trial</span>
                            @elseif(Auth::user()->getActiveSubscription()->stripe_status == 'active')
                                <span class="badge bg-success rounded-pill px-3">Active</span>
                            @elseif(Auth::user()->getActiveSubscription()->stripe_status == 'canceled')
                                <span class="badge bg-warning rounded-pill px-3">Canceled</span>
                            @else
                                <span class="badge bg-secondary rounded-pill px-3">{{ ucfirst(Auth::user()->getActiveSubscription()->stripe_status) }}</span>
                            @endif
                        @else
                            <span class="badge bg-secondary rounded-pill px-3">No Active Subscription</span>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    @if(Auth::user()->getActiveSubscription())
                        <div class="row">
                            <div class="col-md-6">
                                <div class="subscription-info mb-4">
                                    <div class="d-flex align-items-center mb-4">
                                        <div class="subscription-icon me-3">
                                            @if(Auth::user()->subscription_tier == 'premium')
                                                <div class="premium-icon-wrapper">
                                                    <i class="fas fa-crown fa-2x"></i>
                                                </div>
                                            @else
                                                <div class="base-icon-wrapper">
                                                    <i class="fas fa-star fa-2x"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div>
                                            <h4 class="mb-0">{{ ucfirst(Auth::user()->subscription_tier) }} Plan</h4>
                                            <p class="text-muted mb-0">{{ ucfirst(Auth::user()->getActiveSubscription()->billing_cycle) }} billing</p>
                                        </div>
                                    </div>

                                    @if(Auth::user()->onTrial())
                                        <div class="trial-info-box mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="fw-bold">Trial Period</span>
                                                <span>{{ Auth::user()->getActiveSubscription()->trial_ends_at->diffInDays(now()) }} days remaining</span>
                                            </div>
                                            <div class="progress" style="height: 8px;">
                                                @php
                                                    $trialDaysTotal = 7; // Assuming 7-day trial
                                                    $trialDaysLeft = Auth::user()->getActiveSubscription()->trial_ends_at->diffInDays(now());
                                                    $trialProgress = 100 - (($trialDaysLeft / $trialDaysTotal) * 100);
                                                @endphp
                                                <div class="progress-bar bg-info" role="progressbar" style="width: {{ $trialProgress }}%"></div>
                                            </div>
                                            <div class="d-flex justify-content-between mt-1">
                                                <small class="text-muted">Started: {{ Auth::user()->getActiveSubscription()->created_at->format('M d') }}</small>
                                                <small class="text-muted">Ends: {{ Auth::user()->getActiveSubscription()->trial_ends_at->format('M d') }}</small>
                                            </div>
                                        </div>
                                    @endif

                                    <div class="subscription-details">
                                        <div class="detail-item">
                                            <div class="detail-label">Price</div>
                                            <div class="detail-value">${{ number_format(Auth::user()->getActiveSubscription()->price, 2) }}/{{ Auth::user()->getActiveSubscription()->billing_cycle == 'monthly' ? 'month' : 'year' }}</div>
                                        </div>

                                        @if(!Auth::user()->onTrial())
                                            <div class="detail-item">
                                                <div class="detail-label">Start Date</div>
                                                <div class="detail-value">{{ Auth::user()->getActiveSubscription()->created_at->format('M d, Y') }}</div>
                                            </div>

                                            <div class="detail-item">
                                                <div class="detail-label">Next Billing</div>
                                                <div class="detail-value">
                                                    @if(Auth::user()->getActiveSubscription()->stripe_status == 'canceled')
                                                        <span class="text-warning">Canceled</span>
                                                    @else
                                                        @php
                                                            // Calculate next billing date based on billing cycle
                                                            $startDate = Auth::user()->getActiveSubscription()->created_at;
                                                            $nextBillingDate = Auth::user()->getActiveSubscription()->billing_cycle == 'monthly'
                                                                ? $startDate->addMonthsNoOverflow(1 + $startDate->diffInMonths(now()))
                                                                : $startDate->addYearsNoOverflow(1 + $startDate->diffInYears(now()));
                                                        @endphp
                                                        {{ $nextBillingDate->format('M d, Y') }}
                                                    @endif
                                                </div>
                                            </div>
                                        @endif

                                        @if(Auth::user()->getActiveSubscription()->stripe_status == 'canceled')
                                            <div class="detail-item">
                                                <div class="detail-label">Access Until</div>
                                                <div class="detail-value">{{ Auth::user()->getActiveSubscription()->ends_at->format('M d, Y') }}</div>
                                            </div>
                                        @endif

                                        <div class="detail-item">
                                            <div class="detail-label">Payment Method</div>
                                            <div class="detail-value">
                                                <i class="fab fa-cc-visa me-1"></i> •••• 4242
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="subscription-actions mt-4">
                                    @if(Auth::user()->onTrial())
                                        <a href="{{ route('subscriptions.checkout', ['tier' => Auth::user()->subscription_tier]) }}" class="btn btn-primary btn-lg mb-3 w-100">
                                            Continue with {{ ucfirst(Auth::user()->subscription_tier) }} (${{ Auth::user()->subscription_tier == 'premium' ? '10' : '5' }}/month)
                                        </a>
                                        @if(Auth::user()->subscription_tier == 'base')
                                            <a href="{{ route('subscriptions.checkout', ['tier' => 'premium']) }}" class="btn btn-outline-primary mb-3 w-100">
                                                Upgrade to Premium ($10/month)
                                            </a>
                                        @endif
                                    @elseif(Auth::user()->getActiveSubscription()->stripe_status == 'active')
                                        @if(Auth::user()->subscription_tier == 'base')
                                            <a href="{{ route('subscriptions.checkout', ['tier' => 'premium']) }}" class="btn btn-primary mb-3 w-100">
                                                Upgrade to Premium ($10/month)
                                            </a>
                                        @endif
                                        <button type="button" class="btn btn-outline-danger mb-3 w-100" data-bs-toggle="modal" data-bs-target="#cancelSubscriptionModal">
                                            <i class="fas fa-times-circle me-2"></i> Cancel Subscription
                                        </button>
                                    @elseif(Auth::user()->getActiveSubscription()->stripe_status == 'canceled')
                                        <a href="{{ route('subscriptions.resume', Auth::user()->getActiveSubscription()->id) }}" class="btn btn-success mb-3 w-100">
                                            <i class="fas fa-redo me-2"></i> Resume Subscription
                                        </a>
                                    @endif

                                    <a href="{{ route('subscriptions.billing-history') }}" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-history me-2"></i> View Billing History
                                    </a>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="subscription-features">
                                    <h6 class="fw-bold mb-3">Plan Features</h6>
                                    <div class="features-list">
                                        @foreach(Auth::user()->getActiveSubscription()->features as $feature => $enabled)
                                            @php
                                                $featureLabels = [
                                                    'secure_login' => 'Secure Login',
                                                    'bank_account_linking' => 'Bank Account Linking',
                                                    'total_balance_view' => 'Total Balance View',
                                                    'balance_toggle' => 'Balance Toggle',
                                                    'financial_bins' => 'Financial Bins',
                                                    'auto_categorization' => 'Auto Categorization',
                                                    'manual_bin_editing' => 'Manual Bin Editing',
                                                    'graphical_insights' => 'Graphical Insights',
                                                    'recent_transactions' => 'Recent Transactions',
                                                    'chatbot_access' => 'Chatbot Access',
                                                    'notifications' => 'Notifications',
                                                    'max_sub_bin_levels' => 'Sub-Bin Levels',
                                                    'crypto_scanner' => 'Crypto Scanner',
                                                    'unlimited_sub_bins' => 'Unlimited Sub-Bins',
                                                    'priority_notifications' => 'Priority Notifications',
                                                    'advanced_ai_suggestions' => 'Advanced AI Suggestions',
                                                ];
                                            @endphp

                                            @if($feature != 'max_sub_bin_levels')
                                                <div class="feature-item {{ $enabled ? 'feature-enabled' : 'feature-disabled' }}">
                                                    <div class="feature-icon">
                                                        @if($enabled)
                                                            <i class="fas fa-check"></i>
                                                        @else
                                                            <i class="fas fa-times"></i>
                                                        @endif
                                                    </div>
                                                    <div class="feature-name">
                                                        {{ $featureLabels[$feature] ?? ucwords(str_replace('_', ' ', $feature)) }}
                                                    </div>
                                                </div>
                                            @else
                                                <div class="feature-item feature-enabled">
                                                    <div class="feature-icon">
                                                        <i class="fas fa-layer-group"></i>
                                                    </div>
                                                    <div class="feature-name">
                                                        {{ $featureLabels[$feature] }}
                                                        <span class="feature-value">
                                                            {{ $enabled == -1 ? 'Unlimited' : $enabled }}
                                                        </span>
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>

                                    <div class="text-center mt-4">
                                        <a href="{{ route('subscriptions.plans') }}" class="btn btn-outline-primary">
                                            <i class="fas fa-exchange-alt me-2"></i> Compare Plans
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <div class="no-subscription-icon">
                                    <i class="fas fa-crown fa-3x"></i>
                                </div>
                            </div>
                            <h4 class="mb-3">No Active Subscription</h4>
                            <p class="text-muted mb-4">Choose a subscription plan to unlock premium features and enhance your financial management experience.</p>
                            <a href="{{ route('subscriptions.plans') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-tag me-2"></i> View Subscription Plans
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            @if(Auth::user()->getActiveSubscription() && Auth::user()->getActiveSubscription()->stripe_status != 'canceled')
                <div class="card mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold" style="color: var(--primary-color);">Billing Cycle</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <p class="mb-3">You are currently on <strong>{{ ucfirst(Auth::user()->getActiveSubscription()->billing_cycle) }}</strong> billing.</p>
                                <p class="text-muted mb-0">Switch to yearly billing to save approximately 17% compared to monthly billing.</p>
                            </div>
                            <div class="col-md-6">
                                <div class="billing-cycle-toggle d-flex justify-content-center mt-3 mt-md-0">
                                    <div class="toggle-container">
                                        <a href="{{ route('subscriptions.update-billing-cycle', ['cycle' => 'monthly']) }}"
                                           class="toggle-option {{ Auth::user()->getActiveSubscription()->billing_cycle == 'monthly' ? 'active' : '' }}">
                                            Monthly
                                        </a>
                                        <a href="{{ route('subscriptions.update-billing-cycle', ['cycle' => 'yearly']) }}"
                                           class="toggle-option {{ Auth::user()->getActiveSubscription()->billing_cycle == 'yearly' ? 'active' : '' }}">
                                            Yearly <span class="savings-badge">Save 17%</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Cancel Subscription Modal -->
<div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-labelledby="cancelSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelSubscriptionModalLabel">Cancel Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('subscriptions.cancel', Auth::user()->getActiveSubscription()->id ?? 0) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <div class="cancel-icon mb-3">
                            <i class="fas fa-hand-paper fa-3x text-warning"></i>
                        </div>
                        <h5>We're sorry to see you go!</h5>
                        <p class="text-muted">Please let us know why you're canceling your subscription:</p>
                    </div>

                    <div class="mb-3">
                        <select class="form-select" name="cancel_reason" required>
                            <option value="">Select a reason...</option>
                            <option value="too_expensive">Too expensive</option>
                            <option value="missing_features">Missing features I need</option>
                            <option value="not_using">Not using it enough</option>
                            <option value="switching">Switching to another service</option>
                            <option value="technical_issues">Technical issues</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="cancel_feedback" class="form-label">Additional feedback (optional)</label>
                        <textarea class="form-control" id="cancel_feedback" name="cancel_feedback" rows="3" placeholder="Please share any additional feedback to help us improve"></textarea>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i> Your subscription will remain active until the end of your current billing period. You will not be charged again after that.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Subscription</button>
                    <button type="submit" class="btn btn-danger">Cancel Subscription</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    /* Subscription Card */
    .subscription-card {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: none;
    }

    /* Subscription Icons */
    .subscription-icon {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .premium-icon-wrapper {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #FFD700, #FFA500);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
    }

    .base-icon-wrapper {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #6c757d, #495057);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 3px 10px rgba(108, 117, 125, 0.3);
    }

    /* Trial Info Box */
    .trial-info-box {
        background-color: rgba(13, 202, 240, 0.1);
        border-radius: 12px;
        padding: 15px;
        border: 1px solid rgba(13, 202, 240, 0.2);
    }

    /* Subscription Details */
    .subscription-details {
        margin-top: 20px;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .detail-label {
        color: #6c757d;
        font-weight: 500;
    }

    .detail-value {
        font-weight: 600;
    }

    /* Features List */
    .features-list {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .feature-item {
        display: flex;
        align-items: center;
        padding: 10px;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .feature-enabled {
        background-color: rgba(25, 135, 84, 0.1);
    }

    .feature-disabled {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .feature-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 0.8rem;
    }

    .feature-enabled .feature-icon {
        background-color: #198754;
        color: white;
    }

    .feature-disabled .feature-icon {
        background-color: #6c757d;
        color: white;
    }

    .feature-value {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-left: 5px;
    }

    /* No Subscription Icon */
    .no-subscription-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e9ecef, #ced4da);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        color: #6c757d;
    }

    /* Billing Cycle Toggle */
    .billing-cycle-toggle {
        margin-top: 20px;
    }

    .toggle-container {
        display: inline-flex;
        background-color: #f8f9fa;
        border-radius: 30px;
        padding: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .toggle-option {
        padding: 8px 16px;
        border-radius: 25px;
        color: #6c757d;
        text-decoration: none;
        transition: all 0.3s ease;
        position: relative;
    }

    .toggle-option.active {
        background-color: var(--primary-color);
        color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .savings-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #ffc107;
        color: #212529;
        font-size: 0.7rem;
        padding: 2px 5px;
        border-radius: 10px;
        font-weight: 600;
    }

    /* Cancel Modal */
    .cancel-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: rgba(255, 193, 7, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }

    /* Responsive Adjustments */
    @media (max-width: 767.98px) {
        .features-list {
            grid-template-columns: 1fr;
        }

        .detail-item {
            flex-direction: column;
        }

        .detail-value {
            margin-top: 5px;
        }
    }
</style>
@endsection
