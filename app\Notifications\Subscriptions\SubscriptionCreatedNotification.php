<?php

namespace App\Notifications\Subscriptions;

use App\Models\Subscription;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class SubscriptionCreatedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_subscription_updates';

    /**
     * The subscription instance.
     *
     * @var \App\Models\Subscription
     */
    protected $subscription;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Subscription  $subscription
     * @return void
     */

    public function __construct(Subscription $subscription)
    {
        $this->subscription = $subscription;

        $this->subject = 'Your PocketWatch Subscription Has Been Created';
        $this->title = 'Subscription Created';
        $this->content = 'Thank you for subscribing to PocketWatch ' . ucfirst($this->subscription->subscription_tier) . '!
                         Your subscription has been successfully created and is now active.';

        $this->detailsTitle = 'Subscription Details';
        $this->details = [
            'Plan' => ucfirst($this->subscription->subscription_tier) . ' Tier',
            'Billing Cycle' => ucfirst($this->subscription->billing_cycle),
            'Price' => $this->formatCurrency($this->subscription->price, $this->subscription->currency),
        ];

        if ($this->subscription->trial_ends_at) {
            $this->details['Free Trial Ends'] = $this->subscription->trial_ends_at->format('F j, Y');
        }

        $this->actionText = 'Manage Subscription';
        $this->actionUrl = url('/subscriptions');

        $this->closing = 'Thank you for choosing PocketWatch. We hope you enjoy all the premium features!';
        $this->signature = 'The PocketWatch Team';
    }

    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);

        return $symbol . number_format($value, 2) . '/' . ($this->subscription->billing_cycle === 'monthly' ? 'month' : 'year');
    }

    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];

        return $symbols[$currency] ?? $currency;
    }
}
