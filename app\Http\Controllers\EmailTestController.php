<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\EmailVerificationService;
use App\Mail\EmailVerificationMail;
use App\Mail\PasswordResetMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class EmailTestController extends Controller
{
    /**
     * Show the email test page
     */
    public function index()
    {
        return view('test-email');
    }

    /**
     * Send test email
     */
    public function sendTestEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'test_type' => 'required|in:verification,password_reset,simple_test',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $email = $request->email;
        $testType = $request->test_type;

        try {
            switch ($testType) {
                case 'verification':
                    return $this->sendVerificationTest($email);
                
                case 'password_reset':
                    return $this->sendPasswordResetTest($email);
                
                case 'simple_test':
                    return $this->sendSimpleTest($email);
                
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid test type'
                    ], 400);
            }
        } catch (\Exception $e) {
            Log::error('Email test failed', [
                'email' => $email,
                'test_type' => $testType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Email test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send verification email test
     */
    private function sendVerificationTest($email)
    {
        // Find or create test user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $user = User::create([
                'name' => 'Email Test User',
                'email' => $email,
                'password' => bcrypt('test123'),
                'country_code' => '+1',
                'phone_number' => '0000000000',
                'subscription_tier' => 'none',
                'email_verified_at' => null,
            ]);
        }

        $emailService = new EmailVerificationService();
        $result = $emailService->sendEmailVerificationCode($user);

        if ($result['success']) {
            $user->refresh();
            return response()->json([
                'success' => true,
                'message' => 'Email verification code sent successfully!',
                'details' => "Code: {$user->email_verification_code}<br>" .
                           "Expires: {$user->email_verification_code_expires_at}<br>" .
                           "Attempts remaining: {$result['attempts_remaining']}"
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification email',
                'error' => $result['message']
            ]);
        }
    }

    /**
     * Send password reset email test
     */
    private function sendPasswordResetTest($email)
    {
        // Find or create test user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $user = User::create([
                'name' => 'Email Test User',
                'email' => $email,
                'password' => bcrypt('test123'),
                'country_code' => '+1',
                'phone_number' => '0000000000',
                'subscription_tier' => 'none',
            ]);
        }

        $emailService = new EmailVerificationService();
        $result = $emailService->sendPasswordResetCode($user);

        if ($result['success']) {
            $user->refresh();
            return response()->json([
                'success' => true,
                'message' => 'Password reset code sent successfully!',
                'details' => "Code: {$user->password_reset_code}<br>" .
                           "Expires: {$user->password_reset_code_expires_at}<br>" .
                           "Attempts remaining: {$result['attempts_remaining']}"
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send password reset email',
                'error' => $result['message']
            ]);
        }
    }

    /**
     * Send simple test email
     */
    private function sendSimpleTest($email)
    {
        try {
            Mail::raw('This is a simple test email from ' . config('app.name') . '. If you receive this, your email configuration is working correctly!', function ($message) use ($email) {
                $message->to($email)
                        ->subject('Test Email from ' . config('app.name'))
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            return response()->json([
                'success' => true,
                'message' => 'Simple test email sent successfully!',
                'details' => "Sent to: {$email}<br>" .
                           "From: " . config('mail.from.address') . "<br>" .
                           "SMTP Host: " . config('mail.mailers.smtp.host')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send simple test email',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check email status
     */
    public function checkEmailStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email address'
            ], 422);
        }

        $email = $request->email;
        $user = User::where('email', $email)->first();

        if (!$user) {
            return response()->json([
                'success' => true,
                'status' => "❌ No user found with email: {$email}<br>" .
                          "📝 You can create a test user by sending a verification email."
            ]);
        }

        $status = "👤 User found: {$user->name}<br>";
        $status .= "📧 Email: {$user->email}<br>";
        $status .= "✉️ Email verified: " . ($user->email_verified_at ? 'YES (' . $user->email_verified_at . ')' : 'NO') . "<br>";
        
        if ($user->email_verification_code) {
            $status .= "🔢 Verification code: {$user->email_verification_code}<br>";
            $status .= "⏰ Code expires: {$user->email_verification_code_expires_at}<br>";
            $status .= "🎯 Verification attempts: " . ($user->email_verification_attempts ?? 0) . "/5<br>";
        } else {
            $status .= "🔢 No pending verification code<br>";
        }

        if ($user->password_reset_code) {
            $status .= "🔐 Reset code: {$user->password_reset_code}<br>";
            $status .= "⏰ Reset expires: {$user->password_reset_code_expires_at}<br>";
            $status .= "🎯 Reset attempts: " . ($user->password_reset_attempts ?? 0) . "/5<br>";
        } else {
            $status .= "🔐 No pending reset code<br>";
        }

        return response()->json([
            'success' => true,
            'status' => $status
        ]);
    }

    /**
     * Test SMTP connection
     */
    public function testConnection()
    {
        try {
            $transport = Mail::getSwiftMailer()->getTransport();
            $transport->start();
            
            return response()->json([
                'success' => true,
                'message' => 'SMTP connection successful!',
                'details' => 'Connected to ' . config('mail.mailers.smtp.host') . ':' . config('mail.mailers.smtp.port')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'SMTP connection failed',
                'error' => $e->getMessage()
            ]);
        }
    }
}
