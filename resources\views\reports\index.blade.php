@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-between align-items-center mb-4">
        <div class="col-auto">
            <h1 class="h3 mb-0">Reports</h1>
            <p class="text-muted">Generate and manage your financial reports</p>
        </div>
        <div class="col-auto">
            <a href="{{ route('reports.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New Report
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            @if($reports->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Format</th>
                                <th>Period</th>
                                <th>Status</th>
                                <th>Last Generated</th>
                                <th>Recurring</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($reports as $report)
                                <tr>
                                    <td>
                                        <a href="{{ route('reports.show', $report) }}">{{ $report->name }}</a>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $report->type == 'transaction' ? 'primary' : ($report->type == 'bin' ? 'success' : 'info') }}">
                                            {{ ucfirst($report->type) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ strtoupper($report->format) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ ucfirst($report->period_type) }}</span>
                                        @if($report->period_type == 'custom')
                                            <br>
                                            <small class="text-muted">
                                                {{ $report->start_date->format('M d, Y') }} - {{ $report->end_date->format('M d, Y') }}
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($report->status == 'completed')
                                            <span class="badge bg-success">Completed</span>
                                        @elseif($report->status == 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($report->status == 'processing')
                                            <span class="badge bg-info">Processing</span>
                                        @else
                                            <span class="badge bg-danger">Failed</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($report->last_generated_at)
                                            {{ $report->last_generated_at->format('M d, Y H:i') }}
                                        @else
                                            <span class="text-muted">Never</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($report->is_recurring)
                                            <span class="badge bg-success">
                                                {{ ucfirst($report->schedule) }}
                                            </span>
                                            <br>
                                            <small class="text-muted">
                                                Next: {{ $report->next_run_at ? $report->next_run_at->format('M d, Y') : 'N/A' }}
                                            </small>
                                        @else
                                            <span class="badge bg-secondary">No</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            @if($report->status == 'completed')
                                                <a href="{{ route('reports.download', $report) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            @endif
                                            <a href="{{ route('reports.show', $report) }}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form action="{{ route('reports.destroy', $report) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this report?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <div class="d-flex justify-content-center mt-4">
                    {{ $reports->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-file-alt fa-4x text-muted"></i>
                    </div>
                    <h4>No Reports Yet</h4>
                    <p class="text-muted">Create your first report to track your financial data</p>
                    <a href="{{ route('reports.create') }}" class="btn btn-primary mt-3">
                        <i class="fas fa-plus me-2"></i>Create New Report
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
