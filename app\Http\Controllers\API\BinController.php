<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Bin;
use App\Notifications\Bins\BinCreatedNotification;
use App\Notifications\Bins\BinDeletedNotification;
use App\Notifications\Bins\BinUpdatedNotification;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BinController extends Controller
{
    /**
     * The subscription service instance.
     *
     * @var \App\Services\SubscriptionService
     */
    protected $subscriptionService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\SubscriptionService  $subscriptionService
     * @return void
     */
    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $bins = $request->user()->bins()->with('subBins')->get();

        // Add remaining bin count for the user
        $remainingBins = $this->subscriptionService->getRemainingBinCount($request->user());

        return response()->json([
            'bins' => $bins,
            'remaining_bins' => $remainingBins,
            'max_bins' => $request->user()->subscription_tier === 'premium' ? 'unlimited' : SubscriptionService::MAX_BINS_BASE_TIER,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if the user can create more bins
        if (!$this->subscriptionService->canCreateBin($request->user())) {
            return response()->json([
                'message' => $this->subscriptionService->getUpgradeMessage('bin'),
                'error' => 'subscription_limit_reached',
                'remaining_bins' => 0,
                'max_bins' => SubscriptionService::MAX_BINS_BASE_TIER,
                'subscription_tier' => $request->user()->subscription_tier,
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:income,expense',
            'description' => 'nullable|string',
            'threshold_max_limit' => 'required|numeric|min:0',
            'threshold_max_warning' => 'nullable|numeric|lt:threshold_max_limit',
            'currency' => 'nullable|string|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $bin = new Bin($request->all());
        $bin->user_id = $request->user()->id;
        $bin->current_amount = 0;
        $bin->save();

        // Send bin created notification
        $request->user()->notify(new BinCreatedNotification($bin));

        // Update user's cumulative balance
        $request->user()->calculateCumulativeBalance();

        // Calculate remaining bins after creation
        $remainingBins = $this->subscriptionService->getRemainingBinCount($request->user());

        return response()->json([
            'message' => 'Bin created successfully',
            'bin' => $bin,
            'remaining_bins' => $remainingBins,
            'max_bins' => $request->user()->subscription_tier === 'premium' ? 'unlimited' : SubscriptionService::MAX_BINS_BASE_TIER,
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, string $id)
    {
        $bin = Bin::with('subBins')->find($id);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        return response()->json([
            'bin' => $bin,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $bin = Bin::find($id);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'type' => 'sometimes|required|string|in:income,expense',
            'description' => 'sometimes|nullable|string',
            'threshold_max_limit' => 'sometimes|required|numeric|min:0',
            'threshold_max_warning' => 'sometimes|nullable|numeric|lt:threshold_max_limit',
            'currency' => 'sometimes|nullable|string|max:10',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Track which fields are being updated
        $updatedFields = [];
        foreach (['name', 'type', 'description', 'threshold_max_limit', 'threshold_max_warning', 'currency', 'is_active'] as $field) {
            if ($request->has($field) && $bin->{$field} != $request->input($field)) {
                $updatedFields[] = $field;
            }
        }

        $bin->update($request->all());

        // Send bin updated notification if fields were actually updated
        if (!empty($updatedFields)) {
            $request->user()->notify(new BinUpdatedNotification($bin, $updatedFields));
        }

        // Update user's cumulative balance if type or amount changed
        if (in_array('type', $updatedFields) || in_array('current_amount', $updatedFields)) {
            $request->user()->calculateCumulativeBalance();
        }

        return response()->json([
            'message' => 'Bin updated successfully',
            'bin' => $bin,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, string $id)
    {
        $bin = Bin::find($id);

        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found',
            ], 404);
        }

        // Check if bin has sub-bins
        $subBinsCount = $bin->subBins()->count();
        if ($subBinsCount > 0) {
            return response()->json([
                'message' => 'Cannot delete bin with existing sub-bins',
                'error' => 'Please delete all sub-bins first before deleting the bin',
                'sub_bins_count' => $subBinsCount,
            ], 422);
        }

        // Check if bin has transactions
        $transactionsCount = $bin->transactions()->count();
        if ($transactionsCount > 0) {
            return response()->json([
                'message' => 'Cannot delete bin with existing transactions',
                'error' => 'Please delete all transactions first before deleting the bin',
                'transactions_count' => $transactionsCount,
            ], 422);
        }

        // Store bin details before deletion for notification and response
        $binName = $bin->name;
        $binType = $bin->type;
        $binAmount = $bin->current_amount;

        // Delete the bin
        $bin->delete();

        // Update user's cumulative balance after deletion
        $request->user()->calculateCumulativeBalance();

        // Send bin deleted notification
        $request->user()->notify(new BinDeletedNotification($binName));

        return response()->json([
            'message' => 'Bin deleted successfully',
            'deleted_bin' => [
                'name' => $binName,
                'type' => $binType,
                'amount' => $binAmount,
            ],
            'user_cumulative_balance' => $request->user()->cumulative_balance,
        ]);
    }

    /**
     * Get all income bins and their income sub-bins for the authenticated user.
     */
    public function incomeBins(Request $request)
    {
        $bins = $request->user()->bins()
            ->where('type', 'income')
            ->with(['subBins' => function($q) {
                $q->where('type', 'income');
            }])
            ->get();
        return response()->json(['bins' => $bins]);
    }

    /**
     * Get all expense bins and their expense sub-bins for the authenticated user.
     */
    public function expenseBins(Request $request)
    {
        $bins = $request->user()->bins()
            ->where('type', 'expense')
            ->with(['subBins' => function($q) {
                $q->where('type', 'expense');
            }])
            ->get();
        return response()->json(['bins' => $bins]);
    }
}
