@extends('admin.layouts.app')

@section('title', 'Search Results')
@section('subtitle', 'Search results for "' . $query . '"')

@section('breadcrumbs')
    <li class="breadcrumb-item">Search</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0 text-gray-800">Search Results</h5>
                        </div>
                        <div class="col-auto">
                            <form action="{{ route('admin.search') }}" method="GET" class="d-flex">
                                <input type="text" name="query" class="form-control me-2" value="{{ $query }}" placeholder="Search again...">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs mb-4" id="searchTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="true">
                                Users <span class="badge bg-primary ms-1">{{ $users->count() }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions" type="button" role="tab" aria-controls="transactions" aria-selected="false">
                                Transactions <span class="badge bg-primary ms-1">{{ $transactions->count() }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="bins-tab" data-bs-toggle="tab" data-bs-target="#bins" type="button" role="tab" aria-controls="bins" aria-selected="false">
                                Bins <span class="badge bg-primary ms-1">{{ $bins->count() }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="subscriptions-tab" data-bs-toggle="tab" data-bs-target="#subscriptions" type="button" role="tab" aria-controls="subscriptions" aria-selected="false">
                                Subscriptions <span class="badge bg-primary ms-1">{{ $subscriptions->count() }}</span>
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="searchTabsContent">
                        <!-- Users Tab -->
                        <div class="tab-pane fade show active" id="users" role="tabpanel" aria-labelledby="users-tab">
                            @if($users->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Status</th>
                                                <th>Subscription</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($users as $user)
                                                <tr>
                                                    <td>{{ $user->id }}</td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="user-avatar me-2">
                                                                {{ substr($user->name, 0, 1) }}
                                                            </div>
                                                            <div>
                                                                {{ $user->name }}
                                                                @if($user->is_admin)
                                                                    <span class="badge badge-soft-primary ms-1">Admin</span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ $user->email }}</td>
                                                    <td>
                                                        @if($user->is_active)
                                                            <span class="badge bg-success">Active</span>
                                                        @else
                                                            <span class="badge bg-danger">Inactive</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $user->subscription_tier === 'premium' ? 'bg-primary' : 'bg-secondary' }}">
                                                            {{ ucfirst($user->subscription_tier) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('admin.users.index', ['search' => $query]) }}" class="btn btn-outline-primary">
                                        View All Matching Users
                                    </a>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> No users found matching "{{ $query }}".
                                </div>
                            @endif
                        </div>
                        
                        <!-- Transactions Tab -->
                        <div class="tab-pane fade" id="transactions" role="tabpanel" aria-labelledby="transactions-tab">
                            @if($transactions->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>User</th>
                                                <th>Description</th>
                                                <th>Amount</th>
                                                <th>Type</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($transactions as $transaction)
                                                <tr>
                                                    <td>{{ $transaction->id }}</td>
                                                    <td>
                                                        <a href="{{ route('admin.users.show', $transaction->user_id) }}">
                                                            {{ $transaction->user->name }}
                                                        </a>
                                                    </td>
                                                    <td>{{ $transaction->description }}</td>
                                                    <td>
                                                        <span class="{{ $transaction->amount >= 0 ? 'text-success' : 'text-danger' }}">
                                                            ${{ number_format(abs($transaction->amount), 2) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">
                                                            {{ ucfirst($transaction->transaction_type) }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $transaction->transaction_date->format('M d, Y') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('admin.transactions.index', ['search' => $query]) }}" class="btn btn-outline-primary">
                                        View All Matching Transactions
                                    </a>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> No transactions found matching "{{ $query }}".
                                </div>
                            @endif
                        </div>
                        
                        <!-- Bins Tab -->
                        <div class="tab-pane fade" id="bins" role="tabpanel" aria-labelledby="bins-tab">
                            @if($bins->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>User</th>
                                                <th>Description</th>
                                                <th>Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($bins as $bin)
                                                <tr>
                                                    <td>{{ $bin->id }}</td>
                                                    <td>{{ $bin->name }}</td>
                                                    <td>
                                                        <a href="{{ route('admin.users.show', $bin->user_id) }}">
                                                            {{ $bin->user->name }}
                                                        </a>
                                                    </td>
                                                    <td>{{ $bin->description }}</td>
                                                    <td>{{ $bin->created_at->format('M d, Y') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('admin.bins.index', ['search' => $query]) }}" class="btn btn-outline-primary">
                                        View All Matching Bins
                                    </a>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> No bins found matching "{{ $query }}".
                                </div>
                            @endif
                        </div>
                        
                        <!-- Subscriptions Tab -->
                        <div class="tab-pane fade" id="subscriptions" role="tabpanel" aria-labelledby="subscriptions-tab">
                            @if($subscriptions->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>User</th>
                                                <th>Plan</th>
                                                <th>Status</th>
                                                <th>Billing</th>
                                                <th>Price</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($subscriptions as $subscription)
                                                <tr>
                                                    <td>{{ $subscription->id }}</td>
                                                    <td>
                                                        <a href="{{ route('admin.users.show', $subscription->user_id) }}">
                                                            {{ $subscription->user->name }}
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $subscription->subscription_tier === 'premium' ? 'bg-primary' : 'bg-secondary' }}">
                                                            {{ ucfirst($subscription->subscription_tier) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if($subscription->stripe_status === 'active')
                                                            <span class="badge bg-success">Active</span>
                                                        @elseif($subscription->stripe_status === 'trialing')
                                                            <span class="badge bg-info">Trial</span>
                                                        @elseif($subscription->stripe_status === 'canceled')
                                                            <span class="badge bg-warning">Canceled</span>
                                                        @else
                                                            <span class="badge bg-danger">{{ ucfirst($subscription->stripe_status) }}</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ ucfirst($subscription->billing_cycle) }}</td>
                                                    <td>${{ number_format($subscription->price, 2) }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('admin.subscriptions.index', ['search' => $query]) }}" class="btn btn-outline-primary">
                                        View All Matching Subscriptions
                                    </a>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> No subscriptions found matching "{{ $query }}".
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
