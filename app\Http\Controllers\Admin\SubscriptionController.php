<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\IdObfuscator;
use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\User;
use App\Notifications\Subscriptions\SubscriptionCanceledNotification;
use App\Notifications\Subscriptions\SubscriptionCreatedNotification;
use App\Notifications\Subscriptions\SubscriptionUpdatedNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Stripe\Exception\ApiErrorException;
use Stripe\StripeClient;

class SubscriptionController extends Controller
{
    /**
     * The Stripe client instance.
     *
     * @var \Stripe\StripeClient
     */
    protected $stripe;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    /**
     * Display all subscription packages.
     *
     * @return \Illuminate\View\View
     */
    public function packages()
    {
        // Get subscription configuration
        $trialDays = config('services.subscription.trial_days', 7);

        // Get pricing information
        $pricing = [
            'base' => [
                'monthly' => config('services.subscription.base_monthly_price', 5.00),
                'yearly' => config('services.subscription.base_yearly_price', 50.00),
            ],
            'premium' => [
                'monthly' => config('services.subscription.premium_monthly_price', 10.00),
                'yearly' => config('services.subscription.premium_yearly_price', 100.00),
            ],
        ];

        // Get features for each tier
        $baseFeatures = $this->getFeatures('base');
        $premiumFeatures = $this->getFeatures('premium');

        return view('admin.subscriptions.packages', compact(
            'trialDays',
            'pricing',
            'baseFeatures',
            'premiumFeatures'
        ));
    }

    /**
     * Display the subscription dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        // Get subscription statistics
        $activeSubscriptions = Subscription::where(function($query) {
            $query->where('stripe_status', 'active')
                  ->orWhere('stripe_status', 'trialing');
        })->count();

        $premiumSubscriptions = Subscription::where('subscription_tier', 'premium')
            ->where(function($query) {
                $query->where('stripe_status', 'active')
                      ->orWhere('stripe_status', 'trialing');
            })->count();

        $trialSubscriptions = Subscription::where('stripe_status', 'trialing')->count();

        $monthlyRevenue = Subscription::where('stripe_status', 'active')->sum('price');

        // Get recent subscriptions
        $recentSubscriptions = Subscription::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get upcoming trial ends
        $upcomingTrialEnds = Subscription::with('user')
            ->where('stripe_status', 'trialing')
            ->where('trial_ends_at', '>', now())
            ->where('trial_ends_at', '<', now()->addDays(7))
            ->orderBy('trial_ends_at', 'asc')
            ->limit(5)
            ->get();

        // Prepare data for growth chart (last 30 days)
        $growthChart = [
            'labels' => [],
            'base' => [],
            'premium' => []
        ];

        for ($i = 30; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('M d');
            $growthChart['labels'][] = $date;

            $dayStart = now()->subDays($i)->startOfDay();
            $dayEnd = now()->subDays($i)->endOfDay();

            // Count base subscriptions created on this day
            $baseCount = Subscription::where('subscription_tier', 'base')
                ->whereBetween('created_at', [$dayStart, $dayEnd])
                ->count();

            // Count premium subscriptions created on this day
            $premiumCount = Subscription::where('subscription_tier', 'premium')
                ->whereBetween('created_at', [$dayStart, $dayEnd])
                ->count();

            $growthChart['base'][] = $baseCount;
            $growthChart['premium'][] = $premiumCount;
        }

        // Prepare data for distribution chart
        $distributionChart = [
            'base_monthly' => Subscription::where('subscription_tier', 'base')
                ->where('billing_cycle', 'monthly')
                ->where('stripe_status', 'active')
                ->count(),

            'base_yearly' => Subscription::where('subscription_tier', 'base')
                ->where('billing_cycle', 'yearly')
                ->where('stripe_status', 'active')
                ->count(),

            'premium_monthly' => Subscription::where('subscription_tier', 'premium')
                ->where('billing_cycle', 'monthly')
                ->where('stripe_status', 'active')
                ->count(),

            'premium_yearly' => Subscription::where('subscription_tier', 'premium')
                ->where('billing_cycle', 'yearly')
                ->where('stripe_status', 'active')
                ->count(),
        ];

        return view('admin.subscriptions.dashboard', compact(
            'activeSubscriptions',
            'premiumSubscriptions',
            'trialSubscriptions',
            'monthlyRevenue',
            'recentSubscriptions',
            'upcomingTrialEnds',
            'growthChart',
            'distributionChart'
        ));
    }

    /**
     * Display a listing of the subscriptions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = Subscription::with('user')
            ->whereHas('user', function($query) {
                $query->where('is_admin', false);
            });

        // Filter by user if provided
        if ($request->has('user_id') && !empty($request->user_id)) {
            $user = User::where('uuid', $request->user_id)->first();
            if ($user) {
                $query->where('user_id', $user->id);
            } else {
                // Try to find by ID (for backward compatibility)
                $deobfuscatedId = IdObfuscator::deobfuscate($request->user_id, 'user');
                if ($deobfuscatedId) {
                    $query->where('user_id', $deobfuscatedId);
                } else {
                    // If still not found, try with the original ID
                    $query->where('user_id', $request->user_id);
                }
            }
        }

        // Filter by status if provided
        if ($request->has('status') && !empty($request->status)) {
            $query->where('stripe_status', $request->status);
        }

        // Filter by tier if provided
        if ($request->has('tier') && !empty($request->tier)) {
            $query->where('subscription_tier', $request->tier);
        }

        // Filter by billing cycle if provided
        if ($request->has('billing_cycle') && !empty($request->billing_cycle)) {
            $query->where('billing_cycle', $request->billing_cycle);
        }

        // Search by name or Stripe ID
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('stripe_id', 'like', "%{$search}%");
            });
        }

        // Sort by column
        $sortColumn = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $allowedColumns = ['id', 'name', 'stripe_status', 'subscription_tier', 'billing_cycle', 'price', 'created_at', 'trial_ends_at', 'ends_at'];

        if (in_array($sortColumn, $allowedColumns)) {
            $query->orderBy($sortColumn, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $subscriptions = $query->paginate(15)->withQueryString();
        $users = User::where('is_admin', false)->orderBy('name')->get(['id', 'uuid', 'name', 'email', 'is_admin']);
        $statusOptions = ['active', 'trialing', 'past_due', 'canceled', 'unpaid', 'incomplete', 'incomplete_expired', 'pending'];
        $tierOptions = ['trial', 'base', 'premium'];
        $billingCycleOptions = ['monthly', 'yearly'];

        return view('admin.subscriptions.index', compact(
            'subscriptions',
            'users',
            'statusOptions',
            'tierOptions',
            'billingCycleOptions'
        ));
    }

    /**
     * Show the form for creating a new subscription.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $users = User::where('is_admin', false)->orderBy('name')->get(['id', 'uuid', 'name', 'email']);
        $tierOptions = ['trial', 'base', 'premium'];
        $billingCycleOptions = ['monthly', 'yearly'];

        return view('admin.subscriptions.create', compact('users', 'tierOptions', 'billingCycleOptions'));
    }

    /**
     * Store a newly created subscription in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|string',
            'subscription_tier' => 'required|string|in:trial,base,premium',
            'billing_cycle' => 'required|string|in:monthly,yearly',
            'trial_days' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $user = User::where('uuid', $request->user_id)->first();

        if (!$user) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($request->user_id, 'user');
            if ($deobfuscatedId) {
                $user = User::find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$user) {
                $user = User::find($request->user_id);
            }

            if (!$user) {
                return redirect()->back()
                    ->with('error', 'User not found.')
                    ->withInput();
            }
        }

        // Prevent creating subscriptions for admin users
        if ($user->is_admin) {
            return redirect()->back()
                ->with('error', 'Admin users do not need subscriptions. Please select a non-admin user.')
                ->withInput();
        }

        $subscriptionTier = $request->subscription_tier;
        $billingCycle = $request->billing_cycle;
        $trialDays = $request->trial_days ?? config('services.subscription.trial_days', 7);

        try {
            // Check if user already has an active subscription
            $activeSubscription = $user->subscriptions()
                ->where(function($query) {
                    $query->whereNull('ends_at')
                          ->orWhere('ends_at', '>', now());
                })
                ->where('stripe_status', '!=', 'canceled')
                ->first();

            if ($activeSubscription) {
                return redirect()->back()
                    ->with('error', 'User already has an active subscription.')
                    ->withInput();
            }

            // Get or create Stripe customer
            $stripeCustomerId = $user->stripe_id;

            if (!$stripeCustomerId) {
                $customer = $this->stripe->customers->create([
                    'email' => $user->email,
                    'name' => $user->name,
                    'metadata' => [
                        'user_id' => $user->id,
                    ],
                ]);

                $stripeCustomerId = $customer->id;

                // Save Stripe customer ID to user
                $user->stripe_id = $stripeCustomerId;
                $user->save();
            }

            // Get price ID based on tier and billing cycle
            $priceId = $this->getPriceId($subscriptionTier, $billingCycle);

            // Create subscription with trial
            $stripeSubscription = $this->stripe->subscriptions->create([
                'customer' => $stripeCustomerId,
                'items' => [
                    ['price' => $priceId],
                ],
                'trial_period_days' => $trialDays,
                'metadata' => [
                    'user_id' => $user->id,
                    'subscription_tier' => $subscriptionTier,
                    'billing_cycle' => $billingCycle,
                    'created_by_admin' => true,
                ],
            ]);

            // Create subscription record
            $subscription = new Subscription([
                'user_id' => $user->id,
                'name' => ucfirst($subscriptionTier) . ' ' . ucfirst($billingCycle),
                'stripe_id' => $stripeSubscription->id,
                'stripe_status' => $stripeSubscription->status,
                'stripe_price' => $priceId,
                'subscription_tier' => $subscriptionTier,
                'billing_cycle' => $billingCycle,
                'price' => $this->getPrice($subscriptionTier, $billingCycle),
                'currency' => 'USD',
                'features' => $this->getFeatures($subscriptionTier),
                'trial_ends_at' => now()->addDays($trialDays),
            ]);

            $subscription->save();

            // Update user's subscription tier
            $user->subscription_tier = $subscriptionTier;
            $user->save();

            // Send subscription created notification
            if ($request->has('send_notification') && $request->send_notification) {
                $user->notify(new SubscriptionCreatedNotification($subscription));
            }

            return redirect()->route('admin.subscriptions.show', $subscription->uuid)
                ->with('success', 'Subscription created successfully!');

        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error processing with Stripe: ' . $e->getMessage())
                ->withInput();
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error creating the subscription: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified subscription.
     *
     * @param  string  $uuid
     * @return \Illuminate\View\View
     */
    public function show($uuid)
    {
        $subscription = Subscription::with('user')->where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::with('user')->find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$subscription) {
                $subscription = Subscription::with('user')->find($uuid);
            }

            if (!$subscription) {
                abort(404, 'Subscription not found');
            }
        }

        // Get Stripe subscription details if available
        $stripeSubscription = null;
        $invoices = [];

        if ($subscription->stripe_id) {
            try {
                $stripeSubscription = $this->stripe->subscriptions->retrieve(
                    $subscription->stripe_id,
                    ['expand' => ['default_payment_method', 'latest_invoice.payment_intent']]
                );

                // Get invoices for this subscription
                $invoices = $this->stripe->invoices->all([
                    'subscription' => $subscription->stripe_id,
                    'limit' => 5,
                ]);
            } catch (ApiErrorException $e) {
                Log::error('Stripe error: ' . $e->getMessage());
            }
        }

        return view('admin.subscriptions.show', compact('subscription', 'stripeSubscription', 'invoices'));
    }

    /**
     * Show the form for editing the specified subscription.
     *
     * @param  string  $uuid
     * @return \Illuminate\View\View
     */
    public function edit($uuid)
    {
        $subscription = Subscription::with('user')->where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::with('user')->find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$subscription) {
                $subscription = Subscription::with('user')->find($uuid);
            }

            if (!$subscription) {
                abort(404, 'Subscription not found');
            }
        }
        $tierOptions = ['trial', 'base', 'premium'];
        $billingCycleOptions = ['monthly', 'yearly'];

        return view('admin.subscriptions.edit', compact('subscription', 'tierOptions', 'billingCycleOptions'));
    }

    /**
     * Update the specified subscription in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $uuid)
    {
        $validator = Validator::make($request->all(), [
            'subscription_tier' => 'required|string|in:trial,base,premium',
            'billing_cycle' => 'required|string|in:monthly,yearly',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$subscription) {
                $subscription = Subscription::find($uuid);
            }

            if (!$subscription) {
                abort(404, 'Subscription not found');
            }
        }
        $subscriptionTier = $request->subscription_tier;
        $billingCycle = $request->billing_cycle;

        // Check if anything changed
        if ($subscription->subscription_tier === $subscriptionTier &&
            $subscription->billing_cycle === $billingCycle) {
            return redirect()->route('admin.subscriptions.show', $subscription->uuid)
                ->with('info', 'No changes were made to the subscription.');
        }

        try {
            // Get new price ID
            $newPriceId = $this->getPriceId($subscriptionTier, $billingCycle);

            // Update subscription in Stripe if it has a Stripe ID
            if ($subscription->stripe_id) {
                $this->stripe->subscriptions->update($subscription->stripe_id, [
                    'items' => [
                        [
                            'id' => $this->getSubscriptionItemId($subscription->stripe_id),
                            'price' => $newPriceId,
                        ],
                    ],
                    'proration_behavior' => 'create_prorations',
                    'metadata' => [
                        'subscription_tier' => $subscriptionTier,
                        'billing_cycle' => $billingCycle,
                        'updated_by_admin' => true,
                    ],
                ]);
            }

            // Update local subscription record
            $subscription->name = ucfirst($subscriptionTier) . ' ' . ucfirst($billingCycle);
            $subscription->subscription_tier = $subscriptionTier;
            $subscription->billing_cycle = $billingCycle;
            $subscription->stripe_price = $newPriceId;
            $subscription->price = $this->getPrice($subscriptionTier, $billingCycle);
            $subscription->features = $this->getFeatures($subscriptionTier);
            $subscription->save();

            // Update user's subscription tier
            $user = User::find($subscription->user_id);
            if ($user) {
                $user->subscription_tier = $subscriptionTier;
                $user->save();

                // Send notification
                if ($request->has('send_notification') && $request->send_notification) {
                    $user->notify(new SubscriptionUpdatedNotification($subscription));
                }
            }

            return redirect()->route('admin.subscriptions.show', $subscription->uuid)
                ->with('success', 'Subscription updated successfully!');

        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error updating the subscription in Stripe: ' . $e->getMessage())
                ->withInput();
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error updating the subscription: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Cancel the specified subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $uuid)
    {
        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$subscription) {
                $subscription = Subscription::find($uuid);
            }

            if (!$subscription) {
                abort(404, 'Subscription not found');
            }
        }

        // Only allow canceling if subscription is active
        if ($subscription->stripe_status !== 'active' && $subscription->stripe_status !== 'trialing') {
            return redirect()->back()
                ->with('error', 'Cannot cancel inactive subscription.');
        }

        try {
            // Cancel subscription in Stripe if it has a Stripe ID
            if ($subscription->stripe_id) {
                $this->stripe->subscriptions->update($subscription->stripe_id, [
                    'cancel_at_period_end' => true,
                    'metadata' => [
                        'cancel_reason' => $request->cancel_reason ?? 'Canceled by admin',
                        'canceled_by_admin' => true,
                    ],
                ]);

                // Get the subscription end date from Stripe
                $stripeSubscription = $this->stripe->subscriptions->retrieve($subscription->stripe_id);
                $subscription->ends_at = now()->timestamp($stripeSubscription->current_period_end);
            } else {
                // If no Stripe ID, just set ends_at to now
                $subscription->ends_at = now();
            }

            // Update local subscription record
            $subscription->stripe_status = 'canceled';
            $subscription->save();

            // Send cancellation notification
            if ($request->has('send_notification') && $request->send_notification) {
                $user = User::find($subscription->user_id);
                if ($user) {
                    $user->notify(new SubscriptionCanceledNotification($subscription));
                }
            }

            return redirect()->route('admin.subscriptions.show', $subscription->uuid)
                ->with('success', 'Subscription has been canceled successfully.');

        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error canceling the subscription in Stripe: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error canceling the subscription: ' . $e->getMessage());
        }
    }

    /**
     * Resume a canceled subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resume(Request $request, $uuid)
    {
        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$subscription) {
                $subscription = Subscription::find($uuid);
            }

            if (!$subscription) {
                abort(404, 'Subscription not found');
            }
        }

        // Only allow resuming if subscription is canceled but not ended
        if ($subscription->stripe_status !== 'active' || !$subscription->ends_at || $subscription->ends_at->isPast()) {
            return redirect()->back()
                ->with('error', 'This subscription cannot be resumed.');
        }

        try {
            // Resume subscription in Stripe if it has a Stripe ID
            if ($subscription->stripe_id) {
                $this->stripe->subscriptions->update($subscription->stripe_id, [
                    'cancel_at_period_end' => false,
                    'metadata' => [
                        'resumed_by_admin' => true,
                    ],
                ]);
            }

            // Update local subscription record
            $subscription->stripe_status = 'active';
            $subscription->ends_at = null;
            $subscription->save();

            // Send notification
            if ($request->has('send_notification') && $request->send_notification) {
                $user = User::find($subscription->user_id);
                if ($user) {
                    $user->notify(new SubscriptionUpdatedNotification($subscription));
                }
            }

            return redirect()->route('admin.subscriptions.show', $subscription->uuid)
                ->with('success', 'Subscription has been resumed successfully.');

        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error resuming the subscription in Stripe: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error resuming the subscription: ' . $e->getMessage());
        }
    }

    /**
     * Pause a subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function pause(Request $request, $uuid)
    {
        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$subscription) {
                $subscription = Subscription::find($uuid);
            }

            if (!$subscription) {
                abort(404, 'Subscription not found');
            }
        }

        // Only allow pausing if subscription is active
        if ($subscription->stripe_status !== 'active' && $subscription->stripe_status !== 'trialing') {
            return redirect()->back()
                ->with('error', 'Cannot pause inactive subscription.');
        }

        try {
            // Pause subscription in Stripe if it has a Stripe ID
            if ($subscription->stripe_id) {
                $this->stripe->subscriptions->update($subscription->stripe_id, [
                    'pause_collection' => [
                        'behavior' => 'void',
                    ],
                    'metadata' => [
                        'paused_by_admin' => true,
                        'pause_reason' => $request->pause_reason ?? 'Paused by admin',
                    ],
                ]);

                // Get the updated subscription from Stripe
                $stripeSubscription = $this->stripe->subscriptions->retrieve($subscription->stripe_id);
                $subscription->stripe_status = $stripeSubscription->status;
            } else {
                // If no Stripe ID, just set status to paused
                $subscription->stripe_status = 'paused';
            }

            // Update local subscription record
            $subscription->save();

            // Send notification
            if ($request->has('send_notification') && $request->send_notification) {
                $user = User::find($subscription->user_id);
                if ($user) {
                    $user->notify(new SubscriptionUpdatedNotification($subscription));
                }
            }

            return redirect()->route('admin.subscriptions.show', $subscription->uuid)
                ->with('success', 'Subscription has been paused successfully.');

        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error pausing the subscription in Stripe: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error pausing the subscription: ' . $e->getMessage());
        }
    }

    /**
     * Unpause a subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function unpause(Request $request, $uuid)
    {
        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$subscription) {
                $subscription = Subscription::find($uuid);
            }

            if (!$subscription) {
                abort(404, 'Subscription not found');
            }
        }

        // Only allow unpausing if subscription is paused
        if ($subscription->stripe_status !== 'paused') {
            return redirect()->back()
                ->with('error', 'This subscription is not paused.');
        }

        try {
            // Unpause subscription in Stripe if it has a Stripe ID
            if ($subscription->stripe_id) {
                $this->stripe->subscriptions->update($subscription->stripe_id, [
                    'pause_collection' => '',
                    'metadata' => [
                        'unpaused_by_admin' => true,
                    ],
                ]);

                // Get the updated subscription from Stripe
                $stripeSubscription = $this->stripe->subscriptions->retrieve($subscription->stripe_id);
                $subscription->stripe_status = $stripeSubscription->status;
            } else {
                // If no Stripe ID, just set status to active
                $subscription->stripe_status = 'active';
            }

            // Update local subscription record
            $subscription->save();

            // Send notification
            if ($request->has('send_notification') && $request->send_notification) {
                $user = User::find($subscription->user_id);
                if ($user) {
                    $user->notify(new SubscriptionUpdatedNotification($subscription));
                }
            }

            return redirect()->route('admin.subscriptions.show', $subscription->uuid)
                ->with('success', 'Subscription has been unpaused successfully.');

        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error unpausing the subscription in Stripe: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error unpausing the subscription: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified subscription from storage.
     *
     * @param  string  $uuid
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($uuid)
    {
        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }

            // If still not found, try with the original ID
            if (!$subscription) {
                $subscription = Subscription::find($uuid);
            }

            if (!$subscription) {
                abort(404, 'Subscription not found');
            }
        }

        try {
            // Cancel subscription in Stripe if it has a Stripe ID and is active
            if ($subscription->stripe_id &&
                ($subscription->stripe_status === 'active' || $subscription->stripe_status === 'trialing')) {
                $this->stripe->subscriptions->cancel($subscription->stripe_id, [
                    'invoice_now' => false,
                    'prorate' => true,
                ]);
            }

            // Delete the subscription record
            $subscription->delete();

            // Update user's subscription tier to base
            $user = User::find($subscription->user_id);
            if ($user && $user->subscription_tier !== 'base') {
                $user->subscription_tier = 'base';
                $user->save();
            }

            return redirect()->route('admin.subscriptions.index')
                ->with('success', 'Subscription has been deleted successfully.');

        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error deleting the subscription in Stripe: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'There was an error deleting the subscription: ' . $e->getMessage());
        }
    }

    /**
     * Get the subscription item ID.
     *
     * @param  string  $subscriptionId
     * @return string
     */
    protected function getSubscriptionItemId($subscriptionId)
    {
        $subscription = $this->stripe->subscriptions->retrieve($subscriptionId, [
            'expand' => ['items'],
        ]);

        return $subscription->items->data[0]->id;
    }

    /**
     * Get the price ID based on tier and billing cycle.
     *
     * @param  string  $tier
     * @param  string  $billingCycle
     * @return string
     */
    protected function getPriceId($tier, $billingCycle)
    {
        $prices = [
            'base' => [
                'monthly' => config('services.stripe.prices.base_monthly'),
                'yearly' => config('services.stripe.prices.base_yearly'),
            ],
            'premium' => [
                'monthly' => config('services.stripe.prices.premium_monthly'),
                'yearly' => config('services.stripe.prices.premium_yearly'),
            ],
        ];

        return $prices[$tier][$billingCycle];
    }

    /**
     * Get the price based on tier and billing cycle.
     *
     * @param  string  $tier
     * @param  string  $billingCycle
     * @return float
     */
    protected function getPrice($tier, $billingCycle)
    {
        $prices = [
            'base' => [
                'monthly' => config('services.subscription.base_monthly_price', 5.00),
                'yearly' => config('services.subscription.base_yearly_price', 50.00),
            ],
            'premium' => [
                'monthly' => config('services.subscription.premium_monthly_price', 10.00),
                'yearly' => config('services.subscription.premium_yearly_price', 100.00),
            ],
        ];

        return $prices[$tier][$billingCycle];
    }

    /**
     * Get the features based on tier.
     *
     * @param  string  $tier
     * @return array
     */
    protected function getFeatures($tier)
    {
        $features = [
            'base' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => 3,
                'crypto_scanner' => false,
                'unlimited_sub_bins' => false,
                'priority_notifications' => false,
                'advanced_ai_suggestions' => false,
            ],
            'premium' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => -1, // Unlimited
                'crypto_scanner' => true,
                'unlimited_sub_bins' => true,
                'priority_notifications' => true,
                'advanced_ai_suggestions' => true,
            ],
        ];

        return $features[$tier] ?? $features['base'];
    }
}
