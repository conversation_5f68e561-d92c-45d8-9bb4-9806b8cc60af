<?php

namespace App\Services;

use Twi<PERSON>\Rest\Client;
use Twilio\Exceptions\TwilioException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class TwilioService
{
    protected $client;
    protected $from;
    protected $verifySid;

    public function __construct()
    {
        $this->client = new Client(
            config('services.twilio.sid'),
            config('services.twilio.token')
        );
        $this->from = config('services.twilio.from');
        $this->verifySid = config('services.twilio.verify_sid');
    }

    /**
     * Send verification code using Twilio Verify Service
     */
    public function sendVerificationCode(string $phoneNumber): array
    {
        try {
            // Rate limiting: max 3 attempts per phone number per hour
            $cacheKey = "sms_attempts_{$phoneNumber}";
            $attempts = Cache::get($cacheKey, 0);
            
            if ($attempts >= 3) {
                return [
                    'success' => false,
                    'message' => 'Too many attempts. Please try again later.',
                    'error_code' => 'RATE_LIMIT_EXCEEDED'
                ];
            }

            // Format phone number (ensure it starts with +)
            $formattedPhone = $this->formatPhoneNumber($phoneNumber);

            if ($this->verifySid) {
                // Use Twilio Verify Service (recommended)
                $verification = $this->client->verify->v2
                    ->services($this->verifySid)
                    ->verifications
                    ->create($formattedPhone, 'sms');

                $result = [
                    'success' => true,
                    'message' => 'Verification code sent successfully',
                    'sid' => $verification->sid,
                    'status' => $verification->status,
                    'to' => $verification->to,
                    'channel' => $verification->channel,
                    'valid' => $verification->valid,
                    'lookup' => $verification->lookup ?? null
                ];
            } else {
                // Fallback to regular SMS
                $code = $this->generateVerificationCode();
                $message = "Your PocketWatch verification code is: {$code}. This code expires in 10 minutes.";
                
                $sms = $this->client->messages->create(
                    $formattedPhone,
                    [
                        'from' => $this->from,
                        'body' => $message
                    ]
                );

                // Store code in cache for verification (10 minutes)
                Cache::put("verification_code_{$formattedPhone}", $code, 600);

                $result = [
                    'success' => true,
                    'message' => 'Verification code sent successfully',
                    'sid' => $sms->sid,
                    'status' => $sms->status,
                    'to' => $sms->to,
                    'from' => $sms->from,
                    'price' => $sms->price,
                    'price_unit' => $sms->priceUnit
                ];
            }

            // Increment attempt counter
            Cache::put($cacheKey, $attempts + 1, 3600); // 1 hour

            Log::info('SMS verification code sent', [
                'phone' => $formattedPhone,
                'sid' => $result['sid'],
                'status' => $result['status']
            ]);

            return $result;

        } catch (TwilioException $e) {
            Log::error('Twilio SMS sending failed', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send verification code',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        } catch (Exception $e) {
            Log::error('SMS sending failed', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send verification code',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify code using Twilio Verify Service
     */
    public function verifyCode(string $phoneNumber, string $code): array
    {
        try {
            $formattedPhone = $this->formatPhoneNumber($phoneNumber);

            if ($this->verifySid) {
                // Use Twilio Verify Service
                $verificationCheck = $this->client->verify->v2
                    ->services($this->verifySid)
                    ->verificationChecks
                    ->create([
                        'to' => $formattedPhone,
                        'code' => $code
                    ]);

                $result = [
                    'success' => $verificationCheck->status === 'approved',
                    'status' => $verificationCheck->status,
                    'to' => $verificationCheck->to,
                    'channel' => $verificationCheck->channel,
                    'valid' => $verificationCheck->valid,
                    'sid' => $verificationCheck->sid
                ];

                if ($result['success']) {
                    $result['message'] = 'Phone number verified successfully';
                } else {
                    $result['message'] = 'Invalid verification code';
                }
            } else {
                // Fallback verification using cached code
                $cachedCode = Cache::get("verification_code_{$formattedPhone}");
                
                if (!$cachedCode) {
                    return [
                        'success' => false,
                        'message' => 'Verification code expired or not found',
                        'error_code' => 'CODE_EXPIRED'
                    ];
                }

                if ($cachedCode === $code) {
                    // Remove the code after successful verification
                    Cache::forget("verification_code_{$formattedPhone}");
                    
                    $result = [
                        'success' => true,
                        'message' => 'Phone number verified successfully',
                        'status' => 'approved',
                        'to' => $formattedPhone
                    ];
                } else {
                    $result = [
                        'success' => false,
                        'message' => 'Invalid verification code',
                        'status' => 'denied',
                        'to' => $formattedPhone
                    ];
                }
            }

            Log::info('SMS verification attempted', [
                'phone' => $formattedPhone,
                'success' => $result['success'],
                'status' => $result['status']
            ]);

            return $result;

        } catch (TwilioException $e) {
            Log::error('Twilio verification failed', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);

            return [
                'success' => false,
                'message' => 'Verification failed',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        } catch (Exception $e) {
            Log::error('Verification failed', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Verification failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send custom SMS message
     */
    public function sendSMS(string $phoneNumber, string $message): array
    {
        try {
            $formattedPhone = $this->formatPhoneNumber($phoneNumber);

            $sms = $this->client->messages->create(
                $formattedPhone,
                [
                    'from' => $this->from,
                    'body' => $message
                ]
            );

            Log::info('SMS sent successfully', [
                'phone' => $formattedPhone,
                'sid' => $sms->sid,
                'status' => $sms->status
            ]);

            return [
                'success' => true,
                'message' => 'SMS sent successfully',
                'sid' => $sms->sid,
                'status' => $sms->status,
                'to' => $sms->to,
                'from' => $sms->from,
                'price' => $sms->price,
                'price_unit' => $sms->priceUnit,
                'date_sent' => $sms->dateSent
            ];

        } catch (TwilioException $e) {
            Log::error('Twilio SMS failed', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send SMS',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        } catch (Exception $e) {
            Log::error('SMS failed', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send SMS',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get SMS delivery status
     */
    public function getSMSStatus(string $messageSid): array
    {
        try {
            $message = $this->client->messages($messageSid)->fetch();

            return [
                'success' => true,
                'sid' => $message->sid,
                'status' => $message->status,
                'to' => $message->to,
                'from' => $message->from,
                'body' => $message->body,
                'price' => $message->price,
                'price_unit' => $message->priceUnit,
                'date_sent' => $message->dateSent,
                'date_updated' => $message->dateUpdated,
                'error_code' => $message->errorCode,
                'error_message' => $message->errorMessage
            ];

        } catch (TwilioException $e) {
            return [
                'success' => false,
                'message' => 'Failed to get SMS status',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        }
    }

    /**
     * Test Twilio connection
     */
    public function testConnection(): array
    {
        try {
            // Try to fetch account information
            $account = $this->client->api->v2010->accounts(config('services.twilio.sid'))->fetch();

            return [
                'success' => true,
                'message' => 'Twilio connection successful',
                'account_sid' => $account->sid,
                'friendly_name' => $account->friendlyName,
                'status' => $account->status,
                'type' => $account->type
            ];

        } catch (TwilioException $e) {
            return [
                'success' => false,
                'message' => 'Twilio connection failed',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        }
    }

    /**
     * Format phone number to E.164 format
     */
    private function formatPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Add country code if not present (assuming US +1)
        if (strlen($cleaned) === 10) {
            $cleaned = '1' . $cleaned;
        }
        
        // Add + prefix
        return '+' . $cleaned;
    }

    /**
     * Generate random verification code
     */
    private function generateVerificationCode(int $length = 6): string
    {
        return str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }
}
