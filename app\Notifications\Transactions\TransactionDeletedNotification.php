<?php

namespace App\Notifications\Transactions;

use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class TransactionDeletedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The transaction amount.
     *
     * @var float
     */
    protected $amount;

    /**
     * The transaction currency.
     *
     * @var string
     */
    protected $currency;

    /**
     * The transaction description.
     *
     * @var string
     */
    protected $description;

    /**
     * The bin name.
     *
     * @var string
     */
    protected $binName;

    /**
     * The timestamp when the transaction was deleted.
     *
     * @var \Illuminate\Support\Carbon
     */
    protected $timestamp;

    /**
     * Create a new notification instance.
     *
     * @param  float  $amount
     * @param  string  $currency
     * @param  string  $description
     * @param  string  $binName
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_transaction_operations';

    public function __construct($amount, $currency, $description, $binName = null)
    {
        $this->amount = $amount;
        $this->currency = $currency;
        $this->description = $description;
        $this->binName = $binName;
        $this->timestamp = now();

        $this->subject = 'Transaction Deleted';
        $this->title = 'Transaction Deleted';
        $this->content = 'A transaction has been deleted from your PocketWatch account. If you deleted this transaction, 
                         no further action is required.';
        
        $this->detailsTitle = 'Deleted Transaction Details';
        $this->details = [
            'Amount' => $this->formatCurrency($this->amount, $this->currency),
            'Description' => $this->description ?? 'No description',
            'Date & Time' => $this->timestamp->format('F j, Y, g:i a'),
        ];
        
        if ($this->binName) {
            $this->details['Bin'] = $this->binName;
        }
        
        $this->actionText = 'View All Transactions';
        $this->actionUrl = url('/transactions');
        
        $this->closing = 'If you did not delete this transaction, please contact our support team immediately.';
        $this->signature = 'The PocketWatch Team';
    }
    
    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);
        
        return $symbol . number_format($value, 2);
    }
    
    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];
        
        return $symbols[$currency] ?? $currency;
    }
}
