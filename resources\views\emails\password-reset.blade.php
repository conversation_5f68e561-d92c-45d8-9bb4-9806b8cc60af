<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
        }
        .reset-code {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 8px;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 30px 0;
            font-family: 'Courier New', monospace;
        }
        .message {
            font-size: 16px;
            line-height: 1.6;
            color: #4a5568;
            margin-bottom: 20px;
        }
        .warning {
            background: #fee2e2;
            border: 1px solid #fca5a5;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #991b1b;
        }
        .security-notice {
            background: #eff6ff;
            border: 1px solid #93c5fd;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #1e40af;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #718096;
            text-align: center;
        }
        .button {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 0;
        }
        .highlight {
            color: #f5576c;
            font-weight: 600;
        }
        .steps {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
        }
        .step::before {
            content: "→";
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔐 {{ config('app.name') }}</div>
            <h1 class="title">Reset Your Password</h1>
        </div>

        <div class="message">
            Hello <strong>{{ $user->name }}</strong>,
        </div>

        <div class="message">
            We received a request to reset your password for your {{ config('app.name') }} account. Use the verification code below to proceed with resetting your password:
        </div>

        <div class="reset-code">
            {{ $resetCode }}
        </div>

        <div class="message">
            Enter this <span class="highlight">6-digit code</span> in the password reset screen to create a new password. This code will expire in <span class="highlight">{{ $expiryMinutes }} minutes</span>.
        </div>

        <div class="steps">
            <h3 style="margin-top: 0; color: #2d3748;">How to reset your password:</h3>
            <div class="step">Enter the 6-digit code above</div>
            <div class="step">Create a new secure password</div>
            <div class="step">Confirm your new password</div>
            <div class="step">Log in with your new credentials</div>
        </div>

        <div class="warning">
            <strong>🚨 Important Security Information:</strong><br>
            • This code expires in {{ $expiryMinutes }} minutes<br>
            • You have {{ 5 - $attempts }} reset attempts remaining<br>
            • If you didn't request this reset, please secure your account immediately<br>
            • Never share this code with anyone
        </div>

        <div class="security-notice">
            <strong>🛡️ Didn't request this reset?</strong><br>
            If you didn't request a password reset, someone may be trying to access your account. Please:
            <ul style="margin: 10px 0;">
                <li>Ignore this email (the code will expire automatically)</li>
                <li>Check your account for any suspicious activity</li>
                <li>Consider changing your password as a precaution</li>
                <li>Contact our support team if you have concerns</li>
            </ul>
        </div>

        <div class="message">
            <strong>Account Security Tips:</strong>
            <ul>
                <li>🔒 Use a strong, unique password</li>
                <li>🔄 Don't reuse passwords from other accounts</li>
                <li>📱 Consider enabling two-factor authentication</li>
                <li>🚫 Never share your login credentials</li>
            </ul>
        </div>

        <div class="footer">
            <p>
                <strong>{{ config('app.name') }} Security Team</strong><br>
                Protecting Your Financial Data
            </p>
            <p>
                If you need assistance, please contact our support team.<br>
                This email was sent to {{ $user->email }}
            </p>
            <p style="font-size: 12px; color: #a0aec0;">
                © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.<br>
                This is an automated security email. Please do not reply.
            </p>
        </div>
    </div>
</body>
</html>
