<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\AvatarHelper;

class CleanupAvatars extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'avatars:cleanup {--dry-run : Show what would be cleaned without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up invalid avatar paths from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting avatar cleanup...');

        if ($this->option('dry-run')) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $cleaned = AvatarHelper::cleanupInvalidAvatars();

        if ($cleaned > 0) {
            $this->info("Cleaned up {$cleaned} invalid avatar paths.");
        } else {
            $this->info('No invalid avatar paths found.');
        }

        $this->info('Avatar cleanup completed!');
    }
}
