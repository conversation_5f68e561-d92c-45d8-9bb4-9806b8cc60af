<?php

namespace App\Providers;

use App\Helpers\IdObfuscator;
use App\Models\User;
use App\Observers\UserObserver;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register subscription configuration
        $this->app['config']->set('services.subscription', [
            'trial_days' => (int) env('SUBSCRIPTION_TRIAL_DAYS', 7),
            'base_monthly_price' => (float) env('SUBSCRIPTION_BASE_MONTHLY_PRICE', 5.00),
            'base_yearly_price' => (float) env('SUBSCRIPTION_BASE_YEARLY_PRICE', 50.00),
            'premium_monthly_price' => (float) env('SUBSCRIPTION_PREMIUM_MONTHLY_PRICE', 10.00),
            'premium_yearly_price' => (float) env('SUBSCRIPTION_PREMIUM_YEARLY_PRICE', 100.00),
        ]);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Use Bootstrap 5 for pagination
        Paginator::useBootstrapFive();

        // Register observers
        User::observe(UserObserver::class);

        // Register Blade directives for ID obfuscation
        Blade::directive('obfuscateId', function ($expression) {
            return "<?php echo \App\Helpers\IdObfuscator::obfuscate($expression); ?>";
        });

        Blade::directive('secureRoute', function ($expression) {
            return "<?php echo \App\Helpers\IdObfuscator::route($expression); ?>";
        });
    }
}
