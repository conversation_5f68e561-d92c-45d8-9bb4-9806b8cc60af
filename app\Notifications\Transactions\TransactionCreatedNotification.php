<?php

namespace App\Notifications\Transactions;

use App\Models\Transaction;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class TransactionCreatedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The transaction instance.
     *
     * @var \App\Models\Transaction
     */
    protected $transaction;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Transaction  $transaction
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_transaction_operations';

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;

        $this->subject = 'New Transaction Recorded';
        $this->title = 'Transaction Recorded';
        $this->content = 'A new transaction has been recorded in your PocketWatch account.';
        
        $this->detailsTitle = 'Transaction Details';
        $this->details = [
            'Amount' => $this->formatCurrency($this->transaction->amount, $this->transaction->currency),
            'Type' => ucfirst($this->transaction->type),
            'Description' => $this->transaction->description ?? 'No description',
            'Date' => $this->transaction->transaction_date->format('F j, Y'),
        ];
        
        if ($this->transaction->sub_bin_id) {
            $this->details['Sub-Bin'] = $this->transaction->subBin->name;
            $this->details['Bin'] = $this->transaction->subBin->bin->name;
        } elseif ($this->transaction->bin_id) {
            $this->details['Bin'] = $this->transaction->bin->name;
        }
        
        $this->actionText = 'View Transaction';
        $this->actionUrl = url('/transactions/' . $this->transaction->id);
        
        $this->closing = 'Keep track of your finances with PocketWatch!';
        $this->signature = 'The PocketWatch Team';
    }
    
    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);
        
        return $symbol . number_format($value, 2);
    }
    
    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];
        
        return $symbols[$currency] ?? $currency;
    }
}
