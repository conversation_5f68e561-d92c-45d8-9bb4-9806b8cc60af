<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sub_bins', function (Blueprint $table) {
            // Make threshold_max_limit nullable so users can set as they want
            $table->decimal('threshold_max_limit', 15, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sub_bins', function (Blueprint $table) {
            // Revert threshold_max_limit to not nullable with default 0
            $table->decimal('threshold_max_limit', 15, 2)->default(0)->change();
        });
    }
};
