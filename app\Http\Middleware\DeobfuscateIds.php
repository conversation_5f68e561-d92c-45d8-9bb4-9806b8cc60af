<?php

namespace App\Http\Middleware;

use App\Services\IdObfuscationService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DeobfuscateIds
{
    /**
     * The ID obfuscation service.
     *
     * @var \App\Services\IdObfuscationService
     */
    protected $idObfuscationService;

    /**
     * Create a new middleware instance.
     *
     * @param  \App\Services\IdObfuscationService  $idObfuscationService
     * @return void
     */
    public function __construct(IdObfuscationService $idObfuscationService)
    {
        $this->idObfuscationService = $idObfuscationService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Deobfuscate route parameters
        $this->deobfuscateRouteParameters($request);
        
        // Deobfuscate request input
        $this->deobfuscateRequestInput($request);
        
        return $next($request);
    }
    
    /**
     * Deobfuscate route parameters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function deobfuscateRouteParameters(Request $request): void
    {
        $route = $request->route();
        
        if (!$route) {
            return;
        }
        
        $parameters = $route->parameters();
        
        foreach ($parameters as $key => $value) {
            // Skip non-string values
            if (!is_string($value)) {
                continue;
            }
            
            // Try to deobfuscate the value
            $deobfuscated = $this->idObfuscationService->deobfuscate($value);
            
            if ($deobfuscated !== null) {
                $route->setParameter($key, $deobfuscated);
            }
        }
    }
    
    /**
     * Deobfuscate request input.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function deobfuscateRequestInput(Request $request): void
    {
        $input = $request->all();
        
        // Common ID fields to deobfuscate
        $idFields = [
            'id', 'user_id', 'bin_id', 'sub_bin_id', 'transaction_id', 
            'subscription_id', 'crypto_wallet_id', 'plaid_account_id', 'report_id'
        ];
        
        foreach ($idFields as $field) {
            if ($request->has($field) && is_string($request->input($field))) {
                $deobfuscated = $this->idObfuscationService->deobfuscate($request->input($field));
                
                if ($deobfuscated !== null) {
                    $input[$field] = $deobfuscated;
                }
            }
        }
        
        $request->replace($input);
    }
}
