<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PocketWatch API Documentation</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #32704e; /* Sea Green */
            --primary-dark: #1D5E40; /* Dark Green */
            --primary-light: #E8F5E9; /* Light Mint */
            --secondary-color: #3CB371; /* Medium Sea Green */
            --accent-color: #66CDAA; /* Medium Aquamarine */
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --success-color: #28a745;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --gray-color: #6c757d;
            --border-color: #dee2e6;
            --text-color: #333;
            --code-bg: #f5f5f5;
            --sidebar-width: 280px;
            --header-height: 60px;
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            --mono-font: 'JetBrains Mono', SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        [data-theme="dark"] {
            --primary-color: #4a9e73; /* Brighter green for dark mode */
            --primary-dark: #32704e;
            --primary-light: #1e2a23;
            --secondary-color: #66CDAA;
            --accent-color: #8FBC8F;
            --light-color: #212529;
            --dark-color: #f8f9fa;
            --text-color: #e9ecef;
            --border-color: #495057;
            --code-bg: #2d3748;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--font-family);
            color: var(--text-color);
            line-height: 1.6;
            background-color: #fff;
            transition: var(--transition);
            scroll-behavior: smooth;
            scroll-padding-top: var(--header-height);
        }

        [data-theme="dark"] body {
            background-color: #121212;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        code {
            font-family: var(--mono-font);
            background-color: var(--code-bg);
            padding: 2px 4px;
            border-radius: 4px;
            font-size: 0.9em;
            color: var(--primary-dark);
        }

        [data-theme="dark"] code {
            color: var(--primary-color);
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background-color: #fff;
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
            transition: var(--transition);
        }

        [data-theme="dark"] .header {
            background-color: #1e1e1e;
            box-shadow: 0 1px 2px rgba(255, 255, 255, 0.05);
        }

        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .logo i {
            margin-right: 8px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .theme-toggle {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--gray-color);
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: var(--transition);
        }

        .theme-toggle:hover {
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        [data-theme="dark"] .theme-toggle:hover {
            background-color: #2d2d2d;
            color: var(--light-color);
        }

        .search-container {
            position: relative;
        }

        .search-input {
            padding: 8px 16px 8px 36px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-family: var(--font-family);
            font-size: 0.9rem;
            width: 200px;
            transition: var(--transition);
            background-color: var(--light-color);
            color: var(--text-color);
        }

        [data-theme="dark"] .search-input {
            background-color: #2d2d2d;
            border-color: #444;
        }

        .search-input:focus {
            outline: none;
            width: 300px;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-color);
            pointer-events: none;
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--gray-color);
            font-size: 1.5rem;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: var(--header-height);
            left: 0;
            bottom: 0;
            width: var(--sidebar-width);
            background-color: var(--light-color);
            overflow-y: auto;
            padding: 20px;
            transition: var(--transition);
            z-index: 900;
        }

        [data-theme="dark"] .sidebar {
            background-color: #1e1e1e;
        }

        .sidebar-section {
            margin-bottom: 24px;
        }

        .sidebar-title {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: var(--gray-color);
            margin-bottom: 12px;
            font-weight: 600;
        }

        .sidebar-links {
            list-style: none;
        }

        .sidebar-link {
            display: block;
            padding: 8px 12px;
            border-radius: 4px;
            color: var(--text-color);
            font-size: 0.9rem;
            transition: var(--transition);
            margin-bottom: 4px;
        }

        .sidebar-link:hover {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--primary-color);
            text-decoration: none;
        }

        .sidebar-link.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            margin-top: var(--header-height);
            padding: 40px;
            max-width: 1200px;
            transition: var(--transition);
        }

        .content-section {
            margin-bottom: 60px;
            scroll-margin-top: calc(var(--header-height) + 20px);
        }

        .section-title {
            color: var(--primary-color);
            margin-bottom: 24px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--primary-color);
            font-weight: 600;
            font-size: 1.75rem;
        }

        /* Cards */
        .info-card {
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 32px;
            box-shadow: var(--shadow-sm);
            border-left: 4px solid var(--primary-color);
            transition: var(--transition);
        }

        [data-theme="dark"] .info-card {
            background-color: #1e1e1e;
        }

        .info-card h3 {
            margin-bottom: 16px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .info-card p {
            margin-bottom: 12px;
        }

        .info-card p:last-child {
            margin-bottom: 0;
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            box-shadow: var(--shadow-md);
            margin-bottom: 32px;
            transition: var(--transition);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.95rem;
        }

        th {
            background-color: var(--primary-color);
            color: white;
            text-align: left;
            padding: 16px;
            position: sticky;
            top: var(--header-height);
            font-weight: 600;
            white-space: nowrap;
        }

        td {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            vertical-align: top;
            transition: var(--transition);
        }

        [data-theme="dark"] td {
            border-bottom-color: #444;
        }

        tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        [data-theme="dark"] tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.02);
        }

        tr:hover {
            background-color: rgba(40, 167, 69, 0.05);
        }

        [data-theme="dark"] tr:hover {
            background-color: rgba(40, 167, 69, 0.1);
        }

        /* Method badges */
        .method {
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 4px;
            display: inline-block;
            min-width: 80px;
            text-align: center;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .get {
            background-color: var(--secondary-color);
            color: white;
        }

        .post {
            background-color: var(--primary-color);
            color: white;
        }

        .put {
            background-color: var(--warning-color);
            color: #212529;
        }

        .delete {
            background-color: var(--danger-color);
            color: white;
        }

        /* Auth badges */
        .auth {
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-block;
            min-width: 60px;
            text-align: center;
        }

        .yes {
            background-color: var(--success-color);
            color: white;
        }

        .no {
            background-color: var(--danger-color);
            color: white;
        }

        /* Parameters */
        .params {
            white-space: pre-line;
            font-family: var(--mono-font);
            font-size: 0.85rem;
            line-height: 1.7;
            color: var(--text-color);
        }

        .endpoint {
            font-family: var(--mono-font);
            font-weight: 500;
            color: var(--primary-color);
        }

        .premium {
            background-color: var(--warning-color);
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 8px;
            vertical-align: middle;
        }

        /* Footer */
        .footer {
            margin-top: 60px;
            padding: 24px 0;
            text-align: center;
            color: var(--gray-color);
            font-size: 0.9rem;
            border-top: 1px solid var(--border-color);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            :root {
                --sidebar-width: 240px;
            }

            .search-input:focus {
                width: 240px;
            }
        }

        @media (max-width: 768px) {
            :root {
                --sidebar-width: 0px;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .sidebar {
                transform: translateX(-100%);
                box-shadow: var(--shadow-lg);
                width: 280px;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .search-container {
                display: none;
            }

            .mobile-search {
                display: flex;
                margin: 16px 0;
            }

            .mobile-search .search-container {
                display: block;
                width: 100%;
            }

            .mobile-search .search-input {
                width: 100%;
            }

            .mobile-search .search-input:focus {
                width: 100%;
            }
        }

        /* Print styles */
        @media print {
            .header, .sidebar, .search-container, .theme-toggle, .mobile-menu-toggle {
                display: none;
            }

            .main-content {
                margin-left: 0;
                margin-top: 0;
                padding: 0;
            }

            th {
                background-color: var(--primary-color) !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .method, .auth {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            tr:nth-child(even) {
                background-color: rgba(0, 0, 0, 0.02) !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <button class="mobile-menu-toggle" id="mobile-menu-toggle">
            <i class="fas fa-bars"></i>
        </button>
        <div class="logo">
            <i class="fas fa-wallet"></i>
            <span>PocketWatch API</span>
        </div>
        <div class="header-actions">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" id="search-input" placeholder="Search API endpoints...">
            </div>
            <button class="theme-toggle" id="theme-toggle" title="Toggle dark mode">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </header>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="mobile-search">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" id="mobile-search-input" placeholder="Search API endpoints...">
            </div>
        </div>

        <div class="sidebar-section">
            <h3 class="sidebar-title">Getting Started</h3>
            <ul class="sidebar-links">
                <li><a href="#introduction" class="sidebar-link">Introduction</a></li>
                <li><a href="#authentication" class="sidebar-link">Authentication</a></li>
            </ul>
        </div>

        <div class="sidebar-section">
            <h3 class="sidebar-title">API Endpoints</h3>
            <ul class="sidebar-links">
                <li><a href="#auth-endpoints" class="sidebar-link">Authentication</a></li>
                <li><a href="#google-auth-endpoints" class="sidebar-link">🔐 Google OAuth</a></li>
                <li><a href="#package-endpoints" class="sidebar-link">🎯 Package Purchase (Legacy)</a></li>
                <li><a href="#stripe-checkout-endpoints" class="sidebar-link">💳 Stripe Checkout (New)</a></li>
                <li><a href="#trial-endpoints" class="sidebar-link">Trial Management</a></li>
                <li><a href="#bin-endpoints" class="sidebar-link">Bins</a></li>
                <li><a href="#sub-bin-endpoints" class="sidebar-link">🏗️ Sub-Bins (Hierarchical)</a></li>
                <li><a href="#transaction-endpoints" class="sidebar-link">Transactions</a></li>
                <li><a href="#subscription-endpoints" class="sidebar-link">Subscriptions (Legacy)</a></li>
                <li>
                    <a href="#plaid-endpoints" class="sidebar-link">
                        Plaid Integration
                        <span class="premium">Premium</span>
                    </a>
                </li>
                <li><a href="#plaid-account-endpoints" class="sidebar-link">Plaid Accounts</a></li>
                <li><a href="#plaid-payment-endpoints" class="sidebar-link">Plaid Payments</a></li>
                <li><a href="#crypto-endpoints" class="sidebar-link">Crypto Wallet</a></li>
                <li><a href="#reports-endpoints" class="sidebar-link">Reports</a></li>
                <li><a href="#webhook-endpoints" class="sidebar-link">Webhooks</a></li>
                <li><a href="#error-responses" class="sidebar-link">Error Responses</a></li>
                <li><a href="#rate-limits" class="sidebar-link">Rate Limits</a></li>
            </ul>
        </div>

        <div class="sidebar-section">
            <h3 class="sidebar-title">Additional Info</h3>
            <ul class="sidebar-links">
                <li><a href="#subscription-tiers" class="sidebar-link">Subscription Tiers</a></li>
            </ul>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <section id="introduction" class="content-section">
            <h2 class="section-title">Introduction</h2>

            <div class="info-card">
                <h3>Welcome to PocketWatch API</h3>
                <p>
                    This documentation provides detailed information about all API endpoints available in the PocketWatch application.
                    Use this as a reference when integrating with the Flutter mobile app or any other client.
                </p>
                <p>
                    <strong>Base URL:</strong>
                    <code>https://pocketwatch.nexusbridgellc.com/api</code>
                </p>
                <p>
                    <strong>Package System:</strong> PocketWatch uses a simple 2-step package purchase system.
                    Users can view available packages and purchase them with Stripe integration.
                    All packages include a 7-day free trial with full access to selected tier features.
                </p>
                <p>
                    <strong>Available Packages:</strong>
                </p>
                <ul>
                    <li><strong>Base Monthly ($9.99/month):</strong> Essential features with 3-level sub-bin hierarchy</li>
                    <li><strong>Premium Monthly ($19.99/month):</strong> Advanced features with unlimited sub-bin hierarchy</li>
                </ul>
                <p>
                    <strong>Note:</strong> This documentation can be exported to Excel by selecting all content, copying, and pasting into Excel.
                </p>
            </div>

            <div class="info-card">
                <h3>🚀 Quick Start Guide</h3>

                <p><strong>Option 1: Traditional Registration (Testing):</strong></p>
                <ol>
                    <li><strong>Register:</strong> <code>POST /api/register</code></li>
                    <li><strong>Login:</strong> <code>POST /api/login</code> (get auth token)</li>
                    <li><strong>View packages:</strong> <code>GET /api/packages</code></li>
                    <li><strong>Purchase package:</strong> <code>POST /api/packages/purchase-test</code> with <code>{"package_id": "base_monthly"}</code></li>
                    <li><strong>Start using features:</strong> Create bins, sub-bins, transactions, etc.</li>
                </ol>

                <p><strong>Option 2: Google Authentication (Recommended):</strong></p>
                <ol>
                    <li><strong>Get Google URL:</strong> <code>GET /api/auth/google</code></li>
                    <li><strong>Redirect to Google:</strong> User signs in with Google</li>
                    <li><strong>Handle callback:</strong> <code>GET /api/auth/google/callback?code=AUTH_CODE</code> (get auth token)</li>
                    <li><strong>View packages:</strong> <code>GET /api/packages</code></li>
                    <li><strong>Purchase package:</strong> <code>POST /api/packages/purchase-test</code> with <code>{"package_id": "base_monthly"}</code></li>
                    <li><strong>Start using features:</strong> Create bins, sub-bins, transactions, etc.</li>
                </ol>

                <p><strong>Option 3: Mobile App (Google ID Token):</strong></p>
                <ol>
                    <li><strong>Get Google ID Token:</strong> Use Google Sign-In SDK in your mobile app</li>
                    <li><strong>Authenticate:</strong> <code>POST /api/auth/google/token</code> with <code>{"id_token": "..."}</code></li>
                    <li><strong>View packages:</strong> <code>GET /api/packages</code></li>
                    <li><strong>Purchase package:</strong> <code>POST /api/packages/purchase-test</code> with <code>{"package_id": "base_monthly"}</code></li>
                    <li><strong>Start using features:</strong> Create bins, sub-bins, transactions, etc.</li>
                </ol>

                <p><strong>For Production:</strong></p>
                <ol>
                    <li><strong>Set up Google OAuth:</strong> Configure Google Cloud Console and get client credentials</li>
                    <li><strong>Set up Stripe:</strong> Configure API keys and create price IDs</li>
                    <li><strong>Authenticate:</strong> Use any of the above authentication methods</li>
                    <li><strong>View packages:</strong> <code>GET /api/packages</code></li>
                    <li><strong>Create payment method:</strong> Use Stripe.js frontend integration</li>
                    <li><strong>Purchase package:</strong> <code>POST /api/packages/purchase</code> with package_id and payment_method_id</li>
                </ol>

                <p><strong>Key Endpoints:</strong></p>
                <ul>
                    <li><code>GET /api/auth/google</code> - Get Google OAuth URL</li>
                    <li><code>POST /api/auth/google/token</code> - Authenticate with Google ID token</li>
                    <li><code>GET /api/packages</code> - View available packages</li>
                    <li><code>POST /api/packages/purchase-test</code> - Test purchase (no Stripe)</li>
                    <li><code>POST /api/packages/purchase</code> - Real purchase (with Stripe)</li>
                    <li><code>GET /api/me</code> - Check user status and trial info</li>
                </ul>
            </div>
        </section>

        <section id="authentication" class="content-section">
            <h2 class="section-title">Authentication</h2>

            <div class="info-card">
                <h3>Bearer Token Authentication</h3>
                <p>
                    Most endpoints require authentication using a Bearer token in the Authorization header:
                </p>
                <p><code>Authorization: Bearer {your_token}</code></p>
                <p>
                    You can obtain a token by using the login endpoint. The token should be included in all subsequent requests to authenticated endpoints.
                </p>
            </div>

            <div class="info-card">
                <h3>Trial Expiration Responses</h3>
                <p>
                    When a user's trial has expired, most API endpoints will return a <code>402 Payment Required</code> status code:
                </p>
                <p><strong>Response:</strong></p>
                <pre><code>{
    "message": "Your trial has expired. Please subscribe to continue using PocketWatch.",
    "trial_expired": true,
    "subscription_required": true
}</code></pre>
                <p>
                    <strong>Allowed endpoints for expired trials:</strong> /me, /logout, /subscriptions (create/checkout/status)
                </p>
            </div>
        </section>

        <section id="auth-endpoints" class="content-section">
            <h2 class="section-title">Authentication Endpoints</h2>

            <div class="table-container">
                <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="endpoint">/register</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth no">No</span></td>
                    <td class="params">
                        name (required): User's full name email (required):
                        User's email address password (required): Password (min
                        8 characters) password_confirmation (required): Password
                        confirmation country_code (optional): Country calling
                        code phone_number (optional): Phone number
                    </td>
                    <td>
                        Register a new user. Users start with base tier access.
                        The 7-day free trial begins on first login.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/login</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth no">No</span></td>
                    <td class="params">
                        email (required): User's email address password
                        (required): Password
                    </td>
                    <td>Login and get authentication token. For new users, this starts the 7-day free trial period.</td>
                </tr>
                <tr>
                    <td class="endpoint">/me</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">None</td>
                    <td>Get current user profile.</td>
                </tr>
                <tr>
                    <td class="endpoint">/logout</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">None</td>
                    <td>Logout and invalidate token.</td>
                </tr>
                <tr>
                    <td class="endpoint">/profile</td>
                    <td><span class="method put">PUT</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        name (optional): User's full name email (optional):
                        User's email address country_code (optional): Country
                        calling code phone_number (optional): Phone number
                    </td>
                    <td>Update user profile.</td>
                </tr>
                <tr>
                    <td class="endpoint">/password</td>
                    <td><span class="method put">PUT</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        current_password (required): Current password password
                        (required): New password (min 8 characters)
                        password_confirmation (required): New password
                        confirmation
                    </td>
                    <td>Change user password.</td>
                </tr>
                <tr>
                    <td class="endpoint">/avatar</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        avatar (required): Image file (jpeg, png, jpg, gif, max
                        2MB)
                    </td>
                    <td>Update user avatar.</td>
                </tr>
                <tr>
                    <td class="endpoint">/forgot-password</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth no">No</span></td>
                    <td class="params">
                        email (required): User's email address
                    </td>
                    <td>Request password reset link.</td>
                </tr>
                <tr>
                    <td class="endpoint">/reset-password</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth no">No</span></td>
                    <td class="params">
                        email (required): User's email address token (required):
                        Reset token from email password (required): New password
                        (min 8 characters) password_confirmation (required): New
                        password confirmation
                    </td>
                    <td>Reset password with token.</td>
                </tr>
            </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>📊 Authentication Response Examples</h3>

                <h4>🔹 Register Response</h4>
                <pre><code>{
    "message": "User registered successfully",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "subscription_tier": "none",
        "is_active": true,
        "is_admin": false,
        "trial_started_at": null,
        "created_at": "2024-12-19T10:00:00Z"
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}</code></pre>

                <h4>🔹 Login Response</h4>
                <pre><code>{
    "message": "Login successful",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "subscription_tier": "none",
        "is_active": true,
        "trial_started_at": null,
        "trial_expired": false,
        "trial_days_remaining": null
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}</code></pre>

                <h4>🔹 Get User Profile (/me) Response</h4>
                <pre><code>{
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "country_code": "+1",
    "phone_number": "5551234567",
    "avatar": "https://example.com/avatars/user1.jpg",
    "subscription_tier": "base",
    "is_active": true,
    "is_admin": false,
    "trial_started_at": "2024-12-12T10:00:00Z",
    "trial_expired": false,
    "trial_days_remaining": 2,
    "selected_plan_tier": "base",
    "selected_billing_cycle": "monthly",
    "preferences": {
        "notifications": true,
        "theme": "light"
    },
    "last_login_at": "2024-12-19T10:00:00Z",
    "created_at": "2024-12-12T10:00:00Z",
    "cumulative_balance": 1234.56 // <--- NEW FIELD: running total of all income minus all expenses
}</code></pre>
            </div>
        </section>

        <section id="google-auth-endpoints" class="content-section">
            <h2 class="section-title">🔐 Google OAuth Authentication</h2>

            <div class="info-card">
                <h3>🚀 Google Sign-In Integration</h3>
                <p>
                    <strong>Seamless Authentication:</strong> PocketWatch supports Google OAuth 2.0 for easy registration and login.
                    Users can sign up or sign in using their Google accounts without creating separate passwords.
                </p>
                <p>
                    <strong>Two Integration Methods:</strong>
                </p>
                <ol>
                    <li><strong>Web OAuth Flow:</strong> Redirect users to Google for authentication (web apps)</li>
                    <li><strong>ID Token Verification:</strong> Verify Google ID tokens directly (mobile apps)</li>
                </ol>
                <p>
                    <strong>Account Linking:</strong> If a user already exists with the same email, the Google account is automatically linked.
                    New users are created automatically with Google profile information.
                </p>
                <p>
                    <strong>Security Features:</strong> Email verification is automatic for Google users, and accounts can be unlinked
                    with proper security checks.
                </p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Auth Required</th>
                            <th>Parameters</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="endpoint">/auth/google</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth no">No</span></td>
                            <td class="params">None</td>
                            <td>Get Google OAuth redirect URL for web authentication flow.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/auth/google/callback</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth no">No</span></td>
                            <td class="params">
                                code (required): Authorization code from Google
                            </td>
                            <td>Handle Google OAuth callback and authenticate user.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/auth/google/token</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth no">No</span></td>
                            <td class="params">
                                id_token (required): Google ID token from mobile app
                            </td>
                            <td>Authenticate user with Google ID token (for mobile apps).</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/auth/google/unlink</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>Unlink Google account from user profile (requires password set).</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>📊 Google OAuth Response Examples</h3>

                <h4>🔹 Get Google OAuth URL Response</h4>
                <pre><code>{
    "success": true,
    "redirect_url": "https://accounts.google.com/oauth/authorize?client_id=*********&redirect_uri=http://localhost:8000/api/auth/google/callback&scope=openid+email+profile&response_type=code&state=xyz",
    "message": "Redirect to Google OAuth"
}</code></pre>

                <h4>🔹 Google OAuth Success Response</h4>
                <pre><code>{
    "success": true,
    "message": "Account created successfully via Google",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "avatar": "https://lh3.googleusercontent.com/a/default-user=s96-c",
        "provider": "google",
        "subscription_tier": "none",
        "trial_started_at": null,
        "trial_expired": false,
        "trial_days_remaining": null,
        "email_verified_at": "2024-12-19T10:00:00Z",
        "created_at": "2024-12-19T10:00:00Z"
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer"
}</code></pre>

                <h4>🔹 Google Account Linked Response</h4>
                <pre><code>{
    "success": true,
    "message": "Google account linked successfully",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "avatar": "https://lh3.googleusercontent.com/a/default-user=s96-c",
        "provider": "google",
        "subscription_tier": "trial",
        "trial_started_at": "2024-12-18T10:00:00Z",
        "trial_expired": false,
        "trial_days_remaining": 6,
        "email_verified_at": "2024-12-19T10:00:00Z"
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer"
}</code></pre>

                <h4>🔹 Google ID Token Authentication Response</h4>
                <pre><code>{
    "success": true,
    "message": "Login successful via Google",
    "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "avatar": "https://lh3.googleusercontent.com/a/default-user=s96-c",
        "provider": "google",
        "subscription_tier": "premium",
        "email_verified_at": "2024-12-19T10:00:00Z"
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer"
}</code></pre>

                <h4>🔹 Unlink Google Account Response</h4>
                <pre><code>{
    "success": true,
    "message": "Google account unlinked successfully"
}</code></pre>

                <h4>🔹 Google Authentication Error Response</h4>
                <pre><code>{
    "success": false,
    "message": "Google authentication failed: Invalid authorization code",
    "error_code": "google_auth_failed"
}</code></pre>

                <h4>🔹 Invalid Google ID Token Response</h4>
                <pre><code>{
    "success": false,
    "message": "Invalid Google ID token"
}</code></pre>

                <h4>🔹 Unlink Error Response</h4>
                <pre><code>{
    "success": false,
    "message": "Cannot unlink Google account. Please set a password first.",
    "action_required": "set_password"
}</code></pre>
            </div>

            <div class="info-card">
                <h3>🎯 Google OAuth Setup Guide</h3>

                <h4>📋 Prerequisites</h4>
                <ol>
                    <li><strong>Google Cloud Console:</strong> Create a project at <a href="https://console.cloud.google.com" target="_blank">console.cloud.google.com</a></li>
                    <li><strong>Enable Google+ API:</strong> Enable Google+ API in your project</li>
                    <li><strong>Create OAuth Credentials:</strong> Create OAuth 2.0 client credentials</li>
                    <li><strong>Configure Redirect URIs:</strong> Add your callback URL</li>
                </ol>

                <h4>🔧 Environment Configuration</h4>
                <pre><code># Add to your .env file
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:8000/api/auth/google/callback</code></pre>

                <h4>🚀 Testing Flow</h4>
                <p><strong>Web Application Flow:</strong></p>
                <ol>
                    <li><strong>Get OAuth URL:</strong> <code>GET /api/auth/google</code></li>
                    <li><strong>Redirect User:</strong> Send user to the returned URL</li>
                    <li><strong>Handle Callback:</strong> Google redirects to your callback with code</li>
                    <li><strong>Exchange Code:</strong> <code>GET /api/auth/google/callback?code=AUTH_CODE</code></li>
                </ol>

                <p><strong>Mobile Application Flow:</strong></p>
                <ol>
                    <li><strong>Get Google ID Token:</strong> Use Google Sign-In SDK in your mobile app</li>
                    <li><strong>Send Token:</strong> <code>POST /api/auth/google/token</code> with <code>{"id_token": "..."}</code></li>
                    <li><strong>Receive Auth Token:</strong> Use returned token for API calls</li>
                </ol>

                <h4>🔐 Security Features</h4>
                <ul>
                    <li><strong>Automatic Email Verification:</strong> Google users are automatically verified</li>
                    <li><strong>Account Linking:</strong> Existing accounts are linked by email</li>
                    <li><strong>Secure Unlinking:</strong> Requires password for security</li>
                    <li><strong>Token Validation:</strong> Google ID tokens are properly verified</li>
                </ul>
            </div>
        </section>

        <section id="package-endpoints" class="content-section">
            <h2 class="section-title">🎯 Package Purchase System (Legacy)</h2>

            <div class="info-card">
                <h3>🚀 Simple Package Purchase Flow</h3>
                <p>
                    <strong>New Simplified System:</strong> PocketWatch now uses a streamlined 2-step package purchase process:
                </p>
                <ol>
                    <li><strong>View Available Packages</strong> → Browse Base and Premium packages</li>
                    <li><strong>Purchase Package</strong> → Buy with Stripe and start 7-day trial immediately</li>
                </ol>
                <p>
                    <strong>Available Packages:</strong>
                </p>
                <ul>
                    <li><strong>Base Monthly ($9.99/month):</strong> Essential features with 3-level sub-bin hierarchy</li>
                    <li><strong>Premium Monthly ($19.99/month):</strong> Advanced features with unlimited sub-bin hierarchy</li>
                </ul>
                <p>
                    <strong>Trial Benefits:</strong> All packages include a 7-day free trial with full access to selected tier features.
                    Cancel anytime during trial without charges.
                </p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Auth Required</th>
                            <th>Parameters</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="endpoint">/packages</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>Get all available packages with pricing and features.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/packages/purchase</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                package_id (required): Package ID (base_monthly, premium_monthly)
                                payment_method_id (required): Stripe payment method ID
                            </td>
                            <td>Purchase a package with real Stripe integration. Starts 7-day trial immediately.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/packages/purchase-test</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                package_id (required): Package ID (base_monthly, premium_monthly)
                            </td>
                            <td>Purchase a package in test mode (no Stripe required). Perfect for development and testing.</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>📊 Package Purchase Response Examples</h3>

                <h4>🔹 Get Packages Response</h4>
                <pre><code>{
    "success": true,
    "data": {
        "packages": [
            {
                "id": "base_monthly",
                "name": "Base Monthly",
                "description": "Essential financial management tools",
                "price": 9.99,
                "currency": "USD",
                "billing_cycle": "monthly",
                "stripe_price_id": "price_*********0abcdef",
                "features": [
                    "Financial bins with thresholds",
                    "Transaction categorization",
                    "Basic insights",
                    "Hierarchical sub-bins (3 levels)",
                    "Bank account linking"
                ],
                "limits": {
                    "max_bins": 10,
                    "max_sub_bins_per_bin": 10,
                    "max_nesting_depth": 3
                }
            },
            {
                "id": "premium_monthly",
                "name": "Premium Monthly",
                "description": "Advanced financial management with premium features",
                "price": 19.99,
                "currency": "USD",
                "billing_cycle": "monthly",
                "stripe_price_id": "price_0987654321fedcba",
                "features": [
                    "All Base features",
                    "Unlimited hierarchical sub-bins",
                    "Crypto wallet integration",
                    "Advanced AI insights",
                    "Priority notifications",
                    "Advanced reporting"
                ],
                "limits": {
                    "max_bins": "unlimited",
                    "max_sub_bins_per_bin": "unlimited",
                    "max_nesting_depth": "unlimited"
                }
            }
        ],
        "trial_days": 7,
        "currency": "USD"
    }
}</code></pre>

                <h4>🔹 Purchase Package (Test Mode) Response</h4>
                <pre><code>{
    "success": true,
    "message": "TEST MODE: Package purchased successfully! Your 7-day trial has started.",
    "data": {
        "subscription": {
            "id": 1,
            "user_id": 1,
            "name": "Base Monthly (TEST)",
            "stripe_id": "test_sub_abc123",
            "stripe_status": "trialing",
            "subscription_tier": "base",
            "billing_cycle": "monthly",
            "price": 9.99,
            "currency": "USD",
            "features": [
                "Financial bins with thresholds",
                "Transaction categorization",
                "Basic insights",
                "Hierarchical sub-bins (3 levels)"
            ],
            "trial_ends_at": "2024-12-26T10:00:00Z",
            "created_at": "2024-12-19T10:00:00Z"
        },
        "package": {
            "id": "base_monthly",
            "name": "Base Monthly",
            "tier": "base",
            "billing_cycle": "monthly",
            "price": 9.99,
            "features": [...]
        },
        "trial_info": {
            "trial_started": true,
            "trial_days_remaining": 7,
            "trial_ends_at": "2024-12-26T10:00:00Z",
            "test_mode": true
        }
    }
}</code></pre>

                <h4>🔹 Purchase Package (Real Stripe) Response</h4>
                <pre><code>{
    "success": true,
    "message": "Package purchased successfully! Your 7-day trial has started.",
    "data": {
        "subscription": {
            "id": 1,
            "user_id": 1,
            "name": "Premium Monthly",
            "stripe_id": "sub_*********0abcdef",
            "stripe_status": "trialing",
            "subscription_tier": "premium",
            "billing_cycle": "monthly",
            "price": 19.99,
            "currency": "USD",
            "trial_ends_at": "2024-12-26T10:00:00Z"
        },
        "package": {
            "id": "premium_monthly",
            "name": "Premium Monthly",
            "tier": "premium"
        },
        "trial_info": {
            "trial_started": true,
            "trial_days_remaining": 7,
            "trial_ends_at": "2024-12-26T10:00:00Z",
            "auto_billing_date": "2024-12-26T10:00:00Z"
        },
        "stripe_info": {
            "subscription_id": "sub_*********0abcdef",
            "customer_id": "cus_*********0abcdef",
            "status": "trialing"
        }
    }
}</code></pre>

                <h4>🔹 Package Purchase Error Response</h4>
                <pre><code>{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "package_id": [
            "The selected package id is invalid."
        ],
        "payment_method_id": [
            "The payment method id field is required."
        ]
    }
}</code></pre>
            </div>

            <div class="info-card">
                <h3>🎯 Testing Guide</h3>
                <p><strong>For Development/Testing:</strong></p>
                <ol>
                    <li><strong>Get packages:</strong> <code>GET /api/packages</code></li>
                    <li><strong>Purchase (test):</strong> <code>POST /api/packages/purchase-test</code> with <code>{"package_id": "base_monthly"}</code></li>
                    <li><strong>Verify access:</strong> <code>GET /api/me</code> (should show trial status)</li>
                </ol>

                <p><strong>For Production:</strong></p>
                <ol>
                    <li><strong>Get packages:</strong> <code>GET /api/packages</code></li>
                    <li><strong>Create payment method:</strong> Use Stripe.js to create payment method</li>
                    <li><strong>Purchase:</strong> <code>POST /api/packages/purchase</code> with package_id and payment_method_id</li>
                </ol>

                <p><strong>Available Package IDs:</strong></p>
                <ul>
                    <li><code>base_monthly</code> - Base tier with monthly billing</li>
                    <li><code>premium_monthly</code> - Premium tier with monthly billing</li>
                </ul>
            </div>
        </section>

        <section id="stripe-checkout-endpoints" class="content-section">
            <h2 class="section-title">💳 Stripe Checkout System (New & Recommended)</h2>

            <div class="info-card">
                <h3>🚀 Smooth One-Click Payment Flow</h3>
                <p>
                    <strong>Revolutionary Simplicity:</strong> The new Stripe Checkout integration provides the smoothest possible payment experience.
                    Users click a package and are instantly redirected to Stripe's professional checkout page.
                </p>
                <p>
                    <strong>Before vs After:</strong>
                </p>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
                    <div style="background: #fff3cd; padding: 15px; border-radius: 8px;">
                        <h4>❌ Old Flow (Complex)</h4>
                        <ol>
                            <li>Select package</li>
                            <li>Add payment method</li>
                            <li>Start trial</li>
                            <li>Multiple API calls</li>
                        </ol>
                    </div>
                    <div style="background: #d1edff; padding: 15px; border-radius: 8px;">
                        <h4>✅ New Flow (Smooth)</h4>
                        <ol>
                            <li>Click package → Stripe</li>
                            <li>Pay → Auto redirect</li>
                            <li>Trial starts automatically</li>
                            <li>Done! 🎉</li>
                        </ol>
                    </div>
                </div>
                <p>
                    <strong>Benefits:</strong> Professional UI, mobile-optimized, multiple payment methods (cards, Apple Pay, Google Pay),
                    automatic 3D Secure, PCI compliance handled by Stripe, and seamless 7-day trial integration.
                </p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Auth Required</th>
                            <th>Parameters</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="endpoint">/packages-checkout</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>Get packages with checkout capabilities and user information.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/packages/{packageId}/checkout</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                packageId (path): Package ID (base_monthly, premium_monthly)
                            </td>
                            <td>Create Stripe checkout session and return redirect URL.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/payment-success</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                session_id (query): Stripe checkout session ID
                            </td>
                            <td>Handle successful payment and activate trial.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/payment-cancel</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>Handle cancelled payment and provide next steps.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/webhook/stripe-checkout</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth no">No</span></td>
                            <td class="params">
                                Stripe webhook payload with signature verification
                            </td>
                            <td>Handle Stripe webhook events for subscription management.</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>📊 Stripe Checkout Response Examples</h3>

                <h4>🔹 Get Packages with Checkout Response</h4>
                <pre><code>{
    "success": true,
    "data": {
        "packages": [
            {
                "id": "base_monthly",
                "name": "Base Monthly",
                "description": "Essential financial management tools",
                "price": 9.99,
                "currency": "USD",
                "billing_cycle": "monthly",
                "stripe_price_id": "price_*********0abcdef",
                "checkout_action": "create_checkout_session",
                "features": [
                    "Financial bins with thresholds",
                    "Transaction categorization",
                    "Basic insights",
                    "Hierarchical sub-bins (3 levels)",
                    "Bank account linking"
                ],
                "limits": {
                    "max_bins": 10,
                    "max_sub_bins_per_bin": 10,
                    "max_nesting_depth": 3
                }
            },
            {
                "id": "premium_monthly",
                "name": "Premium Monthly",
                "description": "Advanced financial management with premium features",
                "price": 19.99,
                "currency": "USD",
                "billing_cycle": "monthly",
                "stripe_price_id": "price_0987654321fedcba",
                "checkout_action": "create_checkout_session",
                "features": [
                    "All Base features",
                    "Unlimited hierarchical sub-bins",
                    "Crypto wallet integration",
                    "Advanced AI insights",
                    "Priority notifications",
                    "Advanced reporting"
                ],
                "limits": {
                    "max_bins": "unlimited",
                    "max_sub_bins_per_bin": "unlimited",
                    "max_nesting_depth": "unlimited"
                }
            }
        ],
        "trial_days": 7,
        "currency": "USD",
        "user_info": {
            "id": 1,
            "email": "<EMAIL>",
            "name": "John Doe",
            "current_tier": "none"
        }
    }
}</code></pre>

                <h4>🔹 Create Checkout Session Response</h4>
                <pre><code>{
    "success": true,
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
        "session_id": "cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
        "package": {
            "id": "base_monthly",
            "name": "Base Monthly",
            "tier": "base",
            "billing_cycle": "monthly",
            "price": 9.99,
            "stripe_price_id": "price_*********0abcdef"
        },
        "trial_info": {
            "trial_days": 7,
            "trial_start": "immediately_after_payment",
            "auto_billing_date": "2024-12-26"
        }
    },
    "message": "Checkout session created. Redirect user to checkout_url."
}</code></pre>

                <h4>🔹 Payment Success Response</h4>
                <pre><code>{
    "success": true,
    "message": "Payment successful! Your 7-day trial has started.",
    "data": {
        "session_id": "cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
        "subscription_id": "sub_*********0abcdef",
        "customer_id": "cus_*********0abcdef",
        "trial_started": true,
        "trial_ends_at": "2024-12-26T10:00:00Z",
        "package_name": "Base Monthly"
    }
}</code></pre>

                <h4>🔹 Payment Cancel Response</h4>
                <pre><code>{
    "success": false,
    "message": "Payment was cancelled by user",
    "data": {
        "cancelled_at": "2024-12-19T10:00:00Z",
        "next_steps": {
            "view_packages": "/api/packages-checkout",
            "try_again": "Select a package to try payment again"
        }
    }
}</code></pre>

                <h4>🔹 Checkout Creation Error Response</h4>
                <pre><code>{
    "success": false,
    "message": "Failed to create checkout session: No such price: 'price_invalid'",
    "error_code": "checkout_creation_failed"
}</code></pre>
            </div>

            <div class="info-card">
                <h3>🎯 Implementation Guide</h3>

                <h4>📋 Frontend Integration</h4>
                <pre><code>// JavaScript/React/Vue example
async function buyPackage(packageId) {
    try {
        const response = await fetch(`/api/packages/${packageId}/checkout`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Redirect user to Stripe Checkout
            window.location.href = data.data.checkout_url;
        } else {
            console.error('Checkout failed:', data.message);
        }
    } catch (error) {
        console.error('Error:', error);
    }
}</code></pre>

                <h4>🔧 Stripe Configuration Required</h4>
                <pre><code># Add to your .env file
STRIPE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Create these Price IDs in Stripe Dashboard
STRIPE_PRICE_BASE_MONTHLY=price_your_base_monthly_id
STRIPE_PRICE_PREMIUM_MONTHLY=price_your_premium_monthly_id

# App URL for redirects
APP_URL=http://127.0.0.1:8000</code></pre>

                <h4>🚀 Complete User Flow</h4>
                <ol>
                    <li><strong>User Authentication:</strong> Login via email/password or Google OAuth</li>
                    <li><strong>View Packages:</strong> <code>GET /api/packages-checkout</code></li>
                    <li><strong>Select Package:</strong> User clicks "Buy" button</li>
                    <li><strong>Create Checkout:</strong> <code>POST /api/packages/{id}/checkout</code></li>
                    <li><strong>Redirect to Stripe:</strong> User redirected to <code>checkout_url</code></li>
                    <li><strong>Payment on Stripe:</strong> User completes payment with test card <code>4242 4242 4242 4242</code></li>
                    <li><strong>Return to App:</strong> Stripe redirects to success URL</li>
                    <li><strong>Trial Activation:</strong> <code>GET /api/payment-success?session_id=...</code></li>
                    <li><strong>Access Features:</strong> User can now use all trial features</li>
                </ol>

                <h4>🧪 Testing with Stripe Test Cards</h4>
                <ul>
                    <li><strong>Successful payment:</strong> <code>4242 4242 4242 4242</code></li>
                    <li><strong>Declined payment:</strong> <code>4000 0000 0000 0002</code></li>
                    <li><strong>3D Secure required:</strong> <code>4000 0025 0000 3155</code></li>
                    <li><strong>Insufficient funds:</strong> <code>4000 0000 0000 9995</code></li>
                </ul>

                <h4>🔄 Webhook Events Handled</h4>
                <ul>
                    <li><code>checkout.session.completed</code> - Payment completed</li>
                    <li><code>customer.subscription.created</code> - Subscription created</li>
                    <li><code>customer.subscription.updated</code> - Subscription status changed</li>
                    <li><code>invoice.payment_succeeded</code> - Recurring payment successful</li>
                    <li><code>customer.subscription.deleted</code> - Subscription cancelled</li>
                </ul>
            </div>
        </section>

        <section id="trial-endpoints" class="content-section">
            <h2 class="section-title">Trial Management</h2>

            <div class="info-card">
                <h3>🎯 7-Day Free Trial System</h3>
                <p>
                    <strong>Simplified Flow:</strong> PocketWatch now uses a streamlined package purchase system:
                </p>
                <ol>
                    <li><strong>User registers</strong> → Account created, no package selected</li>
                    <li><strong>User purchases package</strong> → 7-day trial starts immediately with full access</li>
                </ol>
                <p>
                    <strong>Trial Features:</strong> During the 7-day trial, users have full access to their purchased package features.
                    Users can cancel anytime during the trial period without being charged.
                </p>
                <p>
                    <strong>Auto-Billing:</strong> After 7 days, if not canceled, the payment method is automatically charged
                    and the subscription becomes active.
                </p>
                <p>
                    <strong>Trial Expiration:</strong> When the trial expires, users receive a 402 Payment Required
                    response for most endpoints and must purchase a new package to continue.
                </p>
                <p>
                    <strong>Package Access:</strong> Users without active subscriptions can still access package browsing
                    and purchase endpoints to buy new packages.
                </p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Auth Required</th>
                            <th>Parameters</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="endpoint">/me</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>
                                Get current user profile including trial status.
                                Returns trial_started_at, trial_expired, and days_remaining fields.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>Trial Status Response Fields</h3>
                <p>When calling the <code>/me</code> endpoint, the response includes trial-related fields:</p>
                <ul>
                    <li><code>trial_started_at</code>: Timestamp when trial started (null if not started)</li>
                    <li><code>subscription_tier</code>: Current tier (trial, base, premium, expired)</li>
                    <li><code>trial_expired</code>: Boolean indicating if trial has expired</li>
                    <li><code>trial_days_remaining</code>: Number of days remaining in trial</li>
                </ul>
            </div>
        </section>

        <section id="bin-endpoints" class="content-section">
            <h2 class="section-title">Bin Endpoints</h2>

            <div class="table-container">
                <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="endpoint">/bins</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        page (optional): Page number per_page (optional): Items
                        per page
                    </td>
                    <td>Get all bins.</td>
                </tr>
                <tr>
                    <td class="endpoint">/bins</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        name (required): Bin name
                        type (required): Bin type (income, expenditure)
                        description (optional): Bin description
                        threshold_min (required): Minimum threshold amount
                        threshold_max (optional): Maximum threshold amount
                        currency (optional): Currency code (default: USD)
                    </td>
                    <td>Create a new bin.</td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{id}</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Bin ID</td>
                    <td>Get bin details.</td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{id}</td>
                    <td><span class="method put">PUT</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        id (required): Bin ID
                        name (optional): Bin name
                        type (optional): Bin type (income, expenditure)
                        description (optional): Bin description
                        threshold_min (optional): Minimum threshold amount
                        threshold_max (optional): Maximum threshold amount
                        currency (optional): Currency code
                        is_active (optional): Bin status (boolean)
                    </td>
                    <td>Update bin.</td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{id}</td>
                    <td><span class="method delete">DELETE</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Bin ID</td>
                    <td>Delete bin.</td>
                </tr>
            </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>📊 Bin Response Examples</h3>

                <h4>🔹 Create Bin Response</h4>
                <pre><code>{
    "message": "Bin created successfully",
    "bin": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Emergency Savings",
        "type": "expenditure",
        "description": "Emergency fund for unexpected expenses",
        "threshold_min": 1000.00,
        "threshold_max": 10000.00,
        "current_amount": 0.00,
        "currency": "USD",
        "is_active": true,
        "user_id": 1,
        "created_at": "2024-12-19T10:00:00Z",
        "updated_at": "2024-12-19T10:00:00Z"
    },
    "remaining_bins": 9,
    "max_bins": 10
}</code></pre>

                <h4>🔹 Get All Bins Response</h4>
                <pre><code>{
    "data": [
        {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Emergency Savings",
            "type": "expenditure",
            "description": "Emergency fund for unexpected expenses",
            "threshold_min": 1000.00,
            "threshold_max": 10000.00,
            "current_amount": 2500.00,
            "currency": "USD",
            "is_active": true,
            "sub_bins_count": 3,
            "created_at": "2024-12-19T10:00:00Z"
        },
        {
            "id": 2,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Vacation Fund",
            "type": "expenditure",
            "description": "Saving for annual vacation",
            "threshold_min": 500.00,
            "threshold_max": 5000.00,
            "current_amount": 1200.00,
            "currency": "USD",
            "is_active": true,
            "sub_bins_count": 2,
            "created_at": "2024-12-19T11:00:00Z"
        }
    ],
    "links": {
        "first": "http://api.pocketwatch.com/bins?page=1",
        "last": "http://api.pocketwatch.com/bins?page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 1,
        "per_page": 15,
        "to": 2,
        "total": 2
    }
}</code></pre>

                <h4>🔹 Get Bin Details Response</h4>
                <pre><code>{
    "bin": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Emergency Savings",
        "type": "expenditure",
        "description": "Emergency fund for unexpected expenses",
        "threshold_min": 1000.00,
        "threshold_max": 10000.00,
        "current_amount": 2500.00,
        "currency": "USD",
        "is_active": true,
        "user_id": 1,
        "created_at": "2024-12-19T10:00:00Z",
        "updated_at": "2024-12-19T12:00:00Z"
    },
    "sub_bins": [
        {
            "id": 123,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Medical Emergency",
            "type": "expenditure",
            "current_amount": 1000.00,
            "depth_level": 1,
            "has_children": true,
            "children_count": 2
        },
        {
            "id": 124,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Car Emergency",
            "type": "expenditure",
            "current_amount": 1500.00,
            "depth_level": 1,
            "has_children": false,
            "children_count": 0
        }
    ],
    "statistics": {
        "total_sub_bins": 5,
        "total_amount": 2500.00,
        "progress_percentage": 25.0,
        "threshold_status": "below_max"
    }
}</code></pre>
            </div>
        </section>

        <section id="sub-bin-endpoints" class="content-section">
            <h2 class="section-title">Sub-Bin Endpoints (Hierarchical)</h2>

            <div class="info-card">
                <h3>🏗️ Hierarchical Sub-Bin System</h3>
                <p>
                    PocketWatch supports unlimited nesting of sub-bins, allowing you to create a pyramid/tree-like structure
                    for better organization of your financial goals. Sub-bins can be created under other sub-bins with
                    subscription-based depth limits.
                </p>
                <p>
                    <strong>Depth Limits:</strong>
                    <span class="premium">Base Tier</span>: Maximum 3 levels deep |
                    <span class="premium">Premium Tier</span>: Unlimited levels
                </p>
                <p>
                    <strong>Structure Example:</strong><br>
                    <code>
                    Bin (Root)<br>
                    ├── Sub-Bin Level 1<br>
                    │   ├── Sub-Bin Level 2<br>
                    │   │   └── Sub-Bin Level 3<br>
                    │   └── Sub-Bin Level 2<br>
                    └── Sub-Bin Level 1
                    </code>
                </p>
            </div>

            <div class="table-container">
                <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="endpoint">/bins/{binId}/sub-bins</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">binId (required): Parent bin ID</td>
                    <td>Get all sub-bins for a bin (flat list).</td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{binId}/sub-bins-hierarchy</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">binId (required): Parent bin ID</td>
                    <td>
                        <strong>🌳 Get hierarchical tree structure</strong> for all sub-bins under a bin.
                        Returns nested tree with children, depth levels, and hierarchy information.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{binId}/sub-bins</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        binId (required): Parent bin ID
                        name (required): Sub-bin name
                        type (required): Sub-bin type (income, expenditure)
                        description (optional): Sub-bin description
                        threshold_min (required): Minimum threshold amount
                        threshold_max (optional): Maximum threshold amount
                        currency (optional): Currency code (default: USD)
                        parent_sub_bin_id (optional): Parent sub-bin ID for nesting
                    </td>
                    <td>
                        <strong>Create a new sub-bin</strong> directly under a bin OR nested under another sub-bin.
                        Use parent_sub_bin_id to create nested sub-bins.
                        <span class="premium">Depth limits apply</span>
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{binId}/sub-bins/{parentSubBinId}/nested</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        binId (required): Parent bin ID
                        parentSubBinId (required): Parent sub-bin ID
                        name (required): Sub-bin name
                        type (required): Sub-bin type (income, expenditure)
                        description (optional): Sub-bin description
                        threshold_min (required): Minimum threshold amount
                        threshold_max (optional): Maximum threshold amount
                        currency (optional): Currency code (default: USD)
                    </td>
                    <td>
                        <strong>🔗 Create nested sub-bin</strong> specifically under another sub-bin.
                        Alternative endpoint for creating hierarchical sub-bins.
                        <span class="premium">Depth limits apply</span>
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{binId}/sub-bins/{id}</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        binId (required): Parent bin ID
                        id (required): Sub-bin ID
                    </td>
                    <td>
                        Get sub-bin details with <strong>hierarchy information</strong> including
                        depth level, path, parent/children relationships, and ancestors.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{binId}/sub-bins/{id}</td>
                    <td><span class="method put">PUT</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        binId (required): Parent bin ID
                        id (required): Sub-bin ID
                        name (optional): Sub-bin name
                        type (optional): Sub-bin type (income, expenditure)
                        description (optional): Sub-bin description
                        threshold_min (optional): Minimum threshold amount
                        threshold_max (optional): Maximum threshold amount
                        currency (optional): Currency code
                        is_active (optional): Sub-bin status (boolean)
                    </td>
                    <td>Update sub-bin. Hierarchy relationships cannot be changed via update.</td>
                </tr>
                <tr>
                    <td class="endpoint">/bins/{binId}/sub-bins/{id}</td>
                    <td><span class="method delete">DELETE</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        binId (required): Parent bin ID
                        id (required): Sub-bin ID
                    </td>
                    <td>
                        <strong>⚠️ Delete sub-bin and all its children</strong> (cascade deletion).
                        This will permanently remove the sub-bin and all nested sub-bins under it.
                    </td>
                </tr>
            </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>📊 API Response Examples</h3>

                <h4>🔹 Create Sub-Bin Response</h4>
                <pre><code>{
    "message": "Sub-bin created successfully",
    "sub_bin": {
        "id": 123,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Emergency Fund",
        "bin_id": 1,
        "parent_sub_bin_id": null,
        "type": "expenditure",
        "description": "Emergency savings fund",
        "threshold_min": 1000.00,
        "threshold_max": 5000.00,
        "current_amount": 0.00,
        "currency": "USD",
        "depth_level": 1,
        "path": "123",
        "is_active": true,
        "created_at": "2024-12-19T10:30:00Z"
    },
    "hierarchy_info": {
        "depth_level": 1,
        "path": "123",
        "is_nested": false,
        "parent_sub_bin": null,
        "max_depth": 3
    },
    "remaining_sub_bins": 9,
    "max_sub_bins": 10
}</code></pre>

                <h4>🔹 Create Nested Sub-Bin Response</h4>
                <pre><code>{
    "message": "Nested sub-bin created successfully under Emergency Fund",
    "sub_bin": {
        "id": 456,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Medical Emergency",
        "bin_id": 1,
        "parent_sub_bin_id": 123,
        "type": "expenditure",
        "description": "Medical emergency fund",
        "threshold_min": 500.00,
        "threshold_max": 2000.00,
        "current_amount": 0.00,
        "currency": "USD",
        "depth_level": 2,
        "path": "123/456",
        "is_active": true,
        "created_at": "2024-12-19T10:35:00Z"
    },
    "hierarchy_info": {
        "depth_level": 2,
        "path": "123/456",
        "is_nested": true,
        "parent_sub_bin": {
            "id": 123,
            "name": "Emergency Fund",
            "depth_level": 1
        },
        "max_depth": 3
    }
}</code></pre>

                <h4>🔹 Get Hierarchy Tree Response</h4>
                <pre><code>{
    "bin": {
        "id": 1,
        "name": "Savings Account",
        "uuid": "550e8400-e29b-41d4-a716-************"
    },
    "hierarchy": [
        {
            "id": 123,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Emergency Fund",
            "type": "expenditure",
            "description": "Emergency savings fund",
            "current_amount": 1500.00,
            "threshold_min": 1000.00,
            "threshold_max": 5000.00,
            "currency": "USD",
            "depth_level": 1,
            "path": "123",
            "is_active": true,
            "created_at": "2024-12-19T10:30:00Z",
            "children": [
                {
                    "id": 456,
                    "uuid": "550e8400-e29b-41d4-a716-************",
                    "name": "Medical Emergency",
                    "type": "expenditure",
                    "current_amount": 500.00,
                    "depth_level": 2,
                    "path": "123/456",
                    "children": [
                        {
                            "id": 789,
                            "name": "Surgery Fund",
                            "depth_level": 3,
                            "path": "123/456/789",
                            "children": [],
                            "children_count": 0,
                            "has_children": false
                        }
                    ],
                    "children_count": 1,
                    "has_children": true
                }
            ],
            "children_count": 1,
            "has_children": true
        }
    ],
    "total_sub_bins": 3,
    "max_depth_allowed": 3
}</code></pre>

                <h4>🔹 Get Sub-Bin Details Response</h4>
                <pre><code>{
    "sub_bin": {
        "id": 456,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Medical Emergency",
        "bin_id": 1,
        "parent_sub_bin_id": 123,
        "type": "expenditure",
        "description": "Medical emergency fund",
        "threshold_min": 500.00,
        "threshold_max": 2000.00,
        "current_amount": 500.00,
        "currency": "USD",
        "depth_level": 2,
        "path": "123/456",
        "is_active": true,
        "created_at": "2024-12-19T10:35:00Z"
    },
    "hierarchy_info": {
        "depth_level": 2,
        "path": "123/456",
        "is_nested": true,
        "has_children": true,
        "children_count": 1,
        "parent_sub_bin": {
            "id": 123,
            "name": "Emergency Fund",
            "depth_level": 1
        },
        "ancestors": [
            {
                "id": 123,
                "name": "Emergency Fund",
                "depth_level": 1
            }
        ]
    },
    "child_sub_bins": [
        {
            "id": 789,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Surgery Fund",
            "type": "expenditure",
            "current_amount": 0.00,
            "depth_level": 3,
            "has_children": false,
            "children_count": 0
        }
    ]
}</code></pre>
            </div>

            <div class="info-card">
                <h3>📊 Hierarchy Response Fields</h3>
                <p>When working with hierarchical sub-bins, responses include additional fields:</p>
                <ul>
                    <li><code>depth_level</code>: How deep the sub-bin is (1 = direct child of bin)</li>
                    <li><code>path</code>: Full path from root (e.g., "123/456/789")</li>
                    <li><code>parent_sub_bin_id</code>: ID of parent sub-bin (null for direct children)</li>
                    <li><code>is_nested</code>: Boolean indicating if sub-bin is nested under another sub-bin</li>
                    <li><code>has_children</code>: Boolean indicating if sub-bin has child sub-bins</li>
                    <li><code>children_count</code>: Number of direct child sub-bins</li>
                    <li><code>ancestors</code>: Array of all parent sub-bins up to the root</li>
                </ul>
            </div>

            <div class="info-card">
                <h3>🚨 Error Responses</h3>
                <p><strong>Maximum Depth Reached:</strong></p>
                <pre><code>{
    "message": "Maximum nesting depth of 3 levels reached. Upgrade to Premium for unlimited nesting.",
    "error": "max_depth_reached",
    "current_depth": 3,
    "max_depth": 3,
    "subscription_tier": "base"
}</code></pre>
                <p><strong>Parent Sub-Bin Not Found:</strong></p>
                <pre><code>{
    "message": "Parent sub-bin not found or does not belong to this bin"
}</code></pre>
            </div>
        </section>

        <section id="transaction-endpoints" class="content-section">
            <h2 class="section-title">Transaction Endpoints</h2>

            <div class="table-container">
                <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="endpoint">/transactions</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        page (optional): Page number per_page (optional): Items
                        per page bin_id (optional): Filter by bin ID sub_bin_id
                        (optional): Filter by sub-bin ID type (optional): Filter
                        by transaction type (income, expense, transfer)
                        start_date (optional): Filter by start date end_date
                        (optional): Filter by end date category (optional):
                        Filter by category <b>user_id (optional): User UUID or integer ID. The system will resolve UUIDs to the correct user and show all their transactions.</b>
                    </td>
                    <td>Get all transactions.<br><b>Note:</b> Previously, filtering by user UUID did not show any transactions. This is now fixed: the API will resolve the UUID to the correct user and display their transactions as expected.</td>
                </tr>
                <tr>
                    <td class="endpoint">/transactions</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        bin_id (optional): Bin ID sub_bin_id (optional): Sub-bin
                        ID transaction_type (required): Type (income, expense,
                        transfer) amount (required): Transaction amount currency
                        (optional): Currency code (default: USD) description
                        (optional): Transaction description category (optional):
                        Transaction category payment_method (optional): Payment
                        method transaction_date (required): Transaction date
                        transaction_time (optional): Transaction time (H:i:s format)
                    </td>
                    <td>Create a new transaction.</td>
                </tr>
                <tr>
                    <td class="endpoint">/transactions/{id}</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Transaction ID</td>
                    <td>Get transaction details.</td>
                </tr>
                <tr>
                    <td class="endpoint">/transactions/{id}</td>
                    <td><span class="method put">PUT</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        id (required): Transaction ID bin_id (optional): Bin ID
                        sub_bin_id (optional): Sub-bin ID transaction_type
                        (optional): Type (income, expense, transfer) amount
                        (optional): Transaction amount currency (optional):
                        Currency code description (optional): Transaction
                        description category (optional): Transaction category
                        payment_method (optional): Payment method
                        transaction_date (optional): Transaction date
                        transaction_time (optional): Transaction time (H:i:s format)
                    </td>
                    <td>Update transaction.</td>
                </tr>
                <tr>
                    <td class="endpoint">/transactions/{id}</td>
                    <td><span class="method delete">DELETE</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Transaction ID</td>
                    <td>Delete transaction.</td>
                </tr>
                <tr>
                    <td class="endpoint">/transactions/stats</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        period (optional): Period (day, week, month, year)
                        start_date (optional): Start date for custom period
                        end_date (optional): End date for custom period
                    </td>
                    <td>Get transaction statistics.</td>
                </tr>
                <tr>
                    <td class="endpoint">/transactions/by-bin/{binId}</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        binId (required): Bin ID start_date (optional): Filter
                        by start date end_date (optional): Filter by end date
                        type (optional): Filter by transaction type page
                        (optional): Page number per_page (optional): Items per
                        page
                    </td>
                    <td>Get transactions by bin.</td>
                </tr>
                <tr>
                    <td class="endpoint">
                        /transactions/by-category/{category}
                    </td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        category (required): Category name start_date
                        (optional): Filter by start date end_date (optional):
                        Filter by end date type (optional): Filter by
                        transaction type page (optional): Page number per_page
                        (optional): Items per page
                    </td>
                    <td>Get transactions by category.</td>
                </tr>
            </tbody>
        </table>

        <section id="subscription-endpoints" class="content-section">
            <h2 class="section-title">Subscription Endpoints (New Trial System)</h2>

            <div class="info-card">
                <h3>🎯 New 3-Step Trial Process</h3>
                <p>
                    The subscription system now follows a 3-step process for trial activation:
                </p>
                <ol>
                    <li><strong>Select Plan:</strong> User chooses base or premium plan</li>
                    <li><strong>Add Payment Method:</strong> User provides payment information</li>
                    <li><strong>Trial Starts:</strong> 7-day trial begins immediately</li>
                </ol>
                <p>
                    <strong>Note:</strong> Only monthly billing is supported. Yearly billing has been removed.
                </p>
            </div>

            <div class="table-container">
                <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="endpoint">/subscriptions</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">None</td>
                    <td>Get all subscriptions for the authenticated user.</td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions/select-plan</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        subscription_tier (required): Subscription tier (base, premium)
                        billing_cycle (required): Billing cycle (monthly only)
                    </td>
                    <td>
                        <strong>Step 1:</strong> Select a subscription plan. This stores the plan choice but doesn't start the trial yet.
                        User status becomes 'plan_selected'.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions/add-payment-method</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        payment_method_id (required): Stripe payment method ID
                    </td>
                    <td>
                        <strong>Step 2:</strong> Add payment method and start 7-day trial immediately.
                        Creates Stripe subscription with trial period. Auto-billing starts after 7 days.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions/cancel</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">None</td>
                    <td>
                        <strong>Cancel subscription</strong> anytime during trial or active period.
                        Cancels Stripe subscription and resets user to 'none' status.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        subscription_tier (required): Subscription tier (base, premium)
                        billing_cycle (required): Billing cycle (monthly only)
                    </td>
                    <td>
                        <strong>Legacy endpoint:</strong> Create a new subscription directly.
                        Base tier is $9.99/month. Premium tier is $19.99/month.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions/plans</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">None</td>
                    <td>Get available subscription plans with pricing and features.</td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions/history</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        page (optional): Page number (default: 1)
                        per_page (optional): Items per page (default: 15)
                    </td>
                    <td>Get subscription history for the authenticated user.</td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions/{uuid}</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">uuid (required): Subscription UUID</td>
                    <td>Get subscription details.</td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions/{uuid}</td>
                    <td><span class="method put">PUT</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        uuid (required): Subscription UUID
                        subscription_tier (required): New subscription tier (base, premium)
                    </td>
                    <td>Update subscription tier (upgrade/downgrade).</td>
                </tr>
                <tr>
                    <td class="endpoint">/subscriptions/{uuid}</td>
                    <td><span class="method delete">DELETE</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        uuid (required): Subscription UUID
                        cancel_reason (optional): Reason for cancellation
                        cancel_feedback (optional): Additional feedback
                    </td>
                    <td>Cancel subscription (legacy endpoint).</td>
                </tr>
            </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>📊 Subscription Response Examples</h3>

                <h4>🔹 Select Plan Response</h4>
                <pre><code>{
    "message": "Plan selected successfully. Add payment method to start your 7-day trial.",
    "user": {
        "id": 1,
        "subscription_tier": "none",
        "selected_plan_tier": "base",
        "selected_billing_cycle": "monthly",
        "trial_started_at": null
    },
    "next_step": "add_payment_method",
    "plan_details": {
        "tier": "base",
        "price": 9.99,
        "currency": "USD",
        "billing_cycle": "monthly",
        "features": [
            "Financial bins with thresholds",
            "Transaction categorization",
            "Basic insights",
            "Hierarchical sub-bins (3 levels)"
        ]
    }
}</code></pre>

                <h4>🔹 Add Payment Method & Start Trial Response</h4>
                <pre><code>{
    "message": "Payment method added successfully. Your 7-day trial has started!",
    "subscription": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "user_id": 1,
        "subscription_tier": "base",
        "stripe_id": "sub_*********0",
        "stripe_status": "trialing",
        "price": 9.99,
        "currency": "USD",
        "billing_cycle": "monthly",
        "trial_ends_at": "2024-12-26T10:00:00Z",
        "created_at": "2024-12-19T10:00:00Z"
    },
    "trial_info": {
        "trial_started": true,
        "trial_days_remaining": 7,
        "trial_ends_at": "2024-12-26T10:00:00Z",
        "auto_billing_date": "2024-12-26T10:00:00Z"
    },
    "user": {
        "subscription_tier": "base",
        "trial_started_at": "2024-12-19T10:00:00Z"
    }
}</code></pre>

                <h4>🔹 Cancel Subscription Response</h4>
                <pre><code>{
    "message": "Subscription canceled successfully. You can continue using PocketWatch until your trial/billing period ends.",
    "subscription": {
        "id": 1,
        "stripe_status": "canceled",
        "ends_at": "2024-12-26T10:00:00Z",
        "canceled_at": "2024-12-19T10:00:00Z"
    },
    "user": {
        "subscription_tier": "none"
    },
    "access_info": {
        "access_until": "2024-12-26T10:00:00Z",
        "days_remaining": 7
    }
}</code></pre>
            </div>
        </section>

        <div class="section">
            <h2>
                Crypto Wallet Endpoints <span class="premium">Premium</span>
            </h2>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="endpoint">/crypto-wallets</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">None</td>
                    <td>
                        Get all crypto wallets. Requires Premium subscription or
                        active trial.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/crypto-wallets</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        wallet_address (required): Blockchain wallet address
                        wallet_name (optional): Custom wallet name
                        blockchain_network (required): Network (ethereum,
                        binance, polygon, avalanche)
                    </td>
                    <td>
                        Create a new crypto wallet. Requires Premium
                        subscription or active trial.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/crypto-wallets/qr-code</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        qr_data (required): QR code data (e.g., "ethereum:0x*********abcdef..." or just "0x*********abcdef...")
                        wallet_name (optional): Custom wallet name
                    </td>
                    <td>
                        Add a crypto wallet by scanning a QR code. Requires Premium
                        subscription or active trial.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/crypto-wallets/{id}</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Wallet ID</td>
                    <td>
                        Get crypto wallet details. Requires Premium subscription
                        or active trial.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/crypto-wallets/{id}</td>
                    <td><span class="method put">PUT</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        id (required): Wallet ID wallet_name (optional): Custom
                        wallet name is_active (optional): Wallet status
                        (boolean)
                    </td>
                    <td>
                        Update crypto wallet. Requires Premium subscription or
                        active trial.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/crypto-wallets/{id}</td>
                    <td><span class="method delete">DELETE</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Wallet ID</td>
                    <td>
                        Delete crypto wallet. Requires Premium subscription or
                        active trial.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/crypto-wallets/{id}/assets</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Wallet ID</td>
                    <td>
                        Get crypto wallet assets. Requires Premium subscription
                        or active trial.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/crypto-wallets/{id}/transactions</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Wallet ID</td>
                    <td>
                        Get crypto wallet transactions. Requires Premium
                        subscription or active trial.
                    </td>
                </tr>
                <tr>
                    <td class="endpoint">/crypto-wallets/{id}/sync</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">id (required): Wallet ID</td>
                    <td>
                        Sync crypto wallet with blockchain. Requires Premium
                        subscription or active trial.
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="section">
            <h2>Webhook Endpoints</h2>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="endpoint">/webhook/stripe</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth no">No</span></td>
                    <td class="params">
                        Stripe-Signature (header, required): Stripe webhook
                        signature
                    </td>
                    <td>Handle Stripe webhook events for subscription processing (payment succeeded, failed, subscription updated, trial will end, etc). Supports automatic trial-to-paid conversion.</td>
                </tr>
            </tbody>
        </table>

        <div class="info-card">
            <h3>Supported Webhook Events</h3>
            <p>The Stripe webhook endpoint handles the following events:</p>
            <ul>
                <li><code>checkout.session.completed</code>: When a user completes subscription setup</li>
                <li><code>customer.subscription.updated</code>: When subscription status changes</li>
                <li><code>customer.subscription.trial_will_end</code>: 3 days before trial expires</li>
                <li><code>customer.subscription.deleted</code>: When subscription is canceled</li>
            </ul>
        </div>

        <section id="reports-endpoints" class="content-section">
            <h2 class="section-title">Reports Endpoints</h2>

            <div class="table-container">
                <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="endpoint">/reports</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        page (optional): Page number
                        per_page (optional): Items per page
                    </td>
                    <td>Get all reports for the authenticated user.</td>
                </tr>
                <tr>
                    <td class="endpoint">/reports</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        name (required): Report name<br>
                        type (required): Report type (transaction, bin, summary)<br>
                        format (required): Report format (csv, pdf)<br>
                        period_type (required): Time period (daily, weekly, monthly, custom)<br>
                        start_date (required if period_type is custom): Start date<br>
                        end_date (required if period_type is custom): End date<br>
                        bin_id (optional): Filter by bin ID<br>
                        sub_bin_id (optional): Filter by sub-bin ID<br>
                        transaction_type (optional): Filter by transaction type (income, expense)<br>
                        min_amount (optional): Filter by minimum amount<br>
                        max_amount (optional): Filter by maximum amount<br>
                        is_recurring (optional): Whether the report is recurring<br>
                        schedule (required if is_recurring is true): Schedule (daily, weekly, monthly)
                    </td>
                    <td>Create a new report. The report will be generated asynchronously and will be available for download when completed.</td>
                </tr>
                <tr>
                    <td class="endpoint">/reports/{id}</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        id (required): Report ID
                    </td>
                    <td>Get details of a specific report.</td>
                </tr>
                <tr>
                    <td class="endpoint">/reports/{id}</td>
                    <td><span class="method delete">DELETE</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        id (required): Report ID
                    </td>
                    <td>Delete a report.</td>
                </tr>
                <tr>
                    <td class="endpoint">/reports/{id}/download</td>
                    <td><span class="method get">GET</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        id (required): Report ID
                    </td>
                    <td>Download a report file (CSV or PDF). The report must be in 'completed' status.</td>
                </tr>
                <tr>
                    <td class="endpoint">/reports/{id}/regenerate</td>
                    <td><span class="method post">POST</span></td>
                    <td><span class="auth yes">Yes</span></td>
                    <td class="params">
                        id (required): Report ID
                    </td>
                    <td>Regenerate a report. This is useful if the report generation failed or if you want to update the report with the latest data.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">
            <h2>Subscription Tiers</h2>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Feature</th>
                    <th>Base Tier ($9.99/month)</th>
                    <th>Premium Tier ($19.99/month)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Secure user registration and login</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Link bank accounts via Plaid</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>View total balance on dashboard</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Hide/Show balance with eye toggle</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Create financial bins with thresholds</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Automatic transaction categorization</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Edit bins manually</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>View graphical insights</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>View recent transactions carousel</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Chat with Binnit chatbot</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Notification system</td>
                    <td>✅</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>🏗️ Hierarchical Sub-Bin Nesting</td>
                    <td>Maximum 3 levels deep</td>
                    <td>Unlimited levels</td>
                </tr>
                <tr>
                    <td>Crypto Scanner Page</td>
                    <td>❌</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Priority notifications</td>
                    <td>❌</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>Advanced Binnit AI suggestions</td>
                    <td>❌</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>🎯 New Free Trial System</td>
                    <td colspan="2" style="text-align: center">
                        7 days starting when payment method is added. 3-step process: Select Plan → Add Payment → Trial Starts.
                        Cancel anytime during trial without charge. Auto-billing after 7 days if not canceled.
                    </td>
                </tr>
            </tbody>
                </table>
            </div>
        </section>

        <section id="plaid-endpoints" class="content-section">
            <h2 class="section-title">Plaid Integration <span class="premium">Premium</span></h2>

            <div class="info-card">
                <h3>Plaid Integration Overview</h3>
                <p>
                    PocketWatch integrates with Plaid to provide secure bank account linking and payment capabilities.
                    This feature allows users to connect their bank accounts, view balances, and make subscription payments directly from their bank accounts.
                </p>
                <p>
                    <strong>Premium Feature:</strong> Plaid integration is available to all users during their free trial period and to Premium subscribers afterward.
                </p>
                <p>
                    <strong>Implementation Notes:</strong>
                </p>
                <ul style="margin-left: 20px; margin-bottom: 15px;">
                    <li>Use the Plaid Link SDK in your Flutter app to initiate the account linking process</li>
                    <li>The Plaid Link flow requires a link token, which you can obtain from the <code>/plaid/link-token</code> endpoint</li>
                    <li>After successful linking, you'll receive a public token that should be exchanged for an access token via the <code>/plaid/accounts</code> endpoint</li>
                    <li>Plaid webhooks are used to keep account information up-to-date</li>
                </ul>
                <p>
                    <strong>Security:</strong> PocketWatch never stores users' bank credentials. All sensitive information is handled by Plaid's secure systems.
                </p>
            </div>

            <h3 style="margin-top: 30px; margin-bottom: 20px; color: var(--primary-color);">Plaid Account Endpoints</h3>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Auth Required</th>
                            <th>Parameters</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="endpoint">/plaid/link-token</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>Get a Plaid link token to initialize the Plaid Link flow.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/plaid/accounts</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                public_token (required): Public token from Plaid Link
                                accounts (required): Array of selected accounts
                            </td>
                            <td>Link Plaid accounts to the user's profile.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/plaid/accounts</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>Get the user's linked Plaid accounts.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/plaid/accounts/{id}/set-default</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">id (required): Plaid account ID</td>
                            <td>Set a Plaid account as the default account.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/plaid/accounts/{id}/toggle-payment</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">id (required): Plaid account ID</td>
                            <td>Enable or disable payment for a Plaid account.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/plaid/accounts/{id}</td>
                            <td><span class="method delete">DELETE</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">id (required): Plaid account ID</td>
                            <td>Delete (unlink) a Plaid account.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="plaid-payment-endpoints" class="content-section">
            <h2 class="section-title">Plaid Payment Endpoints <span class="premium">Premium</span></h2>

            <div class="info-card">
                <h3>Plaid Payment Integration</h3>
                <p>
                    PocketWatch allows users to make subscription payments directly from their bank accounts using Plaid's payment initiation service.
                    This provides a convenient alternative to credit card payments.
                </p>
                <p>
                    <strong>Implementation Flow:</strong>
                </p>
                <ol style="margin-left: 20px; margin-bottom: 15px;">
                    <li>First, enable payment on a linked Plaid account using the <code>/plaid/accounts/{id}/toggle-payment</code> endpoint</li>
                    <li>Retrieve payment-enabled accounts using the <code>/plaid-payment/accounts</code> endpoint</li>
                    <li>Process a payment using the <code>/plaid-payment/process</code> endpoint</li>
                    <li>Payment status updates are delivered via Plaid webhooks</li>
                </ol>
                <p>
                    <strong>Note:</strong> Payment processing may take up to 24 hours to complete, depending on the user's bank.
                </p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Auth Required</th>
                            <th>Parameters</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="endpoint">/plaid-payment/accounts</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>Get the user's payment-enabled Plaid accounts.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/plaid-payment/process</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                plaid_account_id (required): ID of the Plaid account to use for payment
                                subscription_tier (required): Subscription tier (base, premium)
                                billing_cycle (required): Billing cycle (monthly, yearly)
                            </td>
                            <td>Process a payment using a Plaid account.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/plaid-payment/status/{payment_id}</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                payment_id (required): ID of the payment to check
                            </td>
                            <td>Check the status of a Plaid payment.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="crypto-endpoints" class="content-section">
            <h2 class="section-title">Crypto Wallet Endpoints <span class="premium">Premium</span></h2>

            <div class="info-card">
                <h3>Premium Feature</h3>
                <p>
                    These endpoints are only available to users with a Premium subscription or during an active trial period.
                </p>
                <p>
                    <strong>Rate Limits:</strong> Crypto endpoints are limited to 100 requests per hour per user to prevent API abuse.
                </p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Auth Required</th>
                            <th>Parameters</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="endpoint">/crypto/connect</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                wallet_address (required): Cryptocurrency wallet address (e.g., Ethereum address)
                                blockchain_network (required): Network type (ethereum, binance, polygon, etc.)
                                wallet_name (optional): Custom name for the wallet
                            </td>
                            <td>Connect a cryptocurrency wallet to the user's account. Requires Premium subscription.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/crypto/connect/qr-code</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                qr_data (required): QR code data (e.g., "ethereum:0x*********abcdef..." or just "0x*********abcdef...")
                                wallet_name (optional): Custom name for the wallet
                            </td>
                            <td>Connect a cryptocurrency wallet by scanning a QR code. Requires Premium subscription.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/crypto/wallets</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">None</td>
                            <td>Get all connected cryptocurrency wallets for the user. Requires Premium subscription.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/crypto/portfolio/{wallet_id}</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                wallet_id (required): ID of the connected wallet
                            </td>
                            <td>Get detailed portfolio information for a connected wallet. Requires Premium subscription.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/crypto/advice/{wallet_id}</td>
                            <td><span class="method get">GET</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                wallet_id (required): ID of the connected wallet
                            </td>
                            <td>Get AI-generated investment advice for the wallet's portfolio. Requires Premium subscription.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/crypto/refresh/{wallet_id}</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                wallet_id (required): ID of the connected wallet
                            </td>
                            <td>Manually trigger a refresh of the wallet's portfolio data. Requires Premium subscription.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/crypto/analyze</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                wallet (required): Wallet address
                                tokens (required): JSON object containing token holdings
                            </td>
                            <td>Send portfolio data to Binnit AI for analysis and receive investment advice. This endpoint is primarily used by the backend system. Requires Premium subscription.</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/crypto/wallets/{wallet_id}</td>
                            <td><span class="method delete">DELETE</span></td>
                            <td><span class="auth yes">Yes</span></td>
                            <td class="params">
                                wallet_id (required): ID of the connected wallet
                            </td>
                            <td>Disconnect a cryptocurrency wallet from the user's account. Requires Premium subscription.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="webhook-endpoints" class="content-section">
            <h2 class="section-title">Webhook Endpoints</h2>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Auth Required</th>
                            <th>Parameters</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="endpoint">/webhook/stripe</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth no">No</span></td>
                            <td class="params">
                                Stripe-Signature (header, required): Stripe webhook
                                signature
                            </td>
                            <td>Handle Stripe webhook events for subscription processing (payment succeeded, failed, subscription updated, etc).</td>
                        </tr>
                        <tr>
                            <td class="endpoint">/webhook/plaid</td>
                            <td><span class="method post">POST</span></td>
                            <td><span class="auth no">No</span></td>
                            <td class="params">
                                Plaid-Signature (header, required): Plaid webhook
                                signature
                            </td>
                            <td>Handle Plaid webhook events for account updates, transactions, and payment status changes.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="error-responses" class="content-section">
            <h2 class="section-title">Common Error Responses</h2>

            <div class="info-card">
                <h3>📊 Standard HTTP Status Codes</h3>
                <p>The PocketWatch API uses standard HTTP status codes to indicate the success or failure of requests:</p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Status Code</th>
                            <th>Meaning</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="method get">200</span></td>
                            <td>OK</td>
                            <td>Request successful</td>
                        </tr>
                        <tr>
                            <td><span class="method post">201</span></td>
                            <td>Created</td>
                            <td>Resource created successfully</td>
                        </tr>
                        <tr>
                            <td><span class="method delete">400</span></td>
                            <td>Bad Request</td>
                            <td>Invalid request parameters or malformed JSON</td>
                        </tr>
                        <tr>
                            <td><span class="method delete">401</span></td>
                            <td>Unauthorized</td>
                            <td>Authentication required or invalid token</td>
                        </tr>
                        <tr>
                            <td><span class="method delete">402</span></td>
                            <td>Payment Required</td>
                            <td>Trial expired or subscription required</td>
                        </tr>
                        <tr>
                            <td><span class="method delete">403</span></td>
                            <td>Forbidden</td>
                            <td>Insufficient permissions or subscription limits reached</td>
                        </tr>
                        <tr>
                            <td><span class="method delete">404</span></td>
                            <td>Not Found</td>
                            <td>Resource not found or doesn't belong to user</td>
                        </tr>
                        <tr>
                            <td><span class="method delete">422</span></td>
                            <td>Unprocessable Entity</td>
                            <td>Validation errors in request data</td>
                        </tr>
                        <tr>
                            <td><span class="method delete">429</span></td>
                            <td>Too Many Requests</td>
                            <td>Rate limit exceeded</td>
                        </tr>
                        <tr>
                            <td><span class="method delete">500</span></td>
                            <td>Internal Server Error</td>
                            <td>Server error occurred</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="info-card">
                <h3>📊 Common Error Response Examples</h3>

                <h4>🔹 Authentication Error (401)</h4>
                <pre><code>{
    "message": "Unauthenticated.",
    "error": "authentication_required"
}</code></pre>

                <h4>🔹 Trial Expired (402)</h4>
                <pre><code>{
    "message": "Your trial has expired. Please subscribe to continue using PocketWatch.",
    "error": "trial_expired",
    "trial_expired_at": "2024-12-19T10:00:00Z",
    "subscription_required": true,
    "available_plans": [
        {
            "tier": "base",
            "price": 9.99,
            "currency": "USD"
        },
        {
            "tier": "premium",
            "price": 19.99,
            "currency": "USD"
        }
    ]
}</code></pre>

                <h4>🔹 Subscription Limit Reached (403)</h4>
                <pre><code>{
    "message": "You have reached the maximum number of bins for your subscription tier. Upgrade to Premium for unlimited bins.",
    "error": "subscription_limit_reached",
    "current_count": 10,
    "max_allowed": 10,
    "subscription_tier": "base",
    "upgrade_required": true
}</code></pre>

                <h4>🔹 Validation Error (422)</h4>
                <pre><code>{
    "message": "The given data was invalid.",
    "errors": {
        "name": [
            "The name field is required."
        ],
        "threshold_min": [
            "The threshold min must be a number.",
            "The threshold min must be at least 0."
        ],
        "email": [
            "The email has already been taken."
        ]
    }
}</code></pre>

                <h4>🔹 Resource Not Found (404)</h4>
                <pre><code>{
    "message": "Bin not found or does not belong to this user.",
    "error": "resource_not_found",
    "resource_type": "bin",
    "resource_id": "123"
}</code></pre>

                <h4>🔹 Rate Limit Exceeded (429)</h4>
                <pre><code>{
    "message": "Too many requests. Please try again later.",
    "error": "rate_limit_exceeded",
    "retry_after": 60,
    "limit": 60,
    "remaining": 0,
    "reset_at": "2024-12-19T10:01:00Z"
}</code></pre>

                <h4>🔹 Package Purchase Failed (500)</h4>
                <pre><code>{
    "success": false,
    "message": "Failed to purchase package: Invalid API Key provided: sk_test_****_key",
    "error_code": "purchase_failed"
}</code></pre>

                <h4>🔹 Package Not Found (400)</h4>
                <pre><code>{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "package_id": [
            "The selected package id is invalid."
        ]
    }
}</code></pre>

                <h4>🔹 No Package Selected (402)</h4>
                <pre><code>{
    "message": "Please select a plan and purchase a package to continue.",
    "subscription_required": true,
    "user_status": "none",
    "available_actions": {
        "view_packages": "/api/packages",
        "purchase_package": "/api/packages/purchase-test"
    }
}</code></pre>

                <h4>🔹 Server Error (500)</h4>
                <pre><code>{
    "message": "An unexpected error occurred. Please try again later.",
    "error": "internal_server_error",
    "error_id": "550e8400-e29b-41d4-a716-************"
}</code></pre>
            </div>

            <div class="info-card">
                <h3>🔧 Error Handling Best Practices</h3>
                <ul>
                    <li><strong>Always check status codes:</strong> Use HTTP status codes to determine request success</li>
                    <li><strong>Handle validation errors:</strong> Parse the <code>errors</code> object for field-specific validation messages</li>
                    <li><strong>Implement retry logic:</strong> For 429 and 5xx errors, implement exponential backoff</li>
                    <li><strong>Check subscription limits:</strong> Handle 402/403 errors by prompting users to upgrade</li>
                    <li><strong>Log error IDs:</strong> Include error IDs in support requests for faster debugging</li>
                    <li><strong>Graceful degradation:</strong> Provide fallback functionality when possible</li>
                </ul>
            </div>
        </section>

        <section id="rate-limits" class="content-section">
            <h2 class="section-title">API Rate Limits</h2>

            <div class="info-card">
                <h3>Rate Limiting</h3>
                <p>
                    To ensure the stability and performance of the API, rate limits are enforced on all endpoints.
                    When a rate limit is exceeded, the API will return a <code>429 Too Many Requests</code> response.
                </p>
                <p>
                    The response headers include <code>X-RateLimit-Limit</code>, <code>X-RateLimit-Remaining</code>, and <code>X-RateLimit-Reset</code>
                    to help you track your usage.
                </p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint Group</th>
                            <th>Rate Limit</th>
                            <th>Time Window</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Standard Endpoints</td>
                            <td>60 requests</td>
                            <td>Per minute</td>
                            <td>Applied per user or IP address</td>
                        </tr>
                        <tr>
                            <td>Crypto Endpoints</td>
                            <td>100 requests</td>
                            <td>Per hour</td>
                            <td>Applied per user to prevent API abuse</td>
                        </tr>
                        <tr>
                            <td>Webhook Endpoints</td>
                            <td>1000 requests</td>
                            <td>Per day</td>
                            <td>Applied per webhook source (Stripe, Plaid)</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="bins-by-type">
          <h2>Bins: Filtered by Type (Income/Expense)</h2>
          <h3>GET /api/bins/income</h3>
          <p>Returns all income bins and their income sub-bins for the authenticated user.</p>
          <ul>
            <li><strong>Auth required:</strong> Yes (Bearer token)</li>
            <li><strong>Parameters:</strong> None</li>
          </ul>
          <pre><code>{
  "bins": [
    {
      "id": 1,
      "name": "Salary Bin",
      "type": "income",
      "sub_bins": [
        { "id": 10, "name": "Bonus", "type": "income" }
      ]
    },
    ...
  ]
}</code></pre>

          <h3>GET /api/bins/expense</h3>
          <p>Returns all expense bins and their expense sub-bins for the authenticated user.</p>
          <ul>
            <li><strong>Auth required:</strong> Yes (Bearer token)</li>
            <li><strong>Parameters:</strong> None</li>
          </ul>
          <pre><code>{
  "bins": [
    {
      "id": 2,
      "name": "Groceries Bin",
      "type": "expense",
      "sub_bins": [
        { "id": 20, "name": "Supermarket", "type": "expense" }
      ]
    },
    ...
  ]
}</code></pre>

          <p><strong>Usage:</strong></p>
          <ul>
            <li>Call <code>/api/bins/income</code> when the user selects <code>transaction_type = income</code>.</li>
            <li>Call <code>/api/bins/expense</code> when the user selects <code>transaction_type = expense</code>.</li>
            <li>Use the returned bins/sub-bins to populate the bin selection in your transaction creation flow.</li>
          </ul>
        </section>

        <footer class="footer">
            <p>PocketWatch API Documentation | Last updated: December 19, 2024 | v3.0: Stripe Checkout + Google OAuth + Hierarchical Sub-Bins</p>
        </footer>

        <script>
            // Theme toggle functionality
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');

            // Check for saved theme preference or respect OS preference
            const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
            const savedTheme = localStorage.getItem('theme');

            if (savedTheme === 'dark' || (!savedTheme && prefersDarkScheme.matches)) {
                document.documentElement.setAttribute('data-theme', 'dark');
                themeIcon.classList.replace('fa-moon', 'fa-sun');
            }

            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                // Update icon
                if (newTheme === 'dark') {
                    themeIcon.classList.replace('fa-moon', 'fa-sun');
                } else {
                    themeIcon.classList.replace('fa-sun', 'fa-moon');
                }
            });

            // Mobile menu toggle
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const sidebar = document.getElementById('sidebar');

            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('active');

                // Change icon based on sidebar state
                const icon = mobileMenuToggle.querySelector('i');
                if (sidebar.classList.contains('active')) {
                    icon.classList.replace('fa-bars', 'fa-times');
                } else {
                    icon.classList.replace('fa-times', 'fa-bars');
                }
            });

            // Search functionality
            const searchInput = document.getElementById('search-input');
            const mobileSearchInput = document.getElementById('mobile-search-input');
            const endpoints = document.querySelectorAll('.endpoint');

            function performSearch(searchTerm) {
                const normalizedSearchTerm = searchTerm.toLowerCase().trim();

                if (normalizedSearchTerm === '') {
                    // Reset all visibility
                    document.querySelectorAll('tr').forEach(row => {
                        row.style.display = '';
                    });
                    return;
                }

                // Hide all rows first
                document.querySelectorAll('tr').forEach(row => {
                    const endpoint = row.querySelector('.endpoint');
                    if (endpoint) {
                        const endpointText = endpoint.textContent.toLowerCase();
                        const description = row.querySelector('td:last-child').textContent.toLowerCase();

                        if (endpointText.includes(normalizedSearchTerm) || description.includes(normalizedSearchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
            }

            searchInput.addEventListener('input', (e) => {
                performSearch(e.target.value);
                // Sync with mobile search
                mobileSearchInput.value = e.target.value;
            });

            mobileSearchInput.addEventListener('input', (e) => {
                performSearch(e.target.value);
                // Sync with desktop search
                searchInput.value = e.target.value;
            });

            // Active link highlighting
            const sidebarLinks = document.querySelectorAll('.sidebar-link');

            function setActiveLink() {
                const scrollPosition = window.scrollY;

                document.querySelectorAll('.content-section').forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    const sectionBottom = sectionTop + section.offsetHeight;

                    if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                        const id = section.getAttribute('id');

                        // Remove active class from all links
                        sidebarLinks.forEach(link => {
                            link.classList.remove('active');
                        });

                        // Add active class to current link
                        const activeLink = document.querySelector(`.sidebar-link[href="#${id}"]`);
                        if (activeLink) {
                            activeLink.classList.add('active');
                        }
                    }
                });
            }

            window.addEventListener('scroll', setActiveLink);

            // Initial call to set active link
            setActiveLink();
        </script>
    </body>
</html>
