<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PlaidAccount;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PlaidPaymentController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Get payment accounts for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentAccounts()
    {
        $accounts = Auth::user()->plaidAccounts()
            ->where('is_payment_enabled', true)
            ->get()
            ->map(function ($account) {
                return [
                    'id' => $account->id,
                    'uuid' => $account->uuid,
                    'institution_name' => $account->institution_name,
                    'account_name' => $account->account_name,
                    'account_type' => $account->account_type,
                    'account_mask' => $account->account_mask,
                ];
            });
            
        return response()->json([
            'success' => true,
            'accounts' => $accounts,
        ]);
    }

    /**
     * Process a payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processPayment(Request $request)
    {
        $request->validate([
            'account_id' => 'required|exists:plaid_accounts,id',
            'package_id' => 'required|string',
            'amount' => 'required|numeric|min:1',
        ]);
        
        $account = PlaidAccount::where('user_id', Auth::id())
            ->where('id', $request->account_id)
            ->where('is_payment_enabled', true)
            ->firstOrFail();
            
        // In a real implementation, you would use Plaid's payment_initiation
        // product to create a payment. For this example, we'll simulate it.
        
        // Create a payment record
        $paymentId = Str::uuid();
        
        // Create or update subscription based on the package
        $packageParts = explode('_', $request->package_id);
        $tier = $packageParts[0] ?? 'base';
        $cycle = $packageParts[1] ?? 'monthly';
        
        $subscription = Subscription::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'status' => 'active',
            ],
            [
                'name' => ucfirst($tier) . ' Tier',
                'stripe_id' => null, // Not using Stripe
                'stripe_status' => 'active',
                'stripe_price' => null,
                'quantity' => 1,
                'trial_ends_at' => null,
                'ends_at' => null,
                'payment_method' => 'plaid',
                'payment_id' => $paymentId,
                'billing_cycle' => $cycle,
            ]
        );
        
        // Update user's subscription tier
        Auth::user()->update([
            'subscription_tier' => $tier,
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Payment processed successfully',
            'payment_id' => $paymentId,
            'subscription' => [
                'id' => $subscription->id,
                'name' => $subscription->name,
                'status' => $subscription->stripe_status,
                'billing_cycle' => $subscription->billing_cycle,
            ],
        ]);
    }

    /**
     * Get the status of a payment.
     *
     * @param  string  $paymentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentStatus($paymentId)
    {
        // In a real implementation, you would check the status of the payment
        // with Plaid's API. For this example, we'll simulate it.
        
        return response()->json([
            'success' => true,
            'status' => 'completed',
            'last_status_update' => now()->toIso8601String(),
        ]);
    }
}
