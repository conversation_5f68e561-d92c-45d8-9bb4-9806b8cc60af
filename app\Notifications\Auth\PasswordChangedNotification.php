<?php

namespace App\Notifications\Auth;

use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class PasswordChangedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The timestamp when the password was changed.
     *
     * @var \Illuminate\Support\Carbon
     */
    protected $timestamp;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_password_changes';

    public function __construct()
    {
        $this->timestamp = now();

        $this->subject = 'Your PocketWatch Password Has Been Changed';
        $this->title = 'Password Changed';
        $this->content = 'Your PocketWatch account password has been successfully changed. If you made this change, 
                         you can safely ignore this email.';
        
        $this->detailsTitle = 'Change Details';
        $this->details = [
            'Date & Time' => $this->timestamp->format('F j, Y, g:i a'),
        ];
        
        $this->actionText = 'Contact Support';
        $this->actionUrl = url('/contact');
        
        $this->closing = 'If you did not change your password, please contact our support team immediately.';
        $this->signature = 'The PocketWatch Security Team';
    }
}
