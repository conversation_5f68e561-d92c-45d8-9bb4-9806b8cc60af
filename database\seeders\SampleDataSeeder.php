<?php

namespace Database\Seeders;

use App\Models\Bin;
use App\Models\CryptoWallet;
use App\Models\SubBin;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->error('No users found. Please run AdminUserSeeder first.');
            return;
        }

        // Create bins for each user
        $this->createBins($users);

        // Create subscriptions
        $this->createSubscriptions($users);

        // Create crypto wallets for premium users
        $premiumUsers = $users->filter(function ($user) {
            return $user->subscription_tier === 'premium' && $user->is_active && !$user->is_admin;
        });
        $this->createCryptoWallets($premiumUsers);

        // Output success message
        $this->command->info('Sample data created successfully!');
    }

    /**
     * Create bins and sub-bins for users
     */
    private function createBins($users)
    {
        foreach ($users as $user) {
            // Skip inactive users and admin users
            if (!$user->is_active || $user->is_admin) {
                continue;
            }

            // Determine number of bins based on subscription tier
            $binCount = $user->subscription_tier === 'premium' ? rand(3, 5) : rand(1, 3);

            for ($i = 0; $i < $binCount; $i++) {
                $bin = Bin::create([
                    'user_id' => $user->id,
                    'name' => $this->getBinName($i),
                    'description' => 'Sample bin description for ' . $user->name,
                    'threshold_min' => rand(100, 500),
                    'threshold_max' => rand(1000, 5000),
                    'current_amount' => rand(0, 3000),
                    'currency' => 'USD',
                    'is_active' => true,
                ]);

                // Create sub-bins
                $subBinCount = rand(0, 3);
                for ($j = 0; $j < $subBinCount; $j++) {
                    SubBin::create([
                        'bin_id' => $bin->id,
                        'name' => $this->getSubBinName($j),
                        'description' => 'Sample sub-bin description',
                        'threshold_min' => rand(50, 200),
                        'threshold_max' => rand(500, 1000),
                        'current_amount' => rand(0, 800),
                        'currency' => 'USD',
                        'is_active' => true,
                    ]);
                }

                // Create transactions for this bin
                $this->createTransactions($user, $bin);
            }
        }
    }

    /**
     * Create transactions for a bin
     */
    private function createTransactions($user, $bin)
    {
        $transactionCount = rand(5, 15);
        $transactionTypes = ['income', 'expense', 'transfer'];
        $categories = ['Food', 'Transport', 'Entertainment', 'Bills', 'Shopping', 'Salary', 'Investment'];
        $paymentMethods = ['Credit Card', 'Debit Card', 'Cash', 'Bank Transfer', 'PayPal'];

        for ($i = 0; $i < $transactionCount; $i++) {
            $type = $transactionTypes[array_rand($transactionTypes)];
            $amount = $type === 'income' ? rand(100, 1000) : rand(10, 500);
            $date = Carbon::now()->subDays(rand(1, 60));

            // Randomly assign to sub-bin
            $subBinId = null;
            if ($bin->subBins->count() > 0 && rand(0, 1)) {
                $subBinId = $bin->subBins->random()->id;
            }

            Transaction::create([
                'user_id' => $user->id,
                'bin_id' => $bin->id,
                'sub_bin_id' => $subBinId,
                'transaction_type' => $type,
                'amount' => $amount,
                'currency' => 'USD',
                'description' => 'Sample ' . $type . ' transaction',
                'category' => $categories[array_rand($categories)],
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'status' => 'completed',
                'source' => 'manual',
                'transaction_date' => $date,
            ]);
        }
    }

    /**
     * Create subscriptions for users
     */
    private function createSubscriptions($users)
    {
        $stripeIds = [
            'sub_1NxYZ2CZ6qsJgndJK8hX2X1Z',
            'sub_1NxYZaCZ6qsJgndJL9iA3B2Y',
            'sub_1NxYZzCZ6qsJgndJM0jB4C3Z',
            'sub_1NxYaACZ6qsJgndJN1kC5D4A',
        ];

        // Only monthly subscriptions (yearly removed)
        $priceIds = [
            'base_monthly' => 'price_1NxYbBCZ6qsJgndJO2lD6E5B',
            'premium_monthly' => 'price_1NxYbDCZ6qsJgndJQ4nF8G7D',
        ];

        $prices = [
            'base_monthly' => 9.99,
            'premium_monthly' => 19.99,
        ];

        foreach ($users as $user) {
            // Skip inactive users, admin users, and users with no subscription
            if (!$user->is_active || $user->is_admin || $user->subscription_tier === 'none') {
                continue;
            }

            $tier = $user->subscription_tier;

            // Only monthly billing cycle (yearly removed)
            $cycle = 'monthly';
            $status = rand(0, 10) > 8 ? 'canceled' : 'active';

            $priceKey = "{$tier}_{$cycle}";

            // Check if the price key exists
            if (!isset($prices[$priceKey]) || !isset($priceIds[$priceKey])) {
                $this->command->warn("Skipping subscription for user {$user->name} - invalid tier: {$tier}");
                continue;
            }

            $price = $prices[$priceKey];
            $priceId = $priceIds[$priceKey];

            $subscription = Subscription::create([
                'user_id' => $user->id,
                'name' => ucfirst($tier) . ' ' . ucfirst($cycle),
                'stripe_id' => $stripeIds[array_rand($stripeIds)],
                'stripe_status' => $status,
                'stripe_price' => $priceId,
                'subscription_tier' => $tier,
                'price' => $price,
                'currency' => 'USD',
                'billing_cycle' => $cycle,
                'features' => $this->getFeatures($tier),
            ]);

            // Set end date for canceled subscriptions
            if ($status === 'canceled') {
                $subscription->ends_at = Carbon::now()->addDays(rand(1, 30));
                $subscription->save();
            }
        }
    }

    /**
     * Create crypto wallets for premium users
     */
    private function createCryptoWallets($users)
    {
        $networks = ['ethereum', 'binance', 'polygon', 'avalanche'];
        $walletAddresses = [
            '******************************************',
            '******************************************',
            '******************************************',
            '******************************************',
        ];

        foreach ($users as $user) {
            // Create 1-2 wallets per premium user
            $walletCount = rand(1, 2);

            for ($i = 0; $i < $walletCount; $i++) {
                $network = $networks[array_rand($networks)];
                $address = $walletAddresses[array_rand($walletAddresses)];

                $wallet = CryptoWallet::create([
                    'user_id' => $user->id,
                    'wallet_address' => $address,
                    'wallet_name' => 'My ' . ucfirst($network) . ' Wallet',
                    'blockchain_network' => $network,
                    'is_active' => true,
                    'last_synced_at' => Carbon::now()->subHours(rand(1, 48)),
                ]);

                // Simulate wallet sync
                $assets = $this->getSimulatedAssets($network);
                $totalValue = array_sum(array_column($assets, 'value_usd'));

                $wallet->assets = $assets;
                $wallet->total_value_usd = $totalValue;
                $wallet->ai_advice = $this->generateAiAdvice($assets, $totalValue);
                $wallet->save();
            }
        }
    }

    /**
     * Get bin name based on index
     */
    private function getBinName($index)
    {
        $names = [
            'Emergency Fund',
            'Savings',
            'Investments',
            'Vacation',
            'Home Purchase',
            'Education',
            'Retirement',
            'Car Fund',
            'Wedding',
            'Medical Expenses',
        ];

        return $names[$index % count($names)];
    }

    /**
     * Get sub-bin name based on index
     */
    private function getSubBinName($index)
    {
        $names = [
            'Short Term',
            'Long Term',
            'High Priority',
            'Low Priority',
            'Monthly',
            'Quarterly',
            'Annual',
        ];

        return $names[$index % count($names)];
    }

    /**
     * Get features based on subscription tier
     */
    private function getFeatures($tier)
    {
        $features = [
            'base' => [
                'max_bins' => 5,
                'max_sub_bins' => 10,
                'crypto_analysis' => false,
                'advanced_analytics' => false,
            ],
            'premium' => [
                'max_bins' => 20,
                'max_sub_bins' => 50,
                'crypto_analysis' => true,
                'advanced_analytics' => true,
            ],
        ];

        return $features[$tier] ?? $features['base'];
    }

    /**
     * Get simulated assets for testing
     */
    private function getSimulatedAssets($network)
    {
        $assets = [];

        switch ($network) {
            case 'ethereum':
                $assets = [
                    [
                        'token_address' => '******************************************',
                        'symbol' => 'ETH',
                        'name' => 'Ethereum',
                        'balance' => '1.5',
                        'price_usd' => 3500,
                        'value_usd' => 5250,
                    ],
                    [
                        'token_address' => '******************************************',
                        'symbol' => 'USDT',
                        'name' => 'Tether',
                        'balance' => '1000',
                        'price_usd' => 1,
                        'value_usd' => 1000,
                    ],
                ];
                break;
            case 'binance':
                $assets = [
                    [
                        'token_address' => '******************************************',
                        'symbol' => 'BNB',
                        'name' => 'Binance Coin',
                        'balance' => '10',
                        'price_usd' => 500,
                        'value_usd' => 5000,
                    ],
                ];
                break;
            case 'polygon':
                $assets = [
                    [
                        'token_address' => '******************************************',
                        'symbol' => 'MATIC',
                        'name' => 'Polygon',
                        'balance' => '1000',
                        'price_usd' => 1.5,
                        'value_usd' => 1500,
                    ],
                ];
                break;
            case 'avalanche':
                $assets = [
                    [
                        'token_address' => '******************************************',
                        'symbol' => 'AVAX',
                        'name' => 'Avalanche',
                        'balance' => '50',
                        'price_usd' => 30,
                        'value_usd' => 1500,
                    ],
                ];
                break;
        }

        return $assets;
    }

    /**
     * Generate AI advice for wallet
     */
    private function generateAiAdvice($assets, $totalValue)
    {
        $advice = [
            'generated_at' => Carbon::now()->toIso8601String(),
            'recommendations' => [],
        ];

        // Simple logic for recommendations
        if (count($assets) < 3) {
            $advice['recommendations'][] = [
                'type' => 'diversification',
                'message' => 'Your portfolio is not well diversified. Consider adding more assets to reduce risk.',
                'importance' => 'high',
            ];
        }

        // Check for stablecoins
        $hasStablecoins = false;
        foreach ($assets as $asset) {
            if (in_array($asset['symbol'], ['USDT', 'USDC', 'DAI', 'BUSD'])) {
                $hasStablecoins = true;
                break;
            }
        }

        if (!$hasStablecoins) {
            $advice['recommendations'][] = [
                'type' => 'stability',
                'message' => 'Consider adding stablecoins to your portfolio for stability during market volatility.',
                'importance' => 'medium',
            ];
        }

        // Check for high concentration
        foreach ($assets as $asset) {
            $percentage = ($asset['value_usd'] / $totalValue) * 100;
            if ($percentage > 70) {
                $advice['recommendations'][] = [
                    'type' => 'concentration',
                    'message' => "Your portfolio is heavily concentrated in {$asset['name']} ({$asset['symbol']}). Consider diversifying to reduce risk.",
                    'importance' => 'high',
                ];
            }
        }

        return $advice;
    }
}
