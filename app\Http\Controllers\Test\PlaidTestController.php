<?php

namespace App\Http\Controllers\Test;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

class PlaidTestController extends Controller
{
    private $plaidClient;
    private $environment;
    private $clientId;
    private $secret;

    public function __construct()
    {
        $this->environment = config('services.plaid.environment', 'sandbox');
        $this->clientId = config('services.plaid.client_id');
        $this->secret = config('services.plaid.secret');
        
        // Set Plaid API URL based on environment
        $this->plaidClient = $this->getPlaidApiUrl();
    }

    /**
     * Show the Plaid test page
     */
    public function index()
    {
        return view('test.plaid-test');
    }

    /**
     * Test basic API connectivity to Plaid
     */
    public function testApiConnection(): JsonResponse
    {
        try {
            // Test with a simple API call to check connectivity
            $response = Http::timeout(10)->post($this->plaidClient . '/institutions/get', [
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'count' => 1,
                'offset' => 0,
                'country_codes' => ['US']
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return response()->json([
                    'success' => true,
                    'message' => 'Successfully connected to Plaid API',
                    'environment' => $this->environment,
                    'institutions_count' => count($data['institutions'] ?? []),
                    'response_time' => $response->transferStats->getTransferTime(),
                    'status_code' => $response->status(),
                    'plaid_request_id' => $data['request_id'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to connect to Plaid API',
                    'status_code' => $response->status(),
                    'error' => $response->json()
                ]);
            }

        } catch (Exception $e) {
            Log::error('Plaid API connection test failed', [
                'error' => $e->getMessage(),
                'environment' => $this->environment
            ]);

            return response()->json([
                'success' => false,
                'message' => 'API connection failed',
                'error' => $e->getMessage(),
                'environment' => $this->environment
            ]);
        }
    }

    /**
     * Test Link Token generation
     */
    public function testLinkToken(): JsonResponse
    {
        try {
            $response = Http::timeout(10)->post($this->plaidClient . '/link/token/create', [
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'client_name' => config('services.plaid.client_name', 'PocketWatch'),
                'country_codes' => explode(',', config('services.plaid.country_codes', 'US')),
                'language' => config('services.plaid.language', 'en'),
                'user' => [
                    'client_user_id' => 'test_user_' . time()
                ],
                'products' => explode(',', config('services.plaid.products', 'auth,transactions')),
                'webhook' => config('services.plaid.webhook'),
                'redirect_uri' => null
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return response()->json([
                    'success' => true,
                    'message' => 'Link token generated successfully',
                    'link_token' => $data['link_token'],
                    'expiration' => $data['expiration'],
                    'request_id' => $data['request_id'],
                    'environment' => $this->environment
                ]);
            } else {
                $errorData = $response->json();
                
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate link token',
                    'error' => $errorData,
                    'status_code' => $response->status()
                ]);
            }

        } catch (Exception $e) {
            Log::error('Plaid link token generation failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Link token generation failed',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Test public token exchange
     */
    public function exchangePublicToken(Request $request): JsonResponse
    {
        try {
            $publicToken = $request->input('public_token');
            $metadata = $request->input('metadata');

            if (!$publicToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'Public token is required'
                ]);
            }

            $response = Http::timeout(10)->post($this->plaidClient . '/item/public_token/exchange', [
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'public_token' => $publicToken
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                // Store access token in session for testing purposes
                session(['plaid_access_token' => $data['access_token']]);
                session(['plaid_item_id' => $data['item_id']]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Public token exchanged successfully',
                    'access_token' => substr($data['access_token'], 0, 20) . '...', // Partial for security
                    'item_id' => $data['item_id'],
                    'request_id' => $data['request_id'],
                    'metadata' => $metadata
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to exchange public token',
                    'error' => $response->json()
                ]);
            }

        } catch (Exception $e) {
            Log::error('Plaid public token exchange failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Public token exchange failed',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Test account information retrieval
     */
    public function testAccountInfo(): JsonResponse
    {
        try {
            $accessToken = session('plaid_access_token');
            
            if (!$accessToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'No access token found. Please complete the Link flow first.'
                ]);
            }

            $response = Http::timeout(10)->post($this->plaidClient . '/accounts/get', [
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'access_token' => $accessToken
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return response()->json([
                    'success' => true,
                    'message' => 'Account information retrieved successfully',
                    'accounts_count' => count($data['accounts']),
                    'accounts' => $data['accounts'],
                    'item' => $data['item'],
                    'request_id' => $data['request_id']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to retrieve account information',
                    'error' => $response->json()
                ]);
            }

        } catch (Exception $e) {
            Log::error('Plaid account info retrieval failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Account information retrieval failed',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Test transactions retrieval
     */
    public function testTransactions(): JsonResponse
    {
        try {
            $accessToken = session('plaid_access_token');
            
            if (!$accessToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'No access token found. Please complete the Link flow first.'
                ]);
            }

            $startDate = now()->subDays(30)->format('Y-m-d');
            $endDate = now()->format('Y-m-d');

            $response = Http::timeout(10)->post($this->plaidClient . '/transactions/get', [
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'access_token' => $accessToken,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'count' => 10,
                'offset' => 0
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return response()->json([
                    'success' => true,
                    'message' => 'Transactions retrieved successfully',
                    'transactions_count' => count($data['transactions']),
                    'total_transactions' => $data['total_transactions'],
                    'transactions' => $data['transactions'],
                    'accounts' => $data['accounts'],
                    'request_id' => $data['request_id'],
                    'date_range' => [
                        'start' => $startDate,
                        'end' => $endDate
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to retrieve transactions',
                    'error' => $response->json()
                ]);
            }

        } catch (Exception $e) {
            Log::error('Plaid transactions retrieval failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Transactions retrieval failed',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get configuration status
     */
    public function getConfigStatus(): JsonResponse
    {
        $config = [
            'client_id' => !empty($this->clientId),
            'secret' => !empty($this->secret),
            'environment' => $this->environment,
            'client_name' => config('services.plaid.client_name'),
            'products' => config('services.plaid.products'),
            'country_codes' => config('services.plaid.country_codes'),
            'webhook' => config('services.plaid.webhook'),
            'api_url' => $this->plaidClient
        ];

        $isConfigured = $config['client_id'] && $config['secret'];

        return response()->json([
            'success' => $isConfigured,
            'message' => $isConfigured ? 'Plaid is properly configured' : 'Plaid configuration is incomplete',
            'config' => $config
        ]);
    }

    /**
     * Clear test session data
     */
    public function clearSession(): JsonResponse
    {
        session()->forget(['plaid_access_token', 'plaid_item_id']);
        
        return response()->json([
            'success' => true,
            'message' => 'Test session data cleared'
        ]);
    }

    /**
     * Get Plaid API URL based on environment
     */
    private function getPlaidApiUrl(): string
    {
        switch ($this->environment) {
            case 'production':
                return 'https://production.plaid.com';
            case 'development':
                return 'https://development.plaid.com';
            case 'sandbox':
            default:
                return 'https://sandbox.plaid.com';
        }
    }
}
