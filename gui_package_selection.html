<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch - Choose Your Plan</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .auth-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px;
            display: block;
        }
        
        .auth-section.hidden {
            display: none;
        }
        
        .packages-section {
            display: none;
        }
        
        .packages-section.visible {
            display: block;
        }
        
        .packages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .package-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .package-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .package-card.popular::before {
            content: "Most Popular";
            position: absolute;
            top: 0;
            right: 0;
            background: #ff6b6b;
            color: white;
            padding: 8px 20px;
            font-size: 0.8rem;
            font-weight: bold;
            border-bottom-left-radius: 12px;
        }
        
        .package-header {
            text-align: center;
            margin-bottom: 25px;
        }
        
        .package-name {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .package-price {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .package-price .currency {
            font-size: 1.5rem;
        }
        
        .package-price .period {
            font-size: 1rem;
            color: #666;
        }
        
        .package-description {
            color: #666;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .package-features {
            list-style: none;
            margin-bottom: 30px;
        }
        
        .package-features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }
        
        .package-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .select-button {
            width: 100%;
            background: #667eea;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .select-button:hover {
            background: #5a6fd8;
        }
        
        .select-button.popular {
            background: #ff6b6b;
        }
        
        .select-button.popular:hover {
            background: #ff5252;
        }
        
        .confirmation-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }
        
        .confirmation-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 40px;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .auth-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 1rem;
        }
        
        .auth-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 1rem;
        }
        
        .auth-button:hover {
            background: #218838;
        }
        
        .google-button {
            background: #db4437;
        }
        
        .google-button:hover {
            background: #c23321;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .close-button {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }
        
        .confirm-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        
        .confirm-button:hover {
            background: #218838;
        }
        
        .checkbox-group {
            margin: 20px 0;
        }
        
        .checkbox-group label {
            display: flex;
            align-items: center;
            margin: 10px 0;
            cursor: pointer;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Choose Your PocketWatch Plan</h1>
            <p>Start your 7-day free trial today. Cancel anytime.</p>
        </div>

        <!-- Authentication Section -->
        <div class="auth-section" id="auth-section">
            <h3>🔐 Sign In to Continue</h3>
            <input type="email" id="email" class="auth-input" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" class="auth-input" placeholder="Password" value="password123">
            <button onclick="login()" class="auth-button">Sign In</button>
            <button onclick="loginWithGoogle()" class="auth-button google-button">Sign In with Google</button>
            <div id="auth-status"></div>
        </div>

        <!-- Packages Section -->
        <div class="packages-section" id="packages-section">
            <div class="packages-grid" id="packages-grid">
                <!-- Packages will be loaded here -->
            </div>
        </div>

        <!-- Confirmation Modal -->
        <div class="confirmation-modal" id="confirmation-modal">
            <div class="confirmation-content">
                <button class="close-button" onclick="closeConfirmation()">&times;</button>
                <div id="confirmation-details">
                    <!-- Confirmation details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        let authToken = localStorage.getItem('auth_token');
        let selectedPackage = null;

        // Check if user is already logged in
        if (authToken) {
            showPackages();
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (data.token) {
                    authToken = data.token;
                    localStorage.setItem('auth_token', authToken);
                    showStatus('✅ Login successful!', 'success');
                    showPackages();
                } else {
                    showStatus('❌ Login failed: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                showStatus('❌ Login error: ' + error.message, 'error');
            }
        }

        async function loginWithGoogle() {
            try {
                const response = await fetch(`${API_BASE}/auth/google`);
                const data = await response.json();

                if (data.success && data.redirect_url) {
                    window.location.href = data.redirect_url;
                } else {
                    showStatus('❌ Google OAuth failed: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Google OAuth error: ' + error.message, 'error');
            }
        }

        async function showPackages() {
            document.getElementById('auth-section').classList.add('hidden');
            document.getElementById('packages-section').classList.add('visible');

            try {
                const response = await fetch(`${API_BASE}/packages-gui`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const data = await response.json();

                if (data.success) {
                    displayPackages(data.data.packages, data.data.user);
                } else {
                    showStatus('❌ Failed to load packages: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Error loading packages: ' + error.message, 'error');
            }
        }

        function displayPackages(packages, user) {
            const container = document.getElementById('packages-grid');
            container.innerHTML = '';

            packages.forEach(package => {
                const packageDiv = document.createElement('div');
                packageDiv.className = `package-card ${package.popular ? 'popular' : ''}`;
                packageDiv.innerHTML = `
                    <div class="package-header">
                        <div class="package-name">${package.name}</div>
                        <div class="package-price">
                            <span class="currency">$</span>${package.price}
                            <span class="period">/month</span>
                        </div>
                        <div class="package-description">${package.description}</div>
                    </div>
                    <ul class="package-features">
                        ${package.features.slice(0, 8).map(feature => `<li>${feature}</li>`).join('')}
                        ${package.features.length > 8 ? `<li>+ ${package.features.length - 8} more features</li>` : ''}
                    </ul>
                    <button class="select-button ${package.popular ? 'popular' : ''}" 
                            onclick="selectPackage('${package.id}')">
                        Select ${package.name}
                    </button>
                `;
                container.appendChild(packageDiv);
            });
        }

        async function selectPackage(packageId) {
            try {
                const response = await fetch(`${API_BASE}/packages-gui/${packageId}/confirm`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const data = await response.json();

                if (data.success) {
                    selectedPackage = data.data;
                    showConfirmation(data.data);
                } else {
                    showStatus('❌ Failed to load package details: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Error loading package details: ' + error.message, 'error');
            }
        }

        function showConfirmation(packageData) {
            const modal = document.getElementById('confirmation-modal');
            const details = document.getElementById('confirmation-details');
            
            details.innerHTML = `
                <h2>📋 Confirm Your Selection</h2>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3>${packageData.package.name}</h3>
                    <p style="color: #666; margin: 10px 0;">${packageData.package.description}</p>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                        <span style="font-size: 1.1rem;">Monthly Price:</span>
                        <span style="font-size: 1.5rem; font-weight: bold; color: #667eea;">$${packageData.pricing.total}/month</span>
                    </div>
                </div>
                
                <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4>🎉 Trial Information</h4>
                    <p><strong>7-day free trial</strong> starts immediately</p>
                    <p>First billing date: <strong>${packageData.trial_info.first_billing_date}</strong></p>
                    <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                        ${packageData.trial_info.trial_description}
                    </p>
                </div>

                <div style="margin: 20px 0;">
                    <h4>✨ What's Included:</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        ${packageData.features_included.slice(0, 6).map(feature => `<li style="margin: 5px 0;">${feature}</li>`).join('')}
                    </ul>
                </div>

                <div style="margin: 20px 0;">
                    <h4>🚀 What Happens Next:</h4>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        ${packageData.what_happens_next.map(step => `<li style="margin: 5px 0;">${step}</li>`).join('')}
                    </ol>
                </div>

                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="confirm-selection" required>
                        I confirm my selection of ${packageData.package.name} for $${packageData.pricing.total}/month
                    </label>
                    <label>
                        <input type="checkbox" id="accept-terms" required>
                        I accept the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                    </label>
                </div>

                <button class="confirm-button" onclick="confirmAndProceed()">
                    🔒 Proceed to Secure Payment
                </button>
            `;
            
            modal.style.display = 'block';
        }

        async function confirmAndProceed() {
            const confirmCheckbox = document.getElementById('confirm-selection');
            const termsCheckbox = document.getElementById('accept-terms');

            if (!confirmCheckbox.checked || !termsCheckbox.checked) {
                alert('Please confirm your selection and accept the terms to continue.');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/packages-gui/${selectedPackage.package.id}/checkout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        confirmed: true,
                        terms_accepted: true
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Redirect to Stripe
                    window.location.href = data.data.checkout_url;
                } else {
                    showStatus('❌ Failed to create checkout: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Checkout error: ' + error.message, 'error');
            }
        }

        function closeConfirmation() {
            document.getElementById('confirmation-modal').style.display = 'none';
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Handle URL parameters for OAuth callback
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const sessionId = urlParams.get('session_id');

        if (code) {
            handleGoogleCallback(code);
        } else if (sessionId) {
            handlePaymentSuccess(sessionId);
        }

        async function handleGoogleCallback(code) {
            try {
                const response = await fetch(`${API_BASE}/auth/google/callback?code=${code}`);
                const data = await response.json();

                if (data.success && data.token) {
                    authToken = data.token;
                    localStorage.setItem('auth_token', authToken);
                    showStatus(`✅ Welcome ${data.user.name}!`, 'success');
                    showPackages();
                    window.history.replaceState({}, document.title, window.location.pathname);
                } else {
                    showStatus('❌ Google login failed: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Google callback error: ' + error.message, 'error');
            }
        }

        async function handlePaymentSuccess(sessionId) {
            try {
                const response = await fetch(`${API_BASE}/payment-success?session_id=${sessionId}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                const data = await response.json();

                if (data.success) {
                    document.body.innerHTML = `
                        <div style="text-align: center; padding: 50px; background: white; border-radius: 12px; margin: 50px auto; max-width: 600px;">
                            <h1 style="color: #28a745; margin-bottom: 20px;">🎉 Payment Successful!</h1>
                            <p style="font-size: 1.2rem; margin-bottom: 20px;">Your 7-day free trial for <strong>${data.data.package_name}</strong> has started!</p>
                            <p style="color: #666;">You can now access all premium features. Your trial ends on ${new Date(data.data.trial_ends_at).toLocaleDateString()}.</p>
                            <button onclick="window.location.href='/dashboard'" style="background: #667eea; color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 1.1rem; margin-top: 30px; cursor: pointer;">
                                Go to Dashboard
                            </button>
                        </div>
                    `;
                } else {
                    showStatus('❌ Payment verification failed: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Payment verification error: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
