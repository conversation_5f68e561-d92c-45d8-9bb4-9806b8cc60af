<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // General settings
        Setting::set('app_name', 'PocketWatch', 'general', 'string');
        Setting::set('app_description', 'Your personal finance management tool', 'general', 'string');
        Setting::set('footer_text', '&copy; ' . date('Y') . ' PocketWatch. All rights reserved.', 'general', 'string');
        Setting::set('header_text', 'Welcome to PocketWatch', 'general', 'string');

        // Welcome page settings
        Setting::set('navbar_brand', 'PocketWatch', 'welcome', 'string');
        Setting::set('navbar_links', [
            ['name' => 'Features', 'url' => '#features'],
            ['name' => 'Testimonials', 'url' => '#testimonials'],
            ['name' => 'Pricing', 'url' => '#packages']
        ], 'welcome', 'json');

        Setting::set('welcome_sliders', [
            [
                'image' => 'https://images.unsplash.com/photo-1579621970795-87facc2f976d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=600&q=80',
                'title' => 'Take Control of Your Finances',
                'description' => 'Track, manage, and optimize your financial life with our powerful tools'
            ],
            [
                'image' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=600&q=80',
                'title' => 'Smart Financial Planning',
                'description' => 'Make informed decisions with our advanced analytics and insights'
            ],
            [
                'image' => 'https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=600&q=80',
                'title' => 'Secure & Private',
                'description' => 'Your financial data is protected with enterprise-grade security'
            ]
        ], 'welcome', 'json');

        Setting::set('features_title', 'Features', 'welcome', 'string');
        Setting::set('welcome_features', [
            [
                'icon' => 'bi-graph-up',
                'title' => 'Financial Dashboard',
                'description' => 'Get a comprehensive view of your finances with our intuitive dashboard that tracks income, expenses, and investments in real-time.'
            ],
            [
                'icon' => 'bi-wallet2',
                'title' => 'Wallet Management',
                'description' => 'Securely manage multiple wallets and accounts in one place, with automatic categorization and transaction tracking.'
            ],
            [
                'icon' => 'bi-shield-check',
                'title' => 'Secure Transactions',
                'description' => 'All your financial data is protected with bank-level encryption and advanced security protocols.'
            ],
            [
                'icon' => 'bi-bell',
                'title' => 'Smart Alerts',
                'description' => 'Receive timely notifications about unusual spending, upcoming bills, and investment opportunities.'
            ],
            [
                'icon' => 'bi-bar-chart',
                'title' => 'Advanced Analytics',
                'description' => 'Gain valuable insights with detailed reports and visualizations that help you understand your spending habits.'
            ],
            [
                'icon' => 'bi-headset',
                'title' => 'Premium Support',
                'description' => 'Our dedicated support team is available 24/7 to help you with any questions or issues you might have.'
            ]
        ], 'welcome', 'json');

        Setting::set('testimonials_title', 'Testimonials', 'welcome', 'string');
        Setting::set('welcome_testimonials', [
            [
                'avatar' => 'https://randomuser.me/api/portraits/women/32.jpg',
                'content' => 'PocketWatch has completely transformed how I manage my finances. The dashboard gives me a clear picture of my spending habits, and the alerts have helped me avoid unnecessary fees. Highly recommended!',
                'name' => 'Sarah Johnson',
                'position' => 'Marketing Executive'
            ],
            [
                'avatar' => 'https://randomuser.me/api/portraits/men/45.jpg',
                'content' => 'As a small business owner, keeping track of expenses was always a challenge. PocketWatch has simplified everything with its intuitive interface and powerful reporting tools. The customer support is exceptional too!',
                'name' => 'Michael Chen',
                'position' => 'Small Business Owner'
            ],
            [
                'avatar' => 'https://randomuser.me/api/portraits/women/68.jpg',
                'content' => 'I\'ve tried several financial apps, but PocketWatch stands out with its security features and ease of use. The ability to manage multiple accounts in one place has saved me so much time.',
                'name' => 'Emily Rodriguez',
                'position' => 'Financial Analyst'
            ],
            [
                'avatar' => 'https://randomuser.me/api/portraits/men/22.jpg',
                'content' => 'The investment tracking feature in PocketWatch has been a game-changer for me. I can now easily monitor my portfolio performance and make informed decisions. Worth every penny!',
                'name' => 'David Wilson',
                'position' => 'Investment Consultant'
            ]
        ], 'welcome', 'json');

        Setting::set('packages_title', 'Subscription Plans', 'welcome', 'string');

        // Use real subscription pricing from config
        $baseMonthlyPrice = config('services.subscription.base_monthly_price', 5.00);
        $baseYearlyPrice = config('services.subscription.base_yearly_price', 50.00);
        $premiumMonthlyPrice = config('services.subscription.premium_monthly_price', 10.00);
        $premiumYearlyPrice = config('services.subscription.premium_yearly_price', 100.00);
        $trialDays = config('services.subscription.trial_days', 7);

        Setting::set('welcome_packages', [
            [
                'title' => 'Base',
                'price' => '$' . number_format($baseMonthlyPrice, 2) . ' / month',
                'popular' => false,
                'features' => [
                    'Financial Bins with Thresholds',
                    'Transaction Categorization',
                    'Graphical Insights',
                    'Up to 3 Sub-Bin Levels',
                    'Basic Analytics Dashboard',
                    'Email Notifications',
                    'Standard Support',
                    $trialDays . '-Day Free Trial'
                ],
                'button_text' => 'Start Free Trial',
                'button_url' => '#'
            ],
            [
                'title' => 'Premium',
                'price' => '$' . number_format($premiumMonthlyPrice, 2) . ' / month',
                'popular' => true,
                'features' => [
                    'All Base Tier Features',
                    'Unlimited Sub-Bin Levels',
                    'Crypto Scanner',
                    'Priority Notifications',
                    'Advanced AI Suggestions',
                    'Enhanced Analytics',
                    'Priority Support',
                    $trialDays . '-Day Free Trial'
                ],
                'button_text' => 'Start Free Trial',
                'button_url' => '#'
            ],
            [
                'title' => 'Base (Annual)',
                'price' => '$' . number_format($baseYearlyPrice, 2) . ' / year',
                'popular' => false,
                'features' => [
                    'All Base Tier Features',
                    'Up to 3 Sub-Bin Levels',
                    'Save with Annual Billing',
                    $trialDays . '-Day Free Trial'
                ],
                'button_text' => 'Start Free Trial',
                'button_url' => '#'
            ]
        ], 'welcome', 'json');

        Setting::set('social_links', [
            ['icon' => 'bi-facebook', 'url' => '#'],
            ['icon' => 'bi-twitter', 'url' => '#'],
            ['icon' => 'bi-instagram', 'url' => '#'],
            ['icon' => 'bi-linkedin', 'url' => '#']
        ], 'welcome', 'json');

        Setting::set('primary_color', '#2E8B57', 'welcome', 'string');
        Setting::set('background_color', '#ffffff', 'welcome', 'string');
        Setting::set('text_color', '#333333', 'welcome', 'string');

        // Email settings
        Setting::set('mail_mailer', 'smtp', 'email', 'string');
        Setting::set('mail_host', 'smtp.gmail.com', 'email', 'string');
        Setting::set('mail_port', 587, 'email', 'integer');
        Setting::set('mail_username', '<EMAIL>', 'email', 'string');
        Setting::set('mail_password', 'your-password', 'email', 'string');
        Setting::set('mail_encryption', 'tls', 'email', 'string');
        Setting::set('mail_from_address', '<EMAIL>', 'email', 'string');
        Setting::set('mail_from_name', 'PocketWatch', 'email', 'string');

        // Payment settings
        Setting::set('stripe_key', 'pk_test_your_key', 'payment', 'string');
        Setting::set('stripe_secret', 'sk_test_your_key', 'payment', 'string');
        Setting::set('stripe_webhook_secret', 'whsec_your_key', 'payment', 'string');
        Setting::set('stripe_price_base_monthly', 'price_base_monthly', 'payment', 'string');
        Setting::set('stripe_price_premium_monthly', 'price_premium_monthly', 'payment', 'string');
        Setting::set('stripe_price_base_yearly', 'price_base_yearly', 'payment', 'string');
        Setting::set('stripe_price_premium_yearly', 'price_premium_yearly', 'payment', 'string');

        // System settings
        Setting::set('timezone', 'UTC', 'system', 'string');
        Setting::set('date_format', 'Y-m-d', 'system', 'string');
        Setting::set('time_format', 'H:i', 'system', 'string');
        Setting::set('currency', 'USD', 'system', 'string');
        Setting::set('currency_symbol', '$', 'system', 'string');
        Setting::set('maintenance_mode', false, 'system', 'boolean');
    }
}
