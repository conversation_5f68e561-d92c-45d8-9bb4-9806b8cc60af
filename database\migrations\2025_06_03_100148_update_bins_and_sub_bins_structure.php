<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update bins table
        Schema::table('bins', function (Blueprint $table) {
            // Change type values to only income/expense
            $table->string('type')->default('expense')->comment('income, expense')->change();

            // Rename threshold_min to threshold_max (max limit instead of min)
            $table->renameColumn('threshold_min', 'threshold_max_limit');

            // Keep existing threshold_max as threshold_max_warning
            $table->renameColumn('threshold_max', 'threshold_max_warning');
        });

        // Update sub_bins table
        Schema::table('sub_bins', function (Blueprint $table) {
            // Change type values to only income/expense
            $table->string('type')->default('expense')->comment('income, expense')->change();

            // Rename threshold_min to threshold_max (max limit instead of min)
            $table->renameColumn('threshold_min', 'threshold_max_limit');

            // Keep existing threshold_max as threshold_max_warning
            $table->renameColumn('threshold_max', 'threshold_max_warning');
        });

        // Update existing data to use new type values
        DB::statement("UPDATE bins SET type = 'income' WHERE type = 'income'");
        DB::statement("UPDATE bins SET type = 'expense' WHERE type IN ('expenditure', 'expense')");

        DB::statement("UPDATE sub_bins SET type = 'income' WHERE type = 'income'");
        DB::statement("UPDATE sub_bins SET type = 'expense' WHERE type IN ('expenditure', 'expense')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse bins table changes
        Schema::table('bins', function (Blueprint $table) {
            $table->string('type')->default('expenditure')->comment('income, expenditure')->change();
            $table->renameColumn('threshold_max_limit', 'threshold_min');
            $table->renameColumn('threshold_max_warning', 'threshold_max');
        });

        // Reverse sub_bins table changes
        Schema::table('sub_bins', function (Blueprint $table) {
            $table->string('type')->default('expenditure')->comment('income, expenditure')->change();
            $table->renameColumn('threshold_max_limit', 'threshold_min');
            $table->renameColumn('threshold_max_warning', 'threshold_max');
        });

        // Restore original data
        DB::statement("UPDATE bins SET type = 'expenditure' WHERE type = 'expense'");
        DB::statement("UPDATE sub_bins SET type = 'expenditure' WHERE type = 'expense'");
    }
};
