<?php

namespace App\Console\Commands;

use App\Models\PlaidAccount;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class AddDummyPlaidAccounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'plaid:add-dummy-accounts {user? : The ID or email of the user to add accounts for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add dummy Plaid accounts for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user');
        
        if ($userId) {
            // Find user by ID or email
            $user = is_numeric($userId) 
                ? User::find($userId) 
                : User::where('email', $userId)->first();
                
            if (!$user) {
                $this->error("User not found with ID or email: {$userId}");
                return 1;
            }
            
            $this->addDummyAccountsForUser($user);
            $this->info("Dummy Plaid accounts added for user: {$user->name} ({$user->email})");
        } else {
            // Ask if user wants to add for all users or select a specific one
            $choice = $this->choice(
                'Add dummy accounts for all users or select a specific user?',
                ['All users', 'Select a specific user'],
                0
            );
            
            if ($choice === 'All users') {
                $users = User::all();
                $bar = $this->output->createProgressBar(count($users));
                $bar->start();
                
                foreach ($users as $user) {
                    $this->addDummyAccountsForUser($user);
                    $bar->advance();
                }
                
                $bar->finish();
                $this->newLine();
                $this->info("Dummy Plaid accounts added for all users.");
            } else {
                // List users to select from
                $users = User::all();
                $userOptions = $users->map(function ($user) {
                    return "{$user->id}: {$user->name} ({$user->email})";
                })->toArray();
                
                $selectedUser = $this->choice('Select a user:', $userOptions);
                $userId = (int) explode(':', $selectedUser)[0];
                $user = User::find($userId);
                
                $this->addDummyAccountsForUser($user);
                $this->info("Dummy Plaid accounts added for user: {$user->name} ({$user->email})");
            }
        }
        
        return 0;
    }
    
    /**
     * Add dummy Plaid accounts for a specific user.
     *
     * @param \App\Models\User $user
     * @return void
     */
    protected function addDummyAccountsForUser(User $user)
    {
        // Delete existing Plaid accounts for this user
        if ($this->confirm("Delete existing Plaid accounts for {$user->name}?", true)) {
            $count = $user->plaidAccounts()->count();
            $user->plaidAccounts()->delete();
            $this->line("Deleted {$count} existing Plaid accounts.");
        }
        
        // Create dummy checking account
        PlaidAccount::create([
            'uuid' => Str::uuid(),
            'user_id' => $user->id,
            'institution_id' => 'ins_1',
            'institution_name' => 'Chase Bank',
            'account_id' => 'acc_' . Str::random(10),
            'account_name' => 'Checking Account',
            'account_type' => 'depository',
            'account_subtype' => 'checking',
            'account_mask' => '1234',
            'access_token' => 'access-sandbox-' . Str::random(10),
            'item_id' => 'item-sandbox-' . Str::random(10),
            'is_default' => true,
            'is_payment_enabled' => true,
            'metadata' => [
                'balances' => [
                    'available' => 1000.00,
                    'current' => 1050.00,
                    'iso_currency_code' => 'USD',
                ],
            ],
            'last_synced_at' => now(),
        ]);

        // Create dummy savings account
        PlaidAccount::create([
            'uuid' => Str::uuid(),
            'user_id' => $user->id,
            'institution_id' => 'ins_2',
            'institution_name' => 'Bank of America',
            'account_id' => 'acc_' . Str::random(10),
            'account_name' => 'Savings Account',
            'account_type' => 'depository',
            'account_subtype' => 'savings',
            'account_mask' => '5678',
            'access_token' => 'access-sandbox-' . Str::random(10),
            'item_id' => 'item-sandbox-' . Str::random(10),
            'is_default' => false,
            'is_payment_enabled' => true,
            'metadata' => [
                'balances' => [
                    'available' => 5000.00,
                    'current' => 5000.00,
                    'iso_currency_code' => 'USD',
                ],
            ],
            'last_synced_at' => now(),
        ]);

        // Create dummy credit card account
        PlaidAccount::create([
            'uuid' => Str::uuid(),
            'user_id' => $user->id,
            'institution_id' => 'ins_3',
            'institution_name' => 'American Express',
            'account_id' => 'acc_' . Str::random(10),
            'account_name' => 'Credit Card',
            'account_type' => 'credit',
            'account_subtype' => 'credit card',
            'account_mask' => '9012',
            'access_token' => 'access-sandbox-' . Str::random(10),
            'item_id' => 'item-sandbox-' . Str::random(10),
            'is_default' => false,
            'is_payment_enabled' => false,
            'metadata' => [
                'balances' => [
                    'available' => 5000.00,
                    'current' => -1500.00,
                    'iso_currency_code' => 'USD',
                ],
            ],
            'last_synced_at' => now(),
        ]);
    }
}
