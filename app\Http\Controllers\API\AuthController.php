<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Notifications\Auth\LoginNotification;
use App\Notifications\Auth\PasswordChangedNotification;
use App\Notifications\Auth\PasswordResetNotification;
use App\Notifications\Auth\ProfileUpdatedNotification;
use App\Notifications\Auth\WelcomeNotification;
use App\Services\EmailVerificationService;
use App\Services\IdObfuscationService;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Register a new user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'country_code' => 'required|string|max:5',
            'phone_number' => 'required|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'country_code' => $request->country_code,
                'phone_number' => $request->phone_number,
                'subscription_tier' => 'none', // No plan selected yet
                'email_verified_at' => null, // User needs to verify email
            ]);

            $token = $user->createToken('auth_token')->plainTextToken;

            // Send email verification code
            $emailVerificationService = new EmailVerificationService();
            $verificationResult = $emailVerificationService->sendEmailVerificationCode($user);

            $response = [
                'message' => 'User registered successfully',
                'user' => new UserResource($user),
                'token' => $token,
                'email_verification' => [
                    'required' => true,
                    'sent' => $verificationResult['success'],
                ]
            ];

            // Add verification details if email was sent successfully
            if ($verificationResult['success']) {
                $response['email_verification']['message'] = $verificationResult['message'];
                $response['email_verification']['expires_at'] = $verificationResult['expires_at'];
                $response['email_verification']['attempts_remaining'] = $verificationResult['attempts_remaining'];
            } else {
                $response['email_verification']['error'] = $verificationResult['message'];
                $response['email_verification']['error_code'] = $verificationResult['error_code'] ?? 'UNKNOWN_ERROR';
            }

            return response()->json($response, 201);

        } catch (\Exception $e) {
            // If user creation fails, make sure to clean up
            if (isset($user) && $user->exists) {
                $user->delete();
            }

            return response()->json([
                'message' => 'Registration failed. Please try again.',
                'error' => 'An error occurred during registration.'
            ], 500);
        }
    }

    /**
     * Login user and create token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            if (!Auth::attempt($request->only('email', 'password'))) {
                throw ValidationException::withMessages([
                    'email' => ['The provided credentials are incorrect.'],
                ]);
            }

            $user = User::where('email', $request->email)->firstOrFail();

            // Check if user is active
            if (!$user->is_active) {
                return response()->json([
                    'message' => 'Your account has been deactivated. Please contact support.',
                ], 403);
            }

            // Update last login timestamp
            $user->last_login_at = now();
            $user->save();

            $token = $user->createToken('auth_token')->plainTextToken;

            // Send login notification with IP address
            $user->notify(new LoginNotification($request->ip(), $request->userAgent()));

            $response = [
                'message' => 'Login successful',
                'user' => new UserResource($user),
                'token' => $token,
            ];

            // Add email verification status
            $response['email_verification'] = [
                'verified' => !is_null($user->email_verified_at),
                'verified_at' => $user->email_verified_at,
            ];

            // If email is not verified, add verification info
            if (is_null($user->email_verified_at)) {
                $response['email_verification']['required'] = true;
                $response['email_verification']['message'] = 'Please verify your email address to access all features.';

                // Check if there's a pending verification code
                if ($user->email_verification_code && $user->email_verification_code_expires_at) {
                    $response['email_verification']['has_pending_code'] = true;
                    $response['email_verification']['code_expires_at'] = $user->email_verification_code_expires_at;
                    $response['email_verification']['attempts_remaining'] = EmailVerificationService::MAX_ATTEMPTS - ($user->email_verification_attempts ?? 0);
                } else {
                    $response['email_verification']['has_pending_code'] = false;
                    $response['email_verification']['can_request_new'] = true;
                }
            }

            return response()->json($response);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Invalid credentials',
                'errors' => $e->errors(),
            ], 401);
        }
    }

    /**
     * Get the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function me(Request $request)
    {
        $user = $request->user();
        $response = [
            'user' => new UserResource($user),
        ];

        // Check if this is a payment success callback
        if ($request->has('payment_success') && $request->payment_success === 'true') {
            $response['payment_success'] = true;
            $response['message'] = 'Payment successful! Your 7-day trial has started.';

            if ($request->has('plan')) {
                $response['plan'] = $request->plan;
            }

            if ($request->has('trial_started') && $request->trial_started === 'true') {
                $response['trial_started'] = true;
                $response['trial_info'] = [
                    'trial_days' => 7,
                    'trial_ends_at' => $user->trial_started_at ?
                        $user->trial_started_at->addDays(7)->toISOString() :
                        now()->addDays(7)->toISOString()
                ];
            }
        }

        return response()->json($response);
    }

    /**
     * Logout user (revoke the token).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Successfully logged out',
        ]);
    }

    /**
     * Update user profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|string|email|max:255|unique:users,email,' . $request->user()->id,
            'country_code' => 'sometimes|string|max:5|nullable',
            'phone_number' => 'sometimes|string|max:20|nullable',
            'avatar' => 'sometimes|string|nullable',
            'preferences' => 'sometimes|json|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        // Track which fields are being updated
        $updatedFields = [];
        foreach (['name', 'email', 'country_code', 'phone_number', 'avatar', 'preferences'] as $field) {
            if ($request->has($field) && $user->{$field} != $request->input($field)) {
                $updatedFields[] = $field;
            }
        }

        $user->fill($request->only(['name', 'email', 'country_code', 'phone_number', 'avatar', 'preferences']));
        $user->save();

        // Send profile updated notification
        if (!empty($updatedFields)) {
            $user->notify(new ProfileUpdatedNotification($updatedFields));
        }

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => new UserResource($user),
        ]);
    }

    /**
     * Change user password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'message' => 'Current password is incorrect',
            ], 422);
        }

        $user->password = Hash::make($request->password);
        $user->save();

        // Send password changed notification
        $user->notify(new PasswordChangedNotification());

        return response()->json([
            'message' => 'Password changed successfully',
        ]);
    }

    /**
     * Send a 6-digit password reset code to the user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function forgotPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Find user by email
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'message' => 'If an account with that email exists, a password reset code has been sent.',
                ], 200); // Return 200 to prevent email enumeration
            }

            // Send password reset code using EmailVerificationService
            $emailVerificationService = new EmailVerificationService();
            $result = $emailVerificationService->sendPasswordResetCode($user);

            if ($result['success']) {
                return response()->json([
                    'message' => 'Password reset code sent to your email address.',
                    'expires_at' => $result['expires_at'],
                    'attempts_remaining' => $result['attempts_remaining'],
                ], 200);
            } else {
                return response()->json([
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? 'UNKNOWN_ERROR',
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Forgot password failed', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'An error occurred while processing your request. Please try again.',
            ], 500);
        }
    }

    /**
     * Reset the user's password using 6-digit code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'code' => 'required|string|size:6',
            'password' => 'required|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Find user by email
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'message' => 'Invalid email address.',
                ], 404);
            }

            // Reset password using EmailVerificationService
            $emailVerificationService = new EmailVerificationService();
            $result = $emailVerificationService->resetPassword($user, $request->code, $request->password);

            if ($result['success']) {
                return response()->json([
                    'message' => 'Password reset successfully. You can now login with your new password.',
                    'user' => new UserResource($result['user']),
                ], 200);
            } else {
                return response()->json([
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? 'UNKNOWN_ERROR',
                    'attempts_remaining' => $result['attempts_remaining'] ?? null,
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Password reset failed', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'An error occurred while resetting your password. Please try again.',
            ], 500);
        }
    }

    /**
     * Update user avatar.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAvatar(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        // Delete old avatar if exists
        if ($user->avatar && !Str::contains($user->avatar, ['default-avatar', 'ui-avatars.com', 'googleusercontent.com'])) {
            $oldPath = str_replace(url('/'), '', $user->avatar);
            if (file_exists(public_path($oldPath))) {
                unlink(public_path($oldPath));
            }
        }

        // Create avatars directory in public folder if it doesn't exist
        $avatarDir = public_path('avatars');
        if (!file_exists($avatarDir)) {
            mkdir($avatarDir, 0755, true);
        }

        // Generate unique filename
        $extension = $request->file('avatar')->getClientOriginalExtension();
        $filename = Str::random(40) . '.' . $extension;

        // Store avatar in public/avatars directory
        $request->file('avatar')->move($avatarDir, $filename);

        // Save avatar URL
        $user->avatar = url('avatars/' . $filename);
        $user->save();

        return response()->json([
            'message' => 'Avatar updated successfully',
            'avatar_url' => $user->avatar,
            'user' => new UserResource($user),
        ]);
    }
}
