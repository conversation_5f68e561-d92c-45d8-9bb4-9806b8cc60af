<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch API Documentation</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: var(--gradient);
            color: white;
            padding: 2rem 0;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }

        .header-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            animation: slideInDown 1s ease-out;
        }

        .header p {
            font-size: 1.25rem;
            opacity: 0.9;
            animation: slideInUp 1s ease-out 0.3s both;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Navigation */
        .nav {
            background: white;
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        /* Main Content */
        .main {
            padding: 3rem 0;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 3rem;
            align-items: start;
        }

        /* Sidebar */
        .sidebar {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow);
            position: sticky;
            top: 100px;
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }

        .sidebar h3 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: var(--text-secondary);
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .sidebar-menu a:hover {
            background: var(--secondary-color);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .sidebar-menu a.active {
            background: var(--primary-color);
            color: white;
        }

        /* Content Area */
        .content {
            background: white;
            border-radius: 16px;
            padding: 3rem;
            box-shadow: var(--shadow);
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Section Styles */
        .section {
            margin-bottom: 4rem;
            opacity: 0;
            animation: fadeIn 0.6s ease-out forwards;
        }

        .section:nth-child(1) {
            animation-delay: 0.1s;
        }

        .section:nth-child(2) {
            animation-delay: 0.2s;
        }

        .section:nth-child(3) {
            animation-delay: 0.3s;
        }

        .section:nth-child(4) {
            animation-delay: 0.4s;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
        }

        .section h2::after {
            content: '';
            flex: 1;
            height: 2px;
            background: linear-gradient(to right, var(--primary-color), transparent);
        }

        /* API Cards */
        .api-grid {
            display: grid;
            gap: 1.5rem;
        }

        .api-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            background: white;
        }

        .api-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .api-header {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .api-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .api-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }

        .api-body {
            padding: 1.5rem;
        }

        .endpoint {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .endpoint:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
        }

        .endpoint-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
        }

        .endpoint-header:hover {
            background: #f1f5f9;
        }

        .method {
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.875rem;
            min-width: 60px;
            text-align: center;
        }

        .method.get {
            background: #dcfdf7;
            color: #065f46;
        }

        .method.post {
            background: #dbeafe;
            color: #1e40af;
        }

        .method.put {
            background: #fef3c7;
            color: #92400e;
        }

        .method.delete {
            background: #fee2e2;
            color: #991b1b;
        }

        .endpoint-path {
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
        }

        .endpoint-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .expand-icon {
            color: var(--text-secondary);
            transition: transform 0.3s ease;
        }

        .endpoint.expanded .expand-icon {
            transform: rotate(180deg);
        }

        .endpoint-details {
            padding: 1.5rem;
            display: none;
            border-top: 1px solid var(--border-color);
            background: white;
        }

        .endpoint.expanded .endpoint-details {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }

            to {
                opacity: 1;
                max-height: 500px;
            }
        }

        .detail-section {
            margin-bottom: 1.5rem;
        }

        .detail-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .params-table,
        .response-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 0.5rem;
        }

        .params-table th,
        .params-table td,
        .response-table th,
        .response-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .params-table th,
        .response-table th {
            background: var(--secondary-color);
            font-weight: 600;
            color: var(--text-primary);
        }

        .param-name,
        .response-field {
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 500;
            color: var(--primary-color);
        }

        .param-type {
            background: #f1f5f9;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .required {
            background: #fef2f2;
            color: #dc2626;
        }

        .optional {
            background: #f0fdf4;
            color: #16a34a;
        }

        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
            margin-top: 0.5rem;
        }

        .json-key {
            color: #7dd3fc;
        }

        .json-string {
            color: #86efac;
        }

        .json-number {
            color: #fbbf24;
        }

        .json-boolean {
            color: #f472b6;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .sidebar {
                position: static;
                max-height: none;
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }

            .nav-links {
                display: none;
            }

            .content {
                padding: 2rem;
            }
        }
    </style>
</head>

<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1><i class="fas fa-wallet"></i> PocketWatch API</h1>
                <p>Comprehensive Financial Management API Documentation</p>
            </div>
        </div>
    </header>

    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="logo">
                    <strong>PocketWatch</strong>
                </div>
                <ul class="nav-links">
                    <li><a href="#overview"><i class="fas fa-home"></i> Overview</a></li>
                    <li><a href="#authentication"><i class="fas fa-key"></i> Authentication</a></li>
                    <li><a href="#endpoints"><i class="fas fa-code"></i> Endpoints</a></li>
                    <li><a href="#examples"><i class="fas fa-play"></i> Examples</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <main class="main">
        <div class="container">
            <div class="content-grid">
                <aside class="sidebar">
                    <h3><i class="fas fa-list"></i> API Categories</h3>
                    <ul class="sidebar-menu">
                        <li><a href="#auth" class="active"><i class="fas fa-shield-alt"></i> Authentication</a></li>
                        <li><a href="#email"><i class="fas fa-envelope"></i> Email Verification</a></li>
                        <li><a href="#password"><i class="fas fa-lock"></i> Password Reset</a></li>
                        <li><a href="#google"><i class="fab fa-google"></i> Google OAuth</a></li>
                        <li><a href="#bins"><i class="fas fa-folder"></i> Bins Management</a></li>
                        <li><a href="#sub-bins"><i class="fas fa-sitemap"></i> Sub-Bins</a></li>
                        <li><a href="#transactions"><i class="fas fa-exchange-alt"></i> Transactions</a></li>
                        <li><a href="#subscriptions"><i class="fas fa-credit-card"></i> Subscriptions</a></li>
                        <li><a href="#crypto"><i class="fab fa-bitcoin"></i> Crypto Wallets</a></li>
                        <li><a href="#plaid"><i class="fas fa-university"></i> Plaid Banking</a></li>
                        <li><a href="#reports"><i class="fas fa-chart-bar"></i> Reports</a></li>
                        <li><a href="#webhooks"><i class="fas fa-webhook"></i> Webhooks</a></li>
                    </ul>
                </aside>

                <div class="content">
                    <section id="overview" class="section">
                        <h2><i class="fas fa-info-circle"></i> API Overview</h2>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-code"></i></div>
                                <div class="stat-number">92</div>
                                <div class="stat-label">Total Endpoints</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-layer-group"></i></div>
                                <div class="stat-number">16</div>
                                <div class="stat-label">API Categories</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-shield-alt"></i></div>
                                <div class="stat-number">JWT</div>
                                <div class="stat-label">Authentication</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-rocket"></i></div>
                                <div class="stat-number">REST</div>
                                <div class="stat-label">API Standard</div>
                            </div>
                        </div>

                        <p>PocketWatch provides a comprehensive REST API for financial management with advanced features
                            including hierarchical organization, payment processing, and real-time analytics. Built with
                            Laravel and following modern API design principles.</p>
                    </section>

                    <section id="auth" class="section">
                        <h2><i class="fas fa-shield-alt"></i> Authentication & User Management</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-user-plus"></i> Public Authentication</div>
                                    <div class="api-description">User registration, login, and password management</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/register</span>
                                            <span class="endpoint-description">User registration</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">name</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>User's full name</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Valid email address</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Password (min 8 characters)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password_confirmation</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Password confirmation</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Registration successful"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"email_verified_at"</span>: <span
                                                        class="json-string">null</span>,
                                                    <span class="json-key">"is_active"</span>: <span
                                                        class="json-boolean">true</span>,
                                                    <span class="json-key">"created_at"</span>: <span
                                                        class="json-string">"2024-01-01T00:00:00.000000Z"</span>
                                                    },
                                                    <span class="json-key">"token"</span>: <span
                                                        class="json-string">"1|abc123def456..."</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/login</span>
                                            <span class="endpoint-description">User login</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>User's email address</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>User's password</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Login successful"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"email_verified_at"</span>: <span
                                                        class="json-string">"2024-01-01T00:00:00.000000Z"</span>,
                                                    <span class="json-key">"is_active"</span>: <span
                                                        class="json-boolean">true</span>,
                                                    <span class="json-key">"last_login_at"</span>: <span
                                                        class="json-string">"2024-01-01T12:00:00.000000Z"</span>
                                                    },
                                                    <span class="json-key">"token"</span>: <span
                                                        class="json-string">"1|abc123def456..."</span>,
                                                    <span class="json-key">"email_verification"</span>: {
                                                    <span class="json-key">"is_verified"</span>: <span
                                                        class="json-boolean">true</span>,
                                                    <span class="json-key">"has_pending_code"</span>: <span
                                                        class="json-boolean">false</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/forgot-password</span>
                                            <span class="endpoint-description">Request password reset</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>User's email address</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Password reset email sent
                                                        successfully"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/reset-password</span>
                                            <span class="endpoint-description">Reset password with token</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">token</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Password reset token from email</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>User's email address</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>New password (min 8 characters)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password_confirmation</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Password confirmation</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Password reset successfully"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-user-cog"></i> Protected User Routes</div>
                                    <div class="api-description">Profile management and account settings (requires
                                        authentication)</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method get">GET</span>
                                            <span class="endpoint-path">/api/me</span>
                                            <span class="endpoint-description">Get current user profile</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-shield-alt"></i>
                                                    Authentication</div>
                                                <p>Requires Bearer token in Authorization header</p>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"email_verified_at"</span>: <span
                                                        class="json-string">"2024-01-01T00:00:00.000000Z"</span>,
                                                    <span class="json-key">"is_active"</span>: <span
                                                        class="json-boolean">true</span>,
                                                    <span class="json-key">"is_admin"</span>: <span
                                                        class="json-boolean">false</span>,
                                                    <span class="json-key">"avatar"</span>: <span
                                                        class="json-string">"avatars/user1.jpg"</span>,
                                                    <span class="json-key">"last_login_at"</span>: <span
                                                        class="json-string">"2024-01-15T12:00:00.000000Z"</span>,
                                                    <span class="json-key">"created_at"</span>: <span
                                                        class="json-string">"2024-01-01T00:00:00.000000Z"</span>,
                                                    <span class="json-key">"updated_at"</span>: <span
                                                        class="json-string">"2024-01-15T12:00:00.000000Z"</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/logout</span>
                                            <span class="endpoint-description">User logout</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-shield-alt"></i>
                                                    Authentication</div>
                                                <p>Requires Bearer token in Authorization header</p>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Logged out successfully"</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method put">PUT</span>
                                            <span class="endpoint-path">/api/profile</span>
                                            <span class="endpoint-description">Update user profile</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">name</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>User's full name</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>User's email address</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Profile updated successfully"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Smith"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"updated_at"</span>: <span
                                                        class="json-string">"2024-01-15T14:30:00.000000Z"</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method put">PUT</span>
                                            <span class="endpoint-path">/api/password</span>
                                            <span class="endpoint-description">Change password</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">current_password</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Current password</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>New password (min 8 characters)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password_confirmation</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Password confirmation</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Password changed successfully"</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/avatar</span>
                                            <span class="endpoint-description">Update user avatar</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">avatar</td>
                                                            <td><span class="param-type">file</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Image file (jpg, png, gif, max 2MB)</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Avatar updated successfully"</span>,
                                                    <span class="json-key">"avatar_url"</span>: <span
                                                        class="json-string">"avatars/user1_1642248000.jpg"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>,
                                                    <span class="json-key">"avatar"</span>: <span
                                                        class="json-string">"avatars/user1_1642248000.jpg"</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="email" class="section">
                        <h2><i class="fas fa-envelope"></i> Email Verification System</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-envelope-open-text"></i> Email Verification
                                    </div>
                                    <div class="api-description">Complete email verification workflow</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/email/send-verification-code</span>
                                            <span class="endpoint-description">Send verification code</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Email address to verify</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Verification code sent
                                                        successfully"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"code_expires_at"</span>: <span
                                                        class="json-string">"2024-01-15T15:00:00.000000Z"</span>,
                                                    <span class="json-key">"attempts_remaining"</span>: <span
                                                        class="json-number">3</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/email/verify</span>
                                            <span class="endpoint-description">Verify email with code</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Email address being verified</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">code</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>6-digit verification code</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Email verified successfully"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"email_verified_at"</span>: <span
                                                        class="json-string">"2024-01-15T14:30:00.000000Z"</span>,
                                                    <span class="json-key">"is_verified"</span>: <span
                                                        class="json-boolean">true</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/email/resend-verification-code</span>
                                            <span class="endpoint-description">Resend verification code</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Email address to resend code to</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Verification code resent
                                                        successfully"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"code_expires_at"</span>: <span
                                                        class="json-string">"2024-01-15T15:00:00.000000Z"</span>,
                                                    <span class="json-key">"attempts_remaining"</span>: <span
                                                        class="json-number">3</span>,
                                                    <span class="json-key">"can_resend_after"</span>: <span
                                                        class="json-string">"2024-01-15T14:35:00.000000Z"</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/email/check-verification-status</span>
                                            <span class="endpoint-description">Check verification status</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Email address to check status for</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"is_verified"</span>: <span
                                                        class="json-boolean">false</span>,
                                                    <span class="json-key">"has_pending_code"</span>: <span
                                                        class="json-boolean">true</span>,
                                                    <span class="json-key">"code_expires_at"</span>: <span
                                                        class="json-string">"2024-01-15T15:00:00.000000Z"</span>,
                                                    <span class="json-key">"attempts_remaining"</span>: <span
                                                        class="json-number">2</span>,
                                                    <span class="json-key">"can_resend"</span>: <span
                                                        class="json-boolean">true</span>,
                                                    <span class="json-key">"can_resend_after"</span>: <span
                                                        class="json-string">null</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="password" class="section">
                        <h2><i class="fas fa-lock"></i> Password Reset System</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-key"></i> Password Recovery</div>
                                    <div class="api-description">Secure password reset workflow</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/password/send-reset-code</span>
                                            <span class="endpoint-description">Send password reset code</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>User's email address</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Password reset code sent
                                                        successfully"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"code_expires_at"</span>: <span
                                                        class="json-string">"2024-01-15T15:00:00.000000Z"</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/password/verify-reset-code</span>
                                            <span class="endpoint-description">Verify reset code</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>User's email address</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">code</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>6-digit reset code from email</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Reset code verified successfully"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"reset_token"</span>: <span
                                                        class="json-string">"abc123def456..."</span>,
                                                    <span class="json-key">"token_expires_at"</span>: <span
                                                        class="json-string">"2024-01-15T15:30:00.000000Z"</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/password/reset-with-code</span>
                                            <span class="endpoint-description">Reset password with code</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">email</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>User's email address</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">reset_token</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Reset token from verify step</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>New password (min 8 characters)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">password_confirmation</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Password confirmation</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Password reset successfully"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="google" class="section">
                        <h2><i class="fab fa-google"></i> Google OAuth Integration</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fab fa-google"></i> OAuth Flow</div>
                                    <div class="api-description">Google authentication and account linking</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method get">GET</span>
                                            <span class="endpoint-path">/api/auth/google</span>
                                            <span class="endpoint-description">Redirect to Google OAuth</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-info-circle"></i> Description
                                                </div>
                                                <p>Redirects user to Google OAuth consent screen. This is typically used
                                                    in web browsers.</p>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    HTTP 302 Redirect to Google OAuth URL
                                                    Location: https://accounts.google.com/oauth/authorize?client_id=...
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method get">GET</span>
                                            <span class="endpoint-path">/api/auth/google/callback</span>
                                            <span class="endpoint-description">Handle Google OAuth callback</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">code</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Authorization code from Google</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">state</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>State parameter for security</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Google authentication successful"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"google_id"</span>: <span
                                                        class="json-string">"123456789012345678901"</span>,
                                                    <span class="json-key">"avatar"</span>: <span
                                                        class="json-string">"https://lh3.googleusercontent.com/..."</span>
                                                    },
                                                    <span class="json-key">"token"</span>: <span
                                                        class="json-string">"1|abc123def456..."</span>,
                                                    <span class="json-key">"is_new_user"</span>: <span
                                                        class="json-boolean">false</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/auth/google/token</span>
                                            <span class="endpoint-description">Login with Google token</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">google_token</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Google ID token from client</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Google login successful"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"google_id"</span>: <span
                                                        class="json-string">"123456789012345678901"</span>,
                                                    <span class="json-key">"email_verified_at"</span>: <span
                                                        class="json-string">"2024-01-01T00:00:00.000000Z"</span>
                                                    },
                                                    <span class="json-key">"token"</span>: <span
                                                        class="json-string">"1|abc123def456..."</span>,
                                                    <span class="json-key">"is_new_user"</span>: <span
                                                        class="json-boolean">false</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/auth/google/unlink</span>
                                            <span class="endpoint-description">Unlink Google account</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-shield-alt"></i>
                                                    Authentication</div>
                                                <p>Requires Bearer token in Authorization header</p>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Google account unlinked
                                                        successfully"</span>,
                                                    <span class="json-key">"user"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"John Doe"</span>,
                                                    <span class="json-key">"email"</span>: <span
                                                        class="json-string">"<EMAIL>"</span>,
                                                    <span class="json-key">"google_id"</span>: <span
                                                        class="json-string">null</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="bins" class="section">
                        <h2><i class="fas fa-folder"></i> Bins Management</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-folder-open"></i> Financial Bins</div>
                                    <div class="api-description">Organize finances into income and expense categories
                                    </div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method get">GET</span>
                                            <span class="endpoint-path">/api/bins</span>
                                            <span class="endpoint-description">Get all bins (paginated)</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">page</td>
                                                            <td><span class="param-type">integer</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Page number for pagination</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">per_page</td>
                                                            <td><span class="param-type">integer</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Items per page (max 100)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">type</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Filter by type: income, expense</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"bins"</span>: [
                                                    {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"Salary"</span>,
                                                    <span class="json-key">"type"</span>: <span
                                                        class="json-string">"income"</span>,
                                                    <span class="json-key">"description"</span>: <span
                                                        class="json-string">"Monthly salary income"</span>,
                                                    <span class="json-key">"threshold_min"</span>: <span
                                                        class="json-number">0</span>,
                                                    <span class="json-key">"threshold_max"</span>: <span
                                                        class="json-number">10000</span>,
                                                    <span class="json-key">"current_amount"</span>: <span
                                                        class="json-number">5500.00</span>,
                                                    <span class="json-key">"sub_bins_count"</span>: <span
                                                        class="json-number">3</span>,
                                                    <span class="json-key">"created_at"</span>: <span
                                                        class="json-string">"2024-01-01T00:00:00.000000Z"</span>
                                                    }
                                                    ],
                                                    <span class="json-key">"pagination"</span>: {
                                                    <span class="json-key">"current_page"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"total"</span>: <span
                                                        class="json-number">15</span>,
                                                    <span class="json-key">"per_page"</span>: <span
                                                        class="json-number">10</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/bins</span>
                                            <span class="endpoint-description">Create new bin</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">name</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Bin name (max 255 characters)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">type</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Bin type: income, expense</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">description</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Bin description</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">threshold_min</td>
                                                            <td><span class="param-type">decimal</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Minimum threshold amount</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">threshold_max</td>
                                                            <td><span class="param-type">decimal</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Maximum threshold amount</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Bin created successfully"</span>,
                                                    <span class="json-key">"bin"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">2</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"Groceries"</span>,
                                                    <span class="json-key">"type"</span>: <span
                                                        class="json-string">"expense"</span>,
                                                    <span class="json-key">"description"</span>: <span
                                                        class="json-string">"Monthly grocery expenses"</span>,
                                                    <span class="json-key">"threshold_min"</span>: <span
                                                        class="json-number">0</span>,
                                                    <span class="json-key">"threshold_max"</span>: <span
                                                        class="json-number">800</span>,
                                                    <span class="json-key">"current_amount"</span>: <span
                                                        class="json-number">0.00</span>,
                                                    <span class="json-key">"user_id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"created_at"</span>: <span
                                                        class="json-string">"2024-01-01T12:00:00.000000Z"</span>,
                                                    <span class="json-key">"updated_at"</span>: <span
                                                        class="json-string">"2024-01-01T12:00:00.000000Z"</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="sub-bins" class="section">
                        <h2><i class="fas fa-sitemap"></i> Sub-Bins (Hierarchical)</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-tree"></i> Hierarchical Organization</div>
                                    <div class="api-description">Unlimited nesting for detailed financial categorization
                                    </div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/bins/{binId}/sub-bins</span>
                                        <span>Get all sub-bins (flat list)</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/bins/{binId}/sub-bins-hierarchy</span>
                                        <span>Get hierarchical tree structure</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/bins/{binId}/sub-bins</span>
                                        <span>Create sub-bin</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span
                                            class="endpoint-path">/api/bins/{binId}/sub-bins/{parentSubBinId}/nested</span>
                                        <span>Create nested sub-bin</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/bins/{binId}/sub-bins/{id}</span>
                                        <span>Get specific sub-bin</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method put">PUT</span>
                                        <span class="endpoint-path">/api/bins/{binId}/sub-bins/{id}</span>
                                        <span>Update sub-bin</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method delete">DELETE</span>
                                        <span class="endpoint-path">/api/bins/{binId}/sub-bins/{id}</span>
                                        <span>Delete sub-bin</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="transactions" class="section">
                        <h2><i class="fas fa-exchange-alt"></i> Transactions Management</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-receipt"></i> Transaction Operations</div>
                                    <div class="api-description">Complete transaction management and analytics</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method post">POST</span>
                                            <span class="endpoint-path">/api/transactions</span>
                                            <span class="endpoint-description">Create new transaction</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">bin_id</td>
                                                            <td><span class="param-type">integer</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>ID of the bin to assign transaction to</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">sub_bin_id</td>
                                                            <td><span class="param-type">integer</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>ID of the sub-bin to assign transaction to</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">transaction_type</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Type: income, expense, transfer</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">amount</td>
                                                            <td><span class="param-type">decimal</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Transaction amount (min 0.01)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">currency</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Currency code (default: USD)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">description</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Transaction description</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">category</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Transaction category</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">payment_method</td>
                                                            <td><span class="param-type">string</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Payment method used</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">transaction_date</td>
                                                            <td><span class="param-type">date</span></td>
                                                            <td><span class="param-type required">required</span></td>
                                                            <td>Date of transaction (YYYY-MM-DD)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">transaction_time</td>
                                                            <td><span class="param-type">time</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Time of transaction (HH:MM:SS)</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"message"</span>: <span
                                                        class="json-string">"Transaction created successfully"</span>,
                                                    <span class="json-key">"transaction"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">123</span>,
                                                    <span class="json-key">"bin_id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"sub_bin_id"</span>: <span
                                                        class="json-number">5</span>,
                                                    <span class="json-key">"transaction_type"</span>: <span
                                                        class="json-string">"expense"</span>,
                                                    <span class="json-key">"amount"</span>: <span
                                                        class="json-string">"45.99"</span>,
                                                    <span class="json-key">"currency"</span>: <span
                                                        class="json-string">"USD"</span>,
                                                    <span class="json-key">"description"</span>: <span
                                                        class="json-string">"Grocery shopping at Walmart"</span>,
                                                    <span class="json-key">"category"</span>: <span
                                                        class="json-string">"Food & Dining"</span>,
                                                    <span class="json-key">"payment_method"</span>: <span
                                                        class="json-string">"Credit Card"</span>,
                                                    <span class="json-key">"transaction_date"</span>: <span
                                                        class="json-string">"2024-01-15"</span>,
                                                    <span class="json-key">"transaction_time"</span>: <span
                                                        class="json-string">"14:30:00"</span>,
                                                    <span class="json-key">"user_id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"created_at"</span>: <span
                                                        class="json-string">"2024-01-15T14:30:00.000000Z"</span>,
                                                    <span class="json-key">"updated_at"</span>: <span
                                                        class="json-string">"2024-01-15T14:30:00.000000Z"</span>,
                                                    <span class="json-key">"bin"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">1</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"Groceries"</span>,
                                                    <span class="json-key">"type"</span>: <span
                                                        class="json-string">"expense"</span>
                                                    },
                                                    <span class="json-key">"sub_bin"</span>: {
                                                    <span class="json-key">"id"</span>: <span
                                                        class="json-number">5</span>,
                                                    <span class="json-key">"name"</span>: <span
                                                        class="json-string">"Weekly Shopping"</span>,
                                                    <span class="json-key">"type"</span>: <span
                                                        class="json-string">"expense"</span>
                                                    }
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="endpoint">
                                        <div class="endpoint-header">
                                            <span class="method get">GET</span>
                                            <span class="endpoint-path">/api/transactions/stats</span>
                                            <span class="endpoint-description">Get transaction statistics</span>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        </div>
                                        <div class="endpoint-details">
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-cog"></i> Parameters</div>
                                                <table class="params-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Parameter</th>
                                                            <th>Type</th>
                                                            <th>Required</th>
                                                            <th>Description</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td class="param-name">start_date</td>
                                                            <td><span class="param-type">date</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Start date for statistics (YYYY-MM-DD)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">end_date</td>
                                                            <td><span class="param-type">date</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>End date for statistics (YYYY-MM-DD)</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="param-name">bin_id</td>
                                                            <td><span class="param-type">integer</span></td>
                                                            <td><span class="param-type optional">optional</span></td>
                                                            <td>Filter by specific bin</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="detail-section">
                                                <div class="detail-title"><i class="fas fa-arrow-left"></i> Response
                                                </div>
                                                <div class="code-block">
                                                    {
                                                    <span class="json-key">"stats"</span>: {
                                                    <span class="json-key">"total_income"</span>: <span
                                                        class="json-string">"5500.00"</span>,
                                                    <span class="json-key">"total_expenses"</span>: <span
                                                        class="json-string">"3250.75"</span>,
                                                    <span class="json-key">"net_income"</span>: <span
                                                        class="json-string">"2249.25"</span>,
                                                    <span class="json-key">"transaction_count"</span>: <span
                                                        class="json-number">45</span>,
                                                    <span class="json-key">"average_transaction"</span>: <span
                                                        class="json-string">"194.46"</span>,
                                                    <span class="json-key">"by_category"</span>: {
                                                    <span class="json-key">"Food & Dining"</span>: <span
                                                        class="json-string">"850.25"</span>,
                                                    <span class="json-key">"Transportation"</span>: <span
                                                        class="json-string">"320.50"</span>,
                                                    <span class="json-key">"Entertainment"</span>: <span
                                                        class="json-string">"180.00"</span>
                                                    },
                                                    <span class="json-key">"by_month"</span>: {
                                                    <span class="json-key">"2024-01"</span>: {
                                                    <span class="json-key">"income"</span>: <span
                                                        class="json-string">"5500.00"</span>,
                                                    <span class="json-key">"expenses"</span>: <span
                                                        class="json-string">"3250.75"</span>
                                                    }
                                                    }
                                                    },
                                                    <span class="json-key">"period"</span>: {
                                                    <span class="json-key">"start_date"</span>: <span
                                                        class="json-string">"2024-01-01"</span>,
                                                    <span class="json-key">"end_date"</span>: <span
                                                        class="json-string">"2024-01-31"</span>
                                                    }
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="subscriptions" class="section">
                        <h2><i class="fas fa-credit-card"></i> Subscription Management</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-crown"></i> Subscription Operations</div>
                                    <div class="api-description">Complete subscription lifecycle management</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/subscriptions</span>
                                        <span>Get user subscriptions</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/subscriptions</span>
                                        <span>Create subscription</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/subscriptions/select-plan</span>
                                        <span>Select subscription plan</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/subscriptions/add-payment-method</span>
                                        <span>Add payment method and start trial</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/subscriptions/plans</span>
                                        <span>Get available plans</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/subscriptions/history</span>
                                        <span>Get subscription history</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/subscriptions/cancel</span>
                                        <span>Cancel subscription</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method put">PUT</span>
                                        <span class="endpoint-path">/api/subscriptions/{uuid}/change-cycle</span>
                                        <span>Change billing cycle</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="crypto" class="section">
                        <h2><i class="fab fa-bitcoin"></i> Crypto Wallet Management</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-wallet"></i> Cryptocurrency Operations</div>
                                    <div class="api-description">Manage crypto wallets and track digital assets</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/crypto-wallets</span>
                                        <span>Get all crypto wallets</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/crypto-wallets</span>
                                        <span>Add new crypto wallet</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/crypto-wallets/qr-code</span>
                                        <span>Add wallet from QR code</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/crypto-wallets/{id}/assets</span>
                                        <span>Get wallet assets</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/crypto-wallets/{id}/transactions</span>
                                        <span>Get wallet transactions</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/crypto-wallets/{id}/sync</span>
                                        <span>Sync wallet data</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method delete">DELETE</span>
                                        <span class="endpoint-path">/api/crypto-wallets/{id}</span>
                                        <span>Delete wallet</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="plaid" class="section">
                        <h2><i class="fas fa-university"></i> Plaid Banking Integration</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-link"></i> Bank Account Linking</div>
                                    <div class="api-description">Connect and manage bank accounts securely</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/plaid/link-token</span>
                                        <span>Get Plaid link token</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/plaid/accounts</span>
                                        <span>Connect bank account</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/plaid/accounts</span>
                                        <span>Get connected accounts</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/plaid/accounts/{id}/set-default</span>
                                        <span>Set default account</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method delete">DELETE</span>
                                        <span class="endpoint-path">/api/plaid/accounts/{id}</span>
                                        <span>Remove bank account</span>
                                    </div>
                                </div>
                            </div>

                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-money-bill-transfer"></i> Payment Processing
                                    </div>
                                    <div class="api-description">Process payments through connected bank accounts</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/plaid-payment/accounts</span>
                                        <span>Get payment-enabled accounts</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/plaid-payment/process</span>
                                        <span>Process payment</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/plaid-payment/status/{paymentId}</span>
                                        <span>Get payment status</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="reports" class="section">
                        <h2><i class="fas fa-chart-bar"></i> Reports & Analytics</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-file-pdf"></i> Report Generation</div>
                                    <div class="api-description">Generate and manage financial reports</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/reports</span>
                                        <span>Get all reports</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/reports</span>
                                        <span>Generate new report</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/reports/{id}</span>
                                        <span>Get specific report</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method get">GET</span>
                                        <span class="endpoint-path">/api/reports/{id}/download</span>
                                        <span>Download report</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/reports/{id}/regenerate</span>
                                        <span>Regenerate report</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method delete">DELETE</span>
                                        <span class="endpoint-path">/api/reports/{id}</span>
                                        <span>Delete report</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section id="webhooks" class="section">
                        <h2><i class="fas fa-webhook"></i> Webhooks</h2>

                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title"><i class="fas fa-broadcast-tower"></i> Real-time
                                        Notifications</div>
                                    <div class="api-description">Handle external service webhooks</div>
                                </div>
                                <div class="api-body">
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/webhook/stripe</span>
                                        <span>Stripe webhook handler</span>
                                    </div>
                                    <div class="endpoint">
                                        <span class="method post">POST</span>
                                        <span class="endpoint-path">/api/webhook/plaid</span>
                                        <span>Plaid webhook handler</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Active navigation highlighting
        const sections = document.querySelectorAll('.section');
        const navLinks = document.querySelectorAll('.sidebar-menu a');

        function updateActiveNav() {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        }

        window.addEventListener('scroll', updateActiveNav);

        // API card hover effects
        document.querySelectorAll('.api-card').forEach(card => {
            card.addEventListener('mouseenter', function () {
                this.style.transform = 'translateY(-8px)';
            });

            card.addEventListener('mouseleave', function () {
                this.style.transform = 'translateY(-5px)';
            });
        });

        // Endpoint expansion functionality
        document.querySelectorAll('.endpoint-header').forEach(header => {
            header.addEventListener('click', function () {
                const endpoint = this.closest('.endpoint');
                const details = endpoint.querySelector('.endpoint-details');
                const icon = this.querySelector('.expand-icon');

                if (endpoint.classList.contains('expanded')) {
                    endpoint.classList.remove('expanded');
                    details.style.display = 'none';
                } else {
                    // Close other expanded endpoints
                    document.querySelectorAll('.endpoint.expanded').forEach(exp => {
                        exp.classList.remove('expanded');
                        exp.querySelector('.endpoint-details').style.display = 'none';
                    });

                    endpoint.classList.add('expanded');
                    details.style.display = 'block';
                }
            });
        });

        // Endpoint path click to copy
        document.querySelectorAll('.endpoint-path').forEach(path => {
            path.addEventListener('click', function (e) {
                e.stopPropagation(); // Prevent endpoint expansion
                navigator.clipboard.writeText(this.textContent);

                // Show feedback
                const original = this.textContent;
                this.textContent = 'Copied!';
                this.style.color = '#10b981';

                setTimeout(() => {
                    this.textContent = original;
                    this.style.color = '';
                }, 1000);
            });

            path.style.cursor = 'pointer';
            path.title = 'Click to copy endpoint';
        });

        // Add loading animation to stats
        document.querySelectorAll('.stat-number').forEach(stat => {
            const finalValue = stat.textContent;
            if (!isNaN(finalValue)) {
                let current = 0;
                const increment = finalValue / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= finalValue) {
                        stat.textContent = finalValue;
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(current);
                    }
                }, 30);
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function () {
            // Update active navigation on load
            updateActiveNav();

            // Add fade-in animation to sections
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.api-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>

</html>