<?php

namespace App\Console\Commands;

use App\Models\NotificationSetting;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FixEmailNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix email notification settings and process pending notifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing email notification settings...');
        
        // Run the migration to add the email_registration column
        if (!Schema::hasColumn('notification_settings', 'email_registration')) {
            $this->info('Running migration to add email_registration column...');
            Artisan::call('migrate');
            $this->info(Artisan::output());
        }
        
        // Update all notification settings to enable email_registration
        $this->info('Updating notification settings...');
        $count = NotificationSetting::where('email_registration', null)
            ->orWhere('email_registration', false)
            ->update(['email_registration' => true]);
        
        $this->info("Updated {$count} notification settings.");
        
        // Process any pending jobs
        $this->info('Processing pending jobs...');
        Artisan::call('queue:work', [
            '--stop-when-empty' => true,
            '--tries' => 3
        ]);
        
        $this->info(Artisan::output());
        
        // Retry failed jobs
        $this->info('Retrying failed jobs...');
        $failedJobs = DB::table('failed_jobs')->count();
        
        if ($failedJobs > 0) {
            Artisan::call('queue:retry all');
            $this->info(Artisan::output());
        } else {
            $this->info('No failed jobs to retry.');
        }
        
        $this->info('Email notification settings fixed successfully!');
        
        return Command::SUCCESS;
    }
}
