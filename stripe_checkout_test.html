<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch - Stripe Checkout Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .package {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        .package:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }
        .package-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .package-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .package-price {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
        }
        .package-features {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .package-features li {
            padding: 5px 0;
            color: #666;
        }
        .package-features li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        .buy-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s ease;
        }
        .buy-button:hover {
            background: #0056b3;
        }
        .buy-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .auth-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .auth-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .auth-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 PocketWatch - Stripe Checkout Test</h1>
        
        <!-- Authentication Section -->
        <div class="auth-section">
            <h3>🔐 Authentication</h3>
            <input type="email" id="email" class="auth-input" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" class="auth-input" placeholder="Password" value="password123">
            <button onclick="login()" class="auth-button">Login</button>
            <button onclick="loginWithGoogle()" class="auth-button" style="background: #db4437;">Login with Google</button>
            <div id="auth-status"></div>
        </div>

        <!-- Packages Section -->
        <div id="packages-section" style="display: none;">
            <h2>📦 Choose Your Package</h2>
            <div id="packages-container">
                <div class="loading" id="loading">Loading packages...</div>
            </div>
        </div>

        <!-- Status Section -->
        <div id="status-section"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        let authToken = localStorage.getItem('auth_token');

        // Check if user is already logged in
        if (authToken) {
            loadPackages();
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (data.token) {
                    authToken = data.token;
                    localStorage.setItem('auth_token', authToken);
                    showStatus('✅ Login successful!', 'success');
                    loadPackages();
                } else {
                    showStatus('❌ Login failed: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                showStatus('❌ Login error: ' + error.message, 'error');
            }
        }

        async function loginWithGoogle() {
            try {
                const response = await fetch(`${API_BASE}/auth/google`);
                const data = await response.json();

                if (data.success && data.redirect_url) {
                    showStatus('🔄 Redirecting to Google...', 'success');
                    window.location.href = data.redirect_url;
                } else {
                    showStatus('❌ Google OAuth failed: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Google OAuth error: ' + error.message, 'error');
            }
        }

        async function loadPackages() {
            document.getElementById('packages-section').style.display = 'block';
            document.getElementById('loading').style.display = 'block';

            try {
                const response = await fetch(`${API_BASE}/packages-checkout`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.success) {
                    displayPackages(data.data.packages);
                    showStatus(`✅ Welcome ${data.data.user_info.name}! Choose a package below.`, 'success');
                } else {
                    showStatus('❌ Failed to load packages: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Error loading packages: ' + error.message, 'error');
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        function displayPackages(packages) {
            const container = document.getElementById('packages-container');
            container.innerHTML = '';

            packages.forEach(package => {
                const packageDiv = document.createElement('div');
                packageDiv.className = 'package';
                packageDiv.innerHTML = `
                    <div class="package-header">
                        <div class="package-name">${package.name}</div>
                        <div class="package-price">$${package.price}/mo</div>
                    </div>
                    <p style="color: #666; margin: 10px 0;">${package.description}</p>
                    <ul class="package-features">
                        ${package.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    <button class="buy-button" onclick="buyPackage('${package.id}', '${package.name}', ${package.price})">
                        🚀 Buy ${package.name} - Start 7-Day Trial
                    </button>
                `;
                container.appendChild(packageDiv);
            });
        }

        async function buyPackage(packageId, packageName, price) {
            showStatus(`🔄 Creating checkout session for ${packageName}...`, 'success');

            try {
                const response = await fetch(`${API_BASE}/packages/${packageId}/checkout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.success && data.data.checkout_url) {
                    showStatus(`✅ Redirecting to Stripe for ${packageName} ($${price}/month)...`, 'success');
                    
                    // Add a small delay to show the message
                    setTimeout(() => {
                        window.location.href = data.data.checkout_url;
                    }, 1000);
                } else {
                    showStatus('❌ Failed to create checkout: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Checkout error: ' + error.message, 'error');
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Handle URL parameters (for OAuth callback)
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const sessionId = urlParams.get('session_id');

        if (code) {
            // Handle Google OAuth callback
            handleGoogleCallback(code);
        } else if (sessionId) {
            // Handle Stripe success callback
            handleStripeSuccess(sessionId);
        }

        async function handleGoogleCallback(code) {
            try {
                const response = await fetch(`${API_BASE}/auth/google/callback?code=${code}`);
                const data = await response.json();

                if (data.success && data.token) {
                    authToken = data.token;
                    localStorage.setItem('auth_token', authToken);
                    showStatus(`✅ Google login successful! Welcome ${data.user.name}`, 'success');
                    loadPackages();
                    
                    // Clean URL
                    window.history.replaceState({}, document.title, window.location.pathname);
                } else {
                    showStatus('❌ Google login failed: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Google callback error: ' + error.message, 'error');
            }
        }

        async function handleStripeSuccess(sessionId) {
            try {
                const response = await fetch(`${API_BASE}/payment-success?session_id=${sessionId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    }
                });
                const data = await response.json();

                if (data.success) {
                    showStatus(`🎉 Payment successful! Your 7-day trial has started for ${data.data.package_name}`, 'success');
                    
                    // Redirect to app or show success page
                    setTimeout(() => {
                        window.location.href = '/dashboard'; // Replace with your app URL
                    }, 3000);
                } else {
                    showStatus('❌ Payment verification failed: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Payment verification error: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
