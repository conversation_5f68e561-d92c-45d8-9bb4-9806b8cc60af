<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Bin;
use App\Models\Report;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Barryvdh\DomPDF\Facade\Pdf;

class ReportController extends Controller
{
    /**
     * Display a listing of the reports.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $reports = Auth::user()->reports()->latest()->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $reports,
        ]);
    }

    /**
     * Store a newly created report in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:transaction,bin,summary',
            'format' => 'required|string|in:csv,pdf',
            'period_type' => 'required|string|in:daily,weekly,monthly,custom',
            'start_date' => 'required_if:period_type,custom|nullable|date',
            'end_date' => 'required_if:period_type,custom|nullable|date|after_or_equal:start_date',
            'bin_id' => 'nullable|exists:bins,id',
            'sub_bin_id' => 'nullable|exists:sub_bins,id',
            'is_recurring' => 'nullable|boolean',
            'schedule' => 'required_if:is_recurring,true|nullable|string|in:daily,weekly,monthly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Set date range based on period type
        $dates = $this->calculateDateRange($request->period_type, $request->start_date, $request->end_date);

        // Prepare filters
        $filters = [
            'bin_id' => $request->bin_id,
            'sub_bin_id' => $request->sub_bin_id,
            'transaction_type' => $request->transaction_type,
            'min_amount' => $request->min_amount,
            'max_amount' => $request->max_amount,
        ];

        // Create the report
        $report = Auth::user()->reports()->create([
            'name' => $request->name,
            'type' => $request->type,
            'format' => $request->format,
            'period_type' => $request->period_type,
            'start_date' => $dates['start_date'],
            'end_date' => $dates['end_date'],
            'filters' => $filters,
            'status' => 'pending',
            'is_recurring' => $request->has('is_recurring'),
            'schedule' => $request->is_recurring ? $request->schedule : null,
            'next_run_at' => $request->is_recurring ? $this->calculateNextRunDate($request->schedule) : null,
        ]);

        // Generate the report in the background
        dispatch(function () use ($report) {
            $this->generateReport($report);
        })->afterResponse();

        return response()->json([
            'success' => true,
            'message' => 'Report created successfully. It will be available for download shortly.',
            'data' => $report,
        ]);
    }

    /**
     * Display the specified report.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $report = Auth::user()->reports()->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * Remove the specified report from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $report = Auth::user()->reports()->findOrFail($id);

        // Delete the report file if it exists
        if ($report->file_path && Storage::exists($report->file_path)) {
            Storage::delete($report->file_path);
        }

        $report->delete();

        return response()->json([
            'success' => true,
            'message' => 'Report deleted successfully.',
        ]);
    }

    /**
     * Download the specified report.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function download($id)
    {
        $report = Auth::user()->reports()->findOrFail($id);

        // Check if the report is ready for download
        if ($report->status !== 'completed' || !$report->file_path || !Storage::exists($report->file_path)) {
            return response()->json([
                'success' => false,
                'message' => 'Report is not ready for download yet.',
            ], 400);
        }

        $fileName = $report->name . '.' . $report->format;

        return Storage::download($report->file_path, $fileName);
    }

    /**
     * Regenerate the specified report.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function regenerate($id)
    {
        $report = Auth::user()->reports()->findOrFail($id);

        // Delete the old report file if it exists
        if ($report->file_path && Storage::exists($report->file_path)) {
            Storage::delete($report->file_path);
        }

        // Update the report status
        $report->update([
            'status' => 'pending',
            'file_path' => null,
        ]);

        // Generate the report in the background
        dispatch(function () use ($report) {
            $this->generateReport($report);
        })->afterResponse();

        return response()->json([
            'success' => true,
            'message' => 'Report regeneration started. It will be available for download shortly.',
            'data' => $report,
        ]);
    }

    /**
     * Calculate the date range based on the period type.
     *
     * @param  string  $periodType
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function calculateDateRange($periodType, $startDate = null, $endDate = null)
    {
        $now = Carbon::now();

        switch ($periodType) {
            case 'daily':
                return [
                    'start_date' => $now->copy()->startOfDay(),
                    'end_date' => $now->copy()->endOfDay(),
                ];
            case 'weekly':
                return [
                    'start_date' => $now->copy()->startOfWeek(),
                    'end_date' => $now->copy()->endOfWeek(),
                ];
            case 'monthly':
                return [
                    'start_date' => $now->copy()->startOfMonth(),
                    'end_date' => $now->copy()->endOfMonth(),
                ];
            case 'custom':
                return [
                    'start_date' => Carbon::parse($startDate)->startOfDay(),
                    'end_date' => Carbon::parse($endDate)->endOfDay(),
                ];
            default:
                return [
                    'start_date' => $now->copy()->startOfMonth(),
                    'end_date' => $now->copy()->endOfMonth(),
                ];
        }
    }

    /**
     * Calculate the next run date based on the schedule.
     *
     * @param  string  $schedule
     * @return \Carbon\Carbon|null
     */
    private function calculateNextRunDate($schedule)
    {
        $now = Carbon::now();

        switch ($schedule) {
            case 'daily':
                return $now->copy()->addDay()->startOfDay();
            case 'weekly':
                return $now->copy()->addWeek()->startOfWeek();
            case 'monthly':
                return $now->copy()->addMonth()->startOfMonth();
            default:
                return null;
        }
    }

    /**
     * Generate the report.
     *
     * @param  \App\Models\Report  $report
     * @return void
     */
    private function generateReport(Report $report)
    {
        // Update the report status
        $report->update([
            'status' => 'processing',
        ]);

        try {
            // Get the data for the report
            $data = $this->getReportData($report);

            // Generate the file based on the format
            if ($report->format === 'csv') {
                $filePath = $this->generateCsvReport($report, $data);
            } else {
                $filePath = $this->generatePdfReport($report, $data);
            }

            // Update the report with the file path
            $report->update([
                'status' => 'completed',
                'file_path' => $filePath,
                'last_generated_at' => now(),
                'next_run_at' => $report->is_recurring ? $this->calculateNextRunDate($report->schedule) : null,
            ]);
        } catch (\Exception $e) {
            // Update the report status to failed
            $report->update([
                'status' => 'failed',
            ]);

            // Log the error
            \Log::error('Report generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get the data for the report.
     *
     * @param  \App\Models\Report  $report
     * @return array
     */
    private function getReportData(Report $report)
    {
        $user = $report->user;
        $filters = $report->filters;
        $startDate = $report->start_date;
        $endDate = $report->end_date;

        switch ($report->type) {
            case 'transaction':
                $query = Transaction::where('user_id', $user->id)
                    ->whereBetween('date', [$startDate, $endDate])
                    ->orderBy('date', 'desc');

                // Apply filters
                if (!empty($filters['bin_id'])) {
                    $query->where('bin_id', $filters['bin_id']);
                }

                if (!empty($filters['sub_bin_id'])) {
                    $query->where('sub_bin_id', $filters['sub_bin_id']);
                }

                if (!empty($filters['transaction_type'])) {
                    $query->where('type', $filters['transaction_type']);
                }

                if (!empty($filters['min_amount'])) {
                    $query->where('amount', '>=', $filters['min_amount']);
                }

                if (!empty($filters['max_amount'])) {
                    $query->where('amount', '<=', $filters['max_amount']);
                }

                $transactions = $query->get();

                return [
                    'transactions' => $transactions,
                    'total_income' => $transactions->where('type', 'income')->sum('amount'),
                    'total_expense' => $transactions->where('type', 'expense')->sum('amount'),
                    'net_amount' => $transactions->where('type', 'income')->sum('amount') - $transactions->where('type', 'expense')->sum('amount'),
                ];

            case 'bin':
                $query = Bin::where('user_id', $user->id);

                // Apply filters
                if (!empty($filters['bin_id'])) {
                    $query->where('id', $filters['bin_id']);
                }

                $bins = $query->with('subBins')->get();

                return [
                    'bins' => $bins,
                    'total_bins' => $bins->count(),
                    'total_sub_bins' => $bins->flatMap->subBins->count(),
                    'total_amount' => $bins->sum('current_amount'),
                ];

            case 'summary':
                $transactions = Transaction::where('user_id', $user->id)
                    ->whereBetween('date', [$startDate, $endDate])
                    ->get();

                $bins = Bin::where('user_id', $user->id)->with('subBins')->get();

                return [
                    'transactions' => $transactions,
                    'bins' => $bins,
                    'total_income' => $transactions->where('type', 'income')->sum('amount'),
                    'total_expense' => $transactions->where('type', 'expense')->sum('amount'),
                    'net_amount' => $transactions->where('type', 'income')->sum('amount') - $transactions->where('type', 'expense')->sum('amount'),
                    'total_bins' => $bins->count(),
                    'total_sub_bins' => $bins->flatMap->subBins->count(),
                    'total_bin_amount' => $bins->sum('current_amount'),
                ];

            default:
                return [];
        }
    }

    /**
     * Generate a CSV report.
     *
     * @param  \App\Models\Report  $report
     * @param  array  $data
     * @return string
     */
    private function generateCsvReport(Report $report, array $data)
    {
        $headers = [];
        $rows = [];

        switch ($report->type) {
            case 'transaction':
                $headers = ['Date', 'Description', 'Type', 'Amount', 'Bin', 'Sub-Bin', 'Category', 'Notes'];

                foreach ($data['transactions'] as $transaction) {
                    $rows[] = [
                        $transaction->date->format('Y-m-d'),
                        $transaction->description,
                        ucfirst($transaction->type),
                        $transaction->amount,
                        $transaction->bin ? $transaction->bin->name : '',
                        $transaction->subBin ? $transaction->subBin->name : '',
                        $transaction->category,
                        $transaction->notes,
                    ];
                }

                // Add summary rows
                $rows[] = ['', '', '', '', '', '', '', ''];
                $rows[] = ['Summary', '', '', '', '', '', '', ''];
                $rows[] = ['Total Income', '', '', $data['total_income'], '', '', '', ''];
                $rows[] = ['Total Expense', '', '', $data['total_expense'], '', '', '', ''];
                $rows[] = ['Net Amount', '', '', $data['net_amount'], '', '', '', ''];
                break;

            case 'bin':
                $headers = ['Bin Name', 'Type', 'Description', 'Current Amount', 'Min Threshold', 'Max Threshold', 'Currency', 'Status'];

                foreach ($data['bins'] as $bin) {
                    $rows[] = [
                        $bin->name,
                        ucfirst($bin->type),
                        $bin->description,
                        $bin->current_amount,
                        $bin->threshold_min,
                        $bin->threshold_max,
                        $bin->currency,
                        $bin->is_active ? 'Active' : 'Inactive',
                    ];

                    // Add sub-bins
                    foreach ($bin->subBins as $subBin) {
                        $rows[] = [
                            '-- ' . $subBin->name,
                            ucfirst($subBin->type),
                            $subBin->description,
                            $subBin->current_amount,
                            $subBin->threshold_min,
                            $subBin->threshold_max,
                            $subBin->currency,
                            $subBin->is_active ? 'Active' : 'Inactive',
                        ];
                    }
                }

                // Add summary rows
                $rows[] = ['', '', '', '', '', '', '', ''];
                $rows[] = ['Summary', '', '', '', '', '', '', ''];
                $rows[] = ['Total Bins', $data['total_bins'], '', '', '', '', '', ''];
                $rows[] = ['Total Sub-Bins', $data['total_sub_bins'], '', '', '', '', '', ''];
                $rows[] = ['Total Amount', '', '', $data['total_amount'], '', '', '', ''];
                break;

            case 'summary':
                // First section: Transaction summary
                $headers = ['Summary Report', '', '', '', '', '', '', ''];
                $rows[] = ['Period', $report->start_date->format('Y-m-d') . ' to ' . $report->end_date->format('Y-m-d'), '', '', '', '', '', ''];
                $rows[] = ['', '', '', '', '', '', '', ''];
                $rows[] = ['Transaction Summary', '', '', '', '', '', '', ''];
                $rows[] = ['Total Income', '', '', $data['total_income'], '', '', '', ''];
                $rows[] = ['Total Expense', '', '', $data['total_expense'], '', '', '', ''];
                $rows[] = ['Net Amount', '', '', $data['net_amount'], '', '', '', ''];
                $rows[] = ['', '', '', '', '', '', '', ''];

                // Second section: Bin summary
                $rows[] = ['Bin Summary', '', '', '', '', '', '', ''];
                $rows[] = ['Total Bins', $data['total_bins'], '', '', '', '', '', ''];
                $rows[] = ['Total Sub-Bins', $data['total_sub_bins'], '', '', '', '', '', ''];
                $rows[] = ['Total Bin Amount', '', '', $data['total_bin_amount'], '', '', '', ''];
                $rows[] = ['', '', '', '', '', '', '', ''];

                // Third section: Recent transactions
                $rows[] = ['Recent Transactions', '', '', '', '', '', '', ''];
                $rows[] = ['Date', 'Description', 'Type', 'Amount', 'Bin', 'Sub-Bin', 'Category', 'Notes'];

                foreach ($data['transactions']->take(10) as $transaction) {
                    $rows[] = [
                        $transaction->date->format('Y-m-d'),
                        $transaction->description,
                        ucfirst($transaction->type),
                        $transaction->amount,
                        $transaction->bin ? $transaction->bin->name : '',
                        $transaction->subBin ? $transaction->subBin->name : '',
                        $transaction->category,
                        $transaction->notes,
                    ];
                }
                break;
        }

        // Create the CSV file
        $fileName = 'reports/' . $report->user_id . '/' . uniqid() . '.csv';
        $filePath = storage_path('app/' . $fileName);

        // Ensure the directory exists
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        // Write the CSV file
        $file = fopen($filePath, 'w');

        // Add UTF-8 BOM to ensure Excel opens the file correctly with UTF-8 encoding
        fputs($file, "\xEF\xBB\xBF");

        // Write headers
        fputcsv($file, $headers);

        // Write rows
        foreach ($rows as $row) {
            fputcsv($file, $row);
        }

        fclose($file);

        return $fileName;
    }

    /**
     * Generate a PDF report.
     *
     * @param  \App\Models\Report  $report
     * @param  array  $data
     * @return string
     */
    private function generatePdfReport(Report $report, array $data)
    {
        $view = '';
        $viewData = [];

        switch ($report->type) {
            case 'transaction':
                $view = 'reports.pdf.transactions';
                $viewData = [
                    'report' => $report,
                    'transactions' => $data['transactions'],
                    'total_income' => $data['total_income'],
                    'total_expense' => $data['total_expense'],
                    'net_amount' => $data['net_amount'],
                ];
                break;

            case 'bin':
                $view = 'reports.pdf.bins';
                $viewData = [
                    'report' => $report,
                    'bins' => $data['bins'],
                    'total_bins' => $data['total_bins'],
                    'total_sub_bins' => $data['total_sub_bins'],
                    'total_amount' => $data['total_amount'],
                ];
                break;

            case 'summary':
                $view = 'reports.pdf.summary';
                $viewData = [
                    'report' => $report,
                    'transactions' => $data['transactions']->take(10),
                    'bins' => $data['bins'],
                    'total_income' => $data['total_income'],
                    'total_expense' => $data['total_expense'],
                    'net_amount' => $data['net_amount'],
                    'total_bins' => $data['total_bins'],
                    'total_sub_bins' => $data['total_sub_bins'],
                    'total_bin_amount' => $data['total_bin_amount'],
                ];
                break;
        }

        // Generate the PDF
        $pdf = PDF::loadView($view, $viewData);

        // Save the PDF
        $fileName = 'reports/' . $report->user_id . '/' . uniqid() . '.pdf';
        Storage::put($fileName, $pdf->output());

        return $fileName;
    }
}
