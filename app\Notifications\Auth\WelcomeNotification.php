<?php

namespace App\Notifications\Auth;

use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class WelcomeNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_registration';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->subject = 'Welcome to ' . setting('app_name', 'PocketWatch');
        $this->title = 'Welcome to PocketWatch';
        $this->content = 'Thank you for registering with PocketWatch! We\'re excited to have you on board.
                         PocketWatch is your personal finance management tool that helps you track your expenses,
                         manage your budget, and achieve your financial goals.';

        $this->detailsTitle = 'Getting Started';
        $this->details = [
            'Create Bins' => 'Start by creating financial bins to organize your money.',
            'Track Expenses' => 'Record your expenses and income to keep track of your finances.',
            'Set Goals' => 'Set financial goals and track your progress.',
        ];

        $this->actionText = 'Get Started';
        $this->actionUrl = url('/login');

        $this->closing = 'We hope you enjoy using PocketWatch!';
        $this->signature = 'The PocketWatch Team';
    }
}
