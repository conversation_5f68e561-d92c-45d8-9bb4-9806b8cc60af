<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PlaidAccount;
use App\Services\PlaidService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PlaidController extends Controller
{
    /**
     * The Plaid service instance.
     *
     * @var \App\Services\PlaidService
     */
    protected $plaidService;

    /**
     * Create a new controller instance.
     *
     * @param \App\Services\PlaidService $plaidService
     * @return void
     */
    public function __construct(PlaidService $plaidService)
    {
        $this->middleware('auth:sanctum');
        $this->plaidService = $plaidService;
    }

    /**
     * Get a link token for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLinkToken()
    {
        $linkToken = $this->plaidService->createLinkToken(Auth::user());

        if (!$linkToken) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create Plaid link token',
            ], 500);
        }

        return response()->json([
            'success' => true,
            'link_token' => $linkToken['link_token'],
        ]);
    }

    /**
     * Store a newly created Plaid account in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'public_token' => 'required|string',
            'metadata' => 'required',
        ]);

        $metadata = is_string($request->metadata) ? json_decode($request->metadata, true) : $request->metadata;
        $institution = $metadata['institution'] ?? [];
        $accounts = $metadata['accounts'] ?? [];

        // Exchange public token for access token
        $exchangeResponse = $this->plaidService->exchangePublicToken($request->public_token);

        if (!$exchangeResponse) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to exchange Plaid public token',
            ], 500);
        }

        $accessToken = $exchangeResponse['access_token'];
        $itemId = $exchangeResponse['item_id'];

        // Get accounts details
        $accountsResponse = $this->plaidService->getAccounts($accessToken);

        if (!$accountsResponse) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve account details from Plaid',
            ], 500);
        }

        $accountsData = $accountsResponse['accounts'];
        $createdAccounts = [];

        foreach ($accountsData as $accountData) {
            $account = PlaidAccount::create([
                'user_id' => Auth::id(),
                'institution_id' => $institution['institution_id'] ?? '',
                'institution_name' => $institution['name'] ?? '',
                'account_id' => $accountData['account_id'],
                'account_name' => $accountData['name'],
                'account_type' => $accountData['type'],
                'account_subtype' => $accountData['subtype'] ?? null,
                'account_mask' => $accountData['mask'] ?? '',
                'access_token' => $accessToken,
                'item_id' => $itemId,
                'is_default' => Auth::user()->plaidAccounts()->count() === 0,
                'metadata' => [
                    'balances' => $accountData['balances'] ?? null,
                ],
            ]);

            // Sync transactions for the account
            $this->plaidService->syncTransactions($account);

            $createdAccounts[] = [
                'id' => $account->id,
                'uuid' => $account->uuid,
                'institution_name' => $account->institution_name,
                'account_name' => $account->account_name,
                'account_type' => $account->account_type,
                'account_mask' => $account->account_mask,
                'is_default' => $account->is_default,
                'is_payment_enabled' => $account->is_payment_enabled,
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Successfully linked accounts',
            'accounts' => $createdAccounts,
        ]);
    }

    /**
     * Get the authenticated user's Plaid accounts.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAccounts()
    {
        $accounts = Auth::user()->plaidAccounts()->latest()->get()->map(function ($account) {
            return [
                'id' => $account->id,
                'uuid' => $account->uuid,
                'institution_name' => $account->institution_name,
                'account_name' => $account->account_name,
                'account_type' => $account->account_type,
                'account_mask' => $account->account_mask,
                'is_default' => $account->is_default,
                'is_payment_enabled' => $account->is_payment_enabled,
                'last_synced_at' => $account->last_synced_at ? $account->last_synced_at->diffForHumans() : null,
            ];
        });

        return response()->json([
            'success' => true,
            'accounts' => $accounts,
        ]);
    }

    /**
     * Set the specified Plaid account as default.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefault($id)
    {
        $account = PlaidAccount::where('user_id', Auth::id())->findOrFail($id);

        // Reset all accounts to non-default
        Auth::user()->plaidAccounts()->update(['is_default' => false]);

        // Set the selected account as default
        $account->update(['is_default' => true]);

        return response()->json([
            'success' => true,
            'message' => "Set {$account->account_name} as your default account",
        ]);
    }

    /**
     * Toggle payment capability for the specified Plaid account.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function togglePayment($id)
    {
        $account = PlaidAccount::where('user_id', Auth::id())->findOrFail($id);

        $account->update(['is_payment_enabled' => !$account->is_payment_enabled]);

        $status = $account->is_payment_enabled ? 'enabled' : 'disabled';

        return response()->json([
            'success' => true,
            'message' => "Payment {$status} for {$account->account_name}",
            'is_payment_enabled' => $account->is_payment_enabled,
        ]);
    }

    /**
     * Remove the specified Plaid account from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $account = PlaidAccount::where('user_id', Auth::id())->findOrFail($id);

        // Delete the account
        $account->delete();

        // If this was the default account, set another one as default
        if ($account->is_default) {
            $newDefault = Auth::user()->plaidAccounts()->first();

            if ($newDefault) {
                $newDefault->update(['is_default' => true]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully removed {$account->account_name}",
        ]);
    }

    /**
     * Handle Plaid webhook.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleWebhook(Request $request)
    {
        Log::info('Plaid webhook received', $request->all());

        $webhookType = $request->input('webhook_type');
        $webhookCode = $request->input('webhook_code');
        $itemId = $request->input('item_id');

        // Process webhook based on type and code

        return response()->json(['status' => 'success']);
    }

    /**
     * Add dummy Plaid accounts for testing.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function addDummyAccounts()
    {
        // Delete existing Plaid accounts for this user
        Auth::user()->plaidAccounts()->delete();

        // Create dummy checking account
        $checking = PlaidAccount::create([
            'uuid' => (string) Str::uuid(),
            'user_id' => Auth::id(),
            'institution_id' => 'ins_1',
            'institution_name' => 'Chase Bank',
            'account_id' => 'acc_' . Str::random(10),
            'account_name' => 'Checking Account',
            'account_type' => 'depository',
            'account_subtype' => 'checking',
            'account_mask' => '1234',
            'access_token' => 'access-sandbox-' . Str::random(10),
            'item_id' => 'item-sandbox-' . Str::random(10),
            'is_default' => true,
            'is_payment_enabled' => true,
            'metadata' => [
                'balances' => [
                    'available' => 1000.00,
                    'current' => 1050.00,
                    'iso_currency_code' => 'USD',
                ],
            ],
            'last_synced_at' => now(),
        ]);

        // Create dummy savings account
        $savings = PlaidAccount::create([
            'uuid' => (string) Str::uuid(),
            'user_id' => Auth::id(),
            'institution_id' => 'ins_2',
            'institution_name' => 'Bank of America',
            'account_id' => 'acc_' . Str::random(10),
            'account_name' => 'Savings Account',
            'account_type' => 'depository',
            'account_subtype' => 'savings',
            'account_mask' => '5678',
            'access_token' => 'access-sandbox-' . Str::random(10),
            'item_id' => 'item-sandbox-' . Str::random(10),
            'is_default' => false,
            'is_payment_enabled' => true,
            'metadata' => [
                'balances' => [
                    'available' => 5000.00,
                    'current' => 5000.00,
                    'iso_currency_code' => 'USD',
                ],
            ],
            'last_synced_at' => now(),
        ]);

        // Create dummy credit card account
        $creditCard = PlaidAccount::create([
            'uuid' => (string) Str::uuid(),
            'user_id' => Auth::id(),
            'institution_id' => 'ins_3',
            'institution_name' => 'American Express',
            'account_id' => 'acc_' . Str::random(10),
            'account_name' => 'Credit Card',
            'account_type' => 'credit',
            'account_subtype' => 'credit card',
            'account_mask' => '9012',
            'access_token' => 'access-sandbox-' . Str::random(10),
            'item_id' => 'item-sandbox-' . Str::random(10),
            'is_default' => false,
            'is_payment_enabled' => false,
            'metadata' => [
                'balances' => [
                    'available' => 5000.00,
                    'current' => -1500.00,
                    'iso_currency_code' => 'USD',
                ],
            ],
            'last_synced_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Dummy Plaid accounts added successfully',
            'accounts' => [
                [
                    'id' => $checking->id,
                    'uuid' => $checking->uuid,
                    'institution_name' => $checking->institution_name,
                    'account_name' => $checking->account_name,
                    'account_type' => $checking->account_type,
                    'account_mask' => $checking->account_mask,
                    'is_default' => $checking->is_default,
                    'is_payment_enabled' => $checking->is_payment_enabled,
                ],
                [
                    'id' => $savings->id,
                    'uuid' => $savings->uuid,
                    'institution_name' => $savings->institution_name,
                    'account_name' => $savings->account_name,
                    'account_type' => $savings->account_type,
                    'account_mask' => $savings->account_mask,
                    'is_default' => $savings->is_default,
                    'is_payment_enabled' => $savings->is_payment_enabled,
                ],
                [
                    'id' => $creditCard->id,
                    'uuid' => $creditCard->uuid,
                    'institution_name' => $creditCard->institution_name,
                    'account_name' => $creditCard->account_name,
                    'account_type' => $creditCard->account_type,
                    'account_mask' => $creditCard->account_mask,
                    'is_default' => $creditCard->is_default,
                    'is_payment_enabled' => $creditCard->is_payment_enabled,
                ],
            ],
        ]);
    }
}
