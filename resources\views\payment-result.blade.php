<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch - Payment {{ $success ? 'Successful' : 'Failed' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .result-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin: 20px 0;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin: 20px 0;
        }
        
        .cancel-icon {
            font-size: 4rem;
            color: #ffc107;
            margin: 20px 0;
        }
        
        .result-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .success-title {
            color: #28a745;
        }
        
        .error-title {
            color: #dc3545;
        }
        
        .cancel-title {
            color: #856404;
        }
        
        .result-message {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .details-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            text-align: left;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 12px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            color: #666;
            font-weight: 500;
        }
        
        .detail-value {
            font-weight: 600;
            color: #333;
        }
        
        .trial-info {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            color: #155724;
        }
        
        .trial-info h4 {
            margin-bottom: 10px;
            color: #28a745;
        }
        
        .error-info {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            color: #721c24;
        }
        
        .action-buttons {
            margin-top: 30px;
        }
        
        .primary-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: background 0.3s ease;
        }
        
        .primary-button:hover {
            background: #5a6fd8;
        }
        
        .secondary-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: background 0.3s ease;
        }
        
        .secondary-button:hover {
            background: #5a6268;
        }
        
        .retry-button {
            background: #ffc107;
            color: #212529;
        }
        
        .retry-button:hover {
            background: #e0a800;
        }
        
        .features-list {
            text-align: left;
            margin: 15px 0;
        }
        
        .features-list li {
            margin: 8px 0;
            color: #155724;
        }
        
        .features-list li::before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="result-container">
        <div class="logo">🎯 PocketWatch</div>
        
        @if($success)
            <!-- Success State -->
            <div class="success-icon">🎉</div>
            <h1 class="result-title success-title">Payment Successful!</h1>
            <p class="result-message">
                Congratulations! Your payment has been processed successfully and your 7-day free trial has started.
            </p>
            
            @if(isset($subscription))
            <div class="details-card">
                <div class="detail-row">
                    <span class="detail-label">Subscription Plan:</span>
                    <span class="detail-value">{{ $subscription->name }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Monthly Price:</span>
                    <span class="detail-value">${{ number_format($subscription->price, 2) }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Billing Cycle:</span>
                    <span class="detail-value">{{ ucfirst($subscription->billing_cycle) }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Trial Period:</span>
                    <span class="detail-value">7 Days</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Trial Ends:</span>
                    <span class="detail-value">{{ $trialEndsAt ?? 'N/A' }}</span>
                </div>
            </div>
            @endif
            
            <div class="trial-info">
                <h4>🚀 Your Trial Has Started!</h4>
                <p>You now have full access to all {{ $subscription->name ?? 'subscription' }} features for 7 days.</p>
                @if(isset($subscription) && $subscription->features)
                <ul class="features-list">
                    @foreach($subscription->features as $feature)
                    <li>{{ $feature }}</li>
                    @endforeach
                </ul>
                @endif
                <p><strong>Important:</strong> You won't be charged until {{ $trialEndsAt ?? 'your trial ends' }}. Cancel anytime during the trial period.</p>
            </div>
            
            <div class="action-buttons">
                <a href="/dashboard" class="primary-button">Go to Dashboard</a>
                <a href="/api/me" class="secondary-button">View Profile</a>
            </div>
            
        @elseif(isset($cancelled) && $cancelled)
            <!-- Cancelled State -->
            <div class="cancel-icon">⚠️</div>
            <h1 class="result-title cancel-title">Payment Cancelled</h1>
            <p class="result-message">
                You cancelled the payment process. No charges have been made to your account.
            </p>
            
            <div class="action-buttons">
                <a href="/api/packages-simple" class="retry-button">Try Again</a>
                <a href="/dashboard" class="secondary-button">Back to Dashboard</a>
            </div>
            
        @else
            <!-- Error State -->
            <div class="error-icon">❌</div>
            <h1 class="result-title error-title">Payment Failed</h1>
            <p class="result-message">
                {{ $message ?? 'There was an issue processing your payment. Please try again.' }}
            </p>
            
            @if(isset($error))
            <div class="error-info">
                <strong>Error Details:</strong><br>
                {{ $error }}
            </div>
            @endif
            
            <div class="action-buttons">
                <a href="/api/packages-simple" class="retry-button">Try Again</a>
                <a href="/support" class="secondary-button">Contact Support</a>
            </div>
        @endif
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef; color: #666; font-size: 0.9rem;">
            <p>Need help? Contact our support team at <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a></p>
        </div>
    </div>

    <script>
        // Auto-redirect to dashboard after successful payment (optional)
        @if($success)
        setTimeout(function() {
            console.log('Payment successful - ready to redirect to dashboard');
            // Uncomment the line below to auto-redirect after 10 seconds
            // window.location.href = '/dashboard';
        }, 10000);
        @endif
        
        // Log payment result for debugging
        console.log('Payment Result:', {
            success: {{ $success ? 'true' : 'false' }},
            message: '{{ $message ?? '' }}',
            @if(isset($subscription))
            subscription: {
                id: {{ $subscription->id ?? 'null' }},
                name: '{{ $subscription->name ?? '' }}',
                price: {{ $subscription->price ?? 0 }}
            }
            @endif
        });
    </script>
</body>
</html>
