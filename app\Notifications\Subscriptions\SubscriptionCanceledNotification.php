<?php

namespace App\Notifications\Subscriptions;

use App\Models\Subscription;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class SubscriptionCanceledNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The subscription instance.
     *
     * @var \App\Models\Subscription
     */
    protected $subscription;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Subscription  $subscription
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_subscription_updates';

    public function __construct(Subscription $subscription)
    {
        $this->subscription = $subscription;

        $this->subject = 'Your PocketWatch Subscription Has Been Canceled';
        $this->title = 'Subscription Canceled';
        
        if ($this->subscription->ends_at && $this->subscription->ends_at->isFuture()) {
            $this->content = 'Your PocketWatch subscription has been canceled. You will still have access to ' . 
                            ucfirst($this->subscription->subscription_tier) . ' features until the end of your current billing period.';
        } else {
            $this->content = 'Your PocketWatch subscription has been canceled and is no longer active. You have been downgraded to the Base tier.';
        }
        
        $this->detailsTitle = 'Subscription Details';
        $this->details = [
            'Plan' => ucfirst($this->subscription->subscription_tier) . ' Tier',
            'Status' => 'Canceled',
        ];
        
        if ($this->subscription->ends_at && $this->subscription->ends_at->isFuture()) {
            $this->details['Access Until'] = $this->subscription->ends_at->format('F j, Y');
        }
        
        $this->actionText = 'Resubscribe';
        $this->actionUrl = url('/subscriptions/new');
        
        $this->closing = 'We\'re sorry to see you go. If you change your mind, you can resubscribe at any time.';
        $this->signature = 'The PocketWatch Team';
    }
}
