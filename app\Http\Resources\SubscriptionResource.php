<?php

namespace App\Http\Resources;

use App\Helpers\IdObfuscator;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->uuid,
            'user_id' => $this->whenLoaded('user', function () {
                return $this->user->uuid;
            }),
            'name' => $this->name,
            'stripe_id' => $this->stripe_id,
            'stripe_status' => $this->stripe_status,
            'stripe_price' => $this->stripe_price,
            'subscription_tier' => $this->subscription_tier,
            'trial_ends_at' => $this->trial_ends_at,
            'ends_at' => $this->ends_at,
            'price' => $this->price,
            'currency' => $this->currency,
            'billing_cycle' => $this->billing_cycle,
            'features' => $this->features,
            'is_active' => $this->isActive(),
            'on_trial' => $this->onTrial(),
            'has_ended' => $this->hasEnded(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
