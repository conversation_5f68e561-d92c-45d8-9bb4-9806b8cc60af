<?php

namespace App\Notifications\Bins;

use App\Models\SubBin;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class SubBinUpdatedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The sub-bin instance.
     *
     * @var \App\Models\SubBin
     */
    protected $subBin;

    /**
     * The fields that were updated.
     *
     * @var array
     */
    protected $updatedFields;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\SubBin  $subBin
     * @param  array  $updatedFields
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_bin_operations';

    public function __construct(SubBin $subBin, array $updatedFields = [])
    {
        $this->subBin = $subBin;
        $this->updatedFields = $updatedFields;

        $this->subject = 'Sub-Bin Updated';
        $this->title = 'Sub-Bin Updated';
        $this->content = 'Your sub-bin has been successfully updated in your PocketWatch account.';
        
        $this->detailsTitle = 'Updated Sub-Bin Details';
        $this->details = [
            'Name' => $this->subBin->name,
            'Description' => $this->subBin->description ?? 'No description',
            'Parent Bin' => $this->subBin->bin->name,
            'Minimum Threshold' => $this->formatCurrency($this->subBin->threshold_min, $this->subBin->currency),
        ];
        
        if ($this->subBin->threshold_max) {
            $this->details['Maximum Threshold'] = $this->formatCurrency($this->subBin->threshold_max, $this->subBin->currency);
        }
        
        if (!empty($this->updatedFields)) {
            $this->details['Updated Fields'] = implode(', ', $this->updatedFields);
        }
        
        $this->actionText = 'View Sub-Bin';
        $this->actionUrl = url('/bins/' . $this->subBin->bin_id . '/sub-bins/' . $this->subBin->id);
        
        $this->closing = 'Keep your sub-bins up to date for better financial management.';
        $this->signature = 'The PocketWatch Team';
    }
    
    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);
        
        return $symbol . number_format($value, 2);
    }
    
    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];
        
        return $symbols[$currency] ?? $currency;
    }
}
