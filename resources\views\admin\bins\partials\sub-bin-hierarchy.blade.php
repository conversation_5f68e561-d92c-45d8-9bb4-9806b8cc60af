@foreach($subBins as $subBin)
    <div class="sub-bin-item depth-{{ $subBin->depth_level }}">
        <div class="d-flex justify-content-between align-items-start">
            <div class="sub-bin-info flex-grow-1">
                <div class="d-flex align-items-center mb-2">
                    <div class="sub-bin-icon me-2">
                        @if($subBin->depth_level == 1)
                            <i class="fas fa-folder text-primary"></i>
                        @elseif($subBin->depth_level == 2)
                            <i class="fas fa-folder-open text-success"></i>
                        @elseif($subBin->depth_level == 3)
                            <i class="fas fa-file-alt text-warning"></i>
                        @else
                            <i class="fas fa-file text-danger"></i>
                        @endif
                    </div>
                    <div class="sub-bin-name">
                        <strong>{{ $subBin->name }}</strong>
                        @if($subBin->parentSubBin)
                            <small class="text-muted">(under {{ $subBin->parentSubBin->name }})</small>
                        @endif
                    </div>
                    <div class="ms-auto">
                        @if($subBin->is_active)
                            <span class="badge bg-success badge-sm">Active</span>
                        @else
                            <span class="badge bg-danger badge-sm">Inactive</span>
                        @endif
                    </div>
                </div>
                
                <div class="row g-2 small">
                    <div class="col-md-3">
                        <div class="info-item">
                            <i class="fas fa-tag text-muted me-1"></i>
                            <strong>Type:</strong> {{ ucfirst($subBin->type) }}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <i class="fas fa-coins text-muted me-1"></i>
                            <strong>Amount:</strong> 
                            <span class="{{ $subBin->current_amount >= 0 ? 'text-success' : 'text-danger' }}">
                                {{ $subBin->currency }} {{ number_format($subBin->current_amount, 2) }}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <i class="fas fa-chart-line text-muted me-1"></i>
                            <strong>Threshold:</strong> 
                            {{ $subBin->currency }} {{ number_format($subBin->threshold_min, 2) }}
                            @if($subBin->threshold_max)
                                - {{ $subBin->currency }} {{ number_format($subBin->threshold_max, 2) }}
                            @else
                                - ∞
                            @endif
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <i class="fas fa-exchange-alt text-muted me-1"></i>
                            <strong>Transactions:</strong> {{ $subBin->transactions->count() }}
                        </div>
                    </div>
                </div>
                
                @if($subBin->description)
                    <div class="mt-2 small text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        {{ $subBin->description }}
                    </div>
                @endif
                
                @if($subBin->path)
                    <div class="mt-2 small">
                        <i class="fas fa-route text-muted me-1"></i>
                        <strong>Path:</strong> <code class="small">{{ $subBin->path }}</code>
                    </div>
                @endif
                
                <div class="mt-2 small text-muted">
                    <i class="far fa-calendar-alt me-1"></i>
                    Created: {{ $subBin->created_at->format('M d, Y \a\t g:i A') }}
                    @if($subBin->updated_at != $subBin->created_at)
                        | Updated: {{ $subBin->updated_at->format('M d, Y \a\t g:i A') }}
                    @endif
                </div>
            </div>
            
            <div class="sub-bin-actions ms-3">
                <div class="btn-group-vertical btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewSubBinDetails({{ $subBin->id }})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    @if($subBin->transactions->count() > 0)
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="exportSubBinTransactions({{ $subBin->id }})" title="Export Transactions">
                            <i class="fas fa-download"></i>
                        </button>
                    @endif
                    @if($subBin->childSubBins && $subBin->childSubBins->count() > 0)
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleSubBinChildren({{ $subBin->id }})" title="Toggle Children">
                            <i class="fas fa-sitemap"></i>
                        </button>
                    @endif
                </div>
            </div>
        </div>
        
        @if($subBin->childSubBins && $subBin->childSubBins->count() > 0)
            <div class="mt-3">
                <div class="d-flex align-items-center mb-2">
                    <small class="text-muted">
                        <i class="fas fa-level-down-alt me-1"></i>
                        {{ $subBin->childSubBins->count() }} child sub-bin(s)
                    </small>
                </div>
                <div id="children-{{ $subBin->id }}" class="children-container">
                    @include('admin.bins.partials.sub-bin-hierarchy', ['subBins' => $subBin->childSubBins])
                </div>
            </div>
        @endif
    </div>
@endforeach

@if($subBins->isEmpty())
    <div class="text-center py-3">
        <i class="fas fa-folder-open fa-2x text-muted mb-2"></i>
        <p class="text-muted mb-0">No sub-bins at this level</p>
    </div>
@endif
