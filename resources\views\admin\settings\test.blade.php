@extends('admin.layouts.app')

@section('content')
{{-- <div class="admin-content"> --}}
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Test Settings</h1>
            <div class="breadcrumb">
                <span class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></span>
                <span class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Settings</a></span>
                <span class="breadcrumb-item active">Test</span>
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="row">
            <!-- Test Email Settings -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-envelope me-2"></i>Test Email Settings</h5>
                    </div>
                    <div class="card-body">
                        <p>Send a test email to verify your email configuration is working correctly.</p>

                        <form action="{{ route('admin.settings.test.email') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="test_email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="test_email" name="test_email" value="{{ old('test_email') }}" required>
                                <div class="form-text">Enter an email address to receive the test email.</div>
                            </div>

                            <div class="mb-3">
                                <h6>Current Email Configuration:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <th>Mail Driver:</th>
                                        <td>{{ setting('mail_mailer') ?? config('mail.default') }}</td>
                                    </tr>
                                    <tr>
                                        <th>Mail Host:</th>
                                        <td>{{ setting('mail_host') ?? config('mail.mailers.smtp.host') }}</td>
                                    </tr>
                                    <tr>
                                        <th>From Address:</th>
                                        <td>{{ setting('mail_from_address') ?? config('mail.from.address') }}</td>
                                    </tr>
                                </table>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Send Test Email
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Test Stripe Settings -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Test Stripe Settings</h5>
                    </div>
                    <div class="card-body">
                        <p>Test your Stripe API connection to verify your payment configuration is working correctly.</p>

                        <div class="mb-3">
                            <h6>Current Stripe Configuration:</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th>API Key Status:</th>
                                    <td>
                                        @if(setting('stripe_key') && setting('stripe_secret'))
                                            <span class="badge bg-success">Configured</span>
                                        @else
                                            <span class="badge bg-danger">Not Configured</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Webhook Secret:</th>
                                    <td>
                                        @if(setting('stripe_webhook_secret'))
                                            <span class="badge bg-success">Configured</span>
                                        @else
                                            <span class="badge bg-warning text-dark">Not Configured</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Price IDs:</th>
                                    <td>
                                        @if(setting('stripe_price_base_monthly') && setting('stripe_price_premium_monthly'))
                                            <span class="badge bg-success">Configured</span>
                                        @else
                                            <span class="badge bg-danger">Not Configured</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <form action="{{ route('admin.settings.test.stripe') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-primary" {{ (!setting('stripe_key') || !setting('stripe_secret')) ? 'disabled' : '' }}>
                                <i class="fas fa-check-circle me-2"></i>Test Stripe Connection
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Test Plaid Settings -->
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-university me-2"></i>Test Plaid Settings</h5>
                    </div>
                    <div class="card-body">
                        <p>Test your Plaid API connection to verify your bank account linking configuration is working correctly.</p>

                        <div class="mb-3">
                            <h6>Current Plaid Configuration:</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th>API Key Status:</th>
                                    <td>
                                        @if(setting('plaid_client_id') && setting('plaid_secret'))
                                            <span class="badge bg-success">Configured</span>
                                        @else
                                            <span class="badge bg-danger">Not Configured</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Environment:</th>
                                    <td>
                                        @if(setting('plaid_environment'))
                                            <span class="badge bg-info">{{ ucfirst(setting('plaid_environment')) }}</span>
                                        @else
                                            <span class="badge bg-secondary">Not Set</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Webhook URL:</th>
                                    <td>
                                        @if(setting('plaid_webhook'))
                                            <span class="badge bg-success">Configured</span>
                                        @else
                                            <span class="badge bg-warning text-dark">Not Configured</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <form action="{{ route('admin.settings.test.plaid') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-primary" {{ (!setting('plaid_client_id') || !setting('plaid_secret')) ? 'disabled' : '' }}>
                                <i class="fas fa-check-circle me-2"></i>Test Plaid Connection
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-3">
            <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Settings
            </a>
        </div>
    </div>
</div>
@endsection
