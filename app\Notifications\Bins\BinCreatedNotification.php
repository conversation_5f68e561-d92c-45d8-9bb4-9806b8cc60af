<?php

namespace App\Notifications\Bins;

use App\Models\Bin;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class BinCreatedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The bin instance.
     *
     * @var \App\Models\Bin
     */
    protected $bin;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Bin  $bin
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_bin_operations';

    public function __construct(Bin $bin)
    {
        $this->bin = $bin;

        $this->subject = 'New Financial Bin Created';
        $this->title = 'New Bin Created';
        $this->content = 'You have successfully created a new financial bin in your PocketWatch account. 
                         Financial bins help you organize your money and track your expenses more effectively.';
        
        $this->detailsTitle = 'Bin Details';
        $this->details = [
            'Name' => $this->bin->name,
            'Description' => $this->bin->description ?? 'No description',
            'Minimum Threshold' => $this->formatCurrency($this->bin->threshold_min, $this->bin->currency),
        ];
        
        if ($this->bin->threshold_max) {
            $this->details['Maximum Threshold'] = $this->formatCurrency($this->bin->threshold_max, $this->bin->currency);
        }
        
        $this->actionText = 'View Bin';
        $this->actionUrl = url('/bins/' . $this->bin->id);
        
        $this->closing = 'Start adding sub-bins or transactions to make the most of your financial organization.';
        $this->signature = 'The PocketWatch Team';
    }
    
    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);
        
        return $symbol . number_format($value, 2);
    }
    
    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];
        
        return $symbols[$currency] ?? $currency;
    }
}
