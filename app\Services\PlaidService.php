<?php

namespace App\Services;

use App\Models\PlaidAccount;
use App\Models\PlaidTransaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PlaidService
{
    /**
     * The base URL for Plaid API requests.
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * The Plaid client ID.
     *
     * @var string
     */
    protected $clientId;

    /**
     * The Plaid secret key.
     *
     * @var string
     */
    protected $secret;

    /**
     * Create a new Plaid service instance.
     *
     * @return void
     */
    public function __construct()
    {
        $environment = config('plaid.environment', 'sandbox');
        $this->baseUrl = config("plaid.api_host.$environment");
        $this->clientId = config('plaid.client_id');
        $this->secret = config('plaid.secret');
    }

    /**
     * Create a link token for the given user.
     *
     * @param \App\Models\User $user
     * @param array $options
     * @return array|null
     */
    public function createLinkToken(User $user, array $options = []): ?array
    {
        try {
            $response = Http::withHeaders([
                'Plaid-Version' => '2020-09-14',
            ])->post($this->baseUrl . '/link/token/create', array_merge([
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'client_name' => config('plaid.client_name'),
                'user' => [
                    'client_user_id' => (string) $user->id,
                ],
                'products' => config('plaid.products'),
                'country_codes' => config('plaid.country_codes'),
                'language' => config('plaid.language'),
                'webhook' => config('plaid.webhook'),
            ], $options));

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Plaid link token creation failed', [
                'error' => $response->json(),
                'status' => $response->status(),
                'request_data' => [
                    'client_id' => $this->clientId,
                    'client_name' => config('plaid.client_name'),
                    'products' => config('plaid.products'),
                    'country_codes' => config('plaid.country_codes'),
                    'language' => config('plaid.language'),
                    'webhook' => config('plaid.webhook'),
                    'base_url' => $this->baseUrl,
                ],
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Plaid link token creation exception', [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Exchange a public token for an access token.
     *
     * @param string $publicToken
     * @return array|null
     */
    public function exchangePublicToken(string $publicToken): ?array
    {
        try {
            $response = Http::withHeaders([
                'Plaid-Version' => '2020-09-14',
            ])->post($this->baseUrl . '/item/public_token/exchange', [
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'public_token' => $publicToken,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Plaid public token exchange failed', [
                'error' => $response->json(),
                'status' => $response->status(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Plaid public token exchange exception', [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get accounts for the given access token.
     *
     * @param string $accessToken
     * @return array|null
     */
    public function getAccounts(string $accessToken): ?array
    {
        try {
            $response = Http::withHeaders([
                'Plaid-Version' => '2020-09-14',
            ])->post($this->baseUrl . '/accounts/get', [
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'access_token' => $accessToken,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Plaid get accounts failed', [
                'error' => $response->json(),
                'status' => $response->status(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Plaid get accounts exception', [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get transactions for the given access token.
     *
     * @param string $accessToken
     * @param string $startDate
     * @param string $endDate
     * @return array|null
     */
    public function getTransactions(string $accessToken, string $startDate, string $endDate): ?array
    {
        try {
            $response = Http::withHeaders([
                'Plaid-Version' => '2020-09-14',
            ])->post($this->baseUrl . '/transactions/get', [
                'client_id' => $this->clientId,
                'secret' => $this->secret,
                'access_token' => $accessToken,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Plaid get transactions failed', [
                'error' => $response->json(),
                'status' => $response->status(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Plaid get transactions exception', [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Sync transactions for the given account.
     *
     * @param \App\Models\PlaidAccount $account
     * @param int $days
     * @return bool
     */
    public function syncTransactions(PlaidAccount $account, int $days = 30): bool
    {
        try {
            $endDate = Carbon::now()->format('Y-m-d');
            $startDate = Carbon::now()->subDays($days)->format('Y-m-d');

            $transactions = $this->getTransactions($account->access_token, $startDate, $endDate);

            if (!$transactions) {
                return false;
            }

            foreach ($transactions['transactions'] as $transaction) {
                PlaidTransaction::updateOrCreate(
                    [
                        'transaction_id' => $transaction['transaction_id'],
                    ],
                    [
                        'user_id' => $account->user_id,
                        'plaid_account_id' => $account->id,
                        'category_id' => $transaction['category_id'] ?? null,
                        'category' => $transaction['category'] ?? null,
                        'transaction_type' => $transaction['transaction_type'] ?? null,
                        'name' => $transaction['name'],
                        'amount' => $transaction['amount'],
                        'iso_currency_code' => $transaction['iso_currency_code'] ?? null,
                        'unofficial_currency_code' => $transaction['unofficial_currency_code'] ?? null,
                        'date' => $transaction['date'],
                        'pending' => $transaction['pending'] ?? false,
                        'account_owner' => $transaction['account_owner'] ?? null,
                        'location' => $transaction['location'] ?? null,
                        'payment_meta' => $transaction['payment_meta'] ?? null,
                    ]
                );
            }

            $account->update([
                'last_synced_at' => now(),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Plaid sync transactions exception', [
                'error' => $e->getMessage(),
                'account_id' => $account->id,
            ]);

            return false;
        }
    }
}
