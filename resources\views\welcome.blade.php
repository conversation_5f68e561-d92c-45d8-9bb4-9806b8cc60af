<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ $settings['site_name'] }}</title>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  @if($settings['favicon'])
  <link rel="icon" href="{{ asset('settings/fav.png') }}" type="image/x-icon">
  @endif

  <style>
    :root {
      --primary-color: #32704e; /* Sea Green */
      --secondary-color: #32704e; /* Medium Sea Green */
      --accent-color: #66CDAA; /* Medium Aquamarine */
      --light-color: #E0FFF0; /* Light Mint */
      --dark-color: #1D5E40; /* Dark Green */
      --text-color: #333333;
      --card-bg: #ffffff;
      --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      --section-bg: #f8f9fa;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: #ffffff;
      color: var(--text-color);
      min-height: 100vh;
      overflow-x: hidden;
    }

    .navbar {
      background-color: #ffffff;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      padding: 0.75rem 0;
    }

    .navbar-brand {
      font-weight: 600;
      color: var(--primary-color);
      font-size: 1.5rem;
    }

    .navbar-brand i {
      color: var(--primary-color);
    }

    .nav-link {
      color: #555 !important;
      font-weight: 500;
      padding: 0.5rem 1rem !important;
      transition: all 0.3s ease;
    }

    .nav-link:hover {
      color: var(--primary-color) !important;
      transform: translateY(-2px);
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      padding: 0.5rem 1.5rem;
      border-radius: 5px;
      font-weight: 500;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .btn-primary:hover {
      background-color: var(--dark-color);
      border-color: var(--dark-color);
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-outline-primary {
      color: var(--primary-color);
      border-color: var(--primary-color);
      padding: 0.5rem 1.5rem;
      border-radius: 5px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-outline-primary:hover {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      transform: translateY(-2px);
    }

    /* Hero Section */
    .hero-section {
      position: relative;
      overflow: hidden;
    }

    .carousel-item {
      height: 600px;
    }

    .carousel-item img {
      object-fit: cover;
      height: 100%;
      width: 100%;
      filter: brightness(0.85);
    }

    .carousel-caption {
      background-color: rgba(255, 255, 255, 0.9);
      border-left: 4px solid var(--primary-color);
      padding: 2rem;
      border-radius: 5px;
      color: #333;
      text-align: left;
      max-width: 600px;
      margin: 0 auto;
      bottom: 50%;
      transform: translateY(50%);
      box-shadow: var(--card-shadow);
    }

    .carousel-caption h2 {
      color: var(--primary-color);
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .carousel-caption p {
      color: #555;
      font-size: 1.1rem;
    }

    /* Section Styles */
    section {
      padding: 5rem 0;
    }

    section:nth-child(even) {
      background-color: var(--section-bg);
    }

    .section-title {
      font-size: 2.2rem;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 3rem;
      position: relative;
      display: inline-block;
      padding-bottom: 0.5rem;
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background-color: var(--primary-color);
    }

    .text-center .section-title::after {
      left: 50%;
      transform: translateX(-50%);
    }

    /* Feature Cards */
    .feature-card {
      background-color: var(--card-bg);
      border-radius: 10px;
      padding: 2rem;
      height: 100%;
      transition: all 0.3s ease;
      box-shadow: var(--card-shadow);
      border-top: 3px solid transparent;
    }

    .feature-card:hover {
      transform: translateY(-10px);
      border-top: 3px solid var(--primary-color);
    }

    .feature-icon {
      font-size: 2.5rem;
      color: var(--primary-color);
      margin-bottom: 1.5rem;
      display: inline-block;
      background-color: var(--light-color);
      width: 80px;
      height: 80px;
      line-height: 80px;
      border-radius: 50%;
      text-align: center;
    }

    .feature-card h4 {
      font-weight: 600;
      margin-bottom: 1rem;
      color: #333;
    }

    .feature-card p {
      color: #666;
      line-height: 1.6;
    }

    /* Testimonial Cards */
    .testimonial-card {
      background-color: var(--card-bg);
      border-radius: 10px;
      padding: 2rem;
      height: 100%;
      transition: all 0.3s ease;
      box-shadow: var(--card-shadow);
      border-left: 3px solid var(--primary-color);
    }

    .testimonial-card:hover {
      transform: translateY(-5px);
    }

    .testimonial-card p {
      font-style: italic;
      color: #555;
      line-height: 1.6;
      font-size: 1.05rem;
    }

    .testimonial-card .rating {
      color: #FFD700;
      margin-bottom: 1rem;
    }

    .testimonial-card .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid var(--light-color);
    }

    /* Pricing Cards */
    .pricing-card {
      background-color: var(--card-bg);
      border-radius: 10px;
      padding: 2.5rem 2rem;
      height: 100%;
      transition: all 0.3s ease;
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
      border: 1px solid #eee;
    }

    .pricing-card.popular {
      border-color: var(--primary-color);
      transform: scale(1.05);
      z-index: 1;
    }

    .pricing-card:hover {
      transform: translateY(-10px);
    }

    .pricing-card.popular:hover {
      transform: translateY(-10px) scale(1.05);
    }

    .pricing-card .card-title {
      font-weight: 700;
      color: var(--primary-color);
    }

    .pricing-card .price {
      font-size: 2.5rem;
      font-weight: 700;
      color: #333;
    }

    .pricing-card ul li {
      padding: 0.5rem 0;
      color: #555;
    }

    .pricing-card ul li i {
      color: var(--primary-color);
    }

    .badge-popular {
      position: absolute;
      top: 0;
      right: 0;
      background-color: var(--primary-color);
      color: white;
      padding: 0.5rem 1rem;
      font-size: 0.8rem;
      font-weight: 600;
      border-radius: 0 10px 0 10px;
    }

    /* Footer */
    .footer {
      background-color: #fff;
      padding: 3rem 0;
      border-top: 1px solid #eee;
    }

    .footer-text {
      color: #666;
    }

    .social-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: var(--light-color);
      color: var(--primary-color);
      margin: 0 0.3rem;
      transition: all 0.3s ease;
    }

    .social-icon:hover {
      background-color: var(--primary-color);
      color: white;
      transform: translateY(-3px);
    }

    /* Animations */
    .fade-up {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.6s ease, transform 0.6s ease;
    }

    .fade-up.active {
      opacity: 1;
      transform: translateY(0);
    }

    /* Responsive Adjustments */
    @media (max-width: 991px) {
      .carousel-item {
        height: 500px;
      }

      .carousel-caption {
        max-width: 90%;
      }
    }

    @media (max-width: 768px) {
      .carousel-item {
        height: 400px;
      }

      .carousel-caption {
        bottom: 20px;
        transform: none;
        padding: 1.5rem;
      }

      .pricing-card.popular {
        transform: scale(1);
      }

      .pricing-card.popular:hover {
        transform: translateY(-10px);
      }

      section {
        padding: 3rem 0;
      }
    }
  </style>
</head>
<body>

  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-light sticky-top">
    <div class="container">
      <a class="navbar-brand d-flex align-items-center" href="#">
        @if($settings['logo'])
          <img src="{{ asset('settings/logo.png') }}" alt="{{ $settings['site_name'] }}" height="40">
        @else
          <i class="fas fa-wallet me-2"></i>{{ $settings['navbar_brand'] }}
        @endif
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMenu">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarMenu">
        <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
          @foreach($settings['navbar_links'] as $link)
          <li class="nav-item"><a class="nav-link" href="{{ $link['url'] }}">{{ $link['name'] }}</a></li>
          @endforeach
          <li class="nav-item ms-lg-2 mt-2 mt-lg-0">
            <a class="btn btn-primary" href="{{ route('admin.login') }}">
              <i class="fas fa-sign-in-alt me-1"></i>Login
            </a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Hero Section with Slider -->
  @if(count($settings['sliders']) > 0)
  <section class="hero-section">
    <div id="mainSlider" class="carousel slide" data-bs-ride="carousel">
      <div class="carousel-inner">
        @foreach($settings['sliders'] as $index => $slider)
        <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
          <img src="{{ $slider['image'] }}" class="d-block w-100" alt="Slide {{ $index + 1 }}">
          <div class="carousel-caption">
            <div data-aos="fade-up" data-aos-delay="100">
              <h2>{{ $slider['title'] }}</h2>
              <p>{{ $slider['description'] }}</p>
              <a href="{{ route('admin.login') }}" class="btn btn-primary mt-3">Get Started</a>
            </div>
          </div>
        </div>
        @endforeach
      </div>
      <button class="carousel-control-prev" type="button" data-bs-target="#mainSlider" data-bs-slide="prev">
        <span class="carousel-control-prev-icon"></span>
      </button>
      <button class="carousel-control-next" type="button" data-bs-target="#mainSlider" data-bs-slide="next">
        <span class="carousel-control-next-icon"></span>
      </button>
    </div>
  </section>
  @endif

  <!-- Features Section -->
  @if(count($settings['features']) > 0)
  <section id="features">
    <div class="container">
      <div class="row mb-5">
        <div class="col-lg-6 mx-auto text-center">
          <h2 class="section-title">{{ $settings['features_title'] }}</h2>
          <p class="text-muted">Discover the powerful tools and features that make PocketWatch the perfect solution for your financial management needs.</p>
        </div>
      </div>

      <div class="row g-4">
        @foreach($settings['features'] as $index => $feature)
        <div class="col-md-4" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
          <div class="feature-card text-center">
            <div class="feature-icon">
              <i class="bi {{ $feature['icon'] }}"></i>
            </div>
            <h4>{{ $feature['title'] }}</h4>
            <p>{{ $feature['description'] }}</p>
          </div>
        </div>
        @endforeach
      </div>
    </div>
  </section>
  @endif

  <!-- Testimonials Section -->
  @if(count($settings['testimonials']) > 0)
  <section id="testimonials">
    <div class="container">
      <div class="row mb-5">
        <div class="col-lg-6 mx-auto text-center">
          <h2 class="section-title">{{ $settings['testimonials_title'] }}</h2>
          <p class="text-muted">See what our satisfied customers have to say about their experience with PocketWatch.</p>
        </div>
      </div>

      <div class="row g-4">
        @foreach($settings['testimonials'] as $index => $testimonial)
        <div class="col-md-6" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
          <div class="testimonial-card h-100">
            <div class="rating mb-3">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
            </div>
            <p class="mb-4">"{{ $testimonial['content'] }}"</p>
            <div class="d-flex align-items-center">
              @if(isset($testimonial['avatar']))
              <img src="{{ $testimonial['avatar'] }}" class="avatar me-3" alt="{{ $testimonial['name'] }}">
              @else
              <img src="https://randomuser.me/api/portraits/{{ $index % 2 == 0 ? 'women' : 'men' }}/{{ ($index + 1) * 10 }}.jpg" class="avatar me-3" alt="{{ $testimonial['name'] }}">
              @endif
              <div>
                <h5 class="mb-0">{{ $testimonial['name'] }}</h5>
                <p class="mb-0 text-muted">{{ $testimonial['position'] }}</p>
              </div>
            </div>
          </div>
        </div>
        @endforeach
      </div>
    </div>
  </section>
  @endif

  <!-- Pricing Section -->
  @if(count($settings['packages']) > 0)
  <section id="packages">
    <div class="container">
      <div class="row mb-5">
        <div class="col-lg-6 mx-auto text-center">
          <h2 class="section-title">{{ $settings['packages_title'] }}</h2>
          <p class="text-muted">Choose the perfect plan that fits your needs and budget. All plans include a {{ config('services.subscription.trial_days', 7) }}-day free trial.</p>
        </div>
      </div>

      <div class="row g-4 justify-content-center">
        @foreach($settings['packages'] as $index => $package)
        <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
          <div class="pricing-card h-100 {{ $package['popular'] ? 'popular' : '' }}">
            @if($package['popular'])
            <div class="badge-popular">Most Popular</div>
            @endif
            <h3 class="card-title text-center mb-3">{{ $package['title'] }}</h3>
            <div class="text-center mb-4">
              <span class="price">{{ $package['price'] }}</span>
            </div>
            <ul class="list-unstyled mb-4">
              @foreach($package['features'] as $feature)
              <li class="mb-3 d-flex align-items-center">
                <i class="fas fa-check-circle me-2"></i>
                <span>{{ $feature }}</span>
              </li>
              @endforeach
            </ul>
            <div class="d-grid mt-4">
              <a href="{{ $package['button_url'] }}" class="btn {{ $package['popular'] ? 'btn-primary' : 'btn-outline-primary' }}">
                {{ $package['button_text'] }}
              </a>
            </div>
          </div>
        </div>
        @endforeach
      </div>

      <div class="row mt-5">
        <div class="col-md-8 mx-auto text-center">
          <div class="bg-light p-4 rounded-3" data-aos="fade-up">
            <h4 class="mb-3">Compare Subscription Features</h4>
            <p class="text-muted mb-3">See a detailed comparison of all features available in each subscription tier.</p>

            <div class="table-responsive mt-4">
              <table class="table table-bordered">
                <thead class="table-light">
                  <tr>
                    <th>Feature</th>
                    <th class="text-center">Base</th>
                    <th class="text-center">Premium</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Financial Bins</td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                  </tr>
                  <tr>
                    <td>Transaction Categorization</td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                  </tr>
                  <tr>
                    <td>Graphical Insights</td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                  </tr>
                  <tr>
                    <td>Sub-Bin Levels</td>
                    <td class="text-center">Up to 3</td>
                    <td class="text-center">Unlimited</td>
                  </tr>
                  <tr>
                    <td>Crypto Scanner</td>
                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                  </tr>
                  <tr>
                    <td>Priority Notifications</td>
                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                  </tr>
                  <tr>
                    <td>Advanced AI Suggestions</td>
                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-4">
              <a href="{{ route('admin.login') }}" class="btn btn-primary">
                Start Your Free Trial
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  @endif

  <!-- Call to Action Section -->
  <section class="bg-primary text-white py-5">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-8 mb-4 mb-lg-0">
          <h2 class="mb-3">Ready to take control of your finances?</h2>
          <p class="lead mb-0">Start your 7-day free trial today. No credit card required.</p>
        </div>
        <div class="col-lg-4 text-lg-end">
          <a href="{{ route('admin.login') }}" class="btn btn-light btn-lg">
            <i class="fas fa-rocket me-2"></i>Get Started
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="row">
        <div class="col-lg-4 mb-4 mb-lg-0">
          <div class="mb-4">
            <a class="navbar-brand d-flex align-items-center" href="#">
              @if($settings['logo'])
                <img src="{{ $settings['logo'] }}" alt="{{ $settings['site_name'] }}" height="40">
              @else
                <i class="fas fa-wallet me-2"></i>{{ $settings['navbar_brand'] }}
              @endif
            </a>
          </div>
          <p class="footer-text">PocketWatch helps you track, manage, and optimize your financial life with powerful tools and intuitive interfaces.</p>
        </div>
        <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
          <h5 class="text-dark mb-3">Quick Links</h5>
          <ul class="list-unstyled">
            <li class="mb-2"><a href="#features" class="text-muted text-decoration-none">Features</a></li>
            <li class="mb-2"><a href="#testimonials" class="text-muted text-decoration-none">Testimonials</a></li>
            <li class="mb-2"><a href="#packages" class="text-muted text-decoration-none">Pricing</a></li>
          </ul>
        </div>
        <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
          <h5 class="text-dark mb-3">Resources</h5>
          <ul class="list-unstyled">
            <li class="mb-2"><a href="#" class="text-muted text-decoration-none">Help Center</a></li>
            <li class="mb-2"><a href="#" class="text-muted text-decoration-none">Blog</a></li>
            <li class="mb-2"><a href="#" class="text-muted text-decoration-none">Contact</a></li>
          </ul>
        </div>
        <div class="col-lg-4">
          <h5 class="text-dark mb-3">Connect With Us</h5>
          <div class="mb-3">
            @foreach($settings['social_links'] as $social)
            <a href="{{ $social['url'] }}" class="social-icon" target="_blank"><i class="bi {{ $social['icon'] }}"></i></a>
            @endforeach
          </div>
          <p class="footer-text">{!! $settings['footer_text'] !!}</p>
        </div>
      </div>
      <hr class="my-4">
      <div class="row align-items-center">
        <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
          <p class="small text-muted mb-0">&copy; {{ date('Y') }} {{ $settings['site_name'] }}. All rights reserved.</p>
        </div>
        <div class="col-md-6 text-center text-md-end">
          <a href="#" class="text-muted text-decoration-none small me-3">Privacy Policy</a>
          <a href="#" class="text-muted text-decoration-none small">Terms of Service</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <script>
    // Initialize AOS animation library
    document.addEventListener('DOMContentLoaded', function() {
      AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    });
  </script>
</body>
</html>