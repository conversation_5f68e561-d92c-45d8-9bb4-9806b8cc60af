<?php

use App\Models\Setting;

if (!function_exists('setting')) {
    /**
     * Get / set the specified setting value.
     *
     * If an array is passed as the key, we will assume you want to set an array of values.
     *
     * @param  string|array|null  $key
     * @param  mixed  $default
     * @return mixed|\App\Models\Setting
     */
    function setting($key = null, $default = null)
    {
        if (is_null($key)) {
            return Setting::getAllAsArray();
        }

        if (is_array($key)) {
            foreach ($key as $k => $value) {
                Setting::set($k, $value);
            }
            return true;
        }

        return Setting::get($key, $default);
    }
}

if (!function_exists('setting_group')) {
    /**
     * Get all settings for a specific group.
     *
     * @param  string  $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function setting_group($group)
    {
        return Setting::getByGroup($group);
    }
}

if (!function_exists('setting_clear_cache')) {
    /**
     * Clear the settings cache.
     *
     * @return void
     */
    function setting_clear_cache()
    {
        Setting::clearCache();
    }
}
