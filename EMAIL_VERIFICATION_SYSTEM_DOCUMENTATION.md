# 📧 Email Verification & Password Reset System Documentation

## 🎯 **System Overview**

The PocketWatch email verification system provides secure 6-digit code-based email verification and password reset functionality using Gmail SMTP integration.

---

## ✅ **Features Implemented**

### **🔐 Email Verification**
- **6-digit random codes** with 15-minute expiration
- **Rate limiting** (1 minute between requests)
- **Attempt tracking** (max 5 attempts per code)
- **Professional email templates** with PocketWatch branding
- **Auto-inheritance** of parent category for sub-bins

### **🔑 Password Reset**
- **6-digit random codes** with 15-minute expiration
- **Secure verification** before password change
- **Rate limiting** and attempt tracking
- **Professional security-focused email templates**
- **Complete password reset workflow**

### **📧 Email Templates**
- **Responsive HTML design** with modern styling
- **Professional branding** with PocketWatch colors
- **Security warnings** and best practices
- **Clear instructions** and expiration notices
- **Mobile-friendly** layouts

---

## 🗄️ **Database Schema**

### **Users Table - New Fields Added**
```sql
-- Email verification fields
email_verification_code VARCHAR(6) NULL
email_verification_code_expires_at TIMESTAMP NULL
email_verification_attempts INT DEFAULT 0
last_verification_code_sent_at TIMESTAMP NULL

-- Password reset fields  
password_reset_code VARCHAR(6) NULL
password_reset_code_expires_at TIMESTAMP NULL
password_reset_attempts INT DEFAULT 0
last_password_reset_code_sent_at TIMESTAMP NULL
```

---

## 📊 **API Endpoints**

### **📧 Email Verification Endpoints**

#### **POST** `/api/email/send-verification-code`
**Description**: Send 6-digit verification code to user's email

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Verification code sent to your email address.",
  "data": {
    "expires_at": "2024-01-01T12:15:00.000000Z",
    "attempts_remaining": 5
  }
}
```

**Error Responses**:
- **422**: Validation failed
- **404**: User not found
- **400**: Email already verified
- **429**: Rate limited or max attempts exceeded

#### **POST** `/api/email/verify`
**Description**: Verify email with 6-digit code

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Email verified successfully!",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "email_verified_at": "2024-01-01T12:00:00.000000Z"
    }
  }
}
```

#### **POST** `/api/email/resend-verification-code`
**Description**: Resend verification code (same as send-verification-code)

#### **POST** `/api/email/check-verification-status`
**Description**: Check current verification status

**Response**:
```json
{
  "success": true,
  "data": {
    "email_verified": false,
    "email_verified_at": null,
    "verification_attempts": 2,
    "max_attempts": 5,
    "attempts_remaining": 3,
    "has_pending_code": true,
    "code_expires_at": "2024-01-01T12:15:00.000000Z"
  }
}
```

### **🔑 Password Reset Endpoints**

#### **POST** `/api/password/send-reset-code`
**Description**: Send 6-digit password reset code

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

#### **POST** `/api/password/verify-reset-code`
**Description**: Verify password reset code

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**Success Response**:
```json
{
  "success": true,
  "message": "Reset code verified. You can now set a new password.",
  "data": {
    "reset_token": "123456"
  }
}
```

#### **POST** `/api/password/reset-with-code`
**Description**: Complete password reset with verified code

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "code": "123456",
  "password": "newpassword123",
  "password_confirmation": "newpassword123"
}
```

---

## 🎨 **Email Templates**

### **📧 Email Verification Template**
- **File**: `resources/views/emails/email-verification.blade.php`
- **Features**:
  - Professional PocketWatch branding
  - Large, clear 6-digit code display
  - Security warnings and best practices
  - Expiration time and attempt tracking
  - Feature highlights for new users

### **🔐 Password Reset Template**
- **File**: `resources/views/emails/password-reset.blade.php`
- **Features**:
  - Security-focused design with warnings
  - Step-by-step reset instructions
  - Clear security notices for unauthorized attempts
  - Password security tips and best practices

---

## 🔧 **Configuration**

### **📧 Gmail SMTP Setup (.env)**
```env
# Gmail SMTP Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=iegfmudssfleksym
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Email Verification Settings
EMAIL_VERIFICATION_CODE_LENGTH=6
EMAIL_VERIFICATION_CODE_EXPIRY=15
PASSWORD_RESET_CODE_EXPIRY=15
```

### **⚙️ System Constants**
```php
// In EmailVerificationService
const MAX_ATTEMPTS = 5;
const CODE_LENGTH = 6;
const RATE_LIMIT_MINUTES = 1;
```

---

## 🛡️ **Security Features**

### **🔒 Rate Limiting**
- **1 minute** minimum between code requests
- Prevents spam and abuse
- User-specific rate limiting

### **🎯 Attempt Tracking**
- **Maximum 5 attempts** per verification code
- Automatic code invalidation after max attempts
- Separate tracking for email verification and password reset

### **⏰ Code Expiration**
- **15 minutes** expiration for all codes
- Automatic cleanup of expired codes
- Clear expiration notices in emails

### **🔐 Secure Code Generation**
- **Cryptographically secure** random number generation
- **6-digit format** for user convenience
- **Zero-padded** to ensure consistent length

---

## 📱 **Mobile App Integration**

### **🔄 Verification Flow**
1. **User Registration**: App calls send-verification-code
2. **Code Entry**: User enters 6-digit code from email
3. **Verification**: App calls verify endpoint
4. **Success**: User account is verified and activated

### **🔑 Password Reset Flow**
1. **Forgot Password**: App calls send-reset-code
2. **Code Entry**: User enters 6-digit code from email
3. **Code Verification**: App calls verify-reset-code
4. **New Password**: App calls reset-with-code with new password
5. **Success**: User can login with new password

### **📱 Flutter/Dart Integration Example**
```dart
// Send verification code
Future<Map<String, dynamic>> sendVerificationCode(String email) async {
  final response = await http.post(
    Uri.parse('$baseUrl/email/send-verification-code'),
    headers: {'Content-Type': 'application/json'},
    body: json.encode({'email': email}),
  );
  return json.decode(response.body);
}

// Verify email
Future<Map<String, dynamic>> verifyEmail(String email, String code) async {
  final response = await http.post(
    Uri.parse('$baseUrl/email/verify'),
    headers: {'Content-Type': 'application/json'},
    body: json.encode({'email': email, 'code': code}),
  );
  return json.decode(response.body);
}
```

---

## 🧪 **Testing**

### **✅ Test Script**
- **File**: `test_email_verification_system.php`
- **Coverage**: All verification and reset scenarios
- **Features**: Code generation, email sending, verification, rate limiting

### **🎯 Test Scenarios**
1. ✅ **6-digit code generation**
2. ✅ **Email verification code sending**
3. ✅ **Email template rendering**
4. ✅ **Correct code verification**
5. ✅ **Incorrect code rejection**
6. ✅ **Password reset flow**
7. ✅ **Rate limiting enforcement**
8. ✅ **Attempt tracking**

---

## 🚀 **Production Deployment**

### **✅ Pre-Deployment Checklist**
- ✅ **Gmail SMTP credentials** configured
- ✅ **Database migration** executed
- ✅ **Email templates** tested
- ✅ **API endpoints** functional
- ✅ **Rate limiting** working
- ✅ **Security measures** in place

### **📧 Email Deliverability**
- **Gmail SMTP** ensures high deliverability
- **Professional from address**: <EMAIL>
- **Proper SPF/DKIM** setup recommended
- **Email content** optimized for spam filters

---

## 🎉 **Summary**

**The Email Verification & Password Reset System is now complete with:**

### **🎯 Core Features**
- ✅ **6-digit code generation** and validation
- ✅ **Professional email templates** with PocketWatch branding
- ✅ **Comprehensive API endpoints** for all operations
- ✅ **Security features** (rate limiting, attempt tracking, expiration)
- ✅ **Gmail SMTP integration** for reliable delivery

### **🔧 Technical Implementation**
- ✅ **Database schema** updated with verification fields
- ✅ **Service layer** for business logic separation
- ✅ **Mailable classes** for email composition
- ✅ **API controllers** with comprehensive error handling
- ✅ **Route definitions** for all endpoints

### **📱 Integration Ready**
- ✅ **Mobile app integration** examples provided
- ✅ **Clear API documentation** with request/response examples
- ✅ **Error handling** with specific error codes
- ✅ **Testing scripts** for verification

**The system is production-ready and provides a secure, user-friendly email verification and password reset experience!** 🚀
