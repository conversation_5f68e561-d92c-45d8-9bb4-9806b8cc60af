<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $subject ?? 'PocketWatch Notification' }}</title>
    <style>
        /* Base styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background-color: #f9f9f9;
        }
        
        .email-wrapper {
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin: 20px;
        }
        
        /* Header styles */
        .header {
            background-color: #2E8B57;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header img {
            max-height: 50px;
            margin-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        /* Content styles */
        .content {
            padding: 30px 25px;
            background-color: #ffffff;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .message {
            margin-bottom: 25px;
        }
        
        .details {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 25px;
        }
        
        .details h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            color: #555;
        }
        
        .details ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .details li {
            margin-bottom: 5px;
        }
        
        /* Button styles */
        .button-container {
            text-align: center;
            margin: 25px 0;
        }
        
        .button {
            display: inline-block;
            background-color: #2E8B57;
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 16px;
        }
        
        /* Footer styles */
        .footer {
            background-color: #f5f5f5;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eaeaea;
        }
        
        .footer p {
            margin: 5px 0;
        }
        
        .social-links {
            margin: 15px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 5px;
            color: #2E8B57;
            text-decoration: none;
        }
        
        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                margin: 10px;
            }
            
            .header h1 {
                font-size: 20px;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .button {
                display: block;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="header">
            @if(setting('logo'))
                <img src="{{ setting('logo') }}" alt="{{ setting('app_name', 'PocketWatch') }}" />
            @endif
            <h1>{{ $title ?? setting('app_name', 'PocketWatch') }}</h1>
        </div>
        
        <div class="content">
            <div class="greeting">
                {{ $greeting ?? 'Hello,' }}
            </div>
            
            <div class="message">
                @yield('content')
            </div>
            
            @hasSection('details')
                <div class="details">
                    <h3>{{ $detailsTitle ?? 'Details' }}</h3>
                    @yield('details')
                </div>
            @endif
            
            @isset($actionUrl)
                <div class="button-container">
                    <a href="{{ $actionUrl }}" class="button">{{ $actionText ?? 'View Details' }}</a>
                </div>
            @endisset
            
            <p>
                {{ $closing ?? 'Thank you for using PocketWatch!' }}
            </p>
            
            <p>
                {{ $signature ?? 'The PocketWatch Team' }}
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ setting('app_name', 'PocketWatch') }}. All rights reserved.</p>
            <p>{{ setting('footer_text', '') }}</p>
            
            <div class="social-links">
                <a href="#">Twitter</a> | 
                <a href="#">Facebook</a> | 
                <a href="#">Instagram</a>
            </div>
            
            <p>
                If you have any questions, please contact us at 
                <a href="mailto:{{ setting('mail_from_address', '<EMAIL>') }}">
                    {{ setting('mail_from_address', '<EMAIL>') }}
                </a>
            </p>
        </div>
    </div>
</body>
</html>
