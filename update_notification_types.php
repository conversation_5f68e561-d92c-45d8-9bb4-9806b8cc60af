<?php

// Define notification types for each notification class
$notificationTypes = [
    // Auth notifications
    'App\Notifications\Auth\LoginNotification' => 'email_login',
    'App\Notifications\Auth\PasswordChangedNotification' => 'email_password_changes',
    'App\Notifications\Auth\PasswordResetNotification' => 'email_password_changes',
    'App\Notifications\Auth\ProfileUpdatedNotification' => 'email_profile_updates',
    
    // Bin notifications
    'App\Notifications\Bins\BinCreatedNotification' => 'email_bin_operations',
    'App\Notifications\Bins\BinUpdatedNotification' => 'email_bin_operations',
    'App\Notifications\Bins\BinDeletedNotification' => 'email_bin_operations',
    'App\Notifications\Bins\SubBinCreatedNotification' => 'email_bin_operations',
    'App\Notifications\Bins\SubBinUpdatedNotification' => 'email_bin_operations',
    'App\Notifications\Bins\SubBinDeletedNotification' => 'email_bin_operations',
    
    // Transaction notifications
    'App\Notifications\Transactions\TransactionCreatedNotification' => 'email_transaction_operations',
    'App\Notifications\Transactions\TransactionUpdatedNotification' => 'email_transaction_operations',
    'App\Notifications\Transactions\TransactionDeletedNotification' => 'email_transaction_operations',
    
    // Subscription notifications
    'App\Notifications\Subscriptions\SubscriptionCreatedNotification' => 'email_subscription_updates',
    'App\Notifications\Subscriptions\SubscriptionUpdatedNotification' => 'email_subscription_updates',
    'App\Notifications\Subscriptions\SubscriptionCanceledNotification' => 'email_subscription_updates',
    'App\Notifications\Subscriptions\SubscriptionRenewalNotification' => 'email_renewal_reminders',
    
    // Alert notifications
    'App\Notifications\Alerts\ThresholdAlertNotification' => 'email_threshold_alerts',
];

// Function to update a notification class file
function updateNotificationClass($className, $notificationType) {
    // Convert class name to file path
    $filePath = str_replace('\\', '/', $className) . '.php';
    $filePath = str_replace('App/', 'app/', $filePath);
    
    // Check if file exists
    if (!file_exists($filePath)) {
        echo "File not found: $filePath\n";
        return;
    }
    
    // Read file content
    $content = file_get_contents($filePath);
    
    // Check if notificationType is already defined
    if (strpos($content, 'protected $notificationType') !== false) {
        echo "Notification type already defined in $filePath\n";
        return;
    }
    
    // Find the class declaration
    $pattern = '/class\s+(\w+)\s+extends\s+BaseNotification/';
    if (!preg_match($pattern, $content, $matches)) {
        echo "Class declaration not found in $filePath\n";
        return;
    }
    
    // Find the constructor
    $pattern = '/\s+public\s+function\s+__construct/';
    if (!preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
        echo "Constructor not found in $filePath\n";
        return;
    }
    
    // Insert notification type before constructor
    $insertPos = $matches[0][1];
    $insertText = "\n    /**\n     * The notification type for preference checking.\n     *\n     * @var string\n     */\n    protected \$notificationType = '$notificationType';\n";
    
    $newContent = substr($content, 0, $insertPos) . $insertText . substr($content, $insertPos);
    
    // Write updated content back to file
    file_put_contents($filePath, $newContent);
    
    echo "Updated $filePath\n";
}

// Update each notification class
foreach ($notificationTypes as $className => $notificationType) {
    updateNotificationClass($className, $notificationType);
}

echo "Done!\n";
