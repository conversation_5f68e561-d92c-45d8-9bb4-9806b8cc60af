<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckTrialStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        // Skip check for admin users
        if (!$user || $user->is_admin) {
            return $next($request);
        }

        // Check if user's trial has expired
        if ($user->subscription_tier === 'trial' && $user->trialExpired()) {
            // Update user status to expired
            $user->subscription_tier = 'expired';
            $user->save();

            // Update subscription status
            $activeSubscription = $user->getActiveSubscription();
            if ($activeSubscription) {
                $activeSubscription->stripe_status = 'expired';
                $activeSubscription->ends_at = now();
                $activeSubscription->save();
            }
        }

        // Restrict access for users without plans or expired trials
        if (in_array($user->subscription_tier, ['none', 'expired'])) {
            // Allow access to subscription-related endpoints and package endpoints
            $allowedRoutes = [
                'api.subscriptions.select-plan',
                'api.subscriptions.add-payment-method',
                'api.subscriptions.plans',
                'api.subscriptions.index',
                'api.packages.index',
                'api.packages.purchase',
                'api.packages.purchase-test',
                // 'api.packages.checkout.index', // DISABLED
                // 'api.packages.checkout.create', // DISABLED
                // 'api.packages.checkout.success', // DISABLED
                // 'api.packages.checkout.cancel', // DISABLED
                // 'api.packages.gui.index', // DISABLED
                // 'api.packages.gui.confirm', // DISABLED
                // 'api.packages.gui.checkout', // DISABLED
                'api.packages.simple.index',
                'api.packages.simple.stripe-url',
                'api.packages.simple.success',
                'api.packages.simple.cancel',
                'api.auth.me',
                'api.auth.logout',
            ];

            $currentRoute = $request->route()->getName();

            if (!in_array($currentRoute, $allowedRoutes)) {
                if ($request->expectsJson()) {
                    $message = $user->subscription_tier === 'expired'
                        ? 'Your trial has expired. Please subscribe to continue using PocketWatch.'
                        : 'Please select a plan and add payment method to start your free trial.';

                    return response()->json([
                        'message' => $message,
                        'subscription_required' => true,
                        'user_status' => $user->subscription_tier,
                    ], 402); // 402 Payment Required
                }

                // For non-API requests, return JSON anyway since we're primarily an API
                return response()->json([
                    'message' => 'Please select a plan to continue.',
                    'subscription_required' => true,
                    'user_status' => $user->subscription_tier,
                ], 402);
            }
        }

        // Restrict access for users who selected plan but haven't added payment method
        if ($user->subscription_tier === 'plan_selected') {
            $allowedRoutes = [
                'api.subscriptions.add-payment-method',
                'api.subscriptions.start-trial-test',
                'api.subscriptions.select-plan',
                'api.subscriptions.plans',
                'api.packages.index',
                'api.packages.purchase',
                'api.packages.purchase-test',
                'api.auth.me',
                'api.auth.logout',
            ];

            $currentRoute = $request->route()->getName();

            if (!in_array($currentRoute, $allowedRoutes)) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'message' => 'Please add payment method to start your free trial.',
                        'payment_method_required' => true,
                        'selected_plan' => $user->selected_plan_tier,
                        'billing_cycle' => $user->selected_billing_cycle,
                    ], 402);
                }

                // For non-API requests, return JSON anyway since we're primarily an API
                return response()->json([
                    'message' => 'Please add payment method to start your free trial.',
                    'payment_method_required' => true,
                    'selected_plan' => $user->selected_plan_tier,
                    'billing_cycle' => $user->selected_billing_cycle,
                ], 402);
            }
        }

        return $next($request);
    }
}
