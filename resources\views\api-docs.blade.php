<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: {{ setting('primary_color', '#32704e') }};
            --background-color: {{ setting('background_color', '#ffffff') }};
            --text-color: {{ setting('text_color', '#333333') }};
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            padding-top: 56px;
        }

        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand img {
            height: 30px;
        }

        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            width: 250px;
            height: calc(100vh - 56px);
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 20px 0;
            border-right: 1px solid #e9ecef;
        }

        .sidebar .nav-link {
            color: #495057;
            border-left: 3px solid transparent;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: var(--primary-color);
            background-color: rgba(0, 0, 0, 0.05);
        }

        .sidebar .nav-link.active {
            color: var(--primary-color);
            border-left-color: var(--primary-color);
            background-color: rgba(0, 0, 0, 0.05);
        }

        .content {
            margin-left: 250px;
            padding: 20px;
        }

        .endpoint {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .endpoint-header {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .endpoint-method {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            margin-right: 10px;
            min-width: 60px;
            text-align: center;
        }

        .method-get {
            background-color: #61affe;
            color: white;
        }

        .method-post {
            background-color: #49cc90;
            color: white;
        }

        .method-put {
            background-color: #fca130;
            color: white;
        }

        .method-delete {
            background-color: #f93e3e;
            color: white;
        }

        .endpoint-path {
            font-family: monospace;
            font-size: 1.1rem;
        }

        .endpoint-body {
            padding: 15px;
        }

        .endpoint-description {
            margin-bottom: 15px;
        }

        .params-table {
            width: 100%;
            margin-bottom: 15px;
        }

        .response-example {
            background-color: #272822;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }

        .section-title {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .required-badge {
            background-color: #f93e3e;
            color: white;
            font-size: 0.7rem;
            padding: 2px 5px;
            border-radius: 3px;
            margin-left: 5px;
        }

        .optional-badge {
            background-color: #61affe;
            color: white;
            font-size: 0.7rem;
            padding: 2px 5px;
            border-radius: 3px;
            margin-left: 5px;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                top: 0;
            }

            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                @if(setting('logo'))
                    <img src="{{ setting('logo') }}" alt="{{ setting('app_name', 'PocketWatch') }}">
                @else
                    {{ setting('app_name', 'PocketWatch') }} API
                @endif
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/home') }}">
                            <i class="fas fa-home me-1"></i> Dashboard
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#introduction">
                                <i class="fas fa-info-circle me-2"></i> Introduction
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#authentication">
                                <i class="fas fa-lock me-2"></i> Authentication
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#users">
                                <i class="fas fa-users me-2"></i> Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#bins">
                                <i class="fas fa-box me-2"></i> Bins
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#transactions">
                                <i class="fas fa-exchange-alt me-2"></i> Transactions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#subscriptions">
                                <i class="fas fa-crown me-2"></i> Subscriptions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#plaid">
                                <i class="fas fa-university me-2"></i> Plaid Integration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#plaid-payment">
                                <i class="fas fa-credit-card me-2"></i> Plaid Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#crypto">
                                <i class="fab fa-bitcoin me-2"></i> Crypto Wallets
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#reports">
                                <i class="fas fa-chart-bar me-2"></i> Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <section id="introduction" class="mb-5">
                    <h2 class="section-title">Introduction</h2>
                    <p>Welcome to the PocketWatch API documentation. This API allows you to interact with the PocketWatch platform programmatically, enabling you to manage bins, transactions, subscriptions, and more.</p>
                    <p>The API is organized around REST principles. It accepts JSON-encoded request bodies, returns JSON-encoded responses, and uses standard HTTP response codes, authentication, and verbs.</p>
                    <p>Base URL: <code>{{ url('/api') }}</code></p>
                </section>

                <section id="authentication" class="mb-5">
                    <h2 class="section-title">Authentication</h2>
                    <p>The PocketWatch API uses token-based authentication. To authenticate, you need to obtain an API token by logging in with your credentials.</p>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/api/login</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                <p>Authenticate a user and get an API token.</p>
                            </div>

                            <h5>Request Parameters</h5>
                            <table class="table params-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>email <span class="required-badge">Required</span></td>
                                        <td>string</td>
                                        <td>The user's email address.</td>
                                    </tr>
                                    <tr>
                                        <td>password <span class="required-badge">Required</span></td>
                                        <td>string</td>
                                        <td>The user's password.</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h5>Response</h5>
                            <pre class="response-example">
{
    "success": true,
    "token": "1|abcdefghijklmnopqrstuvwxyz1234567890",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "subscription_tier": "premium"
    }
}
</pre>
                        </div>
                    </div>
                </section>

                <!-- Plaid Payment Section -->
                <section id="plaid-payment" class="mb-5">
                    <h2 class="section-title">Plaid Payments</h2>
                    <p>The Plaid Payments API allows you to process payments using linked bank accounts through Plaid.</p>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-get">GET</span>
                            <span class="endpoint-path">/api/plaid-payment/accounts</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                <p>Get a list of payment-enabled bank accounts for the authenticated user.</p>
                            </div>

                            <h5>Response</h5>
                            <pre class="response-example">
{
    "success": true,
    "accounts": [
        {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "institution_name": "Chase",
            "account_name": "Checking Account",
            "account_type": "checking",
            "account_mask": "1234"
        }
    ]
}
</pre>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/api/plaid-payment/process</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                <p>Process a payment using a linked bank account.</p>
                            </div>

                            <h5>Request Parameters</h5>
                            <table class="table params-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>account_id <span class="required-badge">Required</span></td>
                                        <td>integer</td>
                                        <td>The ID of the Plaid account to use for payment.</td>
                                    </tr>
                                    <tr>
                                        <td>package_id <span class="required-badge">Required</span></td>
                                        <td>string</td>
                                        <td>The ID of the subscription package (e.g., "base_monthly", "premium_yearly").</td>
                                    </tr>
                                    <tr>
                                        <td>amount <span class="required-badge">Required</span></td>
                                        <td>number</td>
                                        <td>The payment amount.</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h5>Response</h5>
                            <pre class="response-example">
{
    "success": true,
    "message": "Payment processed successfully",
    "payment_id": "550e8400-e29b-41d4-a716-************",
    "subscription": {
        "id": 1,
        "name": "Premium Tier",
        "status": "active",
        "billing_cycle": "monthly"
    }
}
</pre>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-get">GET</span>
                            <span class="endpoint-path">/api/plaid-payment/status/{paymentId}</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                <p>Get the status of a payment.</p>
                            </div>

                            <h5>Path Parameters</h5>
                            <table class="table params-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>paymentId <span class="required-badge">Required</span></td>
                                        <td>string</td>
                                        <td>The ID of the payment to check.</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h5>Response</h5>
                            <pre class="response-example">
{
    "success": true,
    "status": "completed",
    "last_status_update": "2023-05-15T14:30:00Z"
}
</pre>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 70,
                            behavior: 'smooth'
                        });

                        // Update active link
                        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                            link.classList.remove('active');
                        });
                        this.classList.add('active');
                    }
                });
            });

            // Set active link based on scroll position
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('main section');
                const navLinks = document.querySelectorAll('.sidebar .nav-link');

                let currentSection = '';

                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    const sectionHeight = section.offsetHeight;

                    if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                        currentSection = '#' + section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === currentSection) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>
