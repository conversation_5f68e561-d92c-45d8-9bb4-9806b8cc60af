@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-between align-items-center mb-4">
        <div class="col-auto">
            <h1 class="h3 mb-0">Create Report</h1>
            <p class="text-muted">Generate a new financial report</p>
        </div>
        <div class="col-auto">
            <a href="{{ route('reports.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <form action="{{ route('reports.store') }}" method="POST">
                @csrf
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Report Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="type" class="form-label">Report Type <span class="text-danger">*</span></label>
                            <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                <option value="">Select Report Type</option>
                                <option value="transaction" {{ old('type') == 'transaction' ? 'selected' : '' }}>Transaction Report</option>
                                <option value="bin" {{ old('type') == 'bin' ? 'selected' : '' }}>Bin Report</option>
                                <option value="summary" {{ old('type') == 'summary' ? 'selected' : '' }}>Summary Report</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="format" class="form-label">Report Format <span class="text-danger">*</span></label>
                            <div class="d-flex">
                                <div class="form-check me-4">
                                    <input class="form-check-input" type="radio" name="format" id="format_csv" value="csv" {{ old('format', 'csv') == 'csv' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="format_csv">
                                        <i class="fas fa-file-csv me-1"></i> CSV
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="format" id="format_pdf" value="pdf" {{ old('format') == 'pdf' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="format_pdf">
                                        <i class="fas fa-file-pdf me-1"></i> PDF
                                    </label>
                                </div>
                            </div>
                            @error('format')
                                <div class="text-danger small mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="period_type" class="form-label">Time Period <span class="text-danger">*</span></label>
                            <select class="form-select @error('period_type') is-invalid @enderror" id="period_type" name="period_type" required>
                                <option value="">Select Time Period</option>
                                <option value="daily" {{ old('period_type') == 'daily' ? 'selected' : '' }}>Daily (Today)</option>
                                <option value="weekly" {{ old('period_type') == 'weekly' ? 'selected' : '' }}>Weekly (Current Week)</option>
                                <option value="monthly" {{ old('period_type') == 'monthly' ? 'selected' : '' }}>Monthly (Current Month)</option>
                                <option value="custom" {{ old('period_type') == 'custom' ? 'selected' : '' }}>Custom Date Range</option>
                            </select>
                            @error('period_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div id="custom_date_range" class="row mb-4" style="{{ old('period_type') == 'custom' ? '' : 'display: none;' }}">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('start_date') is-invalid @enderror" id="start_date" name="start_date" value="{{ old('start_date') }}">
                            @error('start_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('end_date') is-invalid @enderror" id="end_date" name="end_date" value="{{ old('end_date') }}">
                            @error('end_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-header">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring" value="1" {{ old('is_recurring') ? 'checked' : '' }}>
                                    <label class="form-check-label fw-bold" for="is_recurring">
                                        Make this a recurring report
                                    </label>
                                </div>
                            </div>
                            <div id="recurring_options" class="card-body" style="{{ old('is_recurring') ? '' : 'display: none;' }}">
                                <div class="mb-3">
                                    <label for="schedule" class="form-label">Schedule <span class="text-danger">*</span></label>
                                    <select class="form-select @error('schedule') is-invalid @enderror" id="schedule" name="schedule">
                                        <option value="">Select Schedule</option>
                                        <option value="daily" {{ old('schedule') == 'daily' ? 'selected' : '' }}>Daily</option>
                                        <option value="weekly" {{ old('schedule') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                                        <option value="monthly" {{ old('schedule') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                    </select>
                                    @error('schedule')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i> Recurring reports will be automatically generated based on the selected schedule.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h5 class="mb-0">Filters (Optional)</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="bin_id" class="form-label">Bin</label>
                                            <select class="form-select" id="bin_id" name="bin_id">
                                                <option value="">All Bins</option>
                                                @foreach($bins as $bin)
                                                    <option value="{{ $bin->id }}" {{ old('bin_id') == $bin->id ? 'selected' : '' }}>
                                                        {{ $bin->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sub_bin_id" class="form-label">Sub-Bin</label>
                                            <select class="form-select" id="sub_bin_id" name="sub_bin_id" disabled>
                                                <option value="">Select Bin First</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="transaction_type" class="form-label">Transaction Type</label>
                                            <select class="form-select" id="transaction_type" name="transaction_type">
                                                <option value="">All Types</option>
                                                <option value="income" {{ old('transaction_type') == 'income' ? 'selected' : '' }}>Income</option>
                                                <option value="expense" {{ old('transaction_type') == 'expense' ? 'selected' : '' }}>Expense</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="min_amount" class="form-label">Min Amount</label>
                                            <input type="number" class="form-control" id="min_amount" name="min_amount" value="{{ old('min_amount') }}" step="0.01" min="0">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="max_amount" class="form-label">Max Amount</label>
                                            <input type="number" class="form-control" id="max_amount" name="max_amount" value="{{ old('max_amount') }}" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-end">
                    <a href="{{ route('reports.index') }}" class="btn btn-outline-secondary me-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-file-alt me-2"></i>Generate Report
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle custom date range visibility
        const periodTypeSelect = document.getElementById('period_type');
        const customDateRange = document.getElementById('custom_date_range');
        
        periodTypeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
            }
        });
        
        // Handle recurring options visibility
        const isRecurringCheckbox = document.getElementById('is_recurring');
        const recurringOptions = document.getElementById('recurring_options');
        
        isRecurringCheckbox.addEventListener('change', function() {
            if (this.checked) {
                recurringOptions.style.display = 'block';
            } else {
                recurringOptions.style.display = 'none';
            }
        });
        
        // Handle sub-bins loading
        const binSelect = document.getElementById('bin_id');
        const subBinSelect = document.getElementById('sub_bin_id');
        
        binSelect.addEventListener('change', function() {
            if (this.value) {
                // Enable sub-bin select
                subBinSelect.disabled = false;
                
                // Clear current options
                subBinSelect.innerHTML = '<option value="">All Sub-Bins</option>';
                
                // Find the selected bin and add its sub-bins
                const selectedBin = @json($bins);
                const bin = selectedBin.find(b => b.id == this.value);
                
                if (bin && bin.sub_bins && bin.sub_bins.length > 0) {
                    bin.sub_bins.forEach(subBin => {
                        const option = document.createElement('option');
                        option.value = subBin.id;
                        option.textContent = subBin.name;
                        subBinSelect.appendChild(option);
                    });
                }
            } else {
                // Disable sub-bin select
                subBinSelect.disabled = true;
                subBinSelect.innerHTML = '<option value="">Select Bin First</option>';
            }
        });
    });
</script>
@endpush
@endsection
