<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\BinController;
use App\Http\Controllers\API\CryptoWalletController;
use App\Http\Controllers\API\EmailVerificationController;
use App\Http\Controllers\API\NotificationSettingController;
use App\Http\Controllers\API\PasswordResetController;
use App\Http\Controllers\API\PlaidController;
use App\Http\Controllers\API\PlaidPaymentController;
use App\Http\Controllers\API\ReportController;
use App\Http\Controllers\API\SubBinController;
use App\Http\Controllers\API\SubscriptionController;
use App\Http\Controllers\API\TransactionController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register'])->name('api.auth.register');
Route::post('/login', [AuthController::class, 'login'])->name('api.auth.login');
Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->name('api.auth.forgot-password');
Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('api.auth.reset-password');

// Email verification routes (public)
Route::post('/email/send-verification-code', [EmailVerificationController::class, 'sendVerificationCode'])->name('api.email.send-verification-code');
Route::post('/email/verify', [EmailVerificationController::class, 'verifyEmail'])->name('api.email.verify');
Route::post('/email/resend-verification-code', [EmailVerificationController::class, 'resendVerificationCode'])->name('api.email.resend-verification-code');
Route::post('/email/check-verification-status', [EmailVerificationController::class, 'checkVerificationStatus'])->name('api.email.check-verification-status');

// Password reset routes (public)
Route::post('/password/send-reset-code', [PasswordResetController::class, 'sendResetCode'])->name('api.password.send-reset-code');
Route::post('/password/verify-reset-code', [PasswordResetController::class, 'verifyResetCode'])->name('api.password.verify-reset-code');
Route::post('/password/reset-with-code', [PasswordResetController::class, 'resetPassword'])->name('api.password.reset-with-code');

// Google OAuth routes
Route::get('/auth/google', [App\Http\Controllers\API\GoogleAuthController::class, 'redirectToGoogle'])->name('api.auth.google');
Route::get('/auth/google/callback', [App\Http\Controllers\API\GoogleAuthController::class, 'handleGoogleCallback'])->name('api.auth.google.callback');
Route::post('/auth/google/token', [App\Http\Controllers\API\GoogleAuthController::class, 'loginWithGoogleToken'])->name('api.auth.google.token');

// Protected routes
Route::middleware(['auth:sanctum', 'auth.active', 'trial.status'])->group(function () {
    // Auth routes
    Route::get('/me', [AuthController::class, 'me'])->name('api.auth.me');
    Route::post('/logout', [AuthController::class, 'logout'])->name('api.auth.logout');
    Route::put('/profile', [AuthController::class, 'updateProfile'])->name('api.auth.update-profile');
    Route::put('/password', [AuthController::class, 'changePassword'])->name('api.auth.change-password');
    Route::post('/avatar', [AuthController::class, 'updateAvatar'])->name('api.auth.update-avatar');
    Route::post('/auth/google/unlink', [App\Http\Controllers\API\GoogleAuthController::class, 'unlinkGoogle'])->name('api.auth.google.unlink');

    // Bin routes
    Route::get('bins', [BinController::class, 'index'])->name('api.bins.index');
    Route::get('bins/income', [BinController::class, 'incomeBins'])->name('api.bins.income');
    Route::get('bins/expense', [BinController::class, 'expenseBins'])->name('api.bins.expense');
    Route::post('bins', [BinController::class, 'store'])->name('api.bins.store');
    Route::get('bins/{id}', [BinController::class, 'show'])->name('api.bins.show');
    Route::put('bins/{id}', [BinController::class, 'update'])->name('api.bins.update');
    Route::delete('bins/{id}', [BinController::class, 'destroy'])->name('api.bins.destroy');

    // Sub-bin routes
    Route::get('bins/{binId}/sub-bins', [SubBinController::class, 'index'])->name('api.sub-bins.index');
    Route::post('bins/{binId}/sub-bins', [SubBinController::class, 'store'])->name('api.sub-bins.store');
    Route::get('bins/{binId}/sub-bins/{id}', [SubBinController::class, 'show'])->name('api.sub-bins.show');
    Route::put('bins/{binId}/sub-bins/{id}', [SubBinController::class, 'update'])->name('api.sub-bins.update');
    Route::delete('bins/{binId}/sub-bins/{id}', [SubBinController::class, 'destroy'])->name('api.sub-bins.destroy');

    // Hierarchical sub-bin routes
    Route::get('bins/{binId}/sub-bins-hierarchy', [SubBinController::class, 'getHierarchy'])->name('api.sub-bins.hierarchy');
    Route::post('bins/{binId}/sub-bins/{parentSubBinId}/nested', [SubBinController::class, 'storeNested'])->name('api.sub-bins.store-nested');

    // Package Purchase routes (Legacy)
    Route::get('packages', [App\Http\Controllers\API\PackagePurchaseController::class, 'getPackages'])->name('api.packages.index');
    Route::post('packages/purchase', [App\Http\Controllers\API\PackagePurchaseController::class, 'purchasePackage'])->name('api.packages.purchase');
    Route::post('packages/purchase-test', [App\Http\Controllers\API\PackagePurchaseController::class, 'purchasePackageTest'])->name('api.packages.purchase-test');

    // Stripe Checkout routes (Direct Flow) - DISABLED
    // Route::get('packages-checkout', [App\Http\Controllers\API\StripeCheckoutController::class, 'getPackagesWithCheckout'])->name('api.packages.checkout.index');
    // Route::post('packages/{packageId}/checkout', [App\Http\Controllers\API\StripeCheckoutController::class, 'createCheckoutSession'])->name('api.packages.checkout.create');
    // Route::get('payment-success', [App\Http\Controllers\API\StripeCheckoutController::class, 'handleSuccess'])->name('api.packages.checkout.success');
    // Route::get('payment-cancel', [App\Http\Controllers\API\StripeCheckoutController::class, 'handleCancel'])->name('api.packages.checkout.cancel');

    // GUI-Based Package Selection & Confirmation - DISABLED
    // Route::get('packages-gui', [App\Http\Controllers\API\PackageConfirmationController::class, 'getAllPackages'])->name('api.packages.gui.index');
    // Route::get('packages-gui/{packageId}/confirm', [App\Http\Controllers\API\PackageConfirmationController::class, 'getPackageForConfirmation'])->name('api.packages.gui.confirm');
    // Route::post('packages-gui/{packageId}/checkout', [App\Http\Controllers\API\PackageConfirmationController::class, 'confirmAndCreateCheckout'])->name('api.packages.gui.checkout');

    // Simple Stripe Integration (Blade-based)
    Route::get('packages-simple', [App\Http\Controllers\API\SimpleStripeController::class, 'getPackages'])->name('api.packages.simple.index');
    Route::post('packages-simple/stripe-url', [App\Http\Controllers\API\SimpleStripeController::class, 'generateStripeUrl'])->name('api.packages.simple.stripe-url');
    Route::get('payment-simple-cancel', [App\Http\Controllers\API\SimpleStripeController::class, 'handleCancel'])->name('api.packages.simple.cancel');

    // Transaction routes
    Route::get('transactions', [TransactionController::class, 'index'])->name('api.transactions.index');
    Route::post('transactions', [TransactionController::class, 'store'])->name('api.transactions.store');
    Route::get('transactions/stats', [TransactionController::class, 'getStats'])->name('api.transactions.stats');
    Route::get('transactions/by-bin/{binId}', [TransactionController::class, 'getByBin'])->name('api.transactions.by-bin');
    Route::get('transactions/by-category/{category}', [TransactionController::class, 'getByCategory'])->name('api.transactions.by-category');
    Route::get('transactions/{id}', [TransactionController::class, 'show'])->name('api.transactions.show');
    Route::put('transactions/{id}', [TransactionController::class, 'update'])->name('api.transactions.update');
    Route::delete('transactions/{id}', [TransactionController::class, 'destroy'])->name('api.transactions.destroy');

    // Subscription routes
    Route::get('subscriptions', [SubscriptionController::class, 'index'])->name('api.subscriptions.index');
    Route::post('subscriptions', [SubscriptionController::class, 'store'])->name('api.subscriptions.store');
    Route::post('subscriptions/select-plan', [SubscriptionController::class, 'selectPlan'])->name('api.subscriptions.select-plan');
    Route::post('subscriptions/add-payment-method', [SubscriptionController::class, 'addPaymentMethodAndStartTrial'])->name('api.subscriptions.add-payment-method');
    Route::post('subscriptions/start-trial-test', [SubscriptionController::class, 'startTrialTest'])->name('api.subscriptions.start-trial-test');
    Route::post('subscriptions/cancel', [SubscriptionController::class, 'cancelSubscription'])->name('api.subscriptions.cancel');
    Route::post('subscriptions/connect-stripe', [SubscriptionController::class, 'connectStripe'])->name('api.subscriptions.connect-stripe');
    Route::post('subscriptions/setup-auto-billing', [SubscriptionController::class, 'setupAutoBilling'])->name('api.subscriptions.setup-auto-billing');
    Route::get('subscriptions/plans', [SubscriptionController::class, 'getPlans'])->name('api.subscriptions.plans');
    Route::get('subscriptions/history', [SubscriptionController::class, 'getHistory'])->name('api.subscriptions.history');
    Route::get('subscriptions/{uuid}', [SubscriptionController::class, 'show'])->name('api.subscriptions.show');
    Route::put('subscriptions/{uuid}', [SubscriptionController::class, 'update'])->name('api.subscriptions.update');
    Route::put('subscriptions/{uuid}/change-cycle', [SubscriptionController::class, 'changeBillingCycle'])->name('api.subscriptions.change-cycle');
    Route::post('subscriptions/{uuid}/pause', [SubscriptionController::class, 'pauseSubscription'])->name('api.subscriptions.pause');
    Route::post('subscriptions/{uuid}/resume', [SubscriptionController::class, 'resumeSubscription'])->name('api.subscriptions.resume');
    Route::delete('subscriptions/{uuid}', [SubscriptionController::class, 'destroy'])->name('api.subscriptions.destroy');

    // Crypto wallet routes
    Route::get('crypto-wallets', [CryptoWalletController::class, 'index'])->name('api.crypto-wallets.index');
    Route::post('crypto-wallets', [CryptoWalletController::class, 'store'])->name('api.crypto-wallets.store');
    Route::post('crypto-wallets/qr-code', [CryptoWalletController::class, 'addFromQrCode'])->name('api.crypto-wallets.add-from-qr');
    Route::get('crypto-wallets/{id}/assets', [CryptoWalletController::class, 'getAssets'])->name('api.crypto-wallets.assets');
    Route::get('crypto-wallets/{id}/transactions', [CryptoWalletController::class, 'getTransactions'])->name('api.crypto-wallets.transactions');
    Route::get('crypto-wallets/{id}', [CryptoWalletController::class, 'show'])->name('api.crypto-wallets.show');
    Route::put('crypto-wallets/{id}', [CryptoWalletController::class, 'update'])->name('api.crypto-wallets.update');
    Route::delete('crypto-wallets/{id}', [CryptoWalletController::class, 'destroy'])->name('api.crypto-wallets.destroy');
    Route::post('crypto-wallets/{id}/sync', [CryptoWalletController::class, 'sync'])->name('api.crypto-wallets.sync');

    // Notification settings routes
    Route::get('notification-settings', [NotificationSettingController::class, 'index'])->name('api.notification-settings.index');
    Route::put('notification-settings', [NotificationSettingController::class, 'update'])->name('api.notification-settings.update');

    // Plaid routes
    Route::get('plaid/link-token', [PlaidController::class, 'getLinkToken'])->name('api.plaid.link-token');
    Route::post('plaid/accounts', [PlaidController::class, 'store'])->name('api.plaid.store');
    Route::get('plaid/accounts', [PlaidController::class, 'getAccounts'])->name('api.plaid.accounts');
    Route::post('plaid/accounts/{id}/set-default', [PlaidController::class, 'setDefault'])->name('api.plaid.set-default');
    Route::post('plaid/accounts/{id}/toggle-payment', [PlaidController::class, 'togglePayment'])->name('api.plaid.toggle-payment');
    Route::delete('plaid/accounts/{id}', [PlaidController::class, 'destroy'])->name('api.plaid.destroy');
    Route::post('plaid/add-dummy-accounts', [PlaidController::class, 'addDummyAccounts'])->name('api.plaid.add-dummy-accounts');

    // Plaid Payment routes
    Route::get('plaid-payment/accounts', [PlaidPaymentController::class, 'getPaymentAccounts'])->name('api.plaid-payment.accounts');
    Route::post('plaid-payment/process', [PlaidPaymentController::class, 'processPayment'])->name('api.plaid-payment.process');
    Route::get('plaid-payment/status/{paymentId}', [PlaidPaymentController::class, 'getPaymentStatus'])->name('api.plaid-payment.status');


    // Reports routes
    Route::get('reports', [ReportController::class, 'index'])->name('api.reports.index');
    Route::post('reports', [ReportController::class, 'store'])->name('api.reports.store');
    Route::get('reports/{id}', [ReportController::class, 'show'])->name('api.reports.show');
    Route::delete('reports/{id}', [ReportController::class, 'destroy'])->name('api.reports.destroy');
    Route::get('reports/{id}/download', [ReportController::class, 'download'])->name('api.reports.download');
    Route::post('reports/{id}/regenerate', [ReportController::class, 'regenerate'])->name('api.reports.regenerate');
});

// Webhooks (public routes)
Route::post('webhook/stripe', [SubscriptionController::class, 'handleWebhook'])->name('api.webhook.stripe');
// Route::post('webhook/stripe-checkout', [App\Http\Controllers\API\StripeCheckoutController::class, 'handleWebhook'])->name('api.webhook.stripe-checkout'); // DISABLED
Route::post('webhook/plaid', [PlaidController::class, 'handleWebhook'])->name('api.webhook.plaid');

// Stripe payment success (public route - no auth required)
Route::get('payment-simple-success', [App\Http\Controllers\API\SimpleStripeController::class, 'handleSuccess'])->name('api.packages.simple.success');
