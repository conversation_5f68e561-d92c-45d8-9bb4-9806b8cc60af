<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\EmailVerificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class EmailVerificationController extends Controller
{
    protected $emailVerificationService;

    public function __construct(EmailVerificationService $emailVerificationService)
    {
        $this->emailVerificationService = $emailVerificationService;
    }

    /**
     * Send email verification code
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function sendVerificationCode(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:users,email',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            if ($user->email_verified_at) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email is already verified'
                ], 400);
            }

            $result = $this->emailVerificationService->sendEmailVerificationCode($user);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'expires_at' => $result['expires_at'],
                        'attempts_remaining' => $result['attempts_remaining']
                    ]
                ], 200);
            } else {
                $statusCode = match($result['error_code'] ?? '') {
                    'RATE_LIMITED' => 429,
                    'MAX_ATTEMPTS_EXCEEDED' => 429,
                    default => 500
                };

                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? 'UNKNOWN_ERROR'
                ], $statusCode);
            }

        } catch (\Exception $e) {
            Log::error('Email verification send failed', [
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification code. Please try again.'
            ], 500);
        }
    }

    /**
     * Verify email with code
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyEmail(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:users,email',
                'code' => 'required|string|size:6',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            if ($user->email_verified_at) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email is already verified'
                ], 400);
            }

            $result = $this->emailVerificationService->verifyEmailCode($user, $request->code);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'user' => [
                            'id' => $result['user']->id,
                            'name' => $result['user']->name,
                            'email' => $result['user']->email,
                            'email_verified_at' => $result['user']->email_verified_at,
                        ]
                    ]
                ], 200);
            } else {
                $statusCode = match($result['error_code'] ?? '') {
                    'CODE_EXPIRED' => 410,
                    'INVALID_CODE' => 400,
                    'MAX_ATTEMPTS_EXCEEDED' => 429,
                    default => 500
                };

                $response = [
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? 'UNKNOWN_ERROR'
                ];

                if (isset($result['attempts_remaining'])) {
                    $response['attempts_remaining'] = $result['attempts_remaining'];
                }

                return response()->json($response, $statusCode);
            }

        } catch (\Exception $e) {
            Log::error('Email verification failed', [
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Email verification failed. Please try again.'
            ], 500);
        }
    }

    /**
     * Resend verification code
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function resendVerificationCode(Request $request): JsonResponse
    {
        // This is the same as sendVerificationCode, but with a different endpoint
        return $this->sendVerificationCode($request);
    }

    /**
     * Check verification status
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function checkVerificationStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:users,email',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'email_verified' => !is_null($user->email_verified_at),
                    'email_verified_at' => $user->email_verified_at,
                    'verification_attempts' => $user->email_verification_attempts ?? 0,
                    'max_attempts' => EmailVerificationService::MAX_ATTEMPTS,
                    'attempts_remaining' => EmailVerificationService::MAX_ATTEMPTS - ($user->email_verification_attempts ?? 0),
                    'has_pending_code' => !is_null($user->email_verification_code),
                    'code_expires_at' => $user->email_verification_code_expires_at,
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('Check verification status failed', [
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check verification status.'
            ], 500);
        }
    }
}
