# 🔐 PocketWatch Google OAuth Testing Guide

## 🚀 **Quick Setup for Testing**

### **Step 1: Environment Configuration**

Add these to your `.env` file:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://127.0.0.1:8000/api/auth/google/callback

# For testing, you can use these test values temporarily:
GOOGLE_CLIENT_ID=*********-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz
```

### **Step 2: Install Required Packages**

```bash
composer require laravel/socialite
composer require google/apiclient
```

## 🧪 **Postman Testing Collection**

### **Test 1: Get Google OAuth URL**

**Request:**
```
GET http://127.0.0.1:8000/api/auth/google
```

**Expected Response:**
```json
{
    "success": true,
    "redirect_url": "https://accounts.google.com/oauth/authorize?client_id=*********&redirect_uri=http://127.0.0.1:8000/api/auth/google/callback&scope=openid+email+profile&response_type=code",
    "message": "Redirect to Google OAuth"
}
```

**What to do:**
1. Copy the `redirect_url` from the response
2. Open it in your browser
3. Sign in with Google
4. You'll be redirected back with an authorization code

### **Test 2: Handle Google OAuth Callback**

**Request:**
```
GET http://127.0.0.1:8000/api/auth/google/callback?code=AUTHORIZATION_CODE_FROM_GOOGLE
```

**Expected Response:**
```json
{
    "success": true,
    "message": "Account created successfully via Google",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "avatar": "https://lh3.googleusercontent.com/a/default-user=s96-c",
        "provider": "google",
        "subscription_tier": "none",
        "trial_started_at": null,
        "trial_expired": false,
        "trial_days_remaining": null,
        "email_verified_at": "2024-12-19T10:00:00Z",
        "created_at": "2024-12-19T10:00:00Z"
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer"
}
```

**Save the token** for subsequent requests!

### **Test 3: Google ID Token Authentication (Mobile)**

**Request:**
```
POST http://127.0.0.1:8000/api/auth/google/token
Content-Type: application/json
```

**Body:**
```json
{
    "id_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjdkYzBkNjI5..."
}
```

**Note:** You need a real Google ID token for this to work. Get it from Google Sign-In SDK.

### **Test 4: Check User Profile**

**Request:**
```
GET http://127.0.0.1:8000/api/me
Authorization: Bearer YOUR_TOKEN_FROM_GOOGLE_AUTH
```

**Expected Response:**
```json
{
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "avatar": "https://lh3.googleusercontent.com/a/default-user=s96-c",
    "provider": "google",
    "subscription_tier": "none",
    "trial_started_at": null,
    "trial_expired": false,
    "trial_days_remaining": null,
    "email_verified_at": "2024-12-19T10:00:00Z"
}
```

### **Test 5: Purchase Package with Google Account**

**Request:**
```
POST http://127.0.0.1:8000/api/packages/purchase-test
Authorization: Bearer YOUR_TOKEN_FROM_GOOGLE_AUTH
Content-Type: application/json
```

**Body:**
```json
{
    "package_id": "base_monthly"
}
```

**Expected Response:**
```json
{
    "success": true,
    "message": "TEST MODE: Package purchased successfully! Your 7-day trial has started.",
    "data": {
        "subscription": {
            "id": 1,
            "user_id": 1,
            "name": "Base Monthly (TEST)",
            "subscription_tier": "base",
            "trial_ends_at": "2024-12-26T10:00:00Z"
        },
        "trial_info": {
            "trial_started": true,
            "trial_days_remaining": 7,
            "test_mode": true
        }
    }
}
```

### **Test 6: Unlink Google Account**

**Request:**
```
POST http://127.0.0.1:8000/api/auth/google/unlink
Authorization: Bearer YOUR_TOKEN_FROM_GOOGLE_AUTH
```

**Expected Response (if no password set):**
```json
{
    "success": false,
    "message": "Cannot unlink Google account. Please set a password first.",
    "action_required": "set_password"
}
```

## 🔧 **Setting Up Real Google OAuth**

### **Step 1: Google Cloud Console Setup**

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized redirect URIs:
   - `http://127.0.0.1:8000/api/auth/google/callback`
   - `http://localhost:8000/api/auth/google/callback`
   - Your production domain callback URL

### **Step 2: Get Credentials**

1. Copy the **Client ID** and **Client Secret**
2. Add them to your `.env` file:

```env
GOOGLE_CLIENT_ID=*********-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz
GOOGLE_REDIRECT_URI=http://127.0.0.1:8000/api/auth/google/callback
```

### **Step 3: Test the Flow**

1. **Get OAuth URL:** `GET /api/auth/google`
2. **Open URL in browser:** User signs in with Google
3. **Handle callback:** Google redirects with authorization code
4. **Get user token:** Use the callback endpoint to get auth token
5. **Use API:** Make authenticated requests with the token

## 🎯 **Complete Testing Scenarios**

### **Scenario 1: New User Registration**
1. User doesn't exist in system
2. Signs in with Google
3. New account created automatically
4. Email verified automatically
5. Can purchase packages and use features

### **Scenario 2: Existing User Account Linking**
1. User exists with email `<EMAIL>`
2. Signs in with Google using same email
3. Google account linked to existing user
4. Retains existing subscription/trial status
5. Can use Google sign-in for future logins

### **Scenario 3: Google User Purchasing Package**
1. User signs in with Google
2. Views available packages
3. Purchases package (test mode)
4. Trial starts immediately
5. Can access all features

### **Scenario 4: Account Security**
1. Google user tries to unlink account
2. System requires password to be set first
3. Prevents account lockout
4. Maintains security standards

## 🚨 **Common Issues & Solutions**

### **Issue 1: Invalid Client ID**
```json
{
    "success": false,
    "message": "Google authentication failed: Invalid client_id"
}
```
**Solution:** Check your `GOOGLE_CLIENT_ID` in `.env`

### **Issue 2: Redirect URI Mismatch**
```json
{
    "error": "redirect_uri_mismatch"
}
```
**Solution:** Add your callback URL to Google Cloud Console

### **Issue 3: Invalid Authorization Code**
```json
{
    "success": false,
    "message": "Google authentication failed: Invalid authorization code"
}
```
**Solution:** Authorization codes expire quickly, get a fresh one

### **Issue 4: Missing Scopes**
**Solution:** Ensure you're requesting `openid email profile` scopes

## 🎉 **Success Indicators**

✅ **OAuth URL Generation:** Returns valid Google OAuth URL
✅ **User Creation:** New users created with Google profile data
✅ **Account Linking:** Existing users linked by email
✅ **Token Generation:** Valid auth tokens returned
✅ **Email Verification:** Automatic verification for Google users
✅ **Package Purchase:** Google users can purchase packages
✅ **Security:** Proper unlinking restrictions

## 📱 **Mobile App Integration**

For mobile apps, use the Google Sign-In SDK to get ID tokens:

**Android:**
```kotlin
// Get Google ID token
val idToken = googleSignInAccount.idToken
// Send to your API
api.authenticateWithGoogle(idToken)
```

**iOS:**
```swift
// Get Google ID token
let idToken = user.authentication.idToken
// Send to your API
api.authenticateWithGoogle(idToken: idToken)
```

**API Call:**
```
POST /api/auth/google/token
{
    "id_token": "eyJhbGciOiJSUzI1NiIs..."
}
```

This testing guide covers all aspects of Google OAuth integration with your PocketWatch API! 🚀
