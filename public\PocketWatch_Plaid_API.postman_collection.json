{"info": {"_postman_id": "b5e6f8d2-3f4a-4b5c-9c7d-8e9a0b1c2d3e", "name": "PocketWatch Plaid API", "description": "A collection for testing the PocketWatch Plaid API integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "if (jsonData.token) {", "    pm.environment.set(\"token\", jsonData.token);", "    console.log(\"Token saved to environment\");", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"your-password\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}, "description": "Authenticate and get a token"}, "response": []}]}, {"name": "Plaid Accounts", "item": [{"name": "Get Accounts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/plaid/accounts", "host": ["{{base_url}}"], "path": ["api", "plaid", "accounts"]}, "description": "Get all Plaid accounts for the authenticated user"}, "response": []}, {"name": "Add Dummy Accounts", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/plaid/add-dummy-accounts", "host": ["{{base_url}}"], "path": ["api", "plaid", "add-dummy-accounts"]}, "description": "Add dummy Plaid accounts for testing"}, "response": []}, {"name": "Get Link Token", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/plaid/link-token", "host": ["{{base_url}}"], "path": ["api", "plaid", "link-token"]}, "description": "Get a Plaid link token for the authenticated user"}, "response": []}, {"name": "<PERSON> De<PERSON>ult Account", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/plaid/accounts/1/set-default", "host": ["{{base_url}}"], "path": ["api", "plaid", "accounts", "1", "set-default"]}, "description": "Set a Plaid account as the default account"}, "response": []}, {"name": "Toggle Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/plaid/accounts/1/toggle-payment", "host": ["{{base_url}}"], "path": ["api", "plaid", "accounts", "1", "toggle-payment"]}, "description": "Toggle payment capability for a Plaid account"}, "response": []}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/plaid/accounts/1", "host": ["{{base_url}}"], "path": ["api", "plaid", "accounts", "1"]}, "description": "Delete a Plaid account"}, "response": []}]}, {"name": "Plaid Payments", "item": [{"name": "Get Payment Accounts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/plaid-payment/accounts", "host": ["{{base_url}}"], "path": ["api", "plaid-payment", "accounts"]}, "description": "Get payment-enabled Plaid accounts"}, "response": []}, {"name": "Process Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"account_id\": 1,\n    \"package_id\": \"premium_monthly\",\n    \"amount\": 10.00\n}"}, "url": {"raw": "{{base_url}}/api/plaid-payment/process", "host": ["{{base_url}}"], "path": ["api", "plaid-payment", "process"]}, "description": "Process a payment using a Plaid account"}, "response": []}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/plaid-payment/status/550e8400-e29b-41d4-a716-************", "host": ["{{base_url}}"], "path": ["api", "plaid-payment", "status", "550e8400-e29b-41d4-a716-************"]}, "description": "Get the status of a payment"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}]}