<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Summary Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .header p {
            font-size: 14px;
            color: #666;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            font-size: 18px;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .summary-box {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .summary-item {
            display: inline-block;
            width: 30%;
            margin-right: 3%;
            vertical-align: top;
        }
        .summary-item:last-child {
            margin-right: 0;
        }
        .summary-item h3 {
            font-size: 14px;
            margin-bottom: 5px;
        }
        .summary-item p {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .income {
            color: #28a745;
        }
        .expense {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Summary Report</h1>
        <p>{{ $report->name }}</p>
        <p>Period: {{ $report->start_date->format('M d, Y') }} - {{ $report->end_date->format('M d, Y') }}</p>
        <p>Generated: {{ now()->format('M d, Y H:i:s') }}</p>
    </div>

    <div class="section">
        <h2>Financial Overview</h2>
        
        <div class="summary-box">
            <div class="summary-item">
                <h3>Total Income</h3>
                <p class="income">{{ $transactions->first()->currency ?? 'USD' }} {{ number_format($total_income, 2) }}</p>
            </div>
            <div class="summary-item">
                <h3>Total Expense</h3>
                <p class="expense">{{ $transactions->first()->currency ?? 'USD' }} {{ number_format($total_expense, 2) }}</p>
            </div>
            <div class="summary-item">
                <h3>Net Amount</h3>
                <p class="{{ $net_amount >= 0 ? 'income' : 'expense' }}">
                    {{ $transactions->first()->currency ?? 'USD' }} {{ number_format($net_amount, 2) }}
                </p>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Bins Summary</h2>
        
        <div class="summary-box">
            <div class="summary-item">
                <h3>Total Bins</h3>
                <p>{{ $total_bins }}</p>
            </div>
            <div class="summary-item">
                <h3>Total Sub-Bins</h3>
                <p>{{ $total_sub_bins }}</p>
            </div>
            <div class="summary-item">
                <h3>Total Amount</h3>
                <p>{{ $bins->first()->currency ?? 'USD' }} {{ number_format($total_bin_amount, 2) }}</p>
            </div>
        </div>
        
        @if($bins->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>Bin Name</th>
                        <th>Type</th>
                        <th>Current Amount</th>
                        <th>Threshold</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($bins->take(5) as $bin)
                        <tr>
                            <td><strong>{{ $bin->name }}</strong></td>
                            <td>{{ ucfirst($bin->type) }}</td>
                            <td class="text-right">{{ $bin->currency }} {{ number_format($bin->current_amount, 2) }}</td>
                            <td>
                                {{ $bin->currency }} {{ number_format($bin->threshold_min, 2) }} - 
                                {{ $bin->threshold_max ? $bin->currency . ' ' . number_format($bin->threshold_max, 2) : '∞' }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            
            @if($bins->count() > 5)
                <p class="text-center">Showing 5 of {{ $bins->count() }} bins</p>
            @endif
        @else
            <p class="text-center">No bins found.</p>
        @endif
    </div>

    <div class="section">
        <h2>Recent Transactions</h2>
        
        @if($transactions->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>Category</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($transactions->take(10) as $transaction)
                        <tr>
                            <td>{{ $transaction->transaction_date->format('Y-m-d') }}</td>
                            <td>{{ $transaction->description }}</td>
                            <td>{{ ucfirst($transaction->transaction_type) }}</td>
                            <td class="text-right {{ $transaction->transaction_type == 'income' ? 'income' : 'expense' }}">
                                {{ $transaction->currency }} {{ number_format($transaction->amount, 2) }}
                            </td>
                            <td>{{ $transaction->category ?: 'N/A' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            
            @if($transactions->count() > 10)
                <p class="text-center">Showing 10 of {{ $transactions->count() }} transactions</p>
            @endif
        @else
            <p class="text-center">No transactions found for the selected period.</p>
        @endif
    </div>

    <div class="footer">
        <p>PocketWatch Financial Management System</p>
        <p>This report was generated automatically. Please contact support if you have any questions.</p>
    </div>
</body>
</html>
