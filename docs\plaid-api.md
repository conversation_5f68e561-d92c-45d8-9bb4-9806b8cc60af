# Plaid API Documentation

This document provides information about the Plaid API endpoints available in the PocketWatch application.

## Authentication

All API requests require authentication using a Bear<PERSON> token.

```
Authorization: Bearer {token}
```

## Endpoints

### Get Link Token

Get a Plaid link token to initialize the Plaid Link flow.

- **URL**: `/api/plaid/link-token`
- **Method**: `GET`
- **Auth Required**: Yes

#### Success Response

```json
{
  "success": true,
  "link_token": "link-sandbox-abc123..."
}
```

### Store Plaid Account

Store Plaid account information after successful account linking.

- **URL**: `/api/plaid/accounts`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### Request Body

```json
{
  "public_token": "public-sandbox-abc123...",
  "institution_id": "ins_123",
  "institution_name": "Bank of America",
  "accounts": [
    {
      "id": "account_123",
      "name": "Checking",
      "type": "depository",
      "subtype": "checking",
      "mask": "1234"
    }
  ]
}
```

#### Success Response

```json
{
  "success": true,
  "message": "Bank accounts linked successfully",
  "accounts": [
    {
      "id": 1,
      "institution_name": "Bank of America",
      "account_name": "Checking",
      "account_type": "depository",
      "account_subtype": "checking",
      "account_mask": "1234",
      "is_default": true,
      "is_payment_enabled": true,
      "is_active": true
    }
  ]
}
```

### Get Plaid Accounts

Get the user's linked Plaid accounts.

- **URL**: `/api/plaid/accounts`
- **Method**: `GET`
- **Auth Required**: Yes

#### Success Response

```json
{
  "success": true,
  "accounts": [
    {
      "id": 1,
      "institution_name": "Bank of America",
      "account_name": "Checking",
      "account_type": "depository",
      "account_subtype": "checking",
      "account_mask": "1234",
      "is_default": true,
      "is_payment_enabled": true,
      "is_active": true,
      "last_synced_at": "2023-06-10T12:00:00Z"
    }
  ]
}
```

### Set Default Account

Set a Plaid account as the default account.

- **URL**: `/api/plaid/accounts/{id}/set-default`
- **Method**: `POST`
- **Auth Required**: Yes

#### URL Parameters

- `id`: The ID of the Plaid account to set as default

#### Success Response

```json
{
  "success": true,
  "message": "Default bank account updated successfully",
  "account": {
    "id": 1,
    "institution_name": "Bank of America",
    "account_name": "Checking",
    "is_default": true
  }
}
```

### Toggle Payment

Enable or disable payment for a Plaid account.

- **URL**: `/api/plaid/accounts/{id}/toggle-payment`
- **Method**: `POST`
- **Auth Required**: Yes

#### URL Parameters

- `id`: The ID of the Plaid account to toggle payment for

#### Success Response

```json
{
  "success": true,
  "message": "Payment enabled for this account successfully",
  "account": {
    "id": 1,
    "institution_name": "Bank of America",
    "account_name": "Checking",
    "is_payment_enabled": true
  }
}
```

### Delete Account

Delete (unlink) a Plaid account.

- **URL**: `/api/plaid/accounts/{id}`
- **Method**: `DELETE`
- **Auth Required**: Yes

#### URL Parameters

- `id`: The ID of the Plaid account to delete

#### Success Response

```json
{
  "success": true,
  "message": "Bank account unlinked successfully"
}
```

### Get Payment Accounts

Get the user's payment-enabled Plaid accounts.

- **URL**: `/api/plaid-payment/accounts`
- **Method**: `GET`
- **Auth Required**: Yes

#### Success Response

```json
{
  "success": true,
  "accounts": [
    {
      "id": 1,
      "institution_name": "Bank of America",
      "account_name": "Checking",
      "account_type": "depository",
      "account_subtype": "checking",
      "account_mask": "1234",
      "is_default": true,
      "is_payment_enabled": true,
      "is_active": true
    }
  ]
}
```

### Process Payment

Process a payment using a Plaid account.

- **URL**: `/api/plaid-payment/process`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content-Type**: `application/json`

#### Request Body

```json
{
  "plaid_account_id": 1,
  "subscription_tier": "premium",
  "billing_cycle": "monthly"
}
```

#### Success Response

```json
{
  "success": true,
  "message": "Payment processed successfully",
  "subscription": {
    "id": 1,
    "name": "Premium Monthly",
    "subscription_tier": "premium",
    "billing_cycle": "monthly",
    "price": 10.00,
    "currency": "USD",
    "trial_ends_at": "2023-06-17T12:00:00Z"
  },
  "payment_id": "payment_123",
  "payment_reference": "PW-ABC123"
}
```

## Webhook

The Plaid webhook endpoint receives notifications from Plaid about account updates, transactions, and payment status changes.

- **URL**: `/api/webhook/plaid`
- **Method**: `POST`
- **Auth Required**: No (Plaid sends a signature header for verification)

### Webhook Types

- `TRANSACTIONS`: Transaction updates
- `ITEM`: Item updates (e.g., errors, pending expiration)
- `AUTH`: Auth updates
- `PAYMENT_INITIATION`: Payment status updates

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "success": false,
  "message": "Error message",
  "errors": {
    "field_name": [
      "Error description"
    ]
  }
}
```

Common error codes:
- `422`: Validation error
- `404`: Resource not found
- `403`: Forbidden
- `500`: Server error
