<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;

class StripeWebController extends Controller
{
    /**
     * Redirect directly to Stripe Checkout.
     */
    public function showStripeRedirect(Request $request)
    {
        $userId = $request->query('user_id');
        $planId = $request->query('plan_id');
        $price = $request->query('price');
        $currency = $request->query('currency', 'USD');
        $planName = $request->query('plan_name');

        // Validate required parameters
        if (!$userId || !$planId || !$price) {
            return redirect()->back()->with('error', 'Missing required payment parameters');
        }

        // Get user details
        $user = User::find($userId);
        if (!$user) {
            return redirect()->back()->with('error', 'User not found');
        }

        // Get package details
        $packageDetails = $this->getPackageDetails($planId);
        if (!$packageDetails) {
            return redirect()->back()->with('error', 'Package not found');
        }

        // Create Stripe Checkout Session
        try {
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

            $session = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => strtolower($currency),
                        'product_data' => [
                            'name' => $planName ?: $packageDetails['name'],
                            'description' => $packageDetails['description'] ?? 'Monthly subscription with 7-day free trial',
                            'images' => ['https://via.placeholder.com/300x200?text=PocketWatch'],
                        ],
                        'unit_amount' => $price * 100, // Convert to cents
                        'recurring' => [
                            'interval' => 'month',
                        ],
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => url('/api/payment-simple-success') . '?user_id=' . $userId . '&plan_id=' . $planId . '&price=' . $price . '&session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('stripe.cancel', [
                    'user_id' => $userId,
                    'plan_id' => $planId
                ]),
                'customer_email' => $user->email,
                'metadata' => [
                    'user_id' => $userId,
                    'plan_id' => $planId,
                    'plan_name' => $planName ?: $packageDetails['name'],
                    'trial_days' => '7',
                ],
                'subscription_data' => [
                    'trial_period_days' => 7,
                    'metadata' => [
                        'user_id' => $userId,
                        'plan_id' => $planId,
                    ],
                ],
                'allow_promotion_codes' => true,
                'billing_address_collection' => 'auto',
            ]);

            // Redirect directly to Stripe Checkout
            return redirect($session->url);

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to create Stripe session: ' . $e->getMessage());
        }
    }

    /**
     * Handle successful payment.
     */
    public function handleSuccess(Request $request)
    {
        $userId = $request->query('user_id');
        $planId = $request->query('plan_id');
        $price = $request->query('price');
        $sessionId = $request->query('session_id');

        if (!$userId || !$planId || !$price) {
            return view('payment-result', [
                'success' => false,
                'message' => 'Missing required parameters',
                'error' => 'Invalid payment callback'
            ]);
        }

        $user = User::find($userId);
        if (!$user) {
            return view('payment-result', [
                'success' => false,
                'message' => 'User not found',
                'error' => 'Invalid user ID'
            ]);
        }

        $packageDetails = $this->getPackageDetails($planId);
        if (!$packageDetails) {
            return view('payment-result', [
                'success' => false,
                'message' => 'Package not found',
                'error' => 'Invalid package ID'
            ]);
        }

        try {
            // Create subscription record
            $subscription = Subscription::create([
                'user_id' => $userId,
                'name' => $packageDetails['name'],
                'stripe_id' => $sessionId ?: 'manual_' . time(),
                'stripe_status' => 'active',
                'stripe_price' => 'price_' . $planId,
                'subscription_tier' => $packageDetails['tier'],
                'billing_cycle' => $packageDetails['billing_cycle'],
                'price' => $price,
                'currency' => 'USD',
                'features' => $packageDetails['features'],
                'trial_ends_at' => now()->addDays(7),
            ]);

            // Update user
            $user->update([
                'subscription_tier' => 'trial',
                'trial_started_at' => now(),
            ]);

            return view('payment-result', [
                'success' => true,
                'message' => 'Payment successful! Your 7-day trial has started.',
                'user' => $user,
                'subscription' => $subscription,
                'trialEndsAt' => now()->addDays(7)->format('M j, Y')
            ]);

        } catch (\Exception $e) {
            return view('payment-result', [
                'success' => false,
                'message' => 'Failed to process payment',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle cancelled payment.
     */
    public function handleCancel(Request $request)
    {
        $userId = $request->query('user_id');
        $planId = $request->query('plan_id');

        return view('payment-result', [
            'success' => false,
            'message' => 'Payment was cancelled',
            'cancelled' => true,
            'userId' => $userId,
            'planId' => $planId
        ]);
    }

    /**
     * Get package details by ID.
     */
    private function getPackageDetails($packageId)
    {
        $packages = [
            'base_monthly' => [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'tier' => 'base',
                'billing_cycle' => 'monthly',
                'price' => 5.00,
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Basic insights',
                    'Hierarchical sub-bins (3 levels)'
                ]
            ],
            'premium_monthly' => [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'tier' => 'premium',
                'billing_cycle' => 'monthly',
                'price' => 10.00,
                'features' => [
                    'All Base features',
                    'Unlimited hierarchical sub-bins',
                    'Crypto wallet integration',
                    'Advanced AI insights'
                ]
            ]
        ];

        return $packages[$packageId] ?? null;
    }
}
