<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Bin;
use App\Models\SubBin;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TransactionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = $request->user()->transactions();

        // Filter by bin_id if provided
        if ($request->has('bin_id')) {
            $query->where('bin_id', $request->bin_id);
        }

        // Filter by sub_bin_id if provided
        if ($request->has('sub_bin_id')) {
            $query->where('sub_bin_id', $request->sub_bin_id);
        }

        // Filter by transaction_type if provided
        if ($request->has('type')) {
            $query->where('transaction_type', $request->type);
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('transaction_date', [$request->start_date, $request->end_date]);
        }

        // Sort by date (default: newest first)
        $sortDirection = $request->has('sort_direction') && $request->sort_direction === 'asc' ? 'asc' : 'desc';
        $query->orderBy('transaction_date', $sortDirection);

        // Paginate results
        $perPage = $request->has('per_page') ? (int) $request->per_page : 15;
        $transactions = $query->paginate($perPage);

        return response()->json([
            'transactions' => $transactions,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'bin_id' => 'nullable|exists:bins,id',
            'sub_bin_id' => 'nullable|exists:sub_bins,id',
            'transaction_type' => 'required|string|in:income,expense,transfer',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'nullable|string|max:10',
            'description' => 'nullable|string',
            'category' => 'nullable|string',
            'payment_method' => 'nullable|string',
            'transaction_date' => 'required|date',
            'transaction_time' => 'nullable|date_format:H:i:s',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Validate bin belongs to user
        if ($request->has('bin_id')) {
            $bin = Bin::find($request->bin_id);
            if (!$bin || $bin->user_id !== $request->user()->id) {
                return response()->json([
                    'message' => 'Bin not found or does not belong to user',
                ], 404);
            }
        }

        // Validate sub_bin belongs to bin
        if ($request->has('sub_bin_id')) {
            $subBin = SubBin::find($request->sub_bin_id);
            if (!$subBin || ($request->has('bin_id') && $subBin->bin_id != $request->bin_id)) {
                return response()->json([
                    'message' => 'Sub-bin not found or does not belong to the specified bin',
                ], 404);
            }
        }

        $transaction = new Transaction($request->all());
        $transaction->user_id = $request->user()->id;
        $transaction->source = 'manual';
        $transaction->status = 'completed';

        // Set transaction_time if not provided
        if (!$request->has('transaction_time')) {
            $transaction->transaction_time = now()->format('H:i:s');
        }

        $transaction->save();

        // Update bin and sub-bin balances
        $this->updateBalances($transaction);

        // Update user's cumulative balance
        $request->user()->calculateCumulativeBalance();

        return response()->json([
            'message' => 'Transaction created successfully',
            'transaction' => $transaction,
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, string $id)
    {
        $transaction = Transaction::find($id);

        if (!$transaction || $transaction->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Transaction not found',
            ], 404);
        }

        return response()->json([
            'transaction' => $transaction,
        ]);
    }

    /**
     * Update bin and sub-bin balances based on transaction.
     *
     * @param  \App\Models\Transaction  $transaction
     * @return void
     */
    private function updateBalances(Transaction $transaction)
    {
        // Update bin balance if transaction is associated with a bin
        if ($transaction->bin_id) {
            $bin = Bin::find($transaction->bin_id);
            if ($bin) {
                if ($transaction->transaction_type === 'income') {
                    $bin->current_amount += $transaction->amount;
                } elseif ($transaction->transaction_type === 'expense') {
                    $bin->current_amount -= $transaction->amount;
                }
                $bin->save();
            }
        }

        // Update sub-bin balance if transaction is associated with a sub-bin
        if ($transaction->sub_bin_id) {
            $subBin = SubBin::find($transaction->sub_bin_id);
            if ($subBin) {
                if ($transaction->transaction_type === 'income') {
                    $subBin->current_amount += $transaction->amount;
                } elseif ($transaction->transaction_type === 'expense') {
                    $subBin->current_amount -= $transaction->amount;
                }
                $subBin->save();
            }
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $transaction = Transaction::find($id);

        if (!$transaction || $transaction->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Transaction not found',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'bin_id' => 'nullable|exists:bins,id',
            'sub_bin_id' => 'nullable|exists:sub_bins,id',
            'transaction_type' => 'sometimes|required|string|in:income,expense,transfer',
            'amount' => 'sometimes|required|numeric|min:0.01',
            'currency' => 'nullable|string|max:10',
            'description' => 'nullable|string',
            'category' => 'nullable|string',
            'payment_method' => 'nullable|string',
            'transaction_date' => 'sometimes|required|date',
            'transaction_time' => 'nullable|date_format:H:i:s',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Validate bin belongs to user
        if ($request->has('bin_id')) {
            $bin = Bin::find($request->bin_id);
            if (!$bin || $bin->user_id !== $request->user()->id) {
                return response()->json([
                    'message' => 'Bin not found or does not belong to user',
                ], 404);
            }
        }

        // Validate sub_bin belongs to bin
        if ($request->has('sub_bin_id')) {
            $subBin = SubBin::find($request->sub_bin_id);
            if (!$subBin || ($request->has('bin_id') && $subBin->bin_id != $request->bin_id)) {
                return response()->json([
                    'message' => 'Sub-bin not found or does not belong to the specified bin',
                ], 404);
            }
        }

        // Reverse previous balance changes
        $this->reverseBalanceChanges($transaction);

        // Update transaction
        $transaction->update($request->all());

        // Apply new balance changes
        $this->updateBalances($transaction);

        // Update user's cumulative balance
        $request->user()->calculateCumulativeBalance();

        return response()->json([
            'message' => 'Transaction updated successfully',
            'transaction' => $transaction,
        ]);
    }

    /**
     * Reverse balance changes from a transaction.
     *
     * @param  \App\Models\Transaction  $transaction
     * @return void
     */
    private function reverseBalanceChanges(Transaction $transaction)
    {
        // Reverse bin balance changes
        if ($transaction->bin_id) {
            $bin = Bin::find($transaction->bin_id);
            if ($bin) {
                if ($transaction->transaction_type === 'income') {
                    $bin->current_amount -= $transaction->amount;
                } elseif ($transaction->transaction_type === 'expense') {
                    $bin->current_amount += $transaction->amount;
                }
                $bin->save();
            }
        }

        // Reverse sub-bin balance changes
        if ($transaction->sub_bin_id) {
            $subBin = SubBin::find($transaction->sub_bin_id);
            if ($subBin) {
                if ($transaction->transaction_type === 'income') {
                    $subBin->current_amount -= $transaction->amount;
                } elseif ($transaction->transaction_type === 'expense') {
                    $subBin->current_amount += $transaction->amount;
                }
                $subBin->save();
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, string $id)
    {
        $transaction = Transaction::find($id);

        if (!$transaction || $transaction->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Transaction not found',
            ], 404);
        }

        // Reverse balance changes before deleting
        $this->reverseBalanceChanges($transaction);

        $transaction->delete();

        return response()->json([
            'message' => 'Transaction deleted successfully',
        ]);
    }

    /**
     * Get transactions by bin.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $binId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByBin(Request $request, string $binId)
    {
        // Verify bin belongs to user
        $bin = Bin::find($binId);
        if (!$bin || $bin->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Bin not found or does not belong to user',
            ], 404);
        }

        $query = Transaction::where('bin_id', $binId)
            ->where('user_id', $request->user()->id);

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('transaction_date', [$request->start_date, $request->end_date]);
        }

        // Filter by transaction_type if provided
        if ($request->has('type')) {
            $query->where('transaction_type', $request->type);
        }

        // Sort by date (default: newest first)
        $sortDirection = $request->has('sort_direction') && $request->sort_direction === 'asc' ? 'asc' : 'desc';
        $query->orderBy('transaction_date', $sortDirection);

        // Paginate results
        $perPage = $request->has('per_page') ? (int) $request->per_page : 15;
        $transactions = $query->paginate($perPage);

        return response()->json([
            'bin' => $bin,
            'transactions' => $transactions,
        ]);
    }

    /**
     * Get transactions by category.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $category
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByCategory(Request $request, string $category)
    {
        $query = Transaction::where('category', $category)
            ->where('user_id', $request->user()->id);

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('transaction_date', [$request->start_date, $request->end_date]);
        }

        // Filter by transaction_type if provided
        if ($request->has('type')) {
            $query->where('transaction_type', $request->type);
        }

        // Sort by date (default: newest first)
        $sortDirection = $request->has('sort_direction') && $request->sort_direction === 'asc' ? 'asc' : 'desc';
        $query->orderBy('transaction_date', $sortDirection);

        // Paginate results
        $perPage = $request->has('per_page') ? (int) $request->per_page : 15;
        $transactions = $query->paginate($perPage);

        return response()->json([
            'category' => $category,
            'transactions' => $transactions,
        ]);
    }

    /**
     * Get transaction statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        $userId = $request->user()->id;
        $period = $request->get('period', 'month');
        $startDate = null;
        $endDate = null;

        // Determine date range based on period
        switch ($period) {
            case 'day':
                $startDate = now()->startOfDay();
                $endDate = now()->endOfDay();
                break;
            case 'week':
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                break;
            case 'month':
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                break;
            case 'year':
                $startDate = now()->startOfYear();
                $endDate = now()->endOfYear();
                break;
            case 'custom':
                $startDate = $request->has('start_date') ? $request->start_date : now()->subMonth();
                $endDate = $request->has('end_date') ? $request->end_date : now();
                break;
            default:
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
        }

        // Get total income
        $totalIncome = Transaction::where('user_id', $userId)
            ->where('transaction_type', 'income')
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->sum('amount');

        // Get total expense
        $totalExpense = Transaction::where('user_id', $userId)
            ->where('transaction_type', 'expense')
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->sum('amount');

        // Get breakdown by category
        $byCategory = Transaction::where('user_id', $userId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->selectRaw('category, transaction_type, SUM(amount) as total')
            ->groupBy('category', 'transaction_type')
            ->get()
            ->groupBy('category');

        // Get breakdown by bin
        $byBin = Transaction::where('user_id', $userId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->whereNotNull('bin_id')
            ->selectRaw('bin_id, transaction_type, SUM(amount) as total')
            ->groupBy('bin_id', 'transaction_type')
            ->with('bin:id,name')
            ->get()
            ->groupBy('bin_id');

        // Get daily totals for chart
        $dailyTotals = Transaction::where('user_id', $userId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->selectRaw('DATE(transaction_date) as date, transaction_type, SUM(amount) as total')
            ->groupBy('date', 'transaction_type')
            ->orderBy('date')
            ->get()
            ->groupBy('date');

        return response()->json([
            'period' => $period,
            'date_range' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate->toDateString(),
            ],
            'summary' => [
                'income' => $totalIncome,
                'expense' => $totalExpense,
                'net' => $totalIncome - $totalExpense,
            ],
            'by_category' => $byCategory,
            'by_bin' => $byBin,
            'daily_totals' => $dailyTotals,
        ]);
    }
}
