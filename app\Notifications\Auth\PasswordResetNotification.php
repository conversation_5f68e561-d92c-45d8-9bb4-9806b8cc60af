<?php

namespace App\Notifications\Auth;

use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class PasswordResetNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The password reset token.
     *
     * @var string
     */
    protected $token;

    /**
     * Create a new notification instance.
     *
     * @param  string  $token
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_password_changes';

    public function __construct($token)
    {
        $this->token = $token;

        $this->subject = 'Reset Your PocketWatch Password';
        $this->title = 'Password Reset Request';
        $this->content = 'You are receiving this email because we received a password reset request for your account. 
                         Click the button below to reset your password. This password reset link will expire in 60 minutes.';
        
        $this->detailsTitle = 'Important Information';
        $this->details = [
            'Expires' => 'This link will expire in 60 minutes',
            'Security Tip' => 'Choose a strong password that you don\'t use elsewhere',
        ];
        
        $this->actionText = 'Reset Password';
        $this->actionUrl = url(route('password.reset', [
            'token' => $this->token,
        ], false));
        
        $this->closing = 'If you did not request a password reset, no further action is required.';
        $this->signature = 'The PocketWatch Security Team';
    }
}
