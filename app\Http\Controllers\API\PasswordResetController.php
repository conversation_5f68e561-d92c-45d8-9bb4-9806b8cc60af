<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\EmailVerificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;

class PasswordResetController extends Controller
{
    protected $emailVerificationService;

    public function __construct(EmailVerificationService $emailVerificationService)
    {
        $this->emailVerificationService = $emailVerificationService;
    }

    /**
     * Send password reset code
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function sendResetCode(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:users,email',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            $result = $this->emailVerificationService->sendPasswordResetCode($user);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'expires_at' => $result['expires_at'],
                        'attempts_remaining' => $result['attempts_remaining']
                    ]
                ], 200);
            } else {
                $statusCode = match($result['error_code'] ?? '') {
                    'RATE_LIMITED' => 429,
                    'MAX_ATTEMPTS_EXCEEDED' => 429,
                    default => 500
                };

                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? 'UNKNOWN_ERROR'
                ], $statusCode);
            }

        } catch (\Exception $e) {
            Log::error('Password reset send failed', [
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send reset code. Please try again.'
            ], 500);
        }
    }

    /**
     * Verify password reset code
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyResetCode(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:users,email',
                'code' => 'required|string|size:6',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            $result = $this->emailVerificationService->verifyPasswordResetCode($user, $request->code);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'reset_token' => $result['reset_token']
                    ]
                ], 200);
            } else {
                $statusCode = match($result['error_code'] ?? '') {
                    'CODE_EXPIRED' => 410,
                    'INVALID_CODE' => 400,
                    'MAX_ATTEMPTS_EXCEEDED' => 429,
                    default => 500
                };

                $response = [
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? 'UNKNOWN_ERROR'
                ];

                if (isset($result['attempts_remaining'])) {
                    $response['attempts_remaining'] = $result['attempts_remaining'];
                }

                return response()->json($response, $statusCode);
            }

        } catch (\Exception $e) {
            Log::error('Password reset verification failed', [
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Reset code verification failed. Please try again.'
            ], 500);
        }
    }

    /**
     * Reset password with verified code
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function resetPassword(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:users,email',
                'code' => 'required|string|size:6',
                'password' => 'required|string|min:8|confirmed',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            $result = $this->emailVerificationService->resetPassword(
                $user, 
                $request->code, 
                $request->password
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'user' => [
                            'id' => $result['user']->id,
                            'name' => $result['user']->name,
                            'email' => $result['user']->email,
                        ]
                    ]
                ], 200);
            } else {
                $statusCode = match($result['error_code'] ?? '') {
                    'CODE_EXPIRED' => 410,
                    'INVALID_CODE' => 400,
                    'MAX_ATTEMPTS_EXCEEDED' => 429,
                    default => 500
                };

                $response = [
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? 'UNKNOWN_ERROR'
                ];

                if (isset($result['attempts_remaining'])) {
                    $response['attempts_remaining'] = $result['attempts_remaining'];
                }

                return response()->json($response, $statusCode);
            }

        } catch (\Exception $e) {
            Log::error('Password reset failed', [
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Password reset failed. Please try again.'
            ], 500);
        }
    }
}
