<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\Subscriptions\TrialExpirationWarningNotification;
use App\Notifications\Subscriptions\TrialExpiredNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckTrialExpirations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trials:check-expirations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for trial expirations and send notifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for trial expirations...');

        // Get users with active trials
        $usersOnTrial = User::whereNotNull('trial_started_at')
            ->where('subscription_tier', 'trial')
            ->get();

        $this->info("Found {$usersOnTrial->count()} users on trial.");

        $expiredCount = 0;
        $warningCount = 0;

        foreach ($usersOnTrial as $user) {
            $daysRemaining = $user->getTrialDaysRemaining();

            if ($user->trialExpired()) {
                // Trial has expired
                $this->handleExpiredTrial($user);
                $expiredCount++;
            } elseif ($daysRemaining <= 2 && $daysRemaining > 0) {
                // Send warning notification (2 days or less remaining)
                $this->sendTrialWarning($user, $daysRemaining);
                $warningCount++;
            }
        }

        $this->info("Processed {$expiredCount} expired trials and sent {$warningCount} warning notifications.");

        return Command::SUCCESS;
    }

    /**
     * Handle expired trial.
     */
    private function handleExpiredTrial(User $user): void
    {
        try {
            // Update user subscription tier to restrict access
            $user->subscription_tier = 'expired';
            $user->save();

            // Update subscription status
            $activeSubscription = $user->getActiveSubscription();
            if ($activeSubscription) {
                $activeSubscription->stripe_status = 'expired';
                $activeSubscription->ends_at = now();
                $activeSubscription->save();
            }

            // Send trial expired notification
            $user->notify(new TrialExpiredNotification());

            $this->info("Expired trial for user: {$user->email}");

            Log::info('Trial expired for user', [
                'user_id' => $user->id,
                'email' => $user->email,
                'trial_started_at' => $user->trial_started_at,
            ]);
        } catch (\Exception $e) {
            $this->error("Failed to handle expired trial for user {$user->email}: {$e->getMessage()}");
            
            Log::error('Failed to handle expired trial', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send trial warning notification.
     */
    private function sendTrialWarning(User $user, int $daysRemaining): void
    {
        try {
            $user->notify(new TrialExpirationWarningNotification($daysRemaining));

            $this->info("Sent trial warning to user: {$user->email} ({$daysRemaining} days remaining)");

            Log::info('Trial warning sent to user', [
                'user_id' => $user->id,
                'email' => $user->email,
                'days_remaining' => $daysRemaining,
            ]);
        } catch (\Exception $e) {
            $this->error("Failed to send trial warning to user {$user->email}: {$e->getMessage()}");
            
            Log::error('Failed to send trial warning', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
