<?php

namespace App\Notifications\Bins;

use App\Models\Bin;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class SubBinDeletedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The sub-bin name.
     *
     * @var string
     */
    protected $subBinName;

    /**
     * The parent bin.
     *
     * @var \App\Models\Bin
     */
    protected $parentBin;

    /**
     * The timestamp when the sub-bin was deleted.
     *
     * @var \Illuminate\Support\Carbon
     */
    protected $timestamp;

    /**
     * Create a new notification instance.
     *
     * @param  string  $subBinName
     * @param  \App\Models\Bin  $parentBin
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_bin_operations';

    public function __construct($subBinName, Bin $parentBin)
    {
        $this->subBinName = $subBinName;
        $this->parentBin = $parentBin;
        $this->timestamp = now();

        $this->subject = 'Sub-Bin Deleted';
        $this->title = 'Sub-Bin Deleted';
        $this->content = 'A sub-bin has been deleted from your PocketWatch account. If you deleted this sub-bin, 
                         no further action is required.';
        
        $this->detailsTitle = 'Deletion Details';
        $this->details = [
            'Sub-Bin Name' => $this->subBinName,
            'Parent Bin' => $this->parentBin->name,
            'Date & Time' => $this->timestamp->format('F j, Y, g:i a'),
        ];
        
        $this->actionText = 'View Parent Bin';
        $this->actionUrl = url('/bins/' . $this->parentBin->id);
        
        $this->closing = 'If you did not delete this sub-bin, please contact our support team immediately.';
        $this->signature = 'The PocketWatch Team';
    }
}
