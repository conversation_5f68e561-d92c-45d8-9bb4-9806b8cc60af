@extends('admin.layouts.app')

@push('styles')
<style>
    .bg-light-color {
        background-color: var(--light-color) !important;
    }

    .table-hover tbody tr:hover {
        background-color: var(--light-color) !important;
    }

    .dropdown-item:active {
        background-color: var(--primary-color);
    }

    /* Modal positioning */
    .modal {
        z-index: 1050;
    }

    .modal-backdrop {
        z-index: 1040;
    }

    /* Ensure modals are properly contained */
    .modals-container {
        position: relative;
        z-index: 1050;
    }

    /* Improve modal appearance */
    .modal-content {
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    }

    /* Danger modal styling */
    .modal-header.bg-danger .btn-close-white {
        filter: brightness(0) invert(1);
    }

    /* Table improvements */
    .table th {
        border-top: none;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table td {
        vertical-align: middle;
        border-color: #e3e6f0;
    }

    /* Search input improvements */
    .input-group .btn {
        border-left: none;
    }

    .input-group-text {
        border-right: none;
    }

    .input-group .form-control {
        border-left: none;
        border-right: none;
    }

    .input-group .form-control:focus {
        box-shadow: none;
        border-color: #86b7fe;
    }

    /* Badge improvements */
    .badge {
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Pagination improvements */
    .pagination {
        margin-bottom: 0;
    }

    /* Per page selector */
    .form-select-sm {
        padding: 0.25rem 1.5rem 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    /* Form improvements */
    .form-label.fw-semibold {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .form-text {
        font-size: 0.75rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    /* Button improvements */
    .btn {
        font-weight: 500;
    }

    .btn-group .btn {
        border-radius: 0.375rem;
    }

    /* Advanced search section */
    #advancedSearch {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
    }

    #advancedSearch.show {
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .card-header .d-flex {
            flex-direction: column;
            align-items: stretch !important;
        }

        .card-header .d-flex > div {
            margin-bottom: 0.5rem;
        }

        .card-header .d-flex > div:last-child {
            margin-bottom: 0;
        }

        .row.g-3.align-items-end > div {
            margin-bottom: 1rem;
        }

        .d-flex.gap-2.flex-wrap {
            gap: 0.5rem !important;
        }

        .flex-fill {
            min-width: 120px;
        }
    }

    @media (max-width: 576px) {
        .col-lg-4.col-md-12 .d-flex {
            flex-direction: column;
        }

        .flex-fill {
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .flex-fill:last-child {
            margin-bottom: 0;
        }
    }
</style>
@endpush

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Regular Users Management</h1>
            <p class="text-muted mb-0">Manage all non-admin users in the system</p>
        </div>
        <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-1"></i> Add New User
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Search & Filters</h6>
            @if(request()->hasAny(['search', 'status', 'tier', 'sort_by', 'sort_order', 'date_from', 'date_to']))
                <a href="{{ route('admin.users.index') }}" class="btn btn-sm btn-outline-danger">
                    <i class="fas fa-times me-1"></i> Clear All Filters
                </a>
            @endif
        </div>
        <div class="card-body">
            <form action="{{ route('admin.users.index') }}" method="GET" id="filterForm">
                <!-- Preserve per_page when filtering -->
                <input type="hidden" name="per_page" value="{{ request('per_page', 5) }}">

                <div class="row g-3 align-items-end">
                    <!-- Search Field -->
                    <div class="col-lg-4 col-md-6">
                        <label for="search" class="form-label fw-semibold">Search Users</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ request('search') }}"
                                   placeholder="Name, Email, or Phone">
                            @if(request('search'))
                                <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()" title="Clear search">
                                    <i class="fas fa-times"></i>
                                </button>
                            @endif
                        </div>
                        {{-- <div class="form-text">Search by name, email, phone number, or country code</div> --}}
                    </div>

                    <!-- Status Filter -->
                    <div class="col-lg-2 col-md-3 col-sm-6">
                        <label for="status" class="form-label fw-semibold">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>
                                <i class="fas fa-check-circle"></i> Active
                            </option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>
                                <i class="fas fa-times-circle"></i> Inactive
                            </option>
                        </select>
                    </div>

                    <!-- Subscription Tier Filter -->
                    <div class="col-lg-2 col-md-3 col-sm-6">
                        <label for="tier" class="form-label fw-semibold">Subscription</label>
                        <select class="form-select" id="tier" name="tier">
                            <option value="">All Tiers</option>
                            <option value="none" {{ request('tier') == 'none' ? 'selected' : '' }}>None</option>
                            <option value="trial" {{ request('tier') == 'trial' ? 'selected' : '' }}>Trial</option>
                            <option value="base" {{ request('tier') == 'base' ? 'selected' : '' }}>Base</option>
                            <option value="premium" {{ request('tier') == 'premium' ? 'selected' : '' }}>Premium</option>
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="col-lg-4 col-md-12">
                        <div class="d-flex gap-2 flex-wrap">
                            <button type="submit" class="btn btn-primary flex-fill">
                                <i class="fas fa-search me-1"></i> Search
                            </button>
                            @if(request()->hasAny(['search', 'status', 'tier', 'sort_by', 'sort_order', 'date_from', 'date_to']))
                                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary flex-fill">
                                    <i class="fas fa-undo me-1"></i> Reset
                                </a>
                            @endif
                            <button type="button" class="btn btn-outline-info" onclick="toggleAdvancedSearch()" id="advancedToggle">
                                <i class="fas fa-cog me-1"></i> Advanced
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Advanced Search Options (Hidden by default) -->
                <div class="row g-3 mt-2 d-none" id="advancedSearch">
                    <div class="col-12">
                        <hr class="my-2">
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-sliders-h me-1"></i> Advanced Search Options
                        </h6>
                    </div>

                    <div class="col-md-3">
                        <label for="sort_by" class="form-label fw-semibold">Sort By</label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="created_at" {{ request('sort_by', 'created_at') == 'created_at' ? 'selected' : '' }}>Registration Date</option>
                            <option value="name" {{ request('sort_by') == 'name' ? 'selected' : '' }}>Name</option>
                            <option value="email" {{ request('sort_by') == 'email' ? 'selected' : '' }}>Email</option>
                            <option value="last_login_at" {{ request('sort_by') == 'last_login_at' ? 'selected' : '' }}>Last Login</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="sort_order" class="form-label fw-semibold">Sort Order</label>
                        <select class="form-select" id="sort_order" name="sort_order">
                            <option value="desc" {{ request('sort_order', 'desc') == 'desc' ? 'selected' : '' }}>Newest First</option>
                            <option value="asc" {{ request('sort_order') == 'asc' ? 'selected' : '' }}>Oldest First</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="date_from" class="form-label fw-semibold">Registered From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                    </div>

                    <div class="col-md-3">
                        <label for="date_to" class="form-label fw-semibold">Registered To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center flex-wrap">
            <div class="d-flex align-items-center">
                <h6 class="m-0 font-weight-bold text-primary me-3">Regular Users</h6>
                <span class="badge bg-primary">Total: {{ $users->total() }}</span>
                @if(request()->hasAny(['search', 'status', 'tier', 'sort_by', 'sort_order', 'date_from', 'date_to']))
                    <span class="badge bg-info ms-2">Filtered</span>
                @endif
            </div>
            <div class="d-flex align-items-center">
                <label for="per_page" class="form-label me-2 mb-0">Show:</label>
                <select class="form-select form-select-sm" id="per_page" name="per_page" onchange="changePerPage(this.value)" style="width: auto;">
                    <option value="5" {{ request('per_page', 5) == 5 ? 'selected' : '' }}>5</option>
                    <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10</option>
                    <option value="20" {{ request('per_page') == 20 ? 'selected' : '' }}>20</option>
                    <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                    <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                </select>
                <span class="ms-2 text-muted">per page</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-striped align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-center">No.</th>
                            <th scope="col">User</th>
                            <th scope="col">Email</th>
                            <th scope="col">Subscription</th>
                            <th scope="col">Status</th>
                            <th scope="col">Registered</th>
                            <th scope="col">Last Login</th>
                            <th scope="col" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                            <tr>
                                <td class="text-center fw-bold">{{ $loop->iteration + ($users->perPage() * ($users->currentPage() - 1)) }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-2">
                                            @if($user->avatar)
                                                <img src="{{ $user->avatar }}" alt="{{ $user->name }}" class="rounded-circle" width="40">
                                            @else
                                                <div class="rounded-circle bg-light-color d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <span class="fw-bold text-primary">{{ substr($user->name, 0, 1) }}</span>
                                                </div>
                                            @endif
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $user->name }}</div>
                                            <div>
                                                @if($user->is_admin)
                                                    <span class="badge bg-dark rounded-pill">Admin</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-envelope text-secondary me-2"></i>
                                        {{ $user->email }}
                                    </div>
                                    @if($user->phone_number)
                                        <div class="small text-muted mt-1">
                                            <i class="fas fa-phone text-secondary me-2"></i>
                                            @if($user->country_code)
                                                +{{ $user->country_code }}
                                            @endif
                                            {{ $user->phone_number }}
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    @if($user->subscription_tier == 'premium')
                                        <span class="badge bg-success rounded-pill">
                                            <i class="fas fa-crown me-1"></i> Premium
                                        </span>
                                    @elseif($user->subscription_tier == 'trial')
                                        <span class="badge bg-info rounded-pill">
                                            <i class="fas fa-star me-1"></i> Trial
                                        </span>
                                    @elseif($user->subscription_tier == 'base')
                                        <span class="badge bg-primary rounded-pill">
                                            <i class="fas fa-user me-1"></i> Base
                                        </span>
                                    @else
                                        <span class="badge bg-secondary rounded-pill">
                                            <i class="fas fa-user-slash me-1"></i> None
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    @if($user->is_banned)
                                        <span class="badge bg-danger rounded-pill">
                                            <i class="fas fa-ban me-1"></i> Banned
                                        </span>
                                    @elseif(!$user->is_active)
                                        <span class="badge bg-warning rounded-pill">
                                            <i class="fas fa-lock me-1"></i> Blocked
                                        </span>
                                    @else
                                        <span class="badge bg-success rounded-pill">
                                            <i class="fas fa-check-circle me-1"></i> Active
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="far fa-calendar-alt text-secondary me-2"></i>
                                        {{ $user->created_at->format('M d, Y') }}
                                    </div>
                                </td>
                                <td>
                                    @if($user->last_login_at)
                                        <div class="d-flex align-items-center">
                                            <i class="far fa-clock text-secondary me-2"></i>
                                            {{ $user->last_login_at->format('M d, Y H:i') }}
                                        </div>
                                    @else
                                        <span class="text-muted">
                                            <i class="fas fa-times-circle me-1"></i> Never
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex justify-content-center gap-1">
                                        <!-- View Button -->
                                        <a href="{{ route('admin.users.show', $user->uuid) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        <!-- Status Dropdown -->
                                        <div class="dropdown d-inline">
                                            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-user-cog"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                @if($user->is_banned)
                                                    <li>
                                                        <form action="{{ route('admin.users.unban', $user->uuid) }}" method="POST">
                                                            @csrf
                                                            <button type="submit" class="dropdown-item text-success" onclick="return confirm('Are you sure you want to unban this user?')">
                                                                <i class="fas fa-user-check me-2"></i> Unban User
                                                            </button>
                                                        </form>
                                                    </li>
                                                @else
                                                    <li>
                                                        <a href="#" class="dropdown-item text-danger" data-bs-toggle="modal" data-bs-target="#banUserModal{{ $user->uuid }}">
                                                            <i class="fas fa-ban me-2"></i> Ban User
                                                        </a>
                                                    </li>
                                                @endif

                                                @if($user->is_active)
                                                    <li>
                                                        <form action="{{ route('admin.users.block', $user->uuid) }}" method="POST">
                                                            @csrf
                                                            <button type="submit" class="dropdown-item text-warning" onclick="return confirm('Are you sure you want to block this user?')">
                                                                <i class="fas fa-user-lock me-2"></i> Block User
                                                            </button>
                                                        </form>
                                                    </li>
                                                @else
                                                    <li>
                                                        <form action="{{ route('admin.users.unblock', $user->uuid) }}" method="POST">
                                                            @csrf
                                                            <button type="submit" class="dropdown-item text-success" onclick="return confirm('Are you sure you want to unblock this user?')">
                                                                <i class="fas fa-user-check me-2"></i> Unblock User
                                                            </button>
                                                        </form>
                                                    </li>
                                                @endif

                                                <li><hr class="dropdown-divider"></li>

                                                <li>
                                                    <button type="button" class="dropdown-item text-danger" onclick="openDeleteModal('{{ $user->uuid }}')">
                                                        <i class="fas fa-trash-alt me-2"></i> Delete User
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>


                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No users found</h5>
                                        <p class="text-muted">Try adjusting your search or filter criteria</p>
                                        <a href="{{ route('admin.users.create') }}" class="btn btn-primary mt-2">
                                            <i class="fas fa-user-plus me-1"></i> Add New User
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination and Info -->
            @if($users->hasPages() || $users->count() > 0)
                <div class="d-flex justify-content-between align-items-center mt-4 flex-wrap">
                    <div class="mb-2 mb-md-0">
                        <span class="text-muted">
                            Showing {{ $users->firstItem() ?? 0 }} to {{ $users->lastItem() ?? 0 }}
                            of {{ $users->total() }} results
                            @if(request()->hasAny(['search', 'status', 'tier', 'sort_by', 'sort_order', 'date_from', 'date_to']))
                                (filtered from {{ \App\Models\User::where('is_admin', false)->count() }} total users)
                            @endif
                        </span>
                    </div>
                    <div>
                        {{ $users->withQueryString()->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Modals Section -->
    <div class="modals-container">
        @foreach($users as $user)
            <!-- Ban User Modal -->
            <div class="modal fade" id="banUserModal{{ $user->uuid }}" tabindex="-1" aria-labelledby="banUserModalLabel{{ $user->uuid }}" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="banUserModalLabel{{ $user->uuid }}">Ban User: {{ $user->name }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form action="{{ route('admin.users.ban', $user->uuid) }}" method="POST">
                            @csrf
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="reason" class="form-label">Reason for Ban</label>
                                    <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="Enter reason for banning this user"></textarea>
                                </div>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Banning this user will prevent them from logging in and using the application.
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-danger">Ban User</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Delete User Modal -->
            <div class="modal fade" id="deleteUserModal{{ $user->uuid }}" tabindex="-1" aria-labelledby="deleteUserModalLabel{{ $user->uuid }}" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="deleteUserModalLabel{{ $user->uuid }}">Delete User: {{ $user->name }}</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form action="{{ route('admin.users.destroy', $user->uuid) }}" method="POST">
                            @csrf
                            @method('DELETE')
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <i class="fas fa-exclamation-triangle text-danger fa-4x mb-3"></i>
                                    <h5 class="text-danger">Permanent Deletion Warning</h5>
                                </div>

                                <p>You are about to <strong>permanently delete</strong> this user and <strong>ALL</strong> associated data:</p>

                                <ul class="list-group mb-4">
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-user-circle text-danger me-2"></i> User account and profile
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-wallet text-danger me-2"></i> All financial bins and sub-bins
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-exchange-alt text-danger me-2"></i> All transactions
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-credit-card text-danger me-2"></i> All subscriptions
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-coins text-danger me-2"></i> All crypto wallets
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-cog text-danger me-2"></i> All settings and preferences
                                    </li>
                                </ul>

                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <strong>This action cannot be undone.</strong> The user will need to register again if they want to use the application.
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="confirmDelete{{ $user->uuid }}" required>
                                    <label class="form-check-label" for="confirmDelete{{ $user->uuid }}">
                                        I understand that this will permanently delete all user data
                                    </label>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash-alt me-1"></i> Delete User Permanently
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
@endsection

@push('scripts')
<script>
    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Auto-submit search on Enter key
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('filterForm').submit();
            }
        });

        // Auto-submit on filter change
        document.getElementById('status').addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });

        document.getElementById('tier').addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });

        // Show advanced search if any advanced parameters are present
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('sort_by') || urlParams.has('sort_order') || urlParams.has('date_from') || urlParams.has('date_to')) {
            toggleAdvancedSearch();
        }
    });

    // Clear search function
    function clearSearch() {
        document.getElementById('search').value = '';
        document.getElementById('filterForm').submit();
    }

    // Change per page function
    function changePerPage(perPage) {
        const url = new URL(window.location);
        url.searchParams.set('per_page', perPage);
        url.searchParams.delete('page'); // Reset to first page when changing per_page
        window.location.href = url.toString();
    }

    // Toggle advanced search
    function toggleAdvancedSearch() {
        const advancedSearch = document.getElementById('advancedSearch');
        const toggleButton = document.getElementById('advancedToggle');

        if (advancedSearch.classList.contains('d-none')) {
            advancedSearch.classList.remove('d-none');
            advancedSearch.classList.add('show');
            toggleButton.innerHTML = '<i class="fas fa-chevron-up me-1"></i> Hide Advanced';
            toggleButton.classList.remove('btn-outline-info');
            toggleButton.classList.add('btn-info', 'text-white');
        } else {
            advancedSearch.classList.add('d-none');
            advancedSearch.classList.remove('show');
            toggleButton.innerHTML = '<i class="fas fa-cog me-1"></i> Advanced';
            toggleButton.classList.remove('btn-info', 'text-white');
            toggleButton.classList.add('btn-outline-info');
        }
    }

    // Function to open delete modal
    function openDeleteModal(userId) {
        console.log('Opening delete modal for user ID:', userId);

        var modalElement = document.getElementById('deleteUserModal' + userId);

        // Check if modal element exists
        if (!modalElement) {
            console.error('Modal element not found: deleteUserModal' + userId);
            return;
        }

        // Check if Bootstrap is available
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap is not loaded');
            return;
        }

        // Try to initialize and show the modal
        try {
            // First, make sure any existing modals are hidden
            var existingModals = document.querySelectorAll('.modal.show');
            existingModals.forEach(function(modal) {
                var bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            });

            // Create a new modal instance and show it
            var deleteModal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',  // Prevent closing when clicking outside
                keyboard: false      // Prevent closing with keyboard
            });

            // Show the modal after a short delay to ensure DOM is ready
            setTimeout(function() {
                deleteModal.show();
            }, 50);

        } catch (error) {
            console.error('Error showing modal:', error);
            // Fallback: use direct form submission if modal fails
            if (confirm('Are you sure you want to delete this user and all associated data? This action cannot be undone.')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/users/' + userId;

                var csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                var methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        }
    }
</script>
@endpush
