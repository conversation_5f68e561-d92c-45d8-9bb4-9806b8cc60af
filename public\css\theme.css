:root {
    --primary-color: #32704e; /* Sea Green */
    --secondary-color: #3CB371; /* Medium Sea Green */
    --accent-color: #66CDAA; /* Medium Aquamarine */
    --light-color: #E0FFF0; /* Light Mint */
    --dark-color: #1D5E40; /* Dark Green */
    --text-color: #333333;
    --card-bg: #ffffff;
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --section-bg: #f8f9fa;
}

/* Primary Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    background-color: var(--dark-color);
    border-color: var(--dark-color);
}

/* Outline Button Styles */
.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Text Colors */
.text-primary {
    color: var(--primary-color) !important;
}

/* Background Colors */
.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Border Colors */
.border-primary {
    border-color: var(--primary-color) !important;
}

.border-left-primary {
    border-left: 4px solid var(--primary-color);
}

/* Link Colors */
a {
    color: var(--primary-color);
}

a:hover {
    color: var(--dark-color);
}

/* Form Controls */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(50, 112, 78, 0.25);
}

/* Custom Checkboxes and Radios */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Progress Bars */
.progress-bar {
    background-color: var(--primary-color);
}

/* Badges */
.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

.badge-soft-primary {
    background-color: rgba(50, 112, 78, 0.1);
    color: var(--primary-color);
}

/* Alerts */
.alert-primary {
    background-color: rgba(50, 112, 78, 0.1);
    border-color: rgba(50, 112, 78, 0.2);
    color: var(--primary-color);
}

/* Cards */
.card-primary {
    border-color: var(--primary-color);
}

.card-primary .card-header {
    background-color: var(--primary-color);
    color: white;
}

/* Navbar */
.navbar-brand {
    color: var(--primary-color);
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

/* Pagination */
.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-link {
    color: var(--primary-color);
}

.page-link:hover {
    color: var(--dark-color);
}

/* List Groups */
.list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Dropdowns */
.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary-color);
}

/* Spinners */
.spinner-border.text-primary {
    color: var(--primary-color) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
}

/* Tooltips */
.tooltip-inner {
    background-color: var(--primary-color);
}

.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before, 
.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--primary-color);
}

/* Popovers */
.popover-header {
    background-color: rgba(50, 112, 78, 0.1);
    color: var(--primary-color);
}

/* Modals */
.modal-header {
    border-bottom-color: rgba(50, 112, 78, 0.2);
}

.modal-footer {
    border-top-color: rgba(50, 112, 78, 0.2);
}

/* Tables */
.table-primary {
    background-color: rgba(50, 112, 78, 0.1);
}

.table-hover .table-primary:hover {
    background-color: rgba(50, 112, 78, 0.2);
}

/* Accordion */
.accordion-button:not(.collapsed) {
    background-color: rgba(50, 112, 78, 0.1);
    color: var(--primary-color);
}

.accordion-button:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(50, 112, 78, 0.25);
}

/* Tabs */
.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.nav-pills .nav-link.active {
    background-color: var(--primary-color);
}

/* Toasts */
.toast-header {
    color: var(--primary-color);
}
