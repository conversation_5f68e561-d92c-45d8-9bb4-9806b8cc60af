<?php

namespace App\Http\Resources;

use App\Services\IdObfuscationService;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\App;

class ApiResource extends JsonResource
{
    /**
     * The resource type.
     *
     * @var string
     */
    protected $resourceType = 'default';

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $data = parent::toArray($request);
        
        // Replace ID with obfuscated ID
        if (isset($data['id'])) {
            $data['id'] = $this->obfuscateId($data['id']);
        }
        
        // Use UUID as the public ID if available
        if (isset($this->uuid)) {
            $data['id'] = $this->uuid;
            // Remove the raw UUID from the response
            unset($data['uuid']);
        }
        
        // Replace related IDs with obfuscated IDs
        $this->obfuscateRelatedIds($data);
        
        return $data;
    }
    
    /**
     * Obfuscate an ID.
     *
     * @param  mixed  $id
     * @return string
     */
    protected function obfuscateId($id)
    {
        return App::make(IdObfuscationService::class)->obfuscate($id, $this->resourceType);
    }
    
    /**
     * Obfuscate related IDs in the data array.
     *
     * @param  array  &$data
     * @return void
     */
    protected function obfuscateRelatedIds(&$data)
    {
        // Common related ID fields
        $relatedIdFields = [
            'user_id' => 'user',
            'bin_id' => 'bin',
            'sub_bin_id' => 'sub_bin',
            'transaction_id' => 'transaction',
            'subscription_id' => 'subscription',
            'crypto_wallet_id' => 'crypto_wallet',
            'plaid_account_id' => 'plaid_account',
            'report_id' => 'report',
        ];
        
        foreach ($relatedIdFields as $field => $type) {
            if (isset($data[$field])) {
                $data[$field] = App::make(IdObfuscationService::class)->obfuscate($data[$field], $type);
            }
        }
    }
}
