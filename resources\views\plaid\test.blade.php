@extends('layouts.app')

@section('content')
<div class="container">
    <h1>Plaid Integration Test</h1>
    
    <div class="card mt-4">
        <div class="card-header">
            Test Results
        </div>
        <div class="card-body">
            @if($error)
                <div class="alert alert-danger">
                    <strong>Error:</strong> {{ $error }}
                </div>
                <p>Please check your Plaid credentials in <code>.env</code> and the logs for more details.</p>
            @elseif($linkToken)
                <div class="alert alert-success">
                    <strong>Success!</strong> Plaid is likely configured correctly.
                </div>
                <p>A link token was successfully generated.</p>
                <div class="mt-3">
                    <strong>Link Token:</strong>
                    <input type="text" class="form-control" value="{{ $linkToken }}" readonly>
                </div>
                <div class="mt-3">
                    <p>You can use this token to initialize Plaid Link for testing.</p>
                </div>
            @else
                <div class="alert alert-warning">
                    No link token or error was provided to the view.
                </div>
            @endif
        </div>
    </div>
</div>
@endsection 