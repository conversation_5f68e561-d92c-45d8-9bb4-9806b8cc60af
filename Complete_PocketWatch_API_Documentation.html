<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>PocketWatch API - Complete Documentation</title>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap"
            rel="stylesheet"
        />
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
        <style>
            :root {
                --primary: #667eea;
                --primary-dark: #5a6fd8;
                --secondary: #764ba2;
                --success: #28a745;
                --warning: #ffc107;
                --danger: #dc3545;
                --info: #17a2b8;
                --light: #f8f9fa;
                --dark: #343a40;
                --white: #ffffff;
                --border: #e9ecef;
                --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.15);
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: "Inter", -apple-system, BlinkMacSystemFont,
                    sans-serif;
                line-height: 1.6;
                color: var(--dark);
                background: linear-gradient(
                    135deg,
                    var(--primary) 0%,
                    var(--secondary) 100%
                );
                min-height: 100vh;
            }

            .container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 20px;
            }

            .header {
                background: var(--white);
                border-radius: 16px;
                padding: 40px;
                margin-bottom: 30px;
                box-shadow: var(--shadow-lg);
                text-align: center;
            }

            .header h1 {
                font-size: 3rem;
                font-weight: 700;
                background: linear-gradient(
                    135deg,
                    var(--primary),
                    var(--secondary)
                );
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 15px;
            }

            .header p {
                font-size: 1.2rem;
                color: #666;
                margin-bottom: 20px;
            }

            .version-badge {
                display: inline-block;
                background: var(--primary);
                color: var(--white);
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 0.9rem;
                font-weight: 500;
            }

            .nav-tabs {
                display: flex;
                background: var(--white);
                border-radius: 12px;
                padding: 8px;
                margin-bottom: 30px;
                box-shadow: var(--shadow);
                overflow-x: auto;
                gap: 8px;
            }

            .nav-tab {
                padding: 12px 20px;
                border: none;
                background: transparent;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 500;
                color: #666;
                transition: all 0.3s ease;
                white-space: nowrap;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .nav-tab.active {
                background: var(--primary);
                color: var(--white);
                box-shadow: var(--shadow);
            }

            .nav-tab:hover:not(.active) {
                background: var(--light);
                color: var(--dark);
            }

            .tab-content {
                display: none;
            }

            .tab-content.active {
                display: block;
            }

            .api-section {
                background: var(--white);
                border-radius: 16px;
                padding: 30px;
                margin-bottom: 30px;
                box-shadow: var(--shadow-lg);
            }

            .section-title {
                font-size: 2rem;
                font-weight: 600;
                margin-bottom: 20px;
                color: var(--primary);
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .endpoint-card {
                border: 2px solid var(--border);
                border-radius: 12px;
                margin-bottom: 25px;
                overflow: hidden;
                transition: all 0.3s ease;
            }

            .endpoint-card:hover {
                border-color: var(--primary);
                box-shadow: var(--shadow);
            }

            .endpoint-header {
                background: var(--light);
                padding: 20px;
                border-bottom: 1px solid var(--border);
                display: flex;
                align-items: center;
                justify-content: space-between;
                flex-wrap: wrap;
                gap: 15px;
            }

            .endpoint-method {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .method-badge {
                padding: 6px 12px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 0.85rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .method-get {
                background: var(--success);
                color: var(--white);
            }
            .method-post {
                background: var(--info);
                color: var(--white);
            }
            .method-put {
                background: var(--warning);
                color: var(--dark);
            }
            .method-delete {
                background: var(--danger);
                color: var(--white);
            }

            .endpoint-url {
                font-family: "JetBrains Mono", monospace;
                font-size: 1.1rem;
                font-weight: 500;
                color: var(--dark);
            }

            .auth-badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.75rem;
                font-weight: 500;
            }

            .auth-required {
                background: #ffe6e6;
                color: var(--danger);
            }
            .auth-optional {
                background: #fff3cd;
                color: #856404;
            }
            .auth-none {
                background: #d4edda;
                color: var(--success);
            }

            .endpoint-body {
                padding: 25px;
            }

            .endpoint-description {
                font-size: 1.1rem;
                margin-bottom: 20px;
                color: #555;
            }

            .params-section {
                margin-bottom: 25px;
            }

            .params-title {
                font-size: 1.2rem;
                font-weight: 600;
                margin-bottom: 15px;
                color: var(--primary);
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .param-item {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 12px;
                border-left: 4px solid var(--primary);
            }

            .param-name {
                font-family: "JetBrains Mono", monospace;
                font-weight: 600;
                color: var(--primary);
                margin-bottom: 5px;
            }

            .param-type {
                display: inline-block;
                background: var(--primary);
                color: var(--white);
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 0.75rem;
                margin-left: 10px;
            }

            .param-required {
                background: var(--danger);
                color: var(--white);
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 0.7rem;
                margin-left: 8px;
            }

            .param-description {
                color: #666;
                margin-top: 8px;
            }

            .response-section {
                margin-top: 25px;
            }

            .response-tabs {
                display: flex;
                gap: 8px;
                margin-bottom: 15px;
            }

            .response-tab {
                padding: 8px 16px;
                border: 1px solid var(--border);
                background: var(--white);
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: all 0.3s ease;
            }

            .response-tab.active {
                background: var(--primary);
                color: var(--white);
                border-color: var(--primary);
            }

            .code-block {
                background: #1e1e1e;
                color: #d4d4d4;
                padding: 20px;
                border-radius: 8px;
                overflow-x: auto;
                font-family: "JetBrains Mono", monospace;
                font-size: 0.9rem;
                line-height: 1.5;
                margin: 15px 0;
            }

            .copy-button {
                position: relative;
                float: right;
                background: var(--primary);
                color: var(--white);
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.8rem;
                margin-bottom: 10px;
            }

            .status-codes {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-top: 20px;
            }

            .status-code {
                padding: 12px;
                border-radius: 8px;
                text-align: center;
                font-weight: 500;
            }

            .status-200 {
                background: #d4edda;
                color: var(--success);
            }
            .status-201 {
                background: #d4edda;
                color: var(--success);
            }
            .status-400 {
                background: #f8d7da;
                color: var(--danger);
            }
            .status-401 {
                background: #f8d7da;
                color: var(--danger);
            }
            .status-403 {
                background: #f8d7da;
                color: var(--danger);
            }
            .status-404 {
                background: #f8d7da;
                color: var(--danger);
            }
            .status-422 {
                background: #fff3cd;
                color: #856404;
            }
            .status-500 {
                background: #f8d7da;
                color: var(--danger);
            }

            .quick-start {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: var(--white);
                border-radius: 16px;
                padding: 30px;
                margin-bottom: 30px;
            }

            .quick-start h3 {
                margin-bottom: 20px;
                font-size: 1.5rem;
            }

            .quick-start-steps {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }

            .quick-start-step {
                background: rgba(255, 255, 255, 0.1);
                padding: 20px;
                border-radius: 12px;
                backdrop-filter: blur(10px);
            }

            .step-number {
                background: var(--white);
                color: var(--primary);
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                margin-bottom: 10px;
            }

            .search-box {
                background: var(--white);
                border: 2px solid var(--border);
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 1rem;
                width: 100%;
                margin-bottom: 20px;
                transition: border-color 0.3s ease;
            }

            .search-box:focus {
                outline: none;
                border-color: var(--primary);
            }

            .filter-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-bottom: 20px;
            }

            .filter-tag {
                padding: 6px 12px;
                background: var(--light);
                border: 1px solid var(--border);
                border-radius: 20px;
                cursor: pointer;
                font-size: 0.85rem;
                transition: all 0.3s ease;
            }

            .filter-tag.active {
                background: var(--primary);
                color: var(--white);
                border-color: var(--primary);
            }

            @media (max-width: 768px) {
                .container {
                    padding: 10px;
                }

                .header h1 {
                    font-size: 2rem;
                }

                .nav-tabs {
                    flex-direction: column;
                }

                .endpoint-header {
                    flex-direction: column;
                    align-items: flex-start;
                }

                .quick-start-steps {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-wallet"></i> PocketWatch API</h1>
                <p>
                    Complete REST API Documentation for Financial Management
                    Platform
                </p>
                <span class="version-badge"
                    >v5.0 - Email Verification System + 6-Digit Codes + Updated Registration + Password Reset</span
                >
            </div>

            <!-- Quick Start -->
            <div class="quick-start">
                <h3><i class="fas fa-rocket"></i> Quick Start Guide</h3>
                <div class="quick-start-steps">
                    <div class="quick-start-step">
                        <div class="step-number">1</div>
                        <h4>Authentication</h4>
                        <p>
                            Register or login with email/password or Google
                            OAuth to get your API token
                        </p>
                    </div>
                    <div class="quick-start-step">
                        <div class="step-number">2</div>
                        <h4>Choose Package</h4>
                        <p>
                            Browse and select from Base ($9.99) or Premium
                            ($19.99) monthly packages
                        </p>
                    </div>
                    <div class="quick-start-step">
                        <div class="step-number">3</div>
                        <h4>Stripe Checkout</h4>
                        <p>
                            Create Stripe session → Redirect to professional Stripe checkout → 7-day trial starts
                        </p>
                    </div>
                    <div class="quick-start-step">
                        <div class="step-number">4</div>
                        <h4>Use Features</h4>
                        <p>
                            Create bins, sub-bins, transactions, and manage your
                            finances
                        </p>
                    </div>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="api-section">
                <input
                    type="text"
                    class="search-box"
                    placeholder="🔍 Search endpoints..."
                    id="searchBox"
                />
                <div class="filter-tags">
                    <span class="filter-tag active" data-filter="all"
                        >All Endpoints</span
                    >
                    <span class="filter-tag" data-filter="auth"
                        >Authentication</span
                    >
                    <span class="filter-tag" data-filter="email"
                        >Email Verification</span
                    >
                    <span class="filter-tag" data-filter="packages"
                        >Packages</span
                    >
                    <span class="filter-tag" data-filter="bins">Bins</span>
                    <span class="filter-tag" data-filter="transactions"
                        >Transactions</span
                    >
                    <span class="filter-tag" data-filter="crypto">Crypto</span>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="nav-tabs">
                <button class="nav-tab active" data-tab="authentication">
                    <i class="fas fa-key"></i> Authentication
                </button>
                <button class="nav-tab" data-tab="email-verification">
                    <i class="fas fa-envelope-check"></i> Email Verification
                </button>
                <button class="nav-tab" data-tab="packages">
                    <i class="fas fa-credit-card"></i> Stripe Checkout
                </button>
                <button class="nav-tab" data-tab="bins">
                    <i class="fas fa-folder"></i> Bins & Sub-Bins
                </button>
                <button class="nav-tab" data-tab="transactions">
                    <i class="fas fa-exchange-alt"></i> Transactions
                </button>
                <button class="nav-tab" data-tab="crypto">
                    <i class="fab fa-bitcoin"></i> Crypto Wallet
                </button>
                <button class="nav-tab" data-tab="reports">
                    <i class="fas fa-chart-bar"></i> Reports
                </button>
                <button class="nav-tab" data-tab="webhooks">
                    <i class="fas fa-webhook"></i> Webhooks
                </button>
            </div>

            <!-- Authentication Tab -->
            <div class="tab-content active" id="authentication">
                <div class="api-section">
                    <h2 class="section-title">
                        <i class="fas fa-key"></i> Authentication Endpoints
                    </h2>

                    <!-- Register -->
                    <div class="endpoint-card" data-category="auth">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post"
                                    >POST</span
                                >
                                <span class="endpoint-url">/api/register</span>
                            </div>
                            <span class="auth-badge auth-none"
                                >No Auth Required</span
                            >
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Register a new user account with email and password.
                                Automatically sends a 6-digit email verification code to the user's email address.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request
                                    Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        name
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        User's full name (min: 2 characters)
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        email
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Valid email address (must be unique)
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        password
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Password (min: 8 characters)
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        password_confirmation
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Password confirmation (must match
                                        password)
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        country_code
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Country code (e.g., +1, +92, +44)
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        phone_number
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Phone number (max 20 characters)
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <div class="response-tabs">
                                    <button
                                        class="response-tab active"
                                        data-response="register-success"
                                    >
                                        Success (201)
                                    </button>
                                    <button
                                        class="response-tab"
                                        data-response="register-error"
                                    >
                                        Error (422)
                                    </button>
                                </div>

                                <div
                                    class="response-content"
                                    id="register-success"
                                >
                                    <button
                                        class="copy-button"
                                        onclick="copyCode('register-success-code')"
                                    >
                                        Copy
                                    </button>
                                    <pre
                                        class="code-block"
                                        id="register-success-code"
                                    >
{
    "message": "User registered successfully",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "country_code": "+1",
        "phone_number": "1234567890",
        "subscription_tier": "none",
        "email_verified_at": null,
        "trial_started_at": null,
        "created_at": "2024-12-19T10:00:00Z"
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "email_verification": {
        "required": true,
        "sent": true,
        "message": "Verification code sent to your email address.",
        "expires_at": "2024-12-19T10:15:00Z",
        "attempts_remaining": 5
    }
}</pre
                                    >
                                </div>

                                <div
                                    class="response-content"
                                    id="register-error"
                                    style="display: none"
                                >
                                    <button
                                        class="copy-button"
                                        onclick="copyCode('register-error-code')"
                                    >
                                        Copy
                                    </button>
                                    <pre
                                        class="code-block"
                                        id="register-error-code"
                                    >
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "email": ["The email has already been taken."],
        "password": ["The password must be at least 8 characters."]
    }
}</pre
                                    >
                                </div>

                                <div class="status-codes">
                                    <div class="status-code status-201">
                                        201 - Created
                                    </div>
                                    <div class="status-code status-422">
                                        422 - Validation Error
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Login -->
                    <div class="endpoint-card" data-category="auth">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post"
                                    >POST</span
                                >
                                <span class="endpoint-url">/api/login</span>
                            </div>
                            <span class="auth-badge auth-none"
                                >No Auth Required</span
                            >
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Authenticate user with email and password to receive access token.
                                Response includes email verification status and pending verification details.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request
                                    Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        email
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        User's email address
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        password
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        User's password
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <div class="response-tabs">
                                    <button
                                        class="response-tab active"
                                        data-response="login-success"
                                    >
                                        Success (200)
                                    </button>
                                    <button
                                        class="response-tab"
                                        data-response="login-error"
                                    >
                                        Error (401)
                                    </button>
                                </div>

                                <div
                                    class="response-content"
                                    id="login-success"
                                >
                                    <button
                                        class="copy-button"
                                        onclick="copyCode('login-success-code')"
                                    >
                                        Copy
                                    </button>
                                    <pre
                                        class="code-block"
                                        id="login-success-code"
                                    >
{
    "message": "Login successful",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "subscription_tier": "trial",
        "email_verified_at": "2024-12-18T09:00:00Z",
        "trial_started_at": "2024-12-18T10:00:00Z",
        "trial_expired": false,
        "trial_days_remaining": 6
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "email_verification": {
        "verified": true,
        "verified_at": "2024-12-18T09:00:00Z"
    }
}</pre
                                    >
                                </div>

                                <div
                                    class="response-content"
                                    id="login-error"
                                    style="display: none"
                                >
                                    <button
                                        class="copy-button"
                                        onclick="copyCode('login-error-code')"
                                    >
                                        Copy
                                    </button>
                                    <pre
                                        class="code-block"
                                        id="login-error-code"
                                    >
{
    "success": false,
    "message": "Invalid credentials"
}</pre
                                    >
                                </div>

                                <div class="status-codes">
                                    <div class="status-code status-200">
                                        200 - Success
                                    </div>
                                    <div class="status-code status-401">
                                        401 - Unauthorized
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google OAuth Login -->
                    <div class="endpoint-card" data-category="auth">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post">POST</span>
                                <span class="endpoint-url">/api/auth/google</span>
                            </div>
                            <span class="auth-badge auth-none">No Auth Required</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Authenticate user with Google OAuth ID token and get API access token.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        id_token
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        Google OAuth ID token from Google Sign-In
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <div class="response-tabs">
                                    <button
                                        class="response-tab active"
                                        data-response="google-success"
                                    >
                                        Success (200)
                                    </button>
                                    <button
                                        class="response-tab"
                                        data-response="google-error"
                                    >
                                        Error (401)
                                    </button>
                                </div>

                                <div
                                    class="response-content"
                                    id="google-success"
                                >
                                    <button
                                        class="copy-button"
                                        onclick="copyCode('google-success-code')"
                                    >
                                        Copy
                                    </button>
                                    <pre
                                        class="code-block"
                                        id="google-success-code"
                                    >
{
    "success": true,
    "message": "Google authentication successful",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "subscription_tier": "none",
        "trial_started_at": null,
        "google_id": "1234567890",
        "avatar": "https://lh3.googleusercontent.com/a/default-user=s96-c",
        "created_at": "2024-12-19T10:00:00Z"
    },
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "is_new_user": false
}</pre>
                                    </div>

                                <div
                                    class="response-content"
                                    id="google-error"
                                    style="display: none"
                                >
                                    <button
                                        class="copy-button"
                                        onclick="copyCode('google-error-code')"
                                    >
                                        Copy
                                    </button>
                                    <pre
                                        class="code-block"
                                        id="google-error-code"
                                    >
{
    "success": false,
    "message": "Invalid Google token",
    "error": "Token verification failed"
}</pre>
                                    </div>

                                <div class="status-codes">
                                    <div class="status-code status-200">
                                        200 - Success
                                    </div>
                                    <div class="status-code status-401">
                                        401 - Invalid Token
                                    </div>
                                    <div class="status-code status-422">
                                        422 - Validation Error
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Verification Tab -->
            <div class="tab-content" id="email-verification">
                <div class="api-section">
                    <h2 class="section-title">
                        <i class="fas fa-envelope-check"></i> Email Verification & Password Reset
                    </h2>

                    <div style="background: #e3f2fd; padding: 20px; border-radius: 12px; margin-bottom: 30px; border-left: 4px solid #2196f3;">
                        <h3 style="color: #1976d2; margin-bottom: 15px;">📧 6-Digit Code System</h3>
                        <p style="margin-bottom: 15px; color: #0d47a1;">
                            <strong>Modern Email Verification:</strong> Secure 6-digit codes sent via Gmail SMTP with professional templates and comprehensive security features.
                        </p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px;">
                                <strong>✅ 6-Digit Codes:</strong> Easy to enter, secure verification
                            </div>
                            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px;">
                                <strong>⏰ 15-Min Expiry:</strong> Automatic code expiration
                            </div>
                            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px;">
                                <strong>🛡️ Rate Limiting:</strong> 1 minute between requests
                            </div>
                            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px;">
                                <strong>🎯 Attempt Tracking:</strong> Max 5 attempts per code
                            </div>
                        </div>
                    </div>

                    <!-- Send Email Verification Code -->
                    <div class="endpoint-card" data-category="email">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post">POST</span>
                                <span class="endpoint-url">/api/email/send-verification-code</span>
                            </div>
                            <span class="auth-badge auth-none">No Auth Required</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Send a 6-digit verification code to user's email address.
                                Code expires in 15 minutes with rate limiting and attempt tracking.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        email
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        User's email address
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <div class="response-tabs">
                                    <button class="response-tab active" onclick="showResponse('email-send-success', this)">
                                        Success (200)
                                    </button>
                                    <button class="response-tab" onclick="showResponse('email-send-error', this)">
                                        Error (400/429)
                                    </button>
                                </div>

                                <div class="response-content" id="email-send-success">
                                    <button class="copy-button" onclick="copyCode('email-send-success-code')">
                                        Copy
                                    </button>
                                    <pre class="code-block" id="email-send-success-code">
{
    "success": true,
    "message": "Verification code sent to your email address.",
    "data": {
        "expires_at": "2024-12-19T10:15:00Z",
        "attempts_remaining": 5
    }
}</pre>
                                </div>

                                <div class="response-content" id="email-send-error" style="display: none">
                                    <button class="copy-button" onclick="copyCode('email-send-error-code')">
                                        Copy
                                    </button>
                                    <pre class="code-block" id="email-send-error-code">
{
    "success": false,
    "message": "Please wait before requesting another verification code.",
    "error_code": "RATE_LIMITED"
}</pre>
                                </div>

                                <div class="status-codes">
                                    <div class="status-code status-200">200 - Success</div>
                                    <div class="status-code status-400">400 - Email Already Verified</div>
                                    <div class="status-code status-404">404 - User Not Found</div>
                                    <div class="status-code status-422">422 - Validation Error</div>
                                    <div class="status-code status-429">429 - Rate Limited</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Verify Email Code -->
                    <div class="endpoint-card" data-category="email">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post">POST</span>
                                <span class="endpoint-url">/api/email/verify</span>
                            </div>
                            <span class="auth-badge auth-none">No Auth Required</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Verify user's email address using the 6-digit code received via email.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        email
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        User's email address
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        code
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        6-digit verification code from email
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <button class="copy-button" onclick="copyCode('email-verify-success-code')">
                                    Copy
                                </button>
                                <pre class="code-block" id="email-verify-success-code">
{
    "success": true,
    "message": "Email verified successfully!",
    "data": {
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "email_verified_at": "2024-12-19T10:00:00Z"
        }
    }
}</pre>

                                <div class="status-codes">
                                    <div class="status-code status-200">200 - Success</div>
                                    <div class="status-code status-400">400 - Invalid/Expired Code</div>
                                    <div class="status-code status-404">404 - User Not Found</div>
                                    <div class="status-code status-422">422 - Validation Error</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Forgot Password -->
                    <div class="endpoint-card" data-category="email">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post">POST</span>
                                <span class="endpoint-url">/api/forgot-password</span>
                            </div>
                            <span class="auth-badge auth-none">No Auth Required</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Send a 6-digit password reset code to user's email address.
                                Code expires in 15 minutes with maximum 5 attempts.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        email
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        User's email address
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <button class="copy-button" onclick="copyCode('forgot-password-success-code')">
                                    Copy
                                </button>
                                <pre class="code-block" id="forgot-password-success-code">
{
    "message": "Password reset code sent to your email address.",
    "expires_at": "2024-12-19T10:15:00Z",
    "attempts_remaining": 5
}</pre>

                                <div class="status-codes">
                                    <div class="status-code status-200">200 - Success</div>
                                    <div class="status-code status-400">400 - Rate Limited</div>
                                    <div class="status-code status-422">422 - Validation Error</div>
                                    <div class="status-code status-500">500 - Server Error</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reset Password -->
                    <div class="endpoint-card" data-category="email">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post">POST</span>
                                <span class="endpoint-url">/api/reset-password</span>
                            </div>
                            <span class="auth-badge auth-none">No Auth Required</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Reset user's password using the 6-digit code received via email.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        email
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        User's email address
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        code
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        6-digit reset code from email
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        password
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        New password (min: 8 characters)
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        password_confirmation
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        Password confirmation (must match password)
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <button class="copy-button" onclick="copyCode('reset-password-success-code')">
                                    Copy
                                </button>
                                <pre class="code-block" id="reset-password-success-code">
{
    "message": "Password reset successfully. You can now login with your new password.",
    "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "email_verified_at": "2024-12-18T09:00:00Z"
    }
}</pre>

                                <div class="status-codes">
                                    <div class="status-code status-200">200 - Success</div>
                                    <div class="status-code status-400">400 - Invalid/Expired Code</div>
                                    <div class="status-code status-404">404 - User Not Found</div>
                                    <div class="status-code status-422">422 - Validation Error</div>
                                    <div class="status-code status-500">500 - Server Error</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Check Verification Status -->
                    <div class="endpoint-card" data-category="email">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post">POST</span>
                                <span class="endpoint-url">/api/email/check-verification-status</span>
                            </div>
                            <span class="auth-badge auth-none">No Auth Required</span>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Check current email verification status and pending code information.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        email
                                        <span class="param-type">string</span>
                                        <span class="param-required">required</span>
                                    </div>
                                    <div class="param-description">
                                        User's email address
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <button class="copy-button" onclick="copyCode('check-status-success-code')">
                                    Copy
                                </button>
                                <pre class="code-block" id="check-status-success-code">
{
    "success": true,
    "data": {
        "email_verified": false,
        "email_verified_at": null,
        "verification_attempts": 2,
        "max_attempts": 5,
        "attempts_remaining": 3,
        "has_pending_code": true,
        "code_expires_at": "2024-12-19T10:15:00Z"
    }
}</pre>

                                <div class="status-codes">
                                    <div class="status-code status-200">200 - Success</div>
                                    <div class="status-code status-404">404 - User Not Found</div>
                                    <div class="status-code status-422">422 - Validation Error</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stripe Checkout Tab -->
            <div class="tab-content" id="packages">
                <div class="api-section">
                    <h2 class="section-title">
                        <i class="fas fa-credit-card"></i> Stripe Checkout Endpoints
                    </h2>

                    <div style="background: #e8f5e8; padding: 20px; border-radius: 12px; margin-bottom: 30px; border-left: 4px solid #28a745;">
                        <h3 style="color: #28a745; margin-bottom: 15px;">🚀 Professional Stripe Checkout Integration</h3>
                        <p style="margin-bottom: 15px; color: #155724;">
                            <strong>Direct Stripe Checkout:</strong> Users are redirected to Stripe's professional payment pages with built-in 7-day trial support.
                        </p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px;">
                                <strong>Step 1:</strong> Get packages via API
                            </div>
                            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px;">
                                <strong>Step 2:</strong> Create Stripe session
                            </div>
                            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px;">
                                <strong>Step 3:</strong> Stripe checkout page
                            </div>
                            <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px;">
                                <strong>Step 4:</strong> Trial activation
                            </div>
                        </div>
                    </div>

                    <!-- Get Packages -->
                    <div class="endpoint-card" data-category="packages">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-get">GET</span>
                                <span class="endpoint-url"
                                    >/api/packages-simple</span
                                >
                            </div>
                            <span class="auth-badge auth-required"
                                >Auth Required</span
                            >
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Get all available packages with dynamic pricing and features for Stripe Checkout.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-key"></i> Headers
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        Authorization
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Bearer {token}
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <button
                                    class="copy-button"
                                    onclick="copyCode('packages-simple-code')"
                                >
                                    Copy
                                </button>
                                <pre class="code-block" id="packages-simple-code">
{
    "success": true,
    "data": {
        "packages": [
            {
                "id": "base_monthly",
                "name": "Base Monthly",
                "tier": "base",
                "billing_cycle": "monthly",
                "price": 9.99,
                "original_price": 9.99,
                "discount": 0,
                "stripe_price_id": "price_1234567890abcdef",
                "description": "Essential financial management tools for personal use",
                "features": [
                    "Financial bins with thresholds",
                    "Transaction categorization",
                    "Basic insights and analytics",
                    "Hierarchical sub-bins (3 levels deep)",
                    "Bank account linking",
                    "Monthly spending reports",
                    "Email notifications",
                    "Mobile app access"
                ],
                "limits": {
                    "max_bins": 10,
                    "max_sub_bins_per_bin": 10,
                    "max_nesting_depth": 3,
                    "max_transactions_per_month": 1000
                },
                "popular": false,
                "recommended_for": "Individuals and small families"
            },
            {
                "id": "premium_monthly",
                "name": "Premium Monthly",
                "tier": "premium",
                "billing_cycle": "monthly",
                "price": 19.99,
                "original_price": 19.99,
                "discount": 0,
                "stripe_price_id": "price_0987654321fedcba",
                "description": "Advanced financial management with premium features",
                "features": [
                    "All Base features included",
                    "Unlimited hierarchical sub-bins",
                    "Crypto wallet integration",
                    "Advanced AI insights and predictions",
                    "Priority notifications",
                    "Advanced reporting and analytics",
                    "Custom spending categories",
                    "Investment tracking",
                    "Multi-currency support",
                    "Priority customer support"
                ],
                "limits": {
                    "max_bins": "unlimited",
                    "max_sub_bins_per_bin": "unlimited",
                    "max_nesting_depth": "unlimited",
                    "max_transactions_per_month": "unlimited"
                },
                "popular": true,
                "recommended_for": "Power users and businesses"
            }
        ],
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "current_tier": "none"
        },
        "stripe_redirect_url": "http://127.0.0.1:8000/stripe/redirect",
        "currency": "USD"
    }
}</pre
                                >

                                <div class="status-codes">
                                    <div class="status-code status-200">
                                        200 - Success
                                    </div>
                                    <div class="status-code status-401">
                                        401 - Unauthorized
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Create Stripe Session -->
                    <div class="endpoint-card" data-category="packages">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-post">POST</span>
                                <span class="endpoint-url"
                                    >/api/packages-simple/stripe-url</span
                                >
                            </div>
                            <span class="auth-badge auth-required"
                                >Auth Required</span
                            >
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Create Stripe Checkout session and return professional payment URL.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-cog"></i> Request Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        package_id
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Package ID (base_monthly,
                                        premium_monthly)
                                    </div>
                                </div>
                                <h4 class="params-title">
                                    <i class="fas fa-key"></i> Headers
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        Authorization
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Bearer {token}
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <button
                                    class="copy-button"
                                    onclick="copyCode('stripe-session-code')"
                                >
                                    Copy
                                </button>
                                <pre
                                    class="code-block"
                                    id="stripe-session-code"
                                >
{
    "success": true,
    "data": {
        "package": {
            "id": "base_monthly",
            "name": "Base Monthly",
            "price": 9.99,
            "features": [...],
            "description": "Essential financial management tools for personal use"
        },
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "current_tier": "none"
        },
        "pricing": {
            "subtotal": 9.99,
            "tax": 0,
            "total": 9.99,
            "currency": "USD",
            "billing_cycle": "monthly"
        },
        "trial_info": {
            "trial_days": 7,
            "trial_start": "immediately_after_payment",
            "trial_end_date": "2024-12-26",
            "first_billing_date": "2024-12-26",
            "trial_description": "Start your 7-day free trial today. You won't be charged until Dec 26, 2024."
        },
        "features_included": [...],
        "what_happens_next": [
            "You will be redirected to Stripe for secure payment",
            "Your 7-day free trial starts immediately",
            "Full access to all Base Monthly features",
            "Cancel anytime during trial period",
            "Automatic billing starts after trial ends"
        ]
    }
}</pre
                                >

                                <div class="status-codes">
                                    <div class="status-code status-200">
                                        200 - Success
                                    </div>
                                    <div class="status-code status-401">
                                        401 - Unauthorized
                                    </div>
                                    <div class="status-code status-404">
                                        404 - Package Not Found
                                    </div>
                                    <div class="status-code status-422">
                                        422 - Invalid Package ID
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Success/Cancel -->
                    <div class="endpoint-card" data-category="packages">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge method-get"
                                    >GET</span
                                >
                                <span class="endpoint-url"
                                    >/api/payment-simple-success</span
                                >
                            </div>
                            <span class="auth-badge auth-required"
                                >Auth Required</span
                            >
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">
                                Handle successful payment and activate 7-day trial.
                            </div>

                            <div class="params-section">
                                <h4 class="params-title">
                                    <i class="fas fa-filter"></i> Query Parameters
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        user_id
                                        <span class="param-type">integer</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        User ID from Stripe redirect
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        plan_id
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Package ID (base_monthly, premium_monthly)
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        price
                                        <span class="param-type">number</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Package price
                                    </div>
                                </div>
                                <div class="param-item">
                                    <div class="param-name">
                                        session_id
                                        <span class="param-type">string</span>
                                    </div>
                                    <div class="param-description">
                                        Stripe session ID (optional)
                                    </div>
                                </div>
                                <h4 class="params-title">
                                    <i class="fas fa-key"></i> Headers
                                </h4>
                                <div class="param-item">
                                    <div class="param-name">
                                        Authorization
                                        <span class="param-type">string</span>
                                        <span class="param-required"
                                            >required</span
                                        >
                                    </div>
                                    <div class="param-description">
                                        Bearer {token}
                                    </div>
                                </div>
                            </div>

                            <div class="response-section">
                                <h4 class="params-title">
                                    <i class="fas fa-reply"></i> Response
                                </h4>
                                <button
                                    class="copy-button"
                                    onclick="copyCode('payment-success-code')"
                                >
                                    Copy
                                </button>
                                <pre
                                    class="code-block"
                                    id="payment-success-code"
                                >
{
    "success": true,
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
        "session_id": "cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
        "package": {
            "id": "base_monthly",
            "name": "Base Monthly",
            "tier": "base",
            "billing_cycle": "monthly",
            "price": 9.99,
            "stripe_price_id": "price_1234567890abcdef"
        },
        "confirmation": {
            "confirmed_at": "2024-12-19T10:00:00Z",
            "user_confirmed": true,
            "terms_accepted": true
        },
        "next_step": "redirect_to_stripe"
    },
    "message": "Package confirmed! Redirecting to Stripe for secure payment..."
}</pre
                                >

                                <div class="status-codes">
                                    <div class="status-code status-200">
                                        200 - Success
                                    </div>
                                    <div class="status-code status-401">
                                        401 - Unauthorized
                                    </div>
                                    <div class="status-code status-404">
                                        404 - Package Not Found
                                    </div>
                                    <div class="status-code status-422">
                                        422 - Validation Error
                                    </div>
                                    <div class="status-code status-500">
                                        500 - Stripe Error
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bins & Sub-Bins Tab -->
        <div class="tab-content" id="bins">
            <div class="api-section">
                <h2 class="section-title">
                    <i class="fas fa-folder"></i> Enhanced Bins & Sub-Bins System
                </h2>
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 12px; margin-bottom: 25px;">
                    <h4><i class="fas fa-star"></i> New Features in v4.0</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>Simplified Categories:</strong> Only Income and Expense types</li>
                        <li><strong>Flexible Sub-Bins:</strong> Optional threshold limits, auto-inherit parent category</li>
                        <li><strong>Cumulative Balance:</strong> Real-time calculation across all bins</li>
                        <li><strong>Enhanced Delete API:</strong> Comprehensive validation and error handling</li>
                        <li><strong>Unlimited Nesting:</strong> Premium users get unlimited sub-bin levels</li>
                    </ul>
                </div>

                <!-- Get All Bins -->
                <div class="endpoint-card" data-category="bins">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api/bins</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Get all bins for the authenticated user with pagination, filtering, and cumulative balance information.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-filter"></i> Query Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">page <span class="param-type">integer</span></div>
                                <div class="param-description">Page number (default: 1)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">per_page <span class="param-type">integer</span></div>
                                <div class="param-description">Items per page (default: 10, max: 100)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">type <span class="param-type">string</span></div>
                                <div class="param-description">Filter by type (income, expense)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">is_active <span class="param-type">boolean</span></div>
                                <div class="param-description">Filter by active status</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">search <span class="param-type">string</span></div>
                                <div class="param-description">Search in bin names and descriptions</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('get-bins-response')">Copy</button>
                            <pre class="code-block" id="get-bins-response">{
    "data": [
        {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Emergency Fund",
            "type": "income",
            "description": "Emergency savings account",
            "threshold_max_limit": "10000.00",
            "threshold_max_warning": "8000.00",
            "current_amount": "5000.00",
            "currency": "USD",
            "is_active": true,
            "sub_bins_count": 3,
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        },
        {
            "id": 2,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Monthly Expenses",
            "type": "expense",
            "description": "Monthly recurring expenses",
            "threshold_max_limit": "3000.00",
            "threshold_max_warning": "2500.00",
            "current_amount": "2200.00",
            "currency": "USD",
            "is_active": true,
            "sub_bins_count": 5,
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "per_page": 10,
        "total": 25,
        "last_page": 3
    },
    "user_cumulative_balance": "15000.00"
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Bin -->
                <div class="endpoint-card" data-category="bins">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/bins</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Create a new financial bin with enhanced threshold management and simplified categories.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-cog"></i> Request Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">name <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Bin name (max: 255 characters)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">type <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Bin type: <code>income</code> or <code>expense</code></div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">threshold_max_limit <span class="param-type">number</span> <span class="param-required">required</span></div>
                                <div class="param-description">Maximum threshold limit (min: 0)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">threshold_max_warning <span class="param-type">number</span></div>
                                <div class="param-description">Warning threshold (must be less than max_limit)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">description <span class="param-type">string</span></div>
                                <div class="param-description">Optional description</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">currency <span class="param-type">string</span></div>
                                <div class="param-description">Currency code (default: USD, max: 10 characters)</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('create-bin-response')">Copy</button>
                            <pre class="code-block" id="create-bin-response">{
    "message": "Bin created successfully",
    "bin": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Emergency Fund",
        "type": "income",
        "description": "Emergency savings account",
        "threshold_max_limit": "10000.00",
        "threshold_max_warning": "8000.00",
        "current_amount": "0.00",
        "currency": "USD",
        "is_active": true,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    "remaining_bins": 47,
    "user_cumulative_balance": "15000.00"
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-201">201 - Created</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-422">422 - Validation Error</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Single Bin -->
                <div class="endpoint-card" data-category="bins">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api/bins/{id}</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Retrieve details of a specific bin with sub-bins and transaction information.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-route"></i> Path Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">id <span class="param-type">integer</span> <span class="param-required">required</span></div>
                                <div class="param-description">Bin ID</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('get-single-bin-response')">Copy</button>
                            <pre class="code-block" id="get-single-bin-response">{
    "bin": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Emergency Fund",
        "type": "income",
        "description": "Emergency savings account",
        "threshold_max_limit": "10000.00",
        "threshold_max_warning": "8000.00",
        "current_amount": "5000.00",
        "currency": "USD",
        "is_active": true,
        "sub_bins": [
            {
                "id": 1,
                "name": "Medical Emergency",
                "type": "income",
                "current_amount": "2000.00",
                "depth_level": 1,
                "parent_sub_bin_id": null
            }
        ],
        "transactions_count": 15,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z"
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-404">404 - Bin Not Found</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Update Bin -->
                <div class="endpoint-card" data-category="bins">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-put">PUT</span>
                            <span class="endpoint-url">/api/bins/{id}</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Update an existing bin. Automatically recalculates cumulative balance.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-route"></i> Path Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">id <span class="param-type">integer</span> <span class="param-required">required</span></div>
                                <div class="param-description">Bin ID</div>
                            </div>
                            <h4 class="params-title"><i class="fas fa-cog"></i> Request Parameters (all optional)</h4>
                            <div class="param-item">
                                <div class="param-name">name <span class="param-type">string</span></div>
                                <div class="param-description">Bin name (max: 255 characters)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">type <span class="param-type">string</span></div>
                                <div class="param-description">Bin type: <code>income</code> or <code>expense</code></div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">threshold_max_limit <span class="param-type">number</span></div>
                                <div class="param-description">Maximum threshold limit (min: 0)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">threshold_max_warning <span class="param-type">number</span></div>
                                <div class="param-description">Warning threshold (must be less than max_limit)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">description <span class="param-type">string</span></div>
                                <div class="param-description">Bin description</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">currency <span class="param-type">string</span></div>
                                <div class="param-description">Currency code (max: 10 characters)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">is_active <span class="param-type">boolean</span></div>
                                <div class="param-description">Bin active status</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('update-bin-response')">Copy</button>
                            <pre class="code-block" id="update-bin-response">{
    "message": "Bin updated successfully",
    "bin": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Updated Emergency Fund",
        "type": "income",
        "description": "Updated description",
        "threshold_max_limit": "12000.00",
        "threshold_max_warning": "10000.00",
        "current_amount": "5000.00",
        "currency": "USD",
        "is_active": true,
        "updated_at": "2024-01-01T12:00:00.000000Z"
    },
    "user_cumulative_balance": "15000.00"
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-404">404 - Bin Not Found</div>
                                <div class="status-code status-422">422 - Validation Error</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Bin -->
                <div class="endpoint-card" data-category="bins">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-delete">DELETE</span>
                            <span class="endpoint-url">/api/bins/{id}</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Delete a bin with comprehensive validation. The bin must not have any sub-bins or transactions.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-route"></i> Path Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">id <span class="param-type">integer</span> <span class="param-required">required</span></div>
                                <div class="param-description">Bin ID to delete</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response Examples</h4>
                            <div class="response-tabs">
                                <button class="response-tab active" data-response="delete-success">Success (200)</button>
                                <button class="response-tab" data-response="delete-has-subbins">Has Sub-Bins (422)</button>
                                <button class="response-tab" data-response="delete-has-transactions">Has Transactions (422)</button>
                                <button class="response-tab" data-response="delete-not-found">Not Found (404)</button>
                            </div>

                            <div class="response-content" id="delete-success">
                                <button class="copy-button" onclick="copyCode('delete-success-code')">Copy</button>
                                <pre class="code-block" id="delete-success-code">{
    "message": "Bin deleted successfully",
    "deleted_bin": {
        "name": "Emergency Fund",
        "type": "income",
        "amount": "5000.00"
    },
    "user_cumulative_balance": "10000.00"
}</pre>
                            </div>

                            <div class="response-content" id="delete-has-subbins" style="display: none;">
                                <button class="copy-button" onclick="copyCode('delete-has-subbins-code')">Copy</button>
                                <pre class="code-block" id="delete-has-subbins-code">{
    "message": "Cannot delete bin with existing sub-bins",
    "error": "Please delete all sub-bins first before deleting the bin",
    "sub_bins_count": 3
}</pre>
                            </div>

                            <div class="response-content" id="delete-has-transactions" style="display: none;">
                                <button class="copy-button" onclick="copyCode('delete-has-transactions-code')">Copy</button>
                                <pre class="code-block" id="delete-has-transactions-code">{
    "message": "Cannot delete bin with existing transactions",
    "error": "Please delete all transactions first before deleting the bin",
    "transactions_count": 15
}</pre>
                            </div>

                            <div class="response-content" id="delete-not-found" style="display: none;">
                                <button class="copy-button" onclick="copyCode('delete-not-found-code')">Copy</button>
                                <pre class="code-block" id="delete-not-found-code">{
    "message": "Bin not found"
}</pre>
                            </div>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Successfully Deleted</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-404">404 - Bin Not Found</div>
                                <div class="status-code status-422">422 - Has Dependencies</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Sub-Bin -->
                <div class="endpoint-card" data-category="bins">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/bins/{binId}/sub-bins</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Create a flexible sub-bin with optional threshold limits and automatic parent category inheritance.
                        </div>
                        <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                            <h5 style="color: #0c5460; margin-bottom: 10px;"><i class="fas fa-star"></i> New Flexible Features</h5>
                            <ul style="color: #0c5460; margin: 0; padding-left: 20px;">
                                <li><strong>Optional Thresholds:</strong> threshold_max_limit is now optional</li>
                                <li><strong>Auto-Inheritance:</strong> Type automatically inherits from parent bin/sub-bin</li>
                                <li><strong>Minimum Value:</strong> Current amount starts at 0 by default</li>
                                <li><strong>User Control:</strong> Set maximum limits as needed, no defaults required</li>
                            </ul>
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-route"></i> Path Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">binId <span class="param-type">integer</span> <span class="param-required">required</span></div>
                                <div class="param-description">Parent bin ID</div>
                            </div>
                            <h4 class="params-title"><i class="fas fa-cog"></i> Request Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">name <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Sub-bin name (max: 255 characters)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">type <span class="param-type">string</span></div>
                                <div class="param-description">Sub-bin type: <code>income</code> or <code>expense</code> (auto-selected from parent if not provided)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">description <span class="param-type">string</span></div>
                                <div class="param-description">Optional sub-bin description</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">threshold_max_limit <span class="param-type">number</span></div>
                                <div class="param-description"><strong>Optional:</strong> Maximum threshold limit (min: 0) - user can set as they want</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">threshold_max_warning <span class="param-type">number</span></div>
                                <div class="param-description">Warning threshold (must be less than max_limit)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">currency <span class="param-type">string</span></div>
                                <div class="param-description">Currency code (default: USD, max: 10 characters)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">parent_sub_bin_id <span class="param-type">integer</span></div>
                                <div class="param-description">Parent sub-bin ID for unlimited nesting</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('create-subbin-response')">Copy</button>
                            <pre class="code-block" id="create-subbin-response">{
    "message": "Sub-bin created successfully",
    "sub_bin": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Medical Emergency",
        "type": "income",
        "description": "Medical emergency fund",
        "threshold_max_limit": null,
        "threshold_max_warning": null,
        "current_amount": "0.00",
        "currency": "USD",
        "is_active": true,
        "depth_level": 1,
        "parent_sub_bin_id": null,
        "parent_category_inherited": true,
        "created_at": "2024-01-01T00:00:00.000000Z"
    },
    "remaining_sub_bins": 97,
    "user_cumulative_balance": "15000.00"
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-201">201 - Created</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-403">403 - Nesting Limit Exceeded</div>
                                <div class="status-code status-422">422 - Validation Error</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Hierarchical Structure -->
                <div class="endpoint-card" data-category="bins">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api/bins/{binId}/sub-bins-hierarchy</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Get complete hierarchical structure of sub-bins with unlimited nesting visualization.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-route"></i> Path Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">binId <span class="param-type">integer</span> <span class="param-required">required</span></div>
                                <div class="param-description">Bin ID to get hierarchy for</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('hierarchy-response')">Copy</button>
                            <pre class="code-block" id="hierarchy-response">{
    "success": true,
    "data": {
        "bin": {
            "id": 1,
            "name": "Groceries",
            "threshold": 500.00,
            "current_amount": 245.50
        },
        "hierarchy": [
            {
                "id": 1,
                "name": "Organic Groceries",
                "threshold": 200.00,
                "level": 1,
                "path": "Groceries > Organic Groceries",
                "children": [
                    {
                        "id": 2,
                        "name": "Organic Vegetables",
                        "threshold": 100.00,
                        "level": 2,
                        "path": "Groceries > Organic Groceries > Organic Vegetables",
                        "children": []
                    }
                ]
            },
            {
                "id": 3,
                "name": "Snacks",
                "threshold": 150.00,
                "level": 1,
                "path": "Groceries > Snacks",
                "children": []
            }
        ],
        "summary": {
            "total_sub_bins": 3,
            "max_nesting_level": 2,
            "user_max_allowed": "unlimited"
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-404">404 - Bin Not Found</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Tab -->
        <div class="tab-content" id="transactions">
            <div class="api-section">
                <h2 class="section-title">
                    <i class="fas fa-exchange-alt"></i> Transaction Endpoints
                </h2>

                <!-- Get All Transactions -->
                <div class="endpoint-card" data-category="transactions">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api/transactions</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Get all transactions for the authenticated user with advanced filtering and pagination.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-filter"></i> Query Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">page <span class="param-type">integer</span></div>
                                <div class="param-description">Page number (default: 1)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">per_page <span class="param-type">integer</span></div>
                                <div class="param-description">Items per page (default: 15, max: 100)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">bin_id <span class="param-type">integer</span></div>
                                <div class="param-description">Filter by bin ID</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">sub_bin_id <span class="param-type">integer</span></div>
                                <div class="param-description">Filter by sub-bin ID</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">category <span class="param-type">string</span></div>
                                <div class="param-description">Filter by category</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">date_from <span class="param-type">date</span></div>
                                <div class="param-description">Start date (YYYY-MM-DD)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">date_to <span class="param-type">date</span></div>
                                <div class="param-description">End date (YYYY-MM-DD)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">min_amount <span class="param-type">number</span></div>
                                <div class="param-description">Minimum transaction amount</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">max_amount <span class="param-type">number</span></div>
                                <div class="param-description">Maximum transaction amount</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('get-transactions-response')">Copy</button>
                            <pre class="code-block" id="get-transactions-response">{
    "success": true,
    "data": {
        "transactions": [
            {
                "id": 1,
                "uuid": "550e8400-e29b-41d4-a716-************",
                "amount": 45.50,
                "description": "Whole Foods Market",
                "category": "groceries",
                "date": "2024-12-19",
                "bin": {
                    "id": 1,
                    "name": "Groceries"
                },
                "sub_bin": {
                    "id": 2,
                    "name": "Organic Groceries"
                },
                "created_at": "2024-12-19T10:00:00Z",
                "updated_at": "2024-12-19T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 25,
            "total_pages": 2,
            "has_next_page": true,
            "has_prev_page": false
        },
        "summary": {
            "total_transactions": 25,
            "total_amount": 1245.75,
            "average_amount": 49.83,
            "date_range": {
                "from": "2024-12-01",
                "to": "2024-12-19"
            }
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Transaction -->
                <div class="endpoint-card" data-category="transactions">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/transactions</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Create a new transaction and automatically update bin/sub-bin amounts.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-cog"></i> Request Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">amount <span class="param-type">number</span> <span class="param-required">required</span></div>
                                <div class="param-description">Transaction amount (positive number)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">description <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Transaction description</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">bin_id <span class="param-type">integer</span> <span class="param-required">required</span></div>
                                <div class="param-description">Target bin ID</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">sub_bin_id <span class="param-type">integer</span></div>
                                <div class="param-description">Optional sub-bin ID</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">category <span class="param-type">string</span></div>
                                <div class="param-description">Transaction category</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">date <span class="param-type">date</span></div>
                                <div class="param-description">Transaction date (default: today)</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('create-transaction-response')">Copy</button>
                            <pre class="code-block" id="create-transaction-response">{
    "success": true,
    "message": "Transaction created successfully",
    "data": {
        "transaction": {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "amount": 45.50,
            "description": "Whole Foods Market",
            "category": "groceries",
            "date": "2024-12-19",
            "bin_id": 1,
            "sub_bin_id": 2,
            "user_id": 1,
            "created_at": "2024-12-19T10:00:00Z",
            "updated_at": "2024-12-19T10:00:00Z"
        },
        "bin_update": {
            "id": 1,
            "name": "Groceries",
            "previous_amount": 200.00,
            "new_amount": 245.50,
            "remaining_amount": 254.50,
            "percentage_used": 49
        },
        "sub_bin_update": {
            "id": 2,
            "name": "Organic Groceries",
            "previous_amount": 100.00,
            "new_amount": 145.50,
            "remaining_amount": 54.50,
            "percentage_used": 73
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-201">201 - Created</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-422">422 - Validation Error</div>
                                <div class="status-code status-400">400 - Insufficient Bin Balance</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Create Transactions -->
                <div class="endpoint-card" data-category="transactions">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/transactions/bulk</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Create multiple transactions in a single request for bulk import functionality.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-cog"></i> Request Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">transactions <span class="param-type">array</span> <span class="param-required">required</span></div>
                                <div class="param-description">Array of transaction objects (max: 100)</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('bulk-transactions-response')">Copy</button>
                            <pre class="code-block" id="bulk-transactions-response">{
    "success": true,
    "message": "Bulk transactions processed",
    "data": {
        "created": 8,
        "failed": 2,
        "total_processed": 10,
        "created_transactions": [
            {
                "id": 1,
                "amount": 45.50,
                "description": "Whole Foods Market",
                "bin_id": 1
            }
        ],
        "failed_transactions": [
            {
                "index": 3,
                "error": "Bin not found",
                "data": {
                    "amount": 25.00,
                    "bin_id": 999
                }
            }
        ],
        "summary": {
            "total_amount": 456.75,
            "bins_affected": 3,
            "processing_time": "0.45s"
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Partial Success</div>
                                <div class="status-code status-201">201 - All Created</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-422">422 - Validation Error</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crypto Tab -->
        <div class="tab-content" id="crypto">
            <div class="api-section">
                <h2 class="section-title">
                    <i class="fab fa-bitcoin"></i> Crypto Wallet Endpoints
                </h2>

                <!-- Get Crypto Wallets -->
                <div class="endpoint-card" data-category="crypto">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api/crypto/wallets</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Get all crypto wallets for the authenticated user with current balances and portfolio value.
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('get-crypto-wallets-response')">Copy</button>
                            <pre class="code-block" id="get-crypto-wallets-response">{
    "success": true,
    "data": {
        "wallets": [
            {
                "id": 1,
                "uuid": "550e8400-e29b-41d4-a716-************",
                "name": "Main Bitcoin Wallet",
                "address": "**********************************",
                "currency": "BTC",
                "network": "mainnet",
                "balance": 0.05432100,
                "balance_usd": 2156.84,
                "last_sync": "2024-12-19T10:00:00Z",
                "transactions_count": 15,
                "created_at": "2024-12-19T10:00:00Z"
            },
            {
                "id": 2,
                "uuid": "550e8400-e29b-41d4-a716-************",
                "name": "Ethereum Wallet",
                "address": "******************************************",
                "currency": "ETH",
                "network": "mainnet",
                "balance": 1.25000000,
                "balance_usd": 2875.50,
                "last_sync": "2024-12-19T10:00:00Z",
                "transactions_count": 8,
                "created_at": "2024-12-19T10:00:00Z"
            }
        ],
        "portfolio": {
            "total_value_usd": 5032.34,
            "total_wallets": 2,
            "supported_currencies": ["BTC", "ETH", "LTC", "ADA", "DOT"],
            "last_updated": "2024-12-19T10:00:00Z"
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add Crypto Wallet -->
                <div class="endpoint-card" data-category="crypto">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/crypto/wallets</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Add a new crypto wallet to track balances and transactions.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-cog"></i> Request Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">name <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Wallet name for identification</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">address <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Wallet address (public key)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">currency <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Cryptocurrency (BTC, ETH, LTC, ADA, DOT)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">network <span class="param-type">string</span></div>
                                <div class="param-description">Network type (mainnet, testnet) - default: mainnet</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('add-crypto-wallet-response')">Copy</button>
                            <pre class="code-block" id="add-crypto-wallet-response">{
    "success": true,
    "message": "Crypto wallet added successfully",
    "data": {
        "wallet": {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "name": "Main Bitcoin Wallet",
            "address": "**********************************",
            "currency": "BTC",
            "network": "mainnet",
            "balance": 0.00000000,
            "balance_usd": 0.00,
            "last_sync": null,
            "transactions_count": 0,
            "created_at": "2024-12-19T10:00:00Z"
        },
        "sync_status": {
            "queued_for_sync": true,
            "estimated_sync_time": "2-5 minutes"
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-201">201 - Created</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-422">422 - Validation Error</div>
                                <div class="status-code status-400">400 - Invalid Address</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sync Wallet -->
                <div class="endpoint-card" data-category="crypto">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/crypto/wallets/{id}/sync</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Manually sync wallet balance and transactions from blockchain.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-route"></i> Path Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">id <span class="param-type">integer</span> <span class="param-required">required</span></div>
                                <div class="param-description">Wallet ID to sync</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('sync-wallet-response')">Copy</button>
                            <pre class="code-block" id="sync-wallet-response">{
    "success": true,
    "message": "Wallet sync completed",
    "data": {
        "wallet": {
            "id": 1,
            "name": "Main Bitcoin Wallet",
            "balance": 0.05432100,
            "balance_usd": 2156.84,
            "last_sync": "2024-12-19T10:00:00Z"
        },
        "sync_results": {
            "new_transactions": 3,
            "balance_change": 0.00125000,
            "sync_duration": "1.2s",
            "last_block_height": 820000
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-404">404 - Wallet Not Found</div>
                                <div class="status-code status-429">429 - Rate Limited</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Tab -->
        <div class="tab-content" id="reports">
            <div class="api-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-bar"></i> Reports Endpoints
                </h2>

                <!-- Generate Report -->
                <div class="endpoint-card" data-category="reports">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/reports/generate</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Generate a new financial report with customizable parameters and export formats.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-cog"></i> Request Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">type <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Report type (spending_summary, bin_analysis, transaction_history, crypto_portfolio)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">date_from <span class="param-type">date</span> <span class="param-required">required</span></div>
                                <div class="param-description">Start date (YYYY-MM-DD)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">date_to <span class="param-type">date</span> <span class="param-required">required</span></div>
                                <div class="param-description">End date (YYYY-MM-DD)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">format <span class="param-type">string</span></div>
                                <div class="param-description">Export format (pdf, excel, csv) - default: pdf</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">bin_ids <span class="param-type">array</span></div>
                                <div class="param-description">Filter by specific bin IDs</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">categories <span class="param-type">array</span></div>
                                <div class="param-description">Filter by categories</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('generate-report-response')">Copy</button>
                            <pre class="code-block" id="generate-report-response">{
    "success": true,
    "message": "Report generated successfully",
    "data": {
        "report": {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "type": "spending_summary",
            "title": "Monthly Spending Summary - December 2024",
            "date_from": "2024-12-01",
            "date_to": "2024-12-19",
            "format": "pdf",
            "status": "completed",
            "file_size": "2.4 MB",
            "download_url": "/api/reports/1/download",
            "expires_at": "2024-12-26T10:00:00Z",
            "created_at": "2024-12-19T10:00:00Z"
        },
        "summary": {
            "total_transactions": 156,
            "total_amount": 3245.67,
            "bins_included": 8,
            "categories_included": 12,
            "generation_time": "3.2s"
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-201">201 - Created</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-422">422 - Validation Error</div>
                                <div class="status-code status-400">400 - Invalid Date Range</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Reports -->
                <div class="endpoint-card" data-category="reports">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api/reports</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Get all generated reports for the authenticated user with pagination.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-filter"></i> Query Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">page <span class="param-type">integer</span></div>
                                <div class="param-description">Page number (default: 1)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">per_page <span class="param-type">integer</span></div>
                                <div class="param-description">Items per page (default: 15)</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">type <span class="param-type">string</span></div>
                                <div class="param-description">Filter by report type</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">status <span class="param-type">string</span></div>
                                <div class="param-description">Filter by status (pending, completed, failed)</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('get-reports-response')">Copy</button>
                            <pre class="code-block" id="get-reports-response">{
    "success": true,
    "data": {
        "reports": [
            {
                "id": 1,
                "uuid": "550e8400-e29b-41d4-a716-************",
                "type": "spending_summary",
                "title": "Monthly Spending Summary - December 2024",
                "date_from": "2024-12-01",
                "date_to": "2024-12-19",
                "format": "pdf",
                "status": "completed",
                "file_size": "2.4 MB",
                "download_url": "/api/reports/1/download",
                "expires_at": "2024-12-26T10:00:00Z",
                "created_at": "2024-12-19T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 5,
            "total_pages": 1
        }
    }
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Download Report -->
                <div class="endpoint-card" data-category="reports">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api/reports/{id}/download</span>
                        </div>
                        <span class="auth-badge auth-required">Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Download a generated report file in the specified format.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-route"></i> Path Parameters</h4>
                            <div class="param-item">
                                <div class="param-name">id <span class="param-type">integer</span> <span class="param-required">required</span></div>
                                <div class="param-description">Report ID to download</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <p style="margin-bottom: 15px;">Returns the report file as a binary download with appropriate headers:</p>
                            <ul style="margin-bottom: 15px; padding-left: 20px;">
                                <li><strong>Content-Type:</strong> application/pdf, application/vnd.ms-excel, or text/csv</li>
                                <li><strong>Content-Disposition:</strong> attachment; filename="report.pdf"</li>
                                <li><strong>Content-Length:</strong> File size in bytes</li>
                            </ul>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - File Download</div>
                                <div class="status-code status-401">401 - Unauthorized</div>
                                <div class="status-code status-404">404 - Report Not Found</div>
                                <div class="status-code status-410">410 - Report Expired</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Webhooks Tab -->
        <div class="tab-content" id="webhooks">
            <div class="api-section">
                <h2 class="section-title">
                    <i class="fas fa-webhook"></i> Webhook Endpoints
                </h2>

                <!-- Stripe Checkout Webhook -->
                <div class="endpoint-card" data-category="webhooks">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/webhook/stripe-checkout</span>
                        </div>
                        <span class="auth-badge auth-none">No Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Handle Stripe webhook events for checkout sessions and subscription management.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-key"></i> Headers</h4>
                            <div class="param-item">
                                <div class="param-name">Stripe-Signature <span class="param-type">string</span> <span class="param-required">required</span></div>
                                <div class="param-description">Stripe webhook signature for verification</div>
                            </div>
                            <h4 class="params-title"><i class="fas fa-cog"></i> Webhook Events</h4>
                            <div class="param-item">
                                <div class="param-name">checkout.session.completed</div>
                                <div class="param-description">Payment completed successfully</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">customer.subscription.created</div>
                                <div class="param-description">New subscription created</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">customer.subscription.updated</div>
                                <div class="param-description">Subscription status changed</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">invoice.payment_succeeded</div>
                                <div class="param-description">Recurring payment successful</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">customer.subscription.deleted</div>
                                <div class="param-description">Subscription cancelled</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('stripe-webhook-response')">Copy</button>
                            <pre class="code-block" id="stripe-webhook-response">{
    "status": "success"
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-400">400 - Invalid Payload</div>
                                <div class="status-code status-401">401 - Invalid Signature</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Plaid Webhook -->
                <div class="endpoint-card" data-category="webhooks">
                    <div class="endpoint-header">
                        <div class="endpoint-method">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/api/webhook/plaid</span>
                        </div>
                        <span class="auth-badge auth-none">No Auth Required</span>
                    </div>
                    <div class="endpoint-body">
                        <div class="endpoint-description">
                            Handle Plaid webhook events for bank account transactions and updates.
                        </div>

                        <div class="params-section">
                            <h4 class="params-title"><i class="fas fa-cog"></i> Webhook Events</h4>
                            <div class="param-item">
                                <div class="param-name">TRANSACTIONS</div>
                                <div class="param-description">New transactions available</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">ACCOUNTS</div>
                                <div class="param-description">Account information updated</div>
                            </div>
                            <div class="param-item">
                                <div class="param-name">ITEM</div>
                                <div class="param-description">Item status changed</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                            <button class="copy-button" onclick="copyCode('plaid-webhook-response')">Copy</button>
                            <pre class="code-block" id="plaid-webhook-response">{
    "status": "success"
}</pre>

                            <div class="status-codes">
                                <div class="status-code status-200">200 - Success</div>
                                <div class="status-code status-400">400 - Invalid Payload</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="api-section" style="text-align: center; background: linear-gradient(135deg, var(--primary), var(--secondary)); color: white;">
            <h3>🎯 PocketWatch API v3.0</h3>
            <p>Complete REST API Documentation | Last updated: December 19, 2024</p>
            <p>Features: GUI Checkout + Google OAuth + Hierarchical Sub-Bins + Crypto Integration</p>
        </div>
    </div>

    <script>
        // Tab functionality
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Response tab functionality
        document.querySelectorAll('.response-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const parent = tab.closest('.response-section');
                parent.querySelectorAll('.response-tab').forEach(t => t.classList.remove('active'));
                parent.querySelectorAll('.response-content').forEach(c => c.style.display = 'none');

                tab.classList.add('active');
                const responseId = tab.getAttribute('data-response');
                document.getElementById(responseId).style.display = 'block';
            });
        });

        // Search functionality
        const searchBox = document.getElementById('searchBox');
        searchBox.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const endpoints = document.querySelectorAll('.endpoint-card');

            endpoints.forEach(endpoint => {
                const text = endpoint.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    endpoint.style.display = 'block';
                } else {
                    endpoint.style.display = 'none';
                }
            });
        });

        // Filter functionality
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.addEventListener('click', () => {
                document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
                tag.classList.add('active');

                const filter = tag.getAttribute('data-filter');
                const endpoints = document.querySelectorAll('.endpoint-card');

                endpoints.forEach(endpoint => {
                    if (filter === 'all' || endpoint.getAttribute('data-category') === filter) {
                        endpoint.style.display = 'block';
                    } else {
                        endpoint.style.display = 'none';
                    }
                });
            });
        });

        // Response tabs functionality
        document.querySelectorAll('.response-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const parent = tab.closest('.response-section');
                const targetId = tab.getAttribute('data-response');

                // Remove active class from all tabs in this section
                parent.querySelectorAll('.response-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                // Hide all response contents in this section
                parent.querySelectorAll('.response-content').forEach(content => {
                    content.style.display = 'none';
                });

                // Show target response content
                const targetContent = document.getElementById(targetId);
                if (targetContent) {
                    targetContent.style.display = 'block';
                }
            });
        });

        // Copy code functionality
        function copyCode(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;

            navigator.clipboard.writeText(text).then(() => {
                const button = element.previousElementSibling;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = 'var(--success)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'var(--primary)';
                }, 2000);
            });
        }

        console.log('🎯 PocketWatch API Documentation v4.0 - Enhanced Bin System loaded successfully!');
    </script>
</body>
</html>
