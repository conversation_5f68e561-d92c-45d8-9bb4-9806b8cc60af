<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

/*
|--------------------------------------------------------------------------
| Schedule Tasks
|--------------------------------------------------------------------------
|
| Here you may define all of your scheduled tasks that run on a regular basis.
| These tasks will be executed according to the schedule you define.
|
*/

Schedule::command('subscriptions:send-renewal-reminders')
    ->daily()
    ->at('09:00')
    ->description('Send subscription renewal reminders');

// Check for threshold alerts daily
Schedule::command('check:threshold-alerts')
    ->daily()
    ->at('10:00')
    ->description('Check for threshold alerts');
