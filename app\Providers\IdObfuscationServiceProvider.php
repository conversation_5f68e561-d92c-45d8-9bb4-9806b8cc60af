<?php

namespace App\Providers;

use App\Services\IdObfuscationService;
use Illuminate\Support\ServiceProvider;

class IdObfuscationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(IdObfuscationService::class, function ($app) {
            return new IdObfuscationService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
