# PocketWatch - Personal Finance Management Application

PocketWatch is a comprehensive personal finance management application built with Laravel. It helps users track their expenses, manage budgets, and gain insights into their financial health.

## Features

- **User Management**: Registration, authentication, and profile management
- **Bin System**: Organize finances into bins and sub-bins
- **Transaction Tracking**: Record and categorize income and expenses
- **Subscription System**: Tiered subscription model with different features
- **Plaid Integration**: Connect bank accounts and process payments
- **Crypto Wallet Integration**: Track cryptocurrency holdings
- **Reports**: Generate financial reports with customizable parameters
- **Admin Dashboard**: Comprehensive admin interface for managing users and system settings

## Installation

1. Clone the repository
2. Run `composer install`
3. Copy `.env.example` to `.env` and configure your environment variables
4. Run `php artisan key:generate`
5. Run `php artisan migrate --seed`
6. Run `php artisan serve`

## Plaid Integration

PocketWatch integrates with Plaid to provide bank account linking and payment processing capabilities. The integration includes:

### Bank Account Linking

- Connect user bank accounts securely using Plaid Link
- View and manage linked accounts
- Automatically import transactions
- Set default accounts for payments

### Payment Processing

- Process subscription payments using linked bank accounts
- Support for both one-time and recurring payments
- Payment status tracking and notifications
- Seamless integration with the subscription system

### Configuration

To configure Plaid integration:

1. Sign up for a Plaid developer account at [https://plaid.com](https://plaid.com)
2. Obtain your API credentials (client ID, secret, and environment)
3. Configure the credentials in the admin dashboard under Settings > Plaid
4. Set up webhook URL for receiving notifications from Plaid

### API Endpoints

The following API endpoints are available for Plaid integration:

- `GET /api/plaid-payment/accounts` - Get payment-enabled accounts
- `POST /api/plaid-payment/process` - Process a payment
- `GET /api/plaid-payment/status/{paymentId}` - Check payment status

## License

PocketWatch is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
