@extends('admin.layouts.app')

@push('styles')
<style>
    .bg-light-color {
        background-color: var(--light-color) !important;
    }

    .table-hover tbody tr:hover {
        background-color: var(--light-color) !important;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .pagination .page-link {
        color: var(--primary-color);
    }

    .pagination .page-link:hover {
        color: var(--dark-color);
    }
</style>
@endpush

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
        <div>
            <span class="text-muted">Today: {{ date('F d, Y') }}</span>
        </div>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body p-0">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value">{{ $totalUsers }}</div>
                        <div class="stat-label">Total Users</div>
                        <div class="mt-3 d-flex align-items-center">
                            <div class="badge bg-success-soft me-2 px-3 py-2">
                                <i class="fas fa-user-check me-1"></i>{{ $activeUsers }} Active
                            </div>
                            <div class="badge bg-danger-soft px-3 py-2">
                                <i class="fas fa-user-slash me-1"></i>{{ $totalUsers - $activeUsers }} Inactive
                            </div>
                        </div>
                        <div class="progress mt-3" style="height: 6px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: {{ $totalUsers > 0 ? ($activeUsers / $totalUsers) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body p-0">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="stat-value">{{ $premiumUsers }}</div>
                        <div class="stat-label">Premium Users</div>
                        <div class="mt-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ $totalUsers > 0 ? ($premiumUsers / $totalUsers) * 100 : 0 }}%"></div>
                                    </div>
                                </div>
                                <div class="ms-2 badge bg-success-soft px-3 py-2">
                                    <i class="fas fa-percentage me-1"></i>{{ $totalUsers > 0 ? round(($premiumUsers / $totalUsers) * 100) : 0 }}% of users
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body p-0">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="stat-value">{{ $totalTransactions }}</div>
                        <div class="stat-label">Transactions</div>
                        <div class="mt-3 d-flex align-items-center">
                            <div class="badge bg-info-soft me-2 px-3 py-2">
                                <i class="fas fa-box me-1"></i>{{ $totalBins }} Bins
                            </div>
                            <a href="{{ route('admin.transactions.index') }}" class="btn btn-sm btn-outline-primary ms-auto">
                                <i class="fas fa-chart-bar me-1"></i>Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body p-0">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-value">${{ number_format($revenue, 2) }}</div>
                        <div class="stat-label">Revenue</div>
                        <div class="mt-3 d-flex align-items-center">
                            <div class="badge bg-warning-soft me-2 px-3 py-2">
                                <i class="fas fa-wallet me-1"></i>{{ $totalWallets }} Wallets
                            </div>
                            <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-sm btn-outline-primary ms-auto">
                                <i class="fas fa-chart-line me-1"></i>Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary card-title">
                        <i class="fas fa-chart-line me-2"></i>User Signups ({{ date('Y') }})
                    </h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light" type="button" id="chartOptions" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="chartOptions">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2"></i>Export Data</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sync me-2"></i>Refresh</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="userSignupsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary card-title">
                        <i class="fas fa-chart-pie me-2"></i>User Distribution
                    </h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light" type="button" id="pieChartOptions" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="pieChartOptions">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2"></i>Export Data</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sync me-2"></i>Refresh</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="userDistributionChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="me-2">
                            <i class="fas fa-circle text-primary"></i> Base
                        </span>
                        <span class="me-2">
                            <i class="fas fa-circle text-success"></i> Premium
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary card-title">
                        <i class="fas fa-exchange-alt me-2"></i>Recent Transactions
                    </h6>
                    <div class="d-flex">
                        <div class="dropdown me-2">
                            <button class="btn btn-sm btn-light" type="button" id="transactionFilter" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-filter me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="transactionFilter">
                                <li><a class="dropdown-item" href="#">All Transactions</a></li>
                                <li><a class="dropdown-item" href="#">Income Only</a></li>
                                <li><a class="dropdown-item" href="#">Expenses Only</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">Last 7 Days</a></li>
                                <li><a class="dropdown-item" href="#">Last 30 Days</a></li>
                            </ul>
                        </div>
                        <a href="{{ route('admin.transactions.index') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye me-1"></i>View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" class="text-center" width="60">#</th>
                                    <th scope="col">User</th>
                                    <th scope="col">Type</th>
                                    <th scope="col">Amount</th>
                                    <th scope="col">Date</th>
                                    <th scope="col">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentTransactions as $index => $transaction)
                                    <tr>
                                        <td class="text-center fw-bold">{{ $recentTransactions->firstItem() + $index }}</td>
                                        <td>
                                            <a href="{{ route('admin.users.show', $transaction->user_id) }}" class="text-decoration-none">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle bg-light-color d-flex align-items-center justify-content-center me-2"
                                                         style="width: 36px; height: 36px; border-radius: 50%;">
                                                        <span class="fw-bold text-primary">{{ substr($transaction->user->name, 0, 1) }}</span>
                                                    </div>
                                                    <div>
                                                        <span class="fw-medium d-block">{{ $transaction->user->name }}</span>
                                                        <small class="text-muted">{{ $transaction->user->email }}</small>
                                                    </div>
                                                </div>
                                            </a>
                                        </td>
                                        <td>
                                            @if($transaction->transaction_type == 'income')
                                                <span class="badge bg-success rounded-pill">
                                                    <i class="fas fa-arrow-down me-1"></i>Income
                                                </span>
                                            @elseif($transaction->transaction_type == 'expense')
                                                <span class="badge bg-danger rounded-pill">
                                                    <i class="fas fa-arrow-up me-1"></i>Expense
                                                </span>
                                            @else
                                                <span class="badge bg-info rounded-pill">
                                                    <i class="fas fa-exchange-alt me-1"></i>Transfer
                                                </span>
                                            @endif
                                        </td>
                                        <td class="fw-bold {{ $transaction->transaction_type == 'income' ? 'text-success' : ($transaction->transaction_type == 'expense' ? 'text-danger' : 'text-info') }}">
                                            ${{ number_format($transaction->amount, 2) }}
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="far fa-calendar-alt me-2 text-secondary"></i>
                                                <span>{{ $transaction->transaction_date->format('M d, Y') }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-success rounded-pill">
                                                <i class="fas fa-check-circle me-1"></i>{{ ucfirst($transaction->status) }}
                                            </span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">No transactions found</h5>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $recentTransactions->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // User Signups Chart
    var ctx = document.getElementById('userSignupsChart').getContext('2d');
    var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    var signupData = [
        @foreach($monthlySignups as $month => $count)
            {{ $count }},
        @endforeach
    ];

    var userSignupsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'New Users',
                data: signupData,
                backgroundColor: 'rgba(46, 139, 87, 0.1)',
                borderColor: '#2E8B57',
                pointBackgroundColor: '#2E8B57',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: '#2E8B57',
                borderWidth: 2,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // User Distribution Chart
    var ctx2 = document.getElementById('userDistributionChart').getContext('2d');
    var baseUsers = {{ $totalUsers - $premiumUsers }};
    var premiumUsers = {{ $premiumUsers }};

    var userDistributionChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: ['Base', 'Premium'],
            datasets: [{
                data: [baseUsers, premiumUsers],
                backgroundColor: ['#2E8B57', '#28a745'],
                hoverBackgroundColor: ['#1D5E40', '#218838'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
</script>
@endpush
