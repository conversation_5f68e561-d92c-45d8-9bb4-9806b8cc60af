<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch - Simple Stripe Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .auth-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px;
            display: block;
        }

        .auth-section.hidden {
            display: none;
        }

        .packages-section {
            display: none;
        }

        .packages-section.visible {
            display: block;
        }

        .packages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .package-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .package-card:hover {
            transform: translateY(-5px);
        }

        .package-card.popular::before {
            content: "Most Popular";
            position: absolute;
            top: 0;
            right: 0;
            background: #ff6b6b;
            color: white;
            padding: 8px 20px;
            font-size: 0.8rem;
            font-weight: bold;
            border-bottom-left-radius: 12px;
        }

        .package-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .package-name {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .package-price {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .package-description {
            color: #666;
            margin-bottom: 25px;
            text-align: center;
        }

        .package-features {
            list-style: none;
            margin-bottom: 30px;
        }

        .package-features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }

        .package-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        .buy-button {
            width: 100%;
            background: #667eea;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .buy-button:hover {
            background: #5a6fd8;
        }

        .buy-button.popular {
            background: #ff6b6b;
        }

        .buy-button.popular:hover {
            background: #ff5252;
        }

        .auth-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 1rem;
        }

        .auth-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 1rem;
        }

        .auth-button:hover {
            background: #218838;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .flow-info {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .flow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Simple Stripe Integration Test</h1>
            <p>Direct Stripe Checkout → Profile API Response with payment success and new token</p>
        </div>

        <!-- Flow Information -->
        <div class="flow-info">
            <h3>🔄 Simple Payment Flow</h3>
            <div class="flow-step">
                <div class="step-number">1</div>
                <div>
                    <strong>Select Package:</strong> User chooses Base ($9.99) or Premium ($19.99)
                </div>
            </div>
            <div class="flow-step">
                <div class="step-number">2</div>
                <div>
                    <strong>Create Stripe Session:</strong> API creates Stripe Checkout session with trial
                </div>
            </div>
            <div class="flow-step">
                <div class="step-number">3</div>
                <div>
                    <strong>Stripe Checkout:</strong> User redirected to beautiful Stripe payment page
                </div>
            </div>
            <div class="flow-step">
                <div class="step-number">4</div>
                <div>
                    <strong>Profile API Response:</strong> Returns profile data with payment success and new token
                </div>
            </div>
        </div>

        <!-- Authentication Section -->
        <div class="auth-section" id="auth-section">
            <h3>🔐 Sign In to Test</h3>
            <input type="email" id="email" class="auth-input" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" class="auth-input" placeholder="Password" value="password123">
            <button onclick="login()" class="auth-button">Sign In</button>
            <div id="auth-status"></div>
        </div>

        <!-- Packages Section -->
        <div class="packages-section" id="packages-section">
            <div class="packages-grid" id="packages-grid">
                <!-- Packages will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        let authToken = localStorage.getItem('auth_token');

        // Check if user is already logged in
        if (authToken) {
            showPackages();
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (data.token) {
                    authToken = data.token;
                    localStorage.setItem('auth_token', authToken);
                    showStatus('✅ Login successful!', 'success');
                    showPackages();
                } else {
                    showStatus('❌ Login failed: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                showStatus('❌ Login error: ' + error.message, 'error');
            }
        }

        async function showPackages() {
            document.getElementById('auth-section').classList.add('hidden');
            document.getElementById('packages-section').classList.add('visible');

            try {
                const response = await fetch(`${API_BASE}/packages-simple`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const data = await response.json();

                if (data.success) {
                    displayPackages(data.data.packages, data.data.user);
                } else {
                    showStatus('❌ Failed to load packages: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Error loading packages: ' + error.message, 'error');
            }
        }

        function displayPackages(packages, user) {
            const container = document.getElementById('packages-grid');
            container.innerHTML = '';

            packages.forEach(package => {
                const packageDiv = document.createElement('div');
                packageDiv.className = `package-card ${package.popular ? 'popular' : ''}`;
                packageDiv.innerHTML = `
                    <div class="package-header">
                        <div class="package-name">${package.name}</div>
                        <div class="package-price">$${package.price}</div>
                        <div class="package-description">${package.description}</div>
                    </div>
                    <ul class="package-features">
                        ${package.features.slice(0, 6).map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    <button class="buy-button ${package.popular ? 'popular' : ''}"
                            onclick="buyPackage('${package.id}')">
                        🚀 Buy ${package.name} - $${package.price}/month
                    </button>
                `;
                container.appendChild(packageDiv);
            });
        }

        async function buyPackage(packageId) {
            showStatus(`🔄 Creating Stripe URL for ${packageId}...`, 'success');

            try {
                const response = await fetch(`${API_BASE}/packages-simple/stripe-url`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ package_id: packageId })
                });

                const data = await response.json();

                if (data.success) {
                    showStatus(`✅ Redirecting to Stripe Checkout for ${data.data.package.name}...`, 'success');

                    console.log('Stripe Checkout Data:', data.data);
                    console.log('User ID:', data.data.user.id);
                    console.log('Plan ID:', packageId);
                    console.log('Price:', data.data.payment_details.amount);
                    console.log('Session ID:', data.data.session_id);

                    // Redirect to Stripe Checkout page
                    setTimeout(() => {
                        window.location.href = data.data.stripe_checkout_url;
                    }, 1500);
                } else {
                    showStatus('❌ Failed to create Stripe session: ' + data.message, 'error');
                }
            } catch (error) {
                showStatus('❌ Error: ' + error.message, 'error');
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        console.log('🎯 Simple Stripe Integration Test loaded!');
        console.log('📋 Flow: Login → Select Package → Create Stripe Session → Stripe Checkout → Profile API Response');
    </script>
</body>
</html>
