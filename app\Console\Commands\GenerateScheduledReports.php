<?php

namespace App\Console\Commands;

use App\Models\Report;
use App\Models\Bin;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class GenerateScheduledReports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:generate-scheduled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate scheduled reports that are due';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting scheduled reports generation...');

        // Get all recurring reports that are due
        $reports = Report::where('is_recurring', true)
            ->where('next_run_at', '<=', now())
            ->get();

        $this->info("Found {$reports->count()} reports to generate.");

        foreach ($reports as $report) {
            $this->info("Generating report: {$report->name} (ID: {$report->id})");

            try {
                // Update the report status
                $report->update([
                    'status' => 'processing',
                ]);

                // Get the data for the report
                $data = $this->getReportData($report);

                // Generate the file based on the format
                if ($report->format === 'csv') {
                    $filePath = $this->generateCsvReport($report, $data);
                } else {
                    $filePath = $this->generatePdfReport($report, $data);
                }

                // Update the report with the file path
                $report->update([
                    'status' => 'completed',
                    'file_path' => $filePath,
                    'last_generated_at' => now(),
                    'next_run_at' => $this->calculateNextRunDate($report->schedule),
                ]);

                $this->info("Report generated successfully: {$filePath}");
            } catch (\Exception $e) {
                // Update the report status to failed
                $report->update([
                    'status' => 'failed',
                ]);

                $this->error("Failed to generate report: {$e->getMessage()}");
                Log::error("Report generation failed for report ID {$report->id}: {$e->getMessage()}");
            }
        }

        $this->info('Scheduled reports generation completed.');
    }

    /**
     * Calculate the next run date based on the schedule.
     */
    private function calculateNextRunDate($schedule)
    {
        $now = Carbon::now();

        switch ($schedule) {
            case 'daily':
                return $now->copy()->addDay()->startOfDay();
            case 'weekly':
                return $now->copy()->addWeek()->startOfWeek();
            case 'monthly':
                return $now->copy()->addMonth()->startOfMonth();
            default:
                return null;
        }
    }

    /**
     * Get the data for the report.
     */
    private function getReportData(Report $report)
    {
        $user = $report->user;
        $filters = $report->filters;
        $startDate = $report->start_date;
        $endDate = $report->end_date;

        // Update date range for recurring reports
        if ($report->is_recurring) {
            $dates = $this->calculateDateRange($report->period_type);
            $startDate = $dates['start_date'];
            $endDate = $dates['end_date'];
        }

        switch ($report->type) {
            case 'transaction':
                $query = Transaction::where('user_id', $user->id)
                    ->whereBetween('transaction_date', [$startDate, $endDate])
                    ->orderBy('transaction_date', 'desc');

                // Apply filters
                if (!empty($filters['bin_id'])) {
                    $query->where('bin_id', $filters['bin_id']);
                }

                if (!empty($filters['sub_bin_id'])) {
                    $query->where('sub_bin_id', $filters['sub_bin_id']);
                }

                if (!empty($filters['transaction_type'])) {
                    $query->where('transaction_type', $filters['transaction_type']);
                }

                if (!empty($filters['min_amount'])) {
                    $query->where('amount', '>=', $filters['min_amount']);
                }

                if (!empty($filters['max_amount'])) {
                    $query->where('amount', '<=', $filters['max_amount']);
                }

                $transactions = $query->get();

                return [
                    'transactions' => $transactions,
                    'total_income' => $transactions->where('transaction_type', 'income')->sum('amount'),
                    'total_expense' => $transactions->where('transaction_type', 'expense')->sum('amount'),
                    'net_amount' => $transactions->where('transaction_type', 'income')->sum('amount') - $transactions->where('transaction_type', 'expense')->sum('amount'),
                ];

            case 'bin':
                $query = Bin::where('user_id', $user->id);

                // Apply filters
                if (!empty($filters['bin_id'])) {
                    $query->where('id', $filters['bin_id']);
                }

                $bins = $query->with('subBins')->get();

                return [
                    'bins' => $bins,
                    'total_bins' => $bins->count(),
                    'total_sub_bins' => $bins->flatMap->subBins->count(),
                    'total_amount' => $bins->sum('current_amount'),
                ];

            case 'summary':
                $transactions = Transaction::where('user_id', $user->id)
                    ->whereBetween('transaction_date', [$startDate, $endDate])
                    ->get();

                $bins = Bin::where('user_id', $user->id)->with('subBins')->get();

                return [
                    'transactions' => $transactions,
                    'bins' => $bins,
                    'total_income' => $transactions->where('transaction_type', 'income')->sum('amount'),
                    'total_expense' => $transactions->where('transaction_type', 'expense')->sum('amount'),
                    'net_amount' => $transactions->where('transaction_type', 'income')->sum('amount') - $transactions->where('transaction_type', 'expense')->sum('amount'),
                    'total_bins' => $bins->count(),
                    'total_sub_bins' => $bins->flatMap->subBins->count(),
                    'total_bin_amount' => $bins->sum('current_amount'),
                ];

            default:
                return [];
        }
    }

    /**
     * Calculate the date range based on the period type.
     */
    private function calculateDateRange($periodType)
    {
        $now = Carbon::now();

        switch ($periodType) {
            case 'daily':
                return [
                    'start_date' => $now->copy()->startOfDay(),
                    'end_date' => $now->copy()->endOfDay(),
                ];
            case 'weekly':
                return [
                    'start_date' => $now->copy()->startOfWeek(),
                    'end_date' => $now->copy()->endOfWeek(),
                ];
            case 'monthly':
                return [
                    'start_date' => $now->copy()->startOfMonth(),
                    'end_date' => $now->copy()->endOfMonth(),
                ];
            default:
                return [
                    'start_date' => $now->copy()->startOfMonth(),
                    'end_date' => $now->copy()->endOfMonth(),
                ];
        }
    }

    /**
     * Generate a CSV report.
     */
    private function generateCsvReport(Report $report, array $data)
    {
        $headers = [];
        $rows = [];

        switch ($report->type) {
            case 'transaction':
                $headers = ['Date', 'Description', 'Type', 'Amount', 'Bin', 'Sub-Bin', 'Category', 'Notes'];

                foreach ($data['transactions'] as $transaction) {
                    $rows[] = [
                        $transaction->transaction_date->format('Y-m-d'),
                        $transaction->description,
                        ucfirst($transaction->transaction_type),
                        $transaction->amount,
                        $transaction->bin ? $transaction->bin->name : '',
                        $transaction->subBin ? $transaction->subBin->name : '',
                        $transaction->category,
                        $transaction->notes ?? '',
                    ];
                }

                // Add summary rows
                $rows[] = ['', '', '', '', '', '', '', ''];
                $rows[] = ['Summary', '', '', '', '', '', '', ''];
                $rows[] = ['Total Income', '', '', $data['total_income'], '', '', '', ''];
                $rows[] = ['Total Expense', '', '', $data['total_expense'], '', '', '', ''];
                $rows[] = ['Net Amount', '', '', $data['net_amount'], '', '', '', ''];
                break;

            case 'bin':
                $headers = ['Bin Name', 'Type', 'Description', 'Current Amount', 'Min Threshold', 'Max Threshold', 'Currency', 'Status'];

                foreach ($data['bins'] as $bin) {
                    $rows[] = [
                        $bin->name,
                        ucfirst($bin->type),
                        $bin->description,
                        $bin->current_amount,
                        $bin->threshold_min,
                        $bin->threshold_max,
                        $bin->currency,
                        $bin->is_active ? 'Active' : 'Inactive',
                    ];

                    // Add sub-bins
                    foreach ($bin->subBins as $subBin) {
                        $rows[] = [
                            '-- ' . $subBin->name,
                            ucfirst($subBin->type),
                            $subBin->description,
                            $subBin->current_amount,
                            $subBin->threshold_min,
                            $subBin->threshold_max,
                            $subBin->currency,
                            $subBin->is_active ? 'Active' : 'Inactive',
                        ];
                    }
                }

                // Add summary rows
                $rows[] = ['', '', '', '', '', '', '', ''];
                $rows[] = ['Summary', '', '', '', '', '', '', ''];
                $rows[] = ['Total Bins', $data['total_bins'], '', '', '', '', '', ''];
                $rows[] = ['Total Sub-Bins', $data['total_sub_bins'], '', '', '', '', '', ''];
                $rows[] = ['Total Amount', '', '', $data['total_amount'], '', '', '', ''];
                break;

            case 'summary':
                // First section: Transaction summary
                $headers = ['Summary Report', '', '', '', '', '', '', ''];
                $rows[] = ['Period', $report->start_date->format('Y-m-d') . ' to ' . $report->end_date->format('Y-m-d'), '', '', '', '', '', ''];
                $rows[] = ['', '', '', '', '', '', '', ''];
                $rows[] = ['Transaction Summary', '', '', '', '', '', '', ''];
                $rows[] = ['Total Income', '', '', $data['total_income'], '', '', '', ''];
                $rows[] = ['Total Expense', '', '', $data['total_expense'], '', '', '', ''];
                $rows[] = ['Net Amount', '', '', $data['net_amount'], '', '', '', ''];
                $rows[] = ['', '', '', '', '', '', '', ''];

                // Second section: Bin summary
                $rows[] = ['Bin Summary', '', '', '', '', '', '', ''];
                $rows[] = ['Total Bins', $data['total_bins'], '', '', '', '', '', ''];
                $rows[] = ['Total Sub-Bins', $data['total_sub_bins'], '', '', '', '', '', ''];
                $rows[] = ['Total Bin Amount', '', '', $data['total_bin_amount'], '', '', '', ''];
                $rows[] = ['', '', '', '', '', '', '', ''];

                // Third section: Recent transactions
                $rows[] = ['Recent Transactions', '', '', '', '', '', '', ''];
                $rows[] = ['Date', 'Description', 'Type', 'Amount', 'Bin', 'Sub-Bin', 'Category', 'Notes'];

                foreach ($data['transactions']->take(10) as $transaction) {
                    $rows[] = [
                        $transaction->transaction_date->format('Y-m-d'),
                        $transaction->description,
                        ucfirst($transaction->transaction_type),
                        $transaction->amount,
                        $transaction->bin ? $transaction->bin->name : '',
                        $transaction->subBin ? $transaction->subBin->name : '',
                        $transaction->category,
                        $transaction->notes ?? '',
                    ];
                }
                break;
        }

        // Create the CSV file
        $fileName = 'reports/' . $report->user_id . '/' . uniqid() . '.csv';
        $filePath = storage_path('app/' . $fileName);

        // Ensure the directory exists
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        // Write the CSV file
        $file = fopen($filePath, 'w');

        // Add UTF-8 BOM to ensure Excel opens the file correctly with UTF-8 encoding
        fputs($file, "\xEF\xBB\xBF");

        // Write headers
        fputcsv($file, $headers);

        // Write rows
        foreach ($rows as $row) {
            fputcsv($file, $row);
        }

        fclose($file);

        return $fileName;
    }

    /**
     * Generate a PDF report.
     */
    private function generatePdfReport(Report $report, array $data)
    {
        $view = '';
        $viewData = [];

        switch ($report->type) {
            case 'transaction':
                $view = 'reports.pdf.transactions';
                $viewData = [
                    'report' => $report,
                    'transactions' => $data['transactions'],
                    'total_income' => $data['total_income'],
                    'total_expense' => $data['total_expense'],
                    'net_amount' => $data['net_amount'],
                ];
                break;

            case 'bin':
                $view = 'reports.pdf.bins';
                $viewData = [
                    'report' => $report,
                    'bins' => $data['bins'],
                    'total_bins' => $data['total_bins'],
                    'total_sub_bins' => $data['total_sub_bins'],
                    'total_amount' => $data['total_amount'],
                ];
                break;

            case 'summary':
                $view = 'reports.pdf.summary';
                $viewData = [
                    'report' => $report,
                    'transactions' => $data['transactions']->take(10),
                    'bins' => $data['bins'],
                    'total_income' => $data['total_income'],
                    'total_expense' => $data['total_expense'],
                    'net_amount' => $data['net_amount'],
                    'total_bins' => $data['total_bins'],
                    'total_sub_bins' => $data['total_sub_bins'],
                    'total_bin_amount' => $data['total_bin_amount'],
                ];
                break;
        }

        // Generate the PDF
        $pdf = PDF::loadView($view, $viewData);

        // Save the PDF
        $fileName = 'reports/' . $report->user_id . '/' . uniqid() . '.pdf';
        Storage::put($fileName, $pdf->output());

        return $fileName;
    }
}
