<?php

namespace App\Http\Controllers;

use App\Services\TwilioService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\User;

class PhoneVerificationController extends Controller
{
    protected $twilioService;

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;
    }

    /**
     * Send verification code to phone number
     */
    public function sendCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone_number' => [
                'required',
                'string',
                'regex:/^[\+]?[1-9][\d]{0,15}$/', // E.164 format validation
            ]
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid phone number format',
                'errors' => $validator->errors()
            ], 422);
        }

        $phoneNumber = $request->input('phone_number');

        // Check if phone number is already verified by another user
        if (Auth::check()) {
            $existingUser = User::where('phone_number', $phoneNumber)
                ->where('phone_verified_at', '!=', null)
                ->where('id', '!=', Auth::id())
                ->first();

            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'This phone number is already verified by another account',
                    'error_code' => 'PHONE_ALREADY_VERIFIED'
                ], 409);
            }
        }

        try {
            $result = $this->twilioService->sendVerificationCode($phoneNumber);

            if ($result['success']) {
                // Store phone number in session for verification
                session(['verification_phone_number' => $phoneNumber]);

                Log::info('Verification code sent', [
                    'phone' => $phoneNumber,
                    'user_id' => Auth::id(),
                    'sid' => $result['sid'] ?? null
                ]);

                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'phone_number' => $phoneNumber,
                    'sid' => $result['sid'] ?? null,
                    'status' => $result['status'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? null
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Phone verification code sending failed', [
                'phone' => $phoneNumber,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification code. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify the code sent to phone number
     */
    public function verifyCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string',
            'code' => 'required|string|min:4|max:8'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid input',
                'errors' => $validator->errors()
            ], 422);
        }

        $phoneNumber = $request->input('phone_number');
        $code = $request->input('code');

        // Verify the phone number matches the one in session
        $sessionPhone = session('verification_phone_number');
        if ($sessionPhone !== $phoneNumber) {
            return response()->json([
                'success' => false,
                'message' => 'Phone number mismatch. Please request a new code.',
                'error_code' => 'PHONE_MISMATCH'
            ], 400);
        }

        try {
            $result = $this->twilioService->verifyCode($phoneNumber, $code);

            if ($result['success']) {
                // Update user's phone verification if authenticated
                if (Auth::check()) {
                    $user = Auth::user();
                    $user->phone_number = $phoneNumber;
                    $user->phone_verified_at = now();
                    $user->save();

                    Log::info('Phone number verified and updated', [
                        'user_id' => $user->id,
                        'phone' => $phoneNumber
                    ]);
                }

                // Clear session data
                session()->forget('verification_phone_number');

                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'phone_number' => $phoneNumber,
                    'verified_at' => now()->toISOString(),
                    'status' => $result['status'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code'] ?? null
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Phone verification failed', [
                'phone' => $phoneNumber,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Verification failed. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resend verification code
     */
    public function resendCode(Request $request): JsonResponse
    {
        $phoneNumber = session('verification_phone_number');

        if (!$phoneNumber) {
            return response()->json([
                'success' => false,
                'message' => 'No phone number found in session. Please start verification process again.',
                'error_code' => 'NO_PHONE_IN_SESSION'
            ], 400);
        }

        // Use the same sendCode logic
        $request->merge(['phone_number' => $phoneNumber]);
        return $this->sendCode($request);
    }

    /**
     * Check verification status
     */
    public function checkStatus(Request $request): JsonResponse
    {
        $phoneNumber = $request->input('phone_number');
        $sessionPhone = session('verification_phone_number');

        if (!$phoneNumber && !$sessionPhone) {
            return response()->json([
                'success' => false,
                'message' => 'No phone number provided',
                'error_code' => 'NO_PHONE_NUMBER'
            ], 400);
        }

        $checkPhone = $phoneNumber ?: $sessionPhone;

        // Check if user has this phone number verified
        if (Auth::check()) {
            $user = Auth::user();
            $isVerified = $user->phone_number === $checkPhone && $user->phone_verified_at !== null;

            return response()->json([
                'success' => true,
                'phone_number' => $checkPhone,
                'is_verified' => $isVerified,
                'verified_at' => $user->phone_verified_at,
                'in_session' => $sessionPhone === $checkPhone
            ]);
        }

        return response()->json([
            'success' => true,
            'phone_number' => $checkPhone,
            'is_verified' => false,
            'verified_at' => null,
            'in_session' => $sessionPhone === $checkPhone
        ]);
    }

    /**
     * Send custom SMS (for authenticated users)
     */
    public function sendSMS(Request $request): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string',
            'message' => 'required|string|max:1600' // SMS character limit
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid input',
                'errors' => $validator->errors()
            ], 422);
        }

        $phoneNumber = $request->input('phone_number');
        $message = $request->input('message');

        try {
            $result = $this->twilioService->sendSMS($phoneNumber, $message);

            Log::info('Custom SMS sent', [
                'user_id' => Auth::id(),
                'phone' => $phoneNumber,
                'sid' => $result['sid'] ?? null
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Custom SMS failed', [
                'user_id' => Auth::id(),
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send SMS',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get SMS status
     */
    public function getSMSStatus(Request $request, string $messageSid): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        try {
            $result = $this->twilioService->getSMSStatus($messageSid);
            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get SMS status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test Twilio connection
     */
    public function testConnection(): JsonResponse
    {
        try {
            $result = $this->twilioService->testConnection();
            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
