<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\User;
use App\Notifications\Subscriptions\SubscriptionCanceledNotification;
use App\Notifications\Subscriptions\SubscriptionCreatedNotification;
use App\Notifications\Subscriptions\SubscriptionUpdatedNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\ApiErrorException;
use Stripe\StripeClient;

class SubscriptionController extends Controller
{
    /**
     * The Stripe client instance.
     *
     * @var \Stripe\StripeClient
     */
    protected $stripe;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    /**
     * Display the user's subscription.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('subscriptions.index');
    }

    /**
     * Display the subscription plans.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function plans(Request $request)
    {
        $billingCycle = $request->query('billing', 'monthly');
        
        return view('subscriptions.plans', [
            'billingCycle' => $billingCycle,
        ]);
    }

    /**
     * Display the checkout page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function checkout(Request $request)
    {
        $tier = $request->query('tier', 'base');
        $billingCycle = $request->query('billing', 'monthly');
        
        // Validate tier and billing cycle
        if (!in_array($tier, ['base', 'premium'])) {
            $tier = 'base';
        }
        
        if (!in_array($billingCycle, ['monthly', 'yearly'])) {
            $billingCycle = 'monthly';
        }
        
        return view('subscriptions.checkout', [
            'tier' => $tier,
            'billingCycle' => $billingCycle,
        ]);
    }

    /**
     * Process the subscription checkout.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function process(Request $request)
    {
        $request->validate([
            'subscription_tier' => 'required|string|in:base,premium',
            'billing_cycle' => 'required|string|in:monthly,yearly',
            'stripeToken' => 'required|string',
        ]);
        
        $user = Auth::user();
        $subscriptionTier = $request->subscription_tier;
        $billingCycle = $request->billing_cycle;
        $stripeToken = $request->stripeToken;
        
        try {
            // Check if user already has an active subscription
            $activeSubscription = $user->subscriptions()
                ->whereNull('ends_at')
                ->orWhere('ends_at', '>', now())
                ->first();
                
            if ($activeSubscription) {
                return redirect()->route('subscriptions.index')
                    ->with('error', 'You already have an active subscription.');
            }
            
            // Get or create Stripe customer
            $stripeCustomerId = $user->stripe_id;
            
            if (!$stripeCustomerId) {
                $customer = $this->stripe->customers->create([
                    'email' => $user->email,
                    'name' => $user->name,
                    'source' => $stripeToken,
                    'metadata' => [
                        'user_id' => $user->id,
                    ],
                ]);
                
                $stripeCustomerId = $customer->id;
                
                // Save Stripe customer ID to user
                $user->stripe_id = $stripeCustomerId;
                $user->save();
            } else {
                // Update customer's payment method
                $this->stripe->customers->update($stripeCustomerId, [
                    'source' => $stripeToken,
                ]);
            }
            
            // Get price ID based on tier and billing cycle
            $priceId = $this->getPriceId($subscriptionTier, $billingCycle);
            
            // Create subscription with trial
            $stripeSubscription = $this->stripe->subscriptions->create([
                'customer' => $stripeCustomerId,
                'items' => [
                    ['price' => $priceId],
                ],
                'trial_period_days' => 7,
                'metadata' => [
                    'user_id' => $user->id,
                    'subscription_tier' => $subscriptionTier,
                    'billing_cycle' => $billingCycle,
                ],
            ]);
            
            // Create subscription record
            $subscription = new Subscription([
                'user_id' => $user->id,
                'name' => ucfirst($subscriptionTier) . ' ' . ucfirst($billingCycle),
                'stripe_id' => $stripeSubscription->id,
                'stripe_status' => $stripeSubscription->status,
                'stripe_price' => $priceId,
                'subscription_tier' => $subscriptionTier,
                'billing_cycle' => $billingCycle,
                'price' => $this->getPrice($subscriptionTier, $billingCycle),
                'currency' => 'USD',
                'features' => $this->getFeatures($subscriptionTier),
                'trial_ends_at' => now()->addDays(7),
            ]);
            
            $subscription->save();
            
            // Update user's subscription tier
            $user->subscription_tier = $subscriptionTier;
            $user->save();
            
            // Send subscription created notification
            $user->notify(new SubscriptionCreatedNotification($subscription));
            
            return redirect()->route('subscriptions.success', ['id' => $subscription->id])
                ->with('success', 'Your subscription has been created successfully!');
                
        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error processing your payment: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error creating your subscription. Please try again.');
        }
    }

    /**
     * Display the subscription success page.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function success($id)
    {
        $subscription = Subscription::findOrFail($id);
        
        // Check if subscription belongs to user
        if ($subscription->user_id !== Auth::id()) {
            abort(403);
        }
        
        return view('subscriptions.success', [
            'subscription' => $subscription,
        ]);
    }

    /**
     * Display the billing history.
     *
     * @return \Illuminate\View\View
     */
    public function billingHistory()
    {
        $user = Auth::user();
        
        // Initialize empty arrays
        $invoices = [];
        $paymentMethods = [];
        $defaultPaymentMethod = null;
        
        // Only fetch if user has a Stripe ID
        if ($user->stripe_id) {
            try {
                // Get invoices
                $invoices = $this->stripe->invoices->all([
                    'customer' => $user->stripe_id,
                    'limit' => 10,
                ]);
                
                // Get payment methods
                $paymentMethods = $this->stripe->paymentMethods->all([
                    'customer' => $user->stripe_id,
                    'type' => 'card',
                ]);
                
                // Get default payment method
                $customer = $this->stripe->customers->retrieve($user->stripe_id);
                if ($customer->invoice_settings->default_payment_method) {
                    $defaultPaymentMethod = $this->stripe->paymentMethods->retrieve(
                        $customer->invoice_settings->default_payment_method
                    );
                } elseif (count($paymentMethods->data) > 0) {
                    $defaultPaymentMethod = $paymentMethods->data[0];
                }
            } catch (ApiErrorException $e) {
                Log::error('Stripe error: ' . $e->getMessage());
            }
        }
        
        return view('subscriptions.billing-history', [
            'invoices' => $invoices->data ?? [],
            'paymentMethods' => $paymentMethods->data ?? [],
            'defaultPaymentMethod' => $defaultPaymentMethod,
        ]);
    }

    /**
     * Add a new payment method.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addPaymentMethod(Request $request)
    {
        $request->validate([
            'stripeToken' => 'required|string',
            'set_as_default' => 'sometimes|boolean',
        ]);
        
        $user = Auth::user();
        
        if (!$user->stripe_id) {
            return redirect()->back()
                ->with('error', 'You need an active subscription to add a payment method.');
        }
        
        try {
            // Create payment method
            $paymentMethod = $this->stripe->paymentMethods->create([
                'type' => 'card',
                'card' => [
                    'token' => $request->stripeToken,
                ],
            ]);
            
            // Attach payment method to customer
            $this->stripe->paymentMethods->attach($paymentMethod->id, [
                'customer' => $user->stripe_id,
            ]);
            
            // Set as default if requested
            if ($request->set_as_default) {
                $this->stripe->customers->update($user->stripe_id, [
                    'invoice_settings' => [
                        'default_payment_method' => $paymentMethod->id,
                    ],
                ]);
            }
            
            return redirect()->back()
                ->with('success', 'Payment method added successfully.');
                
        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error adding your payment method: ' . $e->getMessage());
        }
    }

    /**
     * Set a payment method as default.
     *
     * @param  string  $paymentMethodId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function setDefaultPaymentMethod($paymentMethodId)
    {
        $user = Auth::user();
        
        if (!$user->stripe_id) {
            return redirect()->back()
                ->with('error', 'You need an active subscription to set a default payment method.');
        }
        
        try {
            // Set as default
            $this->stripe->customers->update($user->stripe_id, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId,
                ],
            ]);
            
            return redirect()->back()
                ->with('success', 'Default payment method updated successfully.');
                
        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error updating your default payment method: ' . $e->getMessage());
        }
    }

    /**
     * Remove a payment method.
     *
     * @param  string  $paymentMethodId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function removePaymentMethod($paymentMethodId)
    {
        $user = Auth::user();
        
        if (!$user->stripe_id) {
            return redirect()->back()
                ->with('error', 'You need an active subscription to remove a payment method.');
        }
        
        try {
            // Check if it's the default payment method
            $customer = $this->stripe->customers->retrieve($user->stripe_id);
            if ($customer->invoice_settings->default_payment_method === $paymentMethodId) {
                return redirect()->back()
                    ->with('error', 'You cannot remove your default payment method. Set another payment method as default first.');
            }
            
            // Detach payment method
            $this->stripe->paymentMethods->detach($paymentMethodId);
            
            return redirect()->back()
                ->with('success', 'Payment method removed successfully.');
                
        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error removing your payment method: ' . $e->getMessage());
        }
    }

    /**
     * Update the billing cycle.
     *
     * @param  string  $cycle
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateBillingCycle($cycle)
    {
        if (!in_array($cycle, ['monthly', 'yearly'])) {
            return redirect()->back()
                ->with('error', 'Invalid billing cycle.');
        }
        
        $user = Auth::user();
        $subscription = $user->getActiveSubscription();
        
        if (!$subscription) {
            return redirect()->back()
                ->with('error', 'You don\'t have an active subscription.');
        }
        
        if ($subscription->billing_cycle === $cycle) {
            return redirect()->back()
                ->with('info', 'You are already on ' . $cycle . ' billing.');
        }
        
        try {
            // Get new price ID
            $newPriceId = $this->getPriceId($subscription->subscription_tier, $cycle);
            
            // Update subscription in Stripe
            $this->stripe->subscriptions->update($subscription->stripe_id, [
                'items' => [
                    [
                        'id' => $this->getSubscriptionItemId($subscription->stripe_id),
                        'price' => $newPriceId,
                    ],
                ],
                'proration_behavior' => 'create_prorations',
            ]);
            
            // Update local subscription record
            $subscription->billing_cycle = $cycle;
            $subscription->stripe_price = $newPriceId;
            $subscription->price = $this->getPrice($subscription->subscription_tier, $cycle);
            $subscription->save();
            
            // Send notification
            $user->notify(new SubscriptionUpdatedNotification($subscription));
            
            return redirect()->back()
                ->with('success', 'Billing cycle updated to ' . ucfirst($cycle) . ' successfully.');
                
        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error updating your billing cycle: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error updating your billing cycle. Please try again.');
        }
    }

    /**
     * Cancel a subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $id)
    {
        $request->validate([
            'cancel_reason' => 'required|string',
            'cancel_feedback' => 'nullable|string',
        ]);
        
        $subscription = Subscription::findOrFail($id);
        
        // Check if subscription belongs to user
        if ($subscription->user_id !== Auth::id()) {
            abort(403);
        }
        
        // Only allow canceling if subscription is active
        if ($subscription->stripe_status !== 'active' && $subscription->stripe_status !== 'trialing') {
            return redirect()->back()
                ->with('error', 'Cannot cancel inactive subscription.');
        }
        
        try {
            // Cancel subscription in Stripe
            $this->stripe->subscriptions->update($subscription->stripe_id, [
                'cancel_at_period_end' => true,
                'metadata' => [
                    'cancel_reason' => $request->cancel_reason,
                    'cancel_feedback' => $request->cancel_feedback,
                ],
            ]);
            
            // Get the subscription end date from Stripe
            $stripeSubscription = $this->stripe->subscriptions->retrieve($subscription->stripe_id);
            
            // Update local subscription record
            $subscription->stripe_status = $stripeSubscription->status;
            $subscription->ends_at = now()->timestamp($stripeSubscription->current_period_end);
            $subscription->save();
            
            // Send cancellation notification
            Auth::user()->notify(new SubscriptionCanceledNotification($subscription));
            
            return redirect()->route('subscriptions.index')
                ->with('success', 'Your subscription has been canceled. You will still have access until ' . $subscription->ends_at->format('M d, Y') . '.');
                
        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error canceling your subscription: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error canceling your subscription. Please try again.');
        }
    }

    /**
     * Resume a canceled subscription.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resume($id)
    {
        $subscription = Subscription::findOrFail($id);
        
        // Check if subscription belongs to user
        if ($subscription->user_id !== Auth::id()) {
            abort(403);
        }
        
        // Only allow resuming if subscription is canceled but not ended
        if ($subscription->stripe_status !== 'active' || !$subscription->ends_at || $subscription->ends_at->isPast()) {
            return redirect()->back()
                ->with('error', 'This subscription cannot be resumed.');
        }
        
        try {
            // Resume subscription in Stripe
            $this->stripe->subscriptions->update($subscription->stripe_id, [
                'cancel_at_period_end' => false,
            ]);
            
            // Update local subscription record
            $subscription->ends_at = null;
            $subscription->save();
            
            return redirect()->route('subscriptions.index')
                ->with('success', 'Your subscription has been resumed.');
                
        } catch (ApiErrorException $e) {
            Log::error('Stripe error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error resuming your subscription: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Subscription error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'There was an error resuming your subscription. Please try again.');
        }
    }

    /**
     * Get the subscription item ID.
     *
     * @param  string  $subscriptionId
     * @return string
     */
    protected function getSubscriptionItemId($subscriptionId)
    {
        $subscription = $this->stripe->subscriptions->retrieve($subscriptionId, [
            'expand' => ['items'],
        ]);
        
        return $subscription->items->data[0]->id;
    }

    /**
     * Get the price ID based on tier and billing cycle.
     *
     * @param  string  $tier
     * @param  string  $billingCycle
     * @return string
     */
    protected function getPriceId($tier, $billingCycle)
    {
        $prices = [
            'base' => [
                'monthly' => config('services.stripe.prices.base_monthly'),
                'yearly' => config('services.stripe.prices.base_yearly'),
            ],
            'premium' => [
                'monthly' => config('services.stripe.prices.premium_monthly'),
                'yearly' => config('services.stripe.prices.premium_yearly'),
            ],
        ];
        
        return $prices[$tier][$billingCycle];
    }

    /**
     * Get the price based on tier and billing cycle.
     *
     * @param  string  $tier
     * @param  string  $billingCycle
     * @return float
     */
    protected function getPrice($tier, $billingCycle)
    {
        $prices = [
            'base' => [
                'monthly' => 5.00,
                'yearly' => 50.00,
            ],
            'premium' => [
                'monthly' => 10.00,
                'yearly' => 100.00,
            ],
        ];
        
        return $prices[$tier][$billingCycle];
    }

    /**
     * Get the features based on tier.
     *
     * @param  string  $tier
     * @return array
     */
    protected function getFeatures($tier)
    {
        $features = [
            'base' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => 3,
                'crypto_scanner' => false,
                'unlimited_sub_bins' => false,
                'priority_notifications' => false,
                'advanced_ai_suggestions' => false,
            ],
            'premium' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => -1, // Unlimited
                'crypto_scanner' => true,
                'unlimited_sub_bins' => true,
                'priority_notifications' => true,
                'advanced_ai_suggestions' => true,
            ],
        ];
        
        return $features[$tier] ?? $features['base'];
    }
}
