<?php

namespace App\Http\Controllers\API;

use App\Helpers\IdObfuscator;
use App\Http\Controllers\Controller;
use App\Http\Resources\SubscriptionResource;
use App\Models\Subscription;
use App\Models\SubscriptionHistory;
use App\Models\User;
use App\Notifications\Subscriptions\SubscriptionCanceledNotification;
use App\Notifications\Subscriptions\SubscriptionCreatedNotification;
use App\Notifications\Subscriptions\SubscriptionUpdatedNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Stripe\Exception\ApiErrorException;
use Stripe\StripeClient;

class SubscriptionController extends Controller
{
    /**
     * The Stripe client instance.
     *
     * @var \Stripe\StripeClient
     */
    protected $stripe;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $subscriptions = $request->user()->subscriptions;

        return response()->json([
            'success' => true,
            'data' => [
                'subscriptions' => SubscriptionResource::collection($subscriptions)
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subscription_tier' => 'required|string|in:base,premium',
            'billing_cycle' => 'required|string|in:monthly,yearly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $subscriptionTier = $request->subscription_tier;
        $billingCycle = $request->billing_cycle;

        // Check if user already has an active subscription
        $activeSubscription = $user->subscriptions()
            ->whereNull('ends_at')
            ->orWhere('ends_at', '>', now())
            ->first();

        if ($activeSubscription) {
            return response()->json([
                'success' => false,
                'message' => 'User already has an active subscription',
                'data' => [
                    'subscription' => $activeSubscription
                ]
            ], 400);
        }

        try {
            // Get price ID based on subscription tier and billing cycle
            $priceId = $this->getPriceId($subscriptionTier, $billingCycle);

            // Create a customer in Stripe if not exists
            $stripeCustomerId = $user->stripe_id;
            if (!$stripeCustomerId) {
                $customer = $this->stripe->customers->create([
                    'email' => $user->email,
                    'name' => $user->name,
                    'metadata' => [
                        'user_id' => $user->id,
                    ],
                ]);
                $stripeCustomerId = $customer->id;
                $user->stripe_id = $stripeCustomerId;
                $user->save();
            }

            // Determine trial period based on user's current trial status
            $trialPeriodDays = 0; // Default to no trial for new subscriptions

            // If user is currently on trial and hasn't expired, calculate remaining days
            if ($user->subscription_tier === 'trial' && !$user->trialExpired()) {
                $trialPeriodDays = $user->getTrialDaysRemaining();
            }

            // If user hasn't started trial yet, give them full trial period
            if (!$user->trial_started_at) {
                $trialPeriodDays = (int) config('services.subscription.trial_days', 7);
            }

            // Create a checkout session
            $sessionData = [
                'customer' => $stripeCustomerId,
                'payment_method_types' => ['card'],
                'line_items' => [
                    [
                        'price' => $priceId,
                        'quantity' => 1,
                    ],
                ],
                'mode' => 'subscription',
                'success_url' => config('app.url') . '/subscription/success?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => config('app.url') . '/subscription/cancel',
                'metadata' => [
                    'user_id' => $user->id,
                    'subscription_tier' => $subscriptionTier,
                    'billing_cycle' => $billingCycle,
                ],
            ];

            // Add trial period if applicable
            if ($trialPeriodDays > 0) {
                $sessionData['subscription_data'] = [
                    'trial_period_days' => $trialPeriodDays,
                ];
            }

            $session = $this->stripe->checkout->sessions->create($sessionData);

            // Create a pending subscription record
            $trialEndsAt = null;
            if ($trialPeriodDays > 0) {
                $trialEndsAt = now()->addDays($trialPeriodDays);
            }

            $subscription = new Subscription([
                'user_id' => $user->id,
                'name' => ucfirst($subscriptionTier) . ' ' . ucfirst($billingCycle),
                'stripe_id' => null, // Will be updated after successful payment
                'stripe_status' => 'pending',
                'stripe_price' => $priceId,
                'subscription_tier' => $subscriptionTier,
                'billing_cycle' => $billingCycle,
                'price' => $this->getPrice($subscriptionTier, $billingCycle),
                'currency' => 'USD',
                'features' => $this->getFeatures($subscriptionTier),
                'trial_ends_at' => $trialEndsAt,
            ]);
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => 'Checkout session created',
                'data' => [
                    'checkout_url' => $session->url,
                    'session_id' => $session->id,
                    'subscription' => new SubscriptionResource($subscription)
                ]
            ], 201);
        } catch (ApiErrorException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Stripe price ID based on subscription tier and billing cycle.
     *
     * @param  string  $tier
     * @param  string  $cycle
     * @return string
     */
    private function getPriceId(string $tier, string $cycle): string
    {
        // These should be stored in the database or config
        $prices = [
            'base' => [
                'monthly' => config('services.stripe.prices.base_monthly'),
                'yearly' => config('services.stripe.prices.base_yearly'),
            ],
            'premium' => [
                'monthly' => config('services.stripe.prices.premium_monthly'),
                'yearly' => config('services.stripe.prices.premium_yearly'),
            ],
        ];

        return $prices[$tier][$cycle] ?? $prices['base']['monthly'];
    }

    /**
     * Get price amount based on subscription tier and billing cycle.
     *
     * @param  string  $tier
     * @param  string  $cycle
     * @return float
     */
    private function getPrice(string $tier, string $cycle): float
    {
        // Use prices from configuration
        $prices = [
            'base' => [
                'monthly' => config('services.subscription.base_monthly_price', 5.00),
                'yearly' => config('services.subscription.base_yearly_price', 50.00), // 10 months for the price of 12 (2 months free)
            ],
            'premium' => [
                'monthly' => config('services.subscription.premium_monthly_price', 10.00),
                'yearly' => config('services.subscription.premium_yearly_price', 100.00), // 10 months for the price of 12 (2 months free)
            ],
        ];

        return $prices[$tier][$cycle] ?? $prices['base']['monthly'];
    }

    /**
     * Get features based on subscription tier.
     *
     * @param  string  $tier
     * @return array
     */
    private function getFeatures(string $tier): array
    {
        $features = [
            'base' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => 3,
                'crypto_scanner' => false,
                'unlimited_sub_bins' => false,
                'priority_notifications' => false,
                'advanced_ai_suggestions' => false,
            ],
            'premium' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => -1, // Unlimited
                'crypto_scanner' => true,
                'unlimited_sub_bins' => true,
                'priority_notifications' => true,
                'advanced_ai_suggestions' => true,
            ],
        ];

        return $features[$tier] ?? $features['base'];
    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, string $uuid)
    {
        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }
        }

        if (!$subscription || $subscription->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'subscription' => new SubscriptionResource($subscription)
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $uuid)
    {
        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }
        }

        if (!$subscription || $subscription->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription not found'
            ], 404);
        }

        // Only allow updating if subscription is active
        if ($subscription->stripe_status !== 'active' && $subscription->stripe_status !== 'trialing') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot update inactive subscription'
            ], 400);
        }

        try {
            // Handle subscription upgrade/downgrade
            if ($request->has('subscription_tier') && $request->subscription_tier !== $subscription->subscription_tier) {
                $newTier = $request->subscription_tier;
                $billingCycle = $subscription->billing_cycle;

                // Get new price ID
                $newPriceId = $this->getPriceId($newTier, $billingCycle);

                // Update subscription in Stripe
                $this->stripe->subscriptions->update($subscription->stripe_id, [
                    'items' => [
                        [
                            'id' => $subscription->items[0]->id, // Assuming single item subscription
                            'price' => $newPriceId,
                        ],
                    ],
                ]);

                // Update local subscription record
                $subscription->subscription_tier = $newTier;
                $subscription->stripe_price = $newPriceId;
                $subscription->price = $this->getPrice($newTier, $billingCycle);
                $subscription->features = $this->getFeatures($newTier);
                $subscription->save();

                // Update user's subscription tier
                $user = $request->user();
                $user->subscription_tier = $newTier;
                $user->save();

                // Send subscription updated notification with previous tier
                $user->notify(new SubscriptionUpdatedNotification($subscription, $subscription->subscription_tier));
            }

            return response()->json([
                'success' => true,
                'message' => 'Subscription updated successfully',
                'data' => [
                    'subscription' => new SubscriptionResource($subscription)
                ]
            ]);
        } catch (ApiErrorException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage (cancel subscription).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $uuid
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, string $uuid)
    {
        $subscription = Subscription::where('uuid', $uuid)->first();

        if (!$subscription) {
            // Try to find by ID (for backward compatibility)
            $deobfuscatedId = IdObfuscator::deobfuscate($uuid, 'subscription');
            if ($deobfuscatedId) {
                $subscription = Subscription::find($deobfuscatedId);
            }
        }

        if (!$subscription || $subscription->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription not found'
            ], 404);
        }

        // Only allow canceling if subscription is active
        if ($subscription->stripe_status !== 'active' && $subscription->stripe_status !== 'trialing') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel inactive subscription'
            ], 400);
        }

        try {
            // Cancel subscription in Stripe
            $this->stripe->subscriptions->cancel($subscription->stripe_id, [
                'cancel_at_period_end' => true,
            ]);

            // Update local subscription record
            $subscription->stripe_status = 'canceled';
            $subscription->ends_at = now()->addDays(30); // Approximate end date, will be updated by webhook
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => 'Subscription canceled successfully',
                'data' => [
                    'subscription' => new SubscriptionResource($subscription)
                ]
            ]);
        } catch (ApiErrorException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error canceling subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Stripe webhook events.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleWebhook(Request $request)
    {
        $payload = $request->all();
        $event = null;

        try {
            $event = $this->stripe->events->retrieve($payload['id']);
        } catch (ApiErrorException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving event',
                'error' => $e->getMessage()
            ], 400);
        }

        // Handle the event
        switch ($event->type) {
            case 'checkout.session.completed':
                $this->handleCheckoutSessionCompleted($event->data->object);
                break;
            case 'customer.subscription.updated':
                $this->handleSubscriptionUpdated($event->data->object);
                break;
            case 'customer.subscription.trial_will_end':
                $this->handleTrialWillEnd($event->data->object);
                break;
            case 'customer.subscription.deleted':
                $this->handleSubscriptionDeleted($event->data->object);
                break;
            default:
                // Unexpected event type
                return response()->json([
                    'success' => true,
                    'message' => 'Unhandled event type: ' . $event->type
                ], 200);
        }

        return response()->json([
            'success' => true,
            'message' => 'Webhook handled successfully'
        ]);
    }

    /**
     * Handle checkout.session.completed event.
     *
     * @param  object  $session
     * @return void
     */
    private function handleCheckoutSessionCompleted($session)
    {
        // Find the user by metadata
        $userId = $session->metadata->user_id;
        $user = User::find($userId);

        if (!$user) {
            return;
        }

        // Find the pending subscription
        $subscription = $user->subscriptions()
            ->where('stripe_status', 'pending')
            ->latest()
            ->first();

        if (!$subscription) {
            return;
        }

        // Get the subscription ID from the session
        $stripeSubscriptionId = $session->subscription;

        // Update the subscription with the Stripe subscription ID
        $subscription->stripe_id = $stripeSubscriptionId;
        $subscription->stripe_status = 'active';
        $subscription->save();

        // Update user's subscription tier
        $user->subscription_tier = $subscription->subscription_tier;
        $user->save();

        // Send subscription created notification
        $user->notify(new SubscriptionCreatedNotification($subscription));
    }

    /**
     * Handle customer.subscription.updated event.
     *
     * @param  object  $stripeSubscription
     * @return void
     */
    private function handleSubscriptionUpdated($stripeSubscription)
    {
        // Find the subscription by Stripe ID
        $subscription = Subscription::where('stripe_id', $stripeSubscription->id)->first();

        if (!$subscription) {
            return;
        }

        // Update subscription status
        $subscription->stripe_status = $stripeSubscription->status;

        // Update trial end date if applicable
        if ($stripeSubscription->trial_end) {
            $subscription->trial_ends_at = date('Y-m-d H:i:s', $stripeSubscription->trial_end);
        }

        // Update end date if applicable
        if ($stripeSubscription->cancel_at) {
            $subscription->ends_at = date('Y-m-d H:i:s', $stripeSubscription->cancel_at);
        }

        $subscription->save();

        // Send subscription updated notification
        $user = User::find($subscription->user_id);
        if ($user) {
            $user->notify(new SubscriptionUpdatedNotification($subscription));
        }
    }

    /**
     * Get available subscription plans.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPlans()
    {
        $plans = [
            [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'description' => 'Essential financial management tools',
                'price' => config('services.subscription.base_monthly_price', 5.00),
                'currency' => 'USD',
                'interval' => 'month',
                'stripe_price_id' => config('services.stripe.prices.base_monthly'),
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Graphical insights',
                    'Up to 3 levels of sub-bins'
                ]
            ],
            [
                'id' => 'base_yearly',
                'name' => 'Base Yearly',
                'description' => 'Essential financial management tools (yearly)',
                'price' => config('services.subscription.base_yearly_price', 50.00),
                'currency' => 'USD',
                'interval' => 'year',
                'stripe_price_id' => config('services.stripe.prices.base_yearly'),
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Graphical insights',
                    'Up to 3 levels of sub-bins'
                ]
            ],
            [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'description' => 'Advanced financial management with premium features',
                'price' => config('services.subscription.premium_monthly_price', 10.00),
                'currency' => 'USD',
                'interval' => 'month',
                'stripe_price_id' => config('services.stripe.prices.premium_monthly'),
                'features' => [
                    'All Base Tier features',
                    'Unlimited sub-bin levels',
                    'Crypto Scanner',
                    'Priority notifications',
                    'Advanced AI suggestions'
                ]
            ],
            [
                'id' => 'premium_yearly',
                'name' => 'Premium Yearly',
                'description' => 'Advanced financial management with premium features (yearly)',
                'price' => config('services.subscription.premium_yearly_price', 100.00),
                'currency' => 'USD',
                'interval' => 'year',
                'stripe_price_id' => config('services.stripe.prices.premium_yearly'),
                'features' => [
                    'All Base Tier features',
                    'Unlimited sub-bin levels',
                    'Crypto Scanner',
                    'Priority notifications',
                    'Advanced AI suggestions'
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'plans' => $plans,
                'trial_days' => (int) config('services.subscription.trial_days', 7)
            ]
        ]);
    }

    /**
     * Change the billing cycle of a subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeBillingCycle(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'billing_cycle' => 'required|string|in:monthly,yearly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $subscription = Subscription::find($id);

        if (!$subscription || $subscription->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription not found'
            ], 404);
        }

        // Only allow updating if subscription is active
        if ($subscription->stripe_status !== 'active' && $subscription->stripe_status !== 'trialing') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot update inactive subscription'
            ], 400);
        }

        $newBillingCycle = $request->billing_cycle;

        // If the billing cycle is the same, return early
        if ($subscription->billing_cycle === $newBillingCycle) {
            return response()->json([
                'success' => true,
                'message' => 'Billing cycle is already ' . $newBillingCycle,
                'data' => [
                    'subscription' => $subscription
                ]
            ]);
        }

        try {
            $tier = $subscription->subscription_tier;

            // Get new price ID
            $newPriceId = $this->getPriceId($tier, $newBillingCycle);

            // Update subscription in Stripe
            $this->stripe->subscriptions->update($subscription->stripe_id, [
                'items' => [
                    [
                        'id' => $subscription->items[0]->id, // Assuming single item subscription
                        'price' => $newPriceId,
                    ],
                ],
                'proration_behavior' => 'create_prorations',
            ]);

            // Update local subscription record
            $subscription->billing_cycle = $newBillingCycle;
            $subscription->stripe_price = $newPriceId;
            $subscription->price = $this->getPrice($tier, $newBillingCycle);
            $subscription->name = ucfirst($tier) . ' ' . ucfirst($newBillingCycle);
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => 'Billing cycle updated successfully',
                'data' => [
                    'subscription' => $subscription
                ]
            ]);
        } catch (ApiErrorException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating billing cycle',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Pause a subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function pauseSubscription(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'pause_duration' => 'nullable|integer|min:1|max:90',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $subscription = Subscription::find($id);

        if (!$subscription || $subscription->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription not found'
            ], 404);
        }

        // Only allow pausing if subscription is active
        if ($subscription->stripe_status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot pause inactive subscription'
            ], 400);
        }

        $pauseDuration = $request->pause_duration ?? 30; // Default to 30 days
        $resumeDate = now()->addDays($pauseDuration);

        try {
            // Pause subscription in Stripe
            $this->stripe->subscriptions->update($subscription->stripe_id, [
                'pause_collection' => [
                    'behavior' => 'void',
                    'resumes_at' => $resumeDate->timestamp,
                ],
            ]);

            // Update local subscription record
            $subscription->stripe_status = 'paused';
            $subscription->paused_at = now();
            $subscription->resume_at = $resumeDate;
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => 'Subscription paused successfully',
                'data' => [
                    'subscription' => $subscription,
                    'resume_date' => $resumeDate->format('Y-m-d H:i:s')
                ]
            ]);
        } catch (ApiErrorException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error pausing subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resume a paused subscription.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resumeSubscription(Request $request, string $id)
    {
        $subscription = Subscription::find($id);

        if (!$subscription || $subscription->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription not found'
            ], 404);
        }

        // Only allow resuming if subscription is paused
        if ($subscription->stripe_status !== 'paused') {
            return response()->json([
                'success' => false,
                'message' => 'Subscription is not paused'
            ], 400);
        }

        try {
            // Resume subscription in Stripe
            $this->stripe->subscriptions->update($subscription->stripe_id, [
                'pause_collection' => '',
            ]);

            // Update local subscription record
            $subscription->stripe_status = 'active';
            $subscription->paused_at = null;
            $subscription->resume_at = null;
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => 'Subscription resumed successfully',
                'data' => [
                    'subscription' => $subscription
                ]
            ]);
        } catch (ApiErrorException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error resuming subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get subscription history for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHistory(Request $request)
    {
        $perPage = $request->input('per_page', 15);
        $page = $request->input('page', 1);

        $user = $request->user();

        // Get subscription history from the database
        $history = SubscriptionHistory::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => [
                'history' => $history->items(),
                'pagination' => [
                    'total' => $history->total(),
                    'count' => $history->count(),
                    'per_page' => $history->perPage(),
                    'current_page' => $history->currentPage(),
                    'total_pages' => $history->lastPage()
                ]
            ]
        ]);
    }

    /**
     * Handle customer.subscription.deleted event.
     *
     * @param  object  $stripeSubscription
     * @return void
     */
    private function handleSubscriptionDeleted($stripeSubscription)
    {
        // Find the subscription by Stripe ID
        $subscription = Subscription::where('stripe_id', $stripeSubscription->id)->first();

        if (!$subscription) {
            return;
        }

        // Update subscription status and end date
        $subscription->stripe_status = 'canceled';
        $subscription->ends_at = now();
        $subscription->save();

        // Update user's subscription tier to base
        $user = User::find($subscription->user_id);
        if ($user) {
            $user->subscription_tier = 'base';
            $user->save();

            // Send subscription canceled notification
            $user->notify(new SubscriptionCanceledNotification($subscription));
        }
    }

    /**
     * Handle customer.subscription.trial_will_end event.
     *
     * @param  object  $stripeSubscription
     * @return void
     */
    private function handleTrialWillEnd($stripeSubscription)
    {
        // Find the subscription by Stripe ID
        $subscription = Subscription::where('stripe_id', $stripeSubscription->id)->first();

        if (!$subscription) {
            return;
        }

        $user = User::find($subscription->user_id);
        if ($user) {
            // Send trial ending notification
            $user->notify(new \App\Notifications\Subscriptions\TrialExpirationWarningNotification(3));
        }
    }

    /**
     * Select a subscription plan (step 1).
     */
    public function selectPlan(Request $request)
    {
        $request->validate([
            'subscription_tier' => 'required|in:base,premium',
            'billing_cycle' => 'required|in:monthly,yearly',
        ]);

        $user = $request->user();

        // Only allow plan selection for users without active subscriptions
        if ($user->subscription_tier !== 'none' && $user->subscription_tier !== 'expired') {
            return response()->json([
                'success' => false,
                'message' => 'User already has a plan selected or active subscription',
            ], 400);
        }

        try {
            // Store selected plan in user record (but don't start trial yet)
            $user->selected_plan_tier = $request->subscription_tier;
            $user->selected_billing_cycle = $request->billing_cycle;
            $user->subscription_tier = 'plan_selected'; // Intermediate state
            $user->save();

            return response()->json([
                'success' => true,
                'message' => 'Plan selected successfully. Please add payment method to start your free trial.',
                'data' => [
                    'selected_plan' => $request->subscription_tier,
                    'billing_cycle' => $request->billing_cycle,
                    'price' => $this->getPrice($request->subscription_tier, $request->billing_cycle),
                    'features' => $this->getFeatures($request->subscription_tier),
                    'next_step' => 'add_payment_method',
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to select plan', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to select plan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test endpoint to start trial without Stripe (for development).
     */
    public function startTrialTest(Request $request)
    {
        $user = $request->user();

        // Only allow for users who have selected a plan
        if ($user->subscription_tier !== 'plan_selected') {
            return response()->json([
                'success' => false,
                'message' => 'Please select a plan first',
            ], 400);
        }

        if (!$user->selected_plan_tier || !$user->selected_billing_cycle) {
            return response()->json([
                'success' => false,
                'message' => 'No plan selected. Please select a plan first.',
            ], 400);
        }

        try {
            $subscriptionTier = $user->selected_plan_tier;
            $billingCycle = $user->selected_billing_cycle;

            // Start the 7-day trial
            $user->trial_started_at = now();
            $user->subscription_tier = 'trial';
            $user->save();

            // Calculate trial end date (7 days from now)
            $trialEndDate = now()->addDays(7);

            // Create subscription record (without Stripe for testing)
            $subscription = new Subscription([
                'user_id' => $user->id,
                'name' => ucfirst($subscriptionTier) . ' ' . ucfirst($billingCycle),
                'stripe_id' => 'test_sub_' . uniqid(), // Test subscription ID
                'stripe_status' => 'trialing',
                'stripe_price' => 'test_price_' . $subscriptionTier . '_' . $billingCycle,
                'subscription_tier' => $subscriptionTier,
                'billing_cycle' => $billingCycle,
                'price' => $this->getPrice($subscriptionTier, $billingCycle),
                'currency' => 'USD',
                'features' => $this->getFeatures($subscriptionTier),
                'trial_ends_at' => $trialEndDate,
            ]);

            $subscription->save();

            // Clear selected plan fields
            $user->selected_plan_tier = null;
            $user->selected_billing_cycle = null;
            $user->save();

            return response()->json([
                'success' => true,
                'message' => 'TEST MODE: Your 7-day free trial has started!',
                'data' => [
                    'subscription' => $subscription,
                    'trial_started_at' => $user->trial_started_at,
                    'trial_ends_at' => $trialEndDate,
                    'trial_days_remaining' => 7,
                    'auto_billing_enabled' => false, // Test mode
                    'can_cancel' => true,
                    'test_mode' => true,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start trial: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Add payment method and start trial (step 2).
     */
    public function addPaymentMethodAndStartTrial(Request $request)
    {
        $request->validate([
            'payment_method_id' => 'required|string',
        ]);

        $user = $request->user();

        // Only allow for users who have selected a plan
        if ($user->subscription_tier !== 'plan_selected') {
            return response()->json([
                'success' => false,
                'message' => 'Please select a plan first',
            ], 400);
        }

        if (!$user->selected_plan_tier || !$user->selected_billing_cycle) {
            return response()->json([
                'success' => false,
                'message' => 'No plan selected. Please select a plan first.',
            ], 400);
        }

        try {
            $subscriptionTier = $user->selected_plan_tier;
            $billingCycle = $user->selected_billing_cycle;
            $paymentMethodId = $request->payment_method_id;

            // Create or get Stripe customer
            $stripeCustomerId = $this->getOrCreateStripeCustomer($user);
            $user->stripe_id = $stripeCustomerId;

            // Attach payment method to customer
            $this->stripe->paymentMethods->attach($paymentMethodId, [
                'customer' => $stripeCustomerId,
            ]);

            // Set as default payment method
            $this->stripe->customers->update($stripeCustomerId, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId,
                ],
            ]);

            // NOW start the 7-day trial
            $user->trial_started_at = now();
            $user->subscription_tier = 'trial';
            $user->save();

            // Get price ID
            $priceId = $this->getPriceId($subscriptionTier, $billingCycle);

            // Calculate trial end date (7 days from now)
            $trialEndDate = now()->addDays(7);

            // Create subscription in Stripe with trial
            $stripeSubscription = $this->stripe->subscriptions->create([
                'customer' => $stripeCustomerId,
                'items' => [
                    ['price' => $priceId],
                ],
                'trial_end' => $trialEndDate->timestamp,
                'default_payment_method' => $paymentMethodId,
                'metadata' => [
                    'user_id' => $user->id,
                    'subscription_tier' => $subscriptionTier,
                    'billing_cycle' => $billingCycle,
                ],
            ]);

            // Create subscription record
            $subscription = $user->subscriptions()->create([
                'name' => ucfirst($subscriptionTier) . ' ' . ucfirst($billingCycle),
                'stripe_id' => $stripeSubscription->id,
                'stripe_status' => $stripeSubscription->status,
                'stripe_price' => $priceId,
                'subscription_tier' => $subscriptionTier,
                'billing_cycle' => $billingCycle,
                'price' => $this->getPrice($subscriptionTier, $billingCycle),
                'currency' => 'USD',
                'features' => $this->getFeatures($subscriptionTier),
                'trial_ends_at' => $trialEndDate,
            ]);

            // Clear selected plan fields
            $user->selected_plan_tier = null;
            $user->selected_billing_cycle = null;
            $user->save();

            return response()->json([
                'success' => true,
                'message' => 'Payment method added successfully! Your 7-day free trial has started.',
                'data' => [
                    'subscription' => new SubscriptionResource($subscription),
                    'trial_started_at' => $user->trial_started_at,
                    'trial_ends_at' => $trialEndDate,
                    'trial_days_remaining' => 7,
                    'auto_billing_enabled' => true,
                    'can_cancel' => true,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to add payment method and start trial', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add payment method: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel subscription during trial or active period.
     */
    public function cancelSubscription(Request $request)
    {
        $user = $request->user();

        // Find active subscription
        $activeSubscription = $user->getActiveSubscription();

        if (!$activeSubscription) {
            return response()->json([
                'success' => false,
                'message' => 'No active subscription found',
            ], 404);
        }

        try {
            // Cancel subscription in Stripe
            if ($activeSubscription->stripe_id) {
                $this->stripe->subscriptions->cancel($activeSubscription->stripe_id);
            }

            // Update subscription status
            $activeSubscription->stripe_status = 'canceled';
            $activeSubscription->ends_at = now();
            $activeSubscription->save();

            // Update user status
            $user->subscription_tier = 'none';
            $user->trial_started_at = null;
            $user->save();

            return response()->json([
                'success' => true,
                'message' => 'Subscription canceled successfully',
                'data' => [
                    'canceled_at' => now(),
                    'subscription_status' => 'canceled',
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to cancel subscription', [
                'user_id' => $user->id,
                'subscription_id' => $activeSubscription->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel subscription: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Connect Stripe account during trial period.
     */
    public function connectStripe(Request $request)
    {
        $user = $request->user();

        try {
            // Create or get Stripe customer
            $stripeCustomerId = $this->getOrCreateStripeCustomer($user);

            // Update user with Stripe ID
            $user->stripe_id = $stripeCustomerId;
            $user->save();

            // Create setup intent for payment method
            $setupIntent = $this->stripe->setupIntents->create([
                'customer' => $stripeCustomerId,
                'usage' => 'off_session',
                'metadata' => [
                    'user_id' => $user->id,
                ],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Stripe account connection initiated',
                'setup_intent' => [
                    'client_secret' => $setupIntent->client_secret,
                    'id' => $setupIntent->id,
                ],
                'stripe_customer_id' => $stripeCustomerId,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to connect Stripe account', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to connect Stripe account: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Setup automatic billing for trial users.
     */
    public function setupAutoBilling(Request $request)
    {
        $request->validate([
            'payment_method_id' => 'required|string',
            'subscription_tier' => 'required|in:base,premium',
            'billing_cycle' => 'required|in:monthly,yearly',
        ]);

        $user = $request->user();

        // Only allow for trial users
        if ($user->subscription_tier !== 'trial') {
            return response()->json([
                'success' => false,
                'message' => 'Auto-billing setup is only available during trial period',
            ], 400);
        }

        try {
            $subscriptionTier = $request->subscription_tier;
            $billingCycle = $request->billing_cycle;
            $paymentMethodId = $request->payment_method_id;

            // Get price ID
            $priceId = $this->getPriceId($subscriptionTier, $billingCycle);
            if (!$priceId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid subscription tier or billing cycle',
                ], 400);
            }

            // Attach payment method to customer
            $this->stripe->paymentMethods->attach($paymentMethodId, [
                'customer' => $user->stripe_id,
            ]);

            // Set as default payment method
            $this->stripe->customers->update($user->stripe_id, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId,
                ],
            ]);

            // Calculate trial end date
            $trialEndDate = $user->getTrialExpirationDate();

            // Create subscription with trial
            $subscription = $this->stripe->subscriptions->create([
                'customer' => $user->stripe_id,
                'items' => [
                    ['price' => $priceId],
                ],
                'trial_end' => $trialEndDate->timestamp,
                'default_payment_method' => $paymentMethodId,
                'metadata' => [
                    'user_id' => $user->id,
                    'subscription_tier' => $subscriptionTier,
                    'billing_cycle' => $billingCycle,
                ],
            ]);

            // Update existing trial subscription record
            $existingSubscription = $user->getActiveSubscription();
            if ($existingSubscription) {
                $existingSubscription->stripe_id = $subscription->id;
                $existingSubscription->stripe_status = $subscription->status;
                $existingSubscription->stripe_price = $priceId;
                $existingSubscription->subscription_tier = $subscriptionTier;
                $existingSubscription->billing_cycle = $billingCycle;
                $existingSubscription->price = $this->getPrice($subscriptionTier, $billingCycle);
                $existingSubscription->features = $this->getFeatures($subscriptionTier);
                $existingSubscription->save();
            }

            return response()->json([
                'success' => true,
                'message' => 'Auto-billing setup successful. You will be charged automatically when your trial ends.',
                'subscription' => new SubscriptionResource($existingSubscription),
                'trial_ends_at' => $trialEndDate,
                'auto_billing_enabled' => true,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to setup auto-billing', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to setup auto-billing: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get or create Stripe customer for user.
     */
    private function getOrCreateStripeCustomer(User $user): string
    {
        if ($user->stripe_id) {
            return $user->stripe_id;
        }

        $customer = $this->stripe->customers->create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        return $customer->id;
    }


}
