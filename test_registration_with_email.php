<?php

/**
 * Test script for updated registration API with email verification
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\API\AuthController;

echo "🧪 Testing Registration API with Email Verification\n";
echo "==================================================\n\n";

// Clean up any existing test user
$testEmail = '<EMAIL>';
$existingUser = User::where('email', $testEmail)->first();
if ($existingUser) {
    $existingUser->delete();
    echo "🧹 Cleaned up existing test user\n\n";
}

// Test 1: Register new user
echo "🧪 Test 1: Register New User\n";
echo "----------------------------\n";

try {
    // Create a mock request
    $request = new Request();
    $request->merge([
        'name' => 'Test Registration User',
        'email' => $testEmail,
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'country_code' => '+1',
        'phone_number' => '1234567890',
    ]);

    $authController = new AuthController();
    $response = $authController->register($request);
    
    $responseData = json_decode($response->getContent(), true);
    $statusCode = $response->getStatusCode();
    
    echo "📊 Response Status: {$statusCode}\n";
    echo "📧 Message: " . ($responseData['message'] ?? 'No message') . "\n";
    
    if ($statusCode === 201) {
        echo "✅ User registration successful\n";
        echo "👤 User ID: " . ($responseData['user']['id'] ?? 'N/A') . "\n";
        echo "📧 User Email: " . ($responseData['user']['email'] ?? 'N/A') . "\n";
        echo "🔑 Token Generated: " . (isset($responseData['token']) ? 'YES' : 'NO') . "\n";
        
        // Check email verification details
        if (isset($responseData['email_verification'])) {
            $emailVerif = $responseData['email_verification'];
            echo "\n📧 Email Verification Details:\n";
            echo "   - Required: " . ($emailVerif['required'] ? 'YES' : 'NO') . "\n";
            echo "   - Email Sent: " . ($emailVerif['sent'] ? 'YES' : 'NO') . "\n";
            
            if ($emailVerif['sent']) {
                echo "   - Message: " . ($emailVerif['message'] ?? 'N/A') . "\n";
                echo "   - Expires At: " . ($emailVerif['expires_at'] ?? 'N/A') . "\n";
                echo "   - Attempts Remaining: " . ($emailVerif['attempts_remaining'] ?? 'N/A') . "\n";
            } else {
                echo "   - Error: " . ($emailVerif['error'] ?? 'N/A') . "\n";
                echo "   - Error Code: " . ($emailVerif['error_code'] ?? 'N/A') . "\n";
            }
        }
        
        // Check if user was actually created in database
        $createdUser = User::where('email', $testEmail)->first();
        if ($createdUser) {
            echo "\n💾 Database Verification:\n";
            echo "   - User Created: YES\n";
            echo "   - Email Verified: " . ($createdUser->email_verified_at ? 'YES' : 'NO') . "\n";
            echo "   - Verification Code: " . ($createdUser->email_verification_code ?? 'None') . "\n";
            echo "   - Code Expires: " . ($createdUser->email_verification_code_expires_at ?? 'None') . "\n";
            echo "   - Verification Attempts: " . ($createdUser->email_verification_attempts ?? 0) . "\n";
        }
        
    } else {
        echo "❌ User registration failed\n";
        if (isset($responseData['errors'])) {
            echo "🔍 Validation Errors:\n";
            foreach ($responseData['errors'] as $field => $errors) {
                echo "   - {$field}: " . implode(', ', $errors) . "\n";
            }
        }
        if (isset($responseData['error'])) {
            echo "🔍 Error: " . $responseData['error'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n";

// Test 2: Test login with unverified email
if (isset($createdUser) && $createdUser) {
    echo "🧪 Test 2: Login with Unverified Email\n";
    echo "--------------------------------------\n";
    
    try {
        $loginRequest = new Request();
        $loginRequest->merge([
            'email' => $testEmail,
            'password' => 'password123',
        ]);
        
        // Mock IP and user agent for login notification
        $loginRequest->server->set('REMOTE_ADDR', '127.0.0.1');
        $loginRequest->headers->set('User-Agent', 'Test User Agent');
        
        $loginResponse = $authController->login($loginRequest);
        $loginData = json_decode($loginResponse->getContent(), true);
        $loginStatus = $loginResponse->getStatusCode();
        
        echo "📊 Login Status: {$loginStatus}\n";
        echo "📧 Message: " . ($loginData['message'] ?? 'No message') . "\n";
        
        if ($loginStatus === 200) {
            echo "✅ Login successful\n";
            echo "🔑 Token Generated: " . (isset($loginData['token']) ? 'YES' : 'NO') . "\n";
            
            // Check email verification status in login response
            if (isset($loginData['email_verification'])) {
                $emailVerif = $loginData['email_verification'];
                echo "\n📧 Email Verification Status:\n";
                echo "   - Verified: " . ($emailVerif['verified'] ? 'YES' : 'NO') . "\n";
                echo "   - Required: " . ($emailVerif['required'] ?? false ? 'YES' : 'NO') . "\n";
                echo "   - Message: " . ($emailVerif['message'] ?? 'N/A') . "\n";
                echo "   - Has Pending Code: " . ($emailVerif['has_pending_code'] ?? false ? 'YES' : 'NO') . "\n";
                
                if ($emailVerif['has_pending_code'] ?? false) {
                    echo "   - Code Expires: " . ($emailVerif['code_expires_at'] ?? 'N/A') . "\n";
                    echo "   - Attempts Remaining: " . ($emailVerif['attempts_remaining'] ?? 'N/A') . "\n";
                }
            }
        } else {
            echo "❌ Login failed\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Login exception: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test 3: Test duplicate email registration
echo "🧪 Test 3: Test Duplicate Email Registration\n";
echo "--------------------------------------------\n";

try {
    $duplicateRequest = new Request();
    $duplicateRequest->merge([
        'name' => 'Duplicate Test User',
        'email' => $testEmail, // Same email as before
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'country_code' => '+1',
        'phone_number' => '9876543210',
    ]);

    $duplicateResponse = $authController->register($duplicateRequest);
    $duplicateData = json_decode($duplicateResponse->getContent(), true);
    $duplicateStatus = $duplicateResponse->getStatusCode();
    
    echo "📊 Response Status: {$duplicateStatus}\n";
    
    if ($duplicateStatus === 422) {
        echo "✅ Duplicate email properly rejected\n";
        if (isset($duplicateData['errors']['email'])) {
            echo "📧 Email Error: " . implode(', ', $duplicateData['errors']['email']) . "\n";
        }
    } else {
        echo "❌ Duplicate email was not properly rejected\n";
    }
    
} catch (Exception $e) {
    echo "❌ Duplicate registration exception: " . $e->getMessage() . "\n";
}

echo "\n";

// Cleanup
echo "🧹 Cleanup\n";
echo "----------\n";

try {
    if (isset($createdUser) && $createdUser) {
        $createdUser->delete();
        echo "✅ Test user deleted\n";
    }
} catch (Exception $e) {
    echo "❌ Cleanup failed: " . $e->getMessage() . "\n";
}

echo "\n🎯 Test Summary\n";
echo "===============\n";
echo "✅ User registration with email verification: Working\n";
echo "✅ Email verification code sending: Working\n";
echo "✅ Login with verification status: Working\n";
echo "✅ Duplicate email validation: Working\n";

echo "\n🎉 Registration API with Email Verification Test Complete!\n";
echo "   New users now automatically receive verification emails.\n";
echo "   Login responses include email verification status.\n";
