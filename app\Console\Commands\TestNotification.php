<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\Auth\WelcomeNotification;
use Illuminate\Console\Command;

class TestNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:test {email : The email address of the user to send the test notification to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test notification to a specific user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Sending test notification to {$email}...");
        
        // Find the user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found");
            return Command::FAILURE;
        }
        
        // Send a welcome notification as a test
        $user->notify(new WelcomeNotification());
        
        $this->info("Test notification sent to {$email}");
        
        return Command::SUCCESS;
    }
}
