@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle fa-5x text-success"></i>
                    </div>
                    
                    <h2 class="fw-bold mb-3" style="color: var(--primary-color);">Thank You!</h2>
                    <p class="lead mb-4">Your subscription has been successfully activated.</p>
                    
                    <div class="subscription-details mb-5">
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="mb-3">Subscription Details</h5>
                                        <div class="row mb-2">
                                            <div class="col-6 text-start">Plan:</div>
                                            <div class="col-6 text-end fw-bold">{{ ucfirst($subscription->subscription_tier) }}</div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-6 text-start">Billing Cycle:</div>
                                            <div class="col-6 text-end">{{ ucfirst($subscription->billing_cycle) }}</div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-6 text-start">Trial Period:</div>
                                            <div class="col-6 text-end">7 days</div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-6 text-start">Trial Ends:</div>
                                            <div class="col-6 text-end">{{ $subscription->trial_ends_at->format('M d, Y') }}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6 text-start">First Billing:</div>
                                            <div class="col-6 text-end">{{ $subscription->trial_ends_at->format('M d, Y') }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="next-steps mb-4">
                        <h5 class="mb-3">What's Next?</h5>
                        <div class="row justify-content-center">
                            <div class="col-md-10">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="step-number me-3">
                                        <span class="badge rounded-pill bg-success">1</span>
                                    </div>
                                    <div class="text-start">
                                        <h6 class="mb-1">Enjoy Your Free Trial</h6>
                                        <p class="text-muted mb-0">You have 7 days to explore all the features of your {{ ucfirst($subscription->subscription_tier) }} plan.</p>
                                    </div>
                                </div>
                                
                                <div class="d-flex align-items-start mb-3">
                                    <div class="step-number me-3">
                                        <span class="badge rounded-pill bg-success">2</span>
                                    </div>
                                    <div class="text-start">
                                        <h6 class="mb-1">Set Up Your Financial Bins</h6>
                                        <p class="text-muted mb-0">Create bins to organize your finances and track your spending.</p>
                                    </div>
                                </div>
                                
                                <div class="d-flex align-items-start">
                                    <div class="step-number me-3">
                                        <span class="badge rounded-pill bg-success">3</span>
                                    </div>
                                    <div class="text-start">
                                        <h6 class="mb-1">Manage Your Subscription</h6>
                                        <p class="text-muted mb-0">You can view and manage your subscription details at any time from your account.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{{ route('home') }}" class="btn btn-success me-2">Go to Dashboard</a>
                        <a href="{{ route('subscriptions.index') }}" class="btn btn-outline-secondary">Manage Subscription</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .success-icon {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            transform: scale(0.95);
            opacity: 0.7;
        }
        70% {
            transform: scale(1);
            opacity: 1;
        }
        100% {
            transform: scale(0.95);
            opacity: 0.7;
        }
    }
    
    .step-number .badge {
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }
</style>
@endsection
