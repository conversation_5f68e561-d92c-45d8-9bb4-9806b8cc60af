@extends('admin.layouts.app')

@push('styles')
<style>
    :root {
        --primary-color: #32704e; /* Sea Green */
        --primary-hover: #1D5E40; /* Dark Green */
        --secondary-color: #6c757d;
        --success-color: #28a745;
        --info-color: #17a2b8;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --light-color: #E8F5E9; /* Light Mint - slightly more subtle */
        --dark-color: #343a40;
        --card-border-radius: 0.75rem;
        --section-border-radius: 0.75rem;
        --box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.08);
        --transition-speed: 0.25s;
    }

    body.dark-mode {
        --primary-color: #4CAF50; /* Brighter green for dark mode */
        --primary-hover: #66BB6A;
        --secondary-color: #b7b9c7;
        --light-color: #2d2d3a;
        --dark-color: #e9e9ef;
        --box-shadow: 0 0.25rem 1.5rem rgba(0, 0, 0, 0.3);
    }

    body.dark-mode .settings-card {
        background-color: #1a1a2e;
        border-color: #2d2d3a;
    }

    body.dark-mode .settings-header {
        background-color: #222236;
        border-color: #2d2d3a;
    }

    body.dark-mode .settings-section {
        background-color: #222236;
        border-color: #2d2d3a;
        box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.2);
    }

    body.dark-mode .settings-actions {
        background-color: #222236;
        border-color: #2d2d3a;
    }

    body.dark-mode .form-control,
    body.dark-mode .form-select {
        background-color: #222230;
        border-color: #3d3d4a;
        color: #e9e9ef;
    }

    body.dark-mode .form-control:focus,
    body.dark-mode .form-select:focus {
        background-color: #222230;
        border-color: #6e8fff;
        color: #e9e9ef;
    }

    body.dark-mode .form-label {
        color: #e9e9ef;
    }

    body.dark-mode .form-text {
        color: #b7b9c7;
    }

    body.dark-mode .list-group-item {
        background-color: #2d2d3a;
        border-color: #3d3d4a;
        color: #e9e9ef;
    }

    body.dark-mode .list-group-item.active {
        background-color: #6e8fff;
        border-color: #6e8fff;
    }

    body.dark-mode .list-group-item:hover:not(.active) {
        background-color: #3d3d4a;
    }

    body.dark-mode .input-group-text {
        background-color: #3d3d4a;
        border-color: #3d3d4a;
        color: #e9e9ef;
    }

    body.dark-mode .text-muted {
        color: #b7b9c7 !important;
    }

    body.dark-mode .text-gray-800 {
        color: #e9e9ef !important;
    }

    body.dark-mode .current-value {
        color: #8ea8ff;
    }

    .settings-card {
        border: none;
        box-shadow: var(--box-shadow);
        border-radius: var(--card-border-radius);
        transition: all var(--transition-speed) ease;
        overflow: hidden;
        background-color: white;
    }

    .settings-header {
        background-color: white;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 1.5rem;
        border-top-left-radius: var(--card-border-radius);
        border-top-right-radius: var(--card-border-radius);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .settings-tabs {
        position: sticky;
        top: calc(var(--header-height) + 1rem);
        max-height: calc(100vh - var(--header-height) - 2rem);
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) transparent;
    }

    .settings-tabs::-webkit-scrollbar {
        width: 5px;
    }

    .settings-tabs::-webkit-scrollbar-track {
        background: transparent;
    }

    .settings-tabs::-webkit-scrollbar-thumb {
        background-color: var(--primary-color);
        border-radius: 10px;
    }

    .settings-tabs .list-group-item {
        border-radius: 0;
        border-right: none;
        padding: 1rem 1.5rem;
        font-weight: 500;
        color: var(--secondary-color);
        transition: all var(--transition-speed) ease;
        position: relative;
        overflow: hidden;
        border-left: 4px solid transparent;
        margin-bottom: 2px;
    }

    .settings-tabs .list-group-item:hover {
        color: var(--primary-color);
        background-color: var(--light-color);
        transform: translateX(4px);
    }

    .settings-tabs .list-group-item.active {
        color: var(--primary-color);
        background-color: var(--light-color);
        border-left: 4px solid var(--primary-color);
        font-weight: 600;
    }

    .settings-tabs .list-group-item i {
        margin-right: 0.75rem;
        width: 20px;
        text-align: center;
        font-size: 1rem;
        transition: all var(--transition-speed) ease;
    }

    .settings-tabs .list-group-item:hover i {
        transform: scale(1.1);
    }

    .settings-tabs .list-group-item.active i {
        color: var(--primary-color);
    }

    .settings-tabs .badge {
        transition: all var(--transition-speed) ease;
    }

    .settings-tabs .list-group-item:hover .badge,
    .settings-tabs .list-group-item.active .badge {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    .settings-body {
        padding: 1.75rem;
    }

    .settings-section {
        background-color: white;
        border-radius: var(--section-border-radius);
        padding: 1.75rem;
        margin-bottom: 1.75rem;
        box-shadow: var(--box-shadow);
        border: 1px solid rgba(0, 0, 0, 0.03);
        transition: all var(--transition-speed) ease;
        position: relative;
        overflow: hidden;
    }

    .settings-section:hover {
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
    }

    .settings-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background-color: var(--primary-color);
        opacity: 0.7;
    }

    .settings-section-title {
        font-size: 1.15rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
    }

    .settings-section-title i {
        margin-right: 0.75rem;
        color: var(--primary-color);
        font-size: 1.25rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .form-label.required::after {
        content: '*';
        color: var(--danger-color);
        margin-left: 0.25rem;
    }

    .form-text {
        color: var(--secondary-color);
        font-size: 0.8rem;
        margin-top: 0.35rem;
    }

    .form-control, .form-select {
        padding: 0.7rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
        transition: all var(--transition-speed) ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(46, 139, 87, 0.15);
    }

    .form-control:hover, .form-select:hover {
        border-color: var(--primary-color);
    }

    .settings-actions {
        background-color: white;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.75rem;
        text-align: right;
        border-bottom-left-radius: var(--section-border-radius);
        border-bottom-right-radius: var(--section-border-radius);
        position: sticky;
        bottom: 0;
        z-index: 10;
        box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.03);
    }

    .current-value {
        font-size: 0.85rem;
        color: var(--primary-color);
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        background-color: rgba(46, 139, 87, 0.05);
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        border-left: 3px solid var(--primary-color);
    }

    .current-value i {
        margin-right: 0.5rem;
    }

    .preview-image {
        max-height: 100px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 0.375rem;
        padding: 0.25rem;
        background-color: #fff;
        transition: all var(--transition-speed) ease;
    }

    .preview-image:hover {
        transform: scale(1.05);
    }

    .btn {
        border-radius: 0.375rem;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all var(--transition-speed) ease;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
    }

    .search-settings {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .search-settings input {
        padding-left: 3rem;
        border-radius: 2rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 1rem;
        height: 50px;
        transition: all var(--transition-speed) ease;
    }

    .search-settings input:focus {
        box-shadow: 0 4px 15px rgba(46, 139, 87, 0.15);
        border-color: var(--primary-color);
    }

    .search-settings i {
        position: absolute;
        left: 1.25rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--primary-color);
        font-size: 1.1rem;
        transition: all var(--transition-speed) ease;
    }

    .search-settings input:focus + i {
        color: var(--primary-color);
        transform: translateY(-50%) scale(1.1);
    }

    .setting-highlight {
        background-color: rgba(46, 139, 87, 0.08);
        border-left: 3px solid var(--primary-color);
        border-radius: 0.5rem;
        padding: 0.5rem;
        margin: 0.5rem 0;
        transition: all var(--transition-speed) ease;
        animation: highlight-pulse 2s infinite;
    }

    @keyframes highlight-pulse {
        0% { box-shadow: 0 0 0 0 rgba(46, 139, 87, 0.2); }
        70% { box-shadow: 0 0 0 10px rgba(46, 139, 87, 0); }
        100% { box-shadow: 0 0 0 0 rgba(46, 139, 87, 0); }
    }

    /* Tooltip styles */
    .settings-tooltip {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: 0.5rem;
        color: var(--secondary-color);
        cursor: help;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background-color: rgba(108, 117, 125, 0.1);
        transition: all var(--transition-speed) ease;
    }

    .settings-tooltip:hover {
        color: var(--primary-color);
        background-color: rgba(46, 139, 87, 0.1);
        transform: scale(1.1);
    }

    .settings-tooltip .tooltip-text {
        visibility: hidden;
        width: 250px;
        background-color: #343a40;
        color: #fff;
        text-align: left;
        border-radius: 8px;
        padding: 0.75rem;
        position: absolute;
        z-index: 100;
        bottom: 130%;
        left: 50%;
        margin-left: -125px;
        opacity: 0;
        transition: all var(--transition-speed) ease;
        font-size: 0.85rem;
        font-weight: normal;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        border-left: 3px solid var(--primary-color);
    }

    .settings-tooltip .tooltip-text::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #343a40 transparent transparent transparent;
    }

    .settings-tooltip:hover .tooltip-text {
        visibility: visible;
        opacity: 1;
        bottom: 140%;
    }

    /* Dark mode toggle */
    .dark-mode-toggle {
        position: relative;
        width: 64px;
        height: 32px;
        border-radius: 16px;
        background-color: #e9ecef;
        cursor: pointer;
        transition: all var(--transition-speed) ease;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .dark-mode-toggle.active {
        background-color: var(--primary-color);
    }

    .dark-mode-toggle .toggle-handle {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 26px;
        height: 26px;
        border-radius: 50%;
        background-color: white;
        transition: all var(--transition-speed) ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffc107;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        transform: scale(1);
    }

    .dark-mode-toggle:hover .toggle-handle {
        transform: scale(1.1);
    }

    .dark-mode-toggle.active .toggle-handle {
        left: 36px;
        color: #6c757d;
    }

    .dark-mode-toggle.active:hover .toggle-handle {
        transform: scale(1.1);
    }

    /* Button Styles */
    .btn {
        font-weight: 500;
        padding: 0.6rem 1.25rem;
        border-radius: 0.5rem;
        transition: all var(--transition-speed) ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn i {
        font-size: 0.9rem;
    }

    .btn:active {
        transform: translateY(1px);
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(46, 139, 87, 0.2);
    }

    .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
        background-color: transparent;
    }

    .btn-outline-primary:hover {
        background-color: var(--primary-color);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(46, 139, 87, 0.2);
    }

    .btn-secondary {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        color: white;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(108, 117, 125, 0.2);
    }

    .btn-success {
        background-color: var(--success-color);
        border-color: var(--success-color);
        color: white;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #218838;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
    }

    /* Mobile responsiveness */
    @media (max-width: 767.98px) {
        .settings-tabs {
            margin-bottom: 1.5rem;
            position: static;
            max-height: none;
            overflow-y: visible;
        }

        .settings-tabs .list-group-item {
            border-right: none;
            border-left: 4px solid transparent;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.75rem 1rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .settings-tabs .list-group-item.active {
            border-left: 4px solid var(--primary-color);
            background-color: var(--light-color);
        }

        .settings-section {
            padding: 1.25rem;
        }

        .settings-body {
            padding: 1rem;
        }

        .settings-actions {
            padding: 1rem;
            text-align: center;
        }

        .settings-actions .btn {
            width: 100%;
            margin-top: 0.5rem;
        }

        .search-settings input {
            height: 45px;
            font-size: 0.9rem;
        }

        .current-value {
            font-size: 0.8rem;
            padding: 0.4rem 0.6rem;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set header height variable for sticky sidebar
        document.documentElement.style.setProperty('--header-height', document.querySelector('.navbar').offsetHeight + 'px');

        // Dark Mode Toggle with animation
        const darkModeToggle = document.getElementById('darkModeToggle');
        const body = document.body;

        // Check for saved dark mode preference
        const isDarkMode = localStorage.getItem('darkMode') === 'true';

        // Apply dark mode if saved preference exists
        if (isDarkMode) {
            body.classList.add('dark-mode');
            darkModeToggle.classList.add('active');
            darkModeToggle.querySelector('.toggle-handle').innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            darkModeToggle.querySelector('.toggle-handle').innerHTML = '<i class="fas fa-sun"></i>';
        }

        // Toggle dark mode on click with animation
        darkModeToggle.addEventListener('click', function() {
            // Add transition class to body for smooth color transitions
            body.classList.add('color-transition');

            // Toggle dark mode
            body.classList.toggle('dark-mode');
            const isDark = body.classList.contains('dark-mode');

            // Update toggle appearance with animation
            if (isDark) {
                darkModeToggle.classList.add('active');
                darkModeToggle.querySelector('.toggle-handle').style.transform = 'scale(0.5)';
                setTimeout(() => {
                    darkModeToggle.querySelector('.toggle-handle').innerHTML = '<i class="fas fa-moon"></i>';
                    darkModeToggle.querySelector('.toggle-handle').style.transform = 'scale(1)';
                }, 150);
            } else {
                darkModeToggle.classList.remove('active');
                darkModeToggle.querySelector('.toggle-handle').style.transform = 'scale(0.5)';
                setTimeout(() => {
                    darkModeToggle.querySelector('.toggle-handle').innerHTML = '<i class="fas fa-sun"></i>';
                    darkModeToggle.querySelector('.toggle-handle').style.transform = 'scale(1)';
                }, 150);
            }

            // Save preference to localStorage
            localStorage.setItem('darkMode', isDark);
        });

        // Enhanced Settings Search Functionality
        const searchInput = document.getElementById('settingsSearch');
        const settingItems = document.querySelectorAll('.setting-item');
        const settingsTabs = document.querySelectorAll('#settingsTabs .list-group-item');
        const tabPanes = document.querySelectorAll('.tab-pane');
        const searchIcon = document.querySelector('.search-settings i');

        // Add clear button to search
        const clearButton = document.createElement('button');
        clearButton.type = 'button';
        clearButton.className = 'btn btn-sm position-absolute end-0 me-3 top-50 translate-middle-y';
        clearButton.innerHTML = '<i class="fas fa-times"></i>';
        clearButton.style.display = 'none';
        clearButton.style.zIndex = '5';
        clearButton.style.backgroundColor = 'transparent';
        clearButton.style.border = 'none';
        clearButton.style.color = '#6c757d';
        clearButton.style.cursor = 'pointer';
        clearButton.addEventListener('click', function() {
            searchInput.value = '';
            searchInput.focus();
            clearButton.style.display = 'none';
            // Trigger the input event to reset the search
            searchInput.dispatchEvent(new Event('input'));
        });

        searchInput.parentNode.appendChild(clearButton);

        // Add animation to search icon
        searchInput.addEventListener('focus', function() {
            searchIcon.style.color = 'var(--primary-color)';
        });

        searchInput.addEventListener('blur', function() {
            if (this.value === '') {
                searchIcon.style.color = 'var(--secondary-color)';
            }
        });

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            // Show/hide clear button
            clearButton.style.display = searchTerm ? 'block' : 'none';

            if (searchTerm === '') {
                // Reset all items and tabs
                settingItems.forEach(item => {
                    item.classList.remove('setting-highlight');
                    item.style.display = '';
                });

                // Reset active tab
                document.querySelector('#settingsTabs .active').click();

                // Reset search icon
                searchIcon.className = 'fas fa-search';
                return;
            }

            // Change icon to loading during search
            searchIcon.className = 'fas fa-spinner fa-spin';

            // Delay search slightly for better UX
            setTimeout(() => {
                let foundInTabs = new Set();
                let matchCount = 0;

                // Search through all setting items
                settingItems.forEach(item => {
                    const settingName = item.dataset.setting || '';
                    const settingText = item.textContent.toLowerCase();
                    const matchesSearch = settingText.includes(searchTerm) || settingName.toLowerCase().includes(searchTerm);

                    // Show/hide based on search
                    if (matchesSearch) {
                        item.style.display = '';
                        item.classList.add('setting-highlight');
                        matchCount++;

                        // Find which tab this item belongs to
                        const tabPane = item.closest('.tab-pane');
                        if (tabPane) {
                            foundInTabs.add(tabPane.id);
                        }
                    } else {
                        item.style.display = 'none';
                        item.classList.remove('setting-highlight');
                    }
                });

                // Update tabs to show which ones have matches
                settingsTabs.forEach(tab => {
                    const targetId = tab.getAttribute('data-bs-target').substring(1);
                    const badge = tab.querySelector('.badge');

                    if (foundInTabs.has(targetId)) {
                        tab.classList.add('text-primary');
                        tab.style.fontWeight = 'bold';
                        if (badge) {
                            badge.classList.remove('bg-light');
                            badge.classList.add('bg-primary');
                            badge.classList.add('text-white');
                        }
                    } else {
                        tab.classList.remove('text-primary');
                        tab.style.fontWeight = '';
                        if (badge) {
                            badge.classList.add('bg-light');
                            badge.classList.remove('bg-primary');
                            badge.classList.remove('text-white');
                        }
                    }
                });

                // If we have matches, activate the first tab with matches
                if (foundInTabs.size > 0) {
                    const firstTabId = Array.from(foundInTabs)[0];
                    document.querySelector(`[data-bs-target="#${firstTabId}"]`).click();
                }

                // Change icon based on results
                if (matchCount > 0) {
                    searchIcon.className = 'fas fa-check text-success';
                } else {
                    searchIcon.className = 'fas fa-times text-danger';
                }

                // Reset icon after a delay
                setTimeout(() => {
                    searchIcon.className = 'fas fa-search';
                    searchIcon.style.color = 'var(--primary-color)';
                }, 1500);
            }, 300);
        });

        // Enhanced Form validation
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            // Add required class to labels of required inputs
            form.querySelectorAll('[required]').forEach(input => {
                const label = form.querySelector(`label[for="${input.id}"]`);
                if (label) {
                    label.classList.add('required');
                }
            });

            // Add loading indicator to submit buttons
            const submitButtons = form.querySelectorAll('button[type="submit"]');
            submitButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (form.checkValidity()) {
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
                        this.disabled = true;

                        // Re-enable button after 5 seconds in case of network issues
                        setTimeout(() => {
                            if (this.disabled) {
                                this.innerHTML = originalText;
                                this.disabled = false;
                            }
                        }, 5000);
                    }
                });
            });

            form.addEventListener('submit', function(event) {
                if (!this.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();

                    // Find the first invalid input and focus it
                    const invalidInput = this.querySelector(':invalid');
                    if (invalidInput) {
                        invalidInput.focus();

                        // If the input is in a hidden tab, activate that tab
                        const tabPane = invalidInput.closest('.tab-pane');
                        if (tabPane && !tabPane.classList.contains('show')) {
                            document.querySelector(`[data-bs-target="#${tabPane.id}"]`).click();
                        }

                        // Scroll to the invalid input with animation
                        invalidInput.scrollIntoView({ behavior: 'smooth', block: 'center' });

                        // Highlight the invalid input
                        invalidInput.classList.add('is-invalid');
                        invalidInput.classList.add('animate__animated', 'animate__headShake');
                        setTimeout(() => {
                            invalidInput.classList.remove('animate__animated', 'animate__headShake');
                        }, 1000);

                        // Show error toast
                        const toast = document.createElement('div');
                        toast.className = 'position-fixed bottom-0 end-0 p-3';
                        toast.style.zIndex = '9999';
                        toast.innerHTML = `
                            <div class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="d-flex">
                                    <div class="toast-body">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        Please fix the validation errors before submitting.
                                    </div>
                                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(toast);
                        const bsToast = new bootstrap.Toast(toast.querySelector('.toast'));
                        bsToast.show();
                        setTimeout(() => {
                            toast.remove();
                        }, 5000);
                    }
                }

                this.classList.add('was-validated');
            });
        });

        // Enhanced image preview
        const previewImages = document.querySelectorAll('.preview-image');
        previewImages.forEach(img => {
            // Create a wrapper for the image
            const wrapper = document.createElement('div');
            wrapper.className = 'position-relative preview-image-wrapper';
            wrapper.style.display = 'inline-block';
            wrapper.style.cursor = 'pointer';

            // Replace the image with the wrapper
            img.parentNode.insertBefore(wrapper, img);
            wrapper.appendChild(img);

            // Add zoom icon
            const zoomIcon = document.createElement('div');
            zoomIcon.className = 'position-absolute top-0 end-0 bg-primary text-white rounded-circle p-1 m-1';
            zoomIcon.innerHTML = '<i class="fas fa-search-plus"></i>';
            zoomIcon.style.opacity = '0';
            zoomIcon.style.transition = 'opacity 0.3s ease';
            wrapper.appendChild(zoomIcon);

            // Add hover effects
            wrapper.addEventListener('mouseenter', function() {
                img.style.transform = 'scale(1.05)';
                zoomIcon.style.opacity = '1';
            });

            wrapper.addEventListener('mouseleave', function() {
                img.style.transform = '';
                zoomIcon.style.opacity = '0';
            });

            // Add click event to open modal
            wrapper.addEventListener('click', function() {
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.id = 'imagePreviewModal';
                modal.tabIndex = '-1';
                modal.setAttribute('aria-labelledby', 'imagePreviewModalLabel');
                modal.setAttribute('aria-hidden', 'true');

                modal.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="imagePreviewModalLabel">Image Preview</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="${img.src}" alt="Preview" class="img-fluid">
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();

                modal.addEventListener('hidden.bs.modal', function() {
                    modal.remove();
                });
            });
        });
    });
</script>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card shadow-sm mb-4 border-0 overflow-hidden">
        <div class="card-body p-0">
            <div class="row g-0">
                <div class="col-md-8 p-4">
                    <div class="d-flex flex-column h-100 justify-content-between">
                        <div>
                            <h1 class="h3 mb-2 text-gray-800 d-flex align-items-center">
                                <i class="fas fa-cogs text-primary me-2"></i>System Settings
                                <span class="badge bg-primary ms-2 fs-6">v{{ config('app.version', '1.0') }}</span>
                            </h1>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}" class="text-decoration-none">Dashboard</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">Settings</li>
                                </ol>
                            </nav>
                            <p class="text-muted mt-2 mb-0">
                                Configure your application settings, including general information, email, payment, and system settings.
                            </p>
                        </div>

                        <div class="d-flex align-items-center mt-3">
                            <span class="text-muted me-3">
                                <i class="fas fa-clock me-1"></i> Last updated:
                                <strong>{{ isset($settings['system']['last_updated']) ? \Carbon\Carbon::parse($settings['system']['last_updated'])->diffForHumans() : 'Never' }}</strong>
                            </span>
                            <span class="badge bg-light text-primary">{{ count($settings) }} categories</span>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 bg-light p-4 d-flex flex-column justify-content-between">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0 fw-bold">Quick Actions</h5>

                        <!-- Dark Mode Toggle -->
                        <div class="d-flex align-items-center">
                            <span class="me-2 text-muted small">
                                <i class="fas fa-sun"></i>
                            </span>
                            <div class="dark-mode-toggle" id="darkModeToggle">
                                <div class="toggle-handle">
                                    <i class="fas fa-sun"></i>
                                </div>
                            </div>
                            <span class="ms-2 text-muted small">
                                <i class="fas fa-moon"></i>
                            </span>
                        </div>
                    </div>

                    <div class="list-group shadow-sm">
                        <a href="{{ route('admin.settings.export') }}" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="fas fa-download text-primary me-3"></i> Export Settings
                        </a>
                        <a href="{{ route('admin.settings.import.form') }}" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="fas fa-upload text-primary me-3"></i> Import Settings
                        </a>
                        <a href="{{ route('admin.settings.test') }}" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="fas fa-vial text-success me-3"></i> Test Settings
                        </a>
                        <a href="{{ route('admin.settings.audit') }}" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="fas fa-history text-info me-3"></i> View Audit Log
                        </a>
                    </div>

                    <div class="mt-3">
                        <form action="{{ route('admin.settings.reset') }}" method="POST" onsubmit="return confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.');">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-undo me-2"></i> Reset to Defaults
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Settings -->
    <div class="card shadow-sm mb-4 border-0">
        <div class="card-body p-0">
            <div class="search-settings m-0">
                <i class="fas fa-search"></i>
                <input type="text" class="form-control border-0" id="settingsSearch" placeholder="Search settings by name, value, or description...">
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show shadow-sm border-0 d-flex align-items-center" role="alert">
            <div class="me-3 fs-3">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="flex-grow-1">
                <h5 class="alert-heading mb-1">Success!</h5>
                <p class="mb-0">{{ session('success') }}</p>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show shadow-sm border-0 d-flex align-items-center" role="alert">
            <div class="me-3 fs-3">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="flex-grow-1">
                <h5 class="alert-heading mb-1">Error!</h5>
                <p class="mb-0">{{ session('error') }}</p>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show shadow-sm border-0 d-flex" role="alert">
            <div class="me-3 fs-3">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="flex-grow-1">
                <h5 class="alert-heading mb-1">Validation Error!</h5>
                <ul class="mb-0 ps-3">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Settings Card -->
    <div class="card settings-card mb-4 shadow-sm">
        <div class="settings-header">
            <h5 class="mb-0 text-primary d-flex align-items-center">
                <i class="fas fa-sliders-h me-2"></i>Configuration Settings
            </h5>

            <div class="d-flex align-items-center">
                <div class="badge bg-primary-soft me-2 px-3 py-2">
                    <i class="fas fa-layer-group me-1"></i> {{ count($settings) }} Categories
                </div>
                <div class="badge bg-success-soft px-3 py-2">
                    <i class="fas fa-check-circle me-1"></i> All Systems Operational
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="row g-0">
                <!-- Settings Navigation -->
                <div class="col-md-3 border-end">
                    <div class="settings-tabs">
                        <div class="list-group list-group-flush rounded-0" id="settingsTabs">
                            <button class="list-group-item list-group-item-action active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                                <i class="fas fa-cog me-2"></i>General
                                <span class="badge rounded-pill bg-light text-primary ms-auto">{{ isset($settings['general']) ? count($settings['general']) : 0 }}</span>
                            </button>
                            <button class="list-group-item list-group-item-action" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab" aria-controls="email" aria-selected="false">
                                <i class="fas fa-envelope me-2"></i>Email
                                <span class="badge rounded-pill bg-light text-primary ms-auto">{{ isset($settings['email']) ? count($settings['email']) : 0 }}</span>
                            </button>
                            <button class="list-group-item list-group-item-action" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab" aria-controls="payment" aria-selected="false">
                                <i class="fas fa-credit-card me-2"></i>Payment
                                <span class="badge rounded-pill bg-light text-primary ms-auto">{{ isset($settings['payment']) ? count($settings['payment']) : 0 }}</span>
                            </button>

                            <button class="list-group-item list-group-item-action" id="plaid-tab" data-bs-toggle="tab" data-bs-target="#plaid" type="button" role="tab" aria-controls="plaid" aria-selected="false">
                                <i class="fas fa-university me-2"></i>Plaid
                                <span class="badge rounded-pill bg-light text-primary ms-auto">{{ isset($settings['plaid']) ? count($settings['plaid']) : 0 }}</span>
                            </button>

                            <button class="list-group-item list-group-item-action" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab" aria-controls="system" aria-selected="false">
                                <i class="fas fa-server me-2"></i>System
                                <span class="badge rounded-pill bg-light text-primary ms-auto">{{ isset($settings['system']) ? count($settings['system']) : 0 }}</span>
                            </button>
                            <button class="list-group-item list-group-item-action" id="welcome-tab" data-bs-toggle="tab" data-bs-target="#welcome" type="button" role="tab" aria-controls="welcome" aria-selected="false">
                                <i class="fas fa-home me-2"></i>Welcome Page
                                <span class="badge rounded-pill bg-light text-primary ms-auto">{{ isset($settings['welcome']) ? count($settings['welcome']) : 0 }}</span>
                            </button>
                        </div>

                        <div class="p-3 border-top">
                            <div class="d-grid">
                                <a href="{{ route('admin.settings.test') }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-vial me-1"></i> Test Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Content -->
                <div class="col-md-9">
                    <div class="settings-body">
                        <div class="tab-content" id="settingsTabsContent">
                            <!-- General Settings -->
                            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <div>
                                        <h5 class="settings-section-title mb-0">
                                            <i class="fas fa-cog me-2"></i>General Settings
                                        </h5>
                                        <p class="text-muted mb-0">Configure the basic information about your application.</p>
                                    </div>
                                    <span class="badge bg-primary">{{ isset($settings['general']) ? count($settings['general']) : 0 }} settings</span>
                                </div>

                                <form action="{{ route('admin.settings.update.general') }}" method="POST" enctype="multipart/form-data" id="generalSettingsForm">
                                    @csrf

                                    <div class="settings-section shadow-sm">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0 fw-bold">
                                                <i class="fas fa-info-circle me-2 text-primary"></i>Application Information
                                            </h6>
                                            <span class="badge bg-light text-primary">Core</span>
                                        </div>

                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3 setting-item" data-setting="app_name">
                                                    <label for="app_name" class="form-label d-flex align-items-center">
                                                        Application Name
                                                        <span class="settings-tooltip">
                                                            <i class="fas fa-question-circle"></i>
                                                            <span class="tooltip-text">The name of your application that appears in the browser title, emails, and throughout the application.</span>
                                                        </span>
                                                    </label>
                                                    <input type="text" class="form-control" id="app_name" name="app_name" value="{{ $settings['general']['app_name'] ?? config('app.name') }}" required>
                                                    <div class="form-text">The name of your application.</div>
                                                    @if(isset($settings['general']['app_name']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current value: <strong>{{ $settings['general']['app_name'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3 setting-item" data-setting="app_description">
                                                    <label for="app_description" class="form-label d-flex align-items-center">
                                                        Application Description
                                                        <span class="settings-tooltip">
                                                            <i class="fas fa-question-circle"></i>
                                                            <span class="tooltip-text">A brief description of your application used for SEO and meta tags.</span>
                                                        </span>
                                                    </label>
                                                    <textarea class="form-control" id="app_description" name="app_description" rows="2">{{ $settings['general']['app_description'] ?? '' }}</textarea>
                                                    <div class="form-text">A short description of your application.</div>
                                                    @if(isset($settings['general']['app_description']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current value: <strong>{{ Str::limit($settings['general']['app_description'], 50) }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section shadow-sm">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0 fw-bold">
                                                <i class="fas fa-font me-2 text-primary"></i>Text Content
                                            </h6>
                                            <span class="badge bg-light text-primary">UI</span>
                                        </div>

                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3 setting-item" data-setting="header_text">
                                                    <label for="header_text" class="form-label d-flex align-items-center">
                                                        Header Text
                                                        <span class="settings-tooltip">
                                                            <i class="fas fa-question-circle"></i>
                                                            <span class="tooltip-text">Text displayed in the header section of your application.</span>
                                                        </span>
                                                    </label>
                                                    <input type="text" class="form-control" id="header_text" name="header_text" value="{{ $settings['general']['header_text'] ?? '' }}">
                                                    <div class="form-text">Text to display in the header.</div>
                                                    @if(isset($settings['general']['header_text']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current value: <strong>{{ $settings['general']['header_text'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3 setting-item" data-setting="footer_text">
                                                    <label for="footer_text" class="form-label d-flex align-items-center">
                                                        Footer Text
                                                        <span class="settings-tooltip">
                                                            <i class="fas fa-question-circle"></i>
                                                            <span class="tooltip-text">Text displayed in the footer section of your application. Can include HTML.</span>
                                                        </span>
                                                    </label>
                                                    <textarea class="form-control" id="footer_text" name="footer_text" rows="2">{{ $settings['general']['footer_text'] ?? '&copy; ' . date('Y') . ' ' . config('app.name') . '. All rights reserved.' }}</textarea>
                                                    <div class="form-text">Text to display in the footer. HTML is allowed.</div>
                                                    @if(isset($settings['general']['footer_text']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current value: <strong>{{ Str::limit($settings['general']['footer_text'], 50) }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section shadow-sm">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0 fw-bold">
                                                <i class="fas fa-paint-brush me-2 text-primary"></i>Branding
                                            </h6>
                                            <span class="badge bg-light text-primary">Visual</span>
                                        </div>

                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3 setting-item" data-setting="logo">
                                                    <label for="logo" class="form-label d-flex align-items-center">
                                                        Logo
                                                        <span class="settings-tooltip">
                                                            <i class="fas fa-question-circle"></i>
                                                            <span class="tooltip-text">Your application logo displayed in the header and emails. Recommended size: 200x50 pixels.</span>
                                                        </span>
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="file" class="form-control" id="logo" name="logo">
                                                        @if(isset($settings['general']['logo']))
                                                            <button class="btn btn-outline-secondary" type="button" id="previewLogoBtn" data-bs-toggle="modal" data-bs-target="#logoPreviewModal">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                        @endif
                                                    </div>
                                                    <div class="form-text">Recommended size: 200x50 pixels. Supported formats: PNG, JPG, SVG.</div>
                                                    @if(isset($settings['general']['logo']))
                                                        <div class="mt-2 d-flex align-items-center">
                                                            <span class="me-2">Current logo:</span>
                                                            <img src="{{ $settings['general']['logo'] }}" alt="Logo" class="preview-image">
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3 setting-item" data-setting="favicon">
                                                    <label for="favicon" class="form-label d-flex align-items-center">
                                                        Favicon
                                                        <span class="settings-tooltip">
                                                            <i class="fas fa-question-circle"></i>
                                                            <span class="tooltip-text">The small icon displayed in browser tabs. Recommended size: 32x32 pixels.</span>
                                                        </span>
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="file" class="form-control" id="favicon" name="favicon">
                                                        @if(isset($settings['general']['favicon']))
                                                            <button class="btn btn-outline-secondary" type="button" id="previewFaviconBtn" data-bs-toggle="modal" data-bs-target="#faviconPreviewModal">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                        @endif
                                                    </div>
                                                    <div class="form-text">Recommended size: 32x32 pixels. Supported formats: ICO, PNG.</div>
                                                    @if(isset($settings['general']['favicon']))
                                                        <div class="mt-2 d-flex align-items-center">
                                                            <span class="me-2">Current favicon:</span>
                                                            <img src="{{ $settings['general']['favicon'] }}" alt="Favicon" class="preview-image" style="max-height: 32px;">
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-actions">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <button type="reset" class="btn btn-outline-secondary">
                                                <i class="fas fa-undo me-2"></i>Reset Changes
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>Save General Settings
                                            </button>
                                        </div>
                                    </div>
                                </form>

                                <!-- Logo Preview Modal -->
                                @if(isset($settings['general']['logo']))
                                <div class="modal fade" id="logoPreviewModal" tabindex="-1" aria-labelledby="logoPreviewModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="logoPreviewModalLabel">Logo Preview</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body text-center">
                                                <img src="{{ $settings['general']['logo'] }}" alt="Logo" class="img-fluid">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif

                                <!-- Favicon Preview Modal -->
                                @if(isset($settings['general']['favicon']))
                                <div class="modal fade" id="faviconPreviewModal" tabindex="-1" aria-labelledby="faviconPreviewModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered modal-sm">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="faviconPreviewModalLabel">Favicon Preview</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body text-center">
                                                <img src="{{ $settings['general']['favicon'] }}" alt="Favicon" class="img-fluid">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>

                            <!-- Email Settings -->
                            <div class="tab-pane fade" id="email" role="tabpanel" aria-labelledby="email-tab">
                                <h5 class="settings-section-title">
                                    <i class="fas fa-envelope me-2"></i>Email Settings
                                </h5>
                                <p class="text-muted mb-4">Configure your email server settings for sending notifications and system emails.</p>

                                <form action="{{ route('admin.settings.update.email') }}" method="POST">
                                    @csrf

                                    <div class="settings-section">
                                        <h6 class="mb-3">Mail Server Configuration</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mail_mailer" class="form-label">Mail Driver</label>
                                                    <select class="form-select" id="mail_mailer" name="mail_mailer">
                                                        <option value="smtp" {{ ($settings['email']['mail_mailer'] ?? env('MAIL_MAILER')) == 'smtp' ? 'selected' : '' }}>SMTP</option>
                                                        <option value="sendmail" {{ ($settings['email']['mail_mailer'] ?? env('MAIL_MAILER')) == 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                                                        <option value="mailgun" {{ ($settings['email']['mail_mailer'] ?? env('MAIL_MAILER')) == 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                                                        <option value="ses" {{ ($settings['email']['mail_mailer'] ?? env('MAIL_MAILER')) == 'ses' ? 'selected' : '' }}>Amazon SES</option>
                                                        <option value="postmark" {{ ($settings['email']['mail_mailer'] ?? env('MAIL_MAILER')) == 'postmark' ? 'selected' : '' }}>Postmark</option>
                                                        <option value="log" {{ ($settings['email']['mail_mailer'] ?? env('MAIL_MAILER')) == 'log' ? 'selected' : '' }}>Log</option>
                                                        <option value="array" {{ ($settings['email']['mail_mailer'] ?? env('MAIL_MAILER')) == 'array' ? 'selected' : '' }}>Array</option>
                                                    </select>
                                                    <div class="form-text">Select the mail driver to use for sending emails.</div>
                                                    @if(isset($settings['email']['mail_mailer']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current driver: <strong>{{ $settings['email']['mail_mailer'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mail_encryption" class="form-label">Mail Encryption</label>
                                                    <select class="form-select" id="mail_encryption" name="mail_encryption">
                                                        <option value="tls" {{ ($settings['email']['mail_encryption'] ?? env('MAIL_ENCRYPTION')) == 'tls' ? 'selected' : '' }}>TLS</option>
                                                        <option value="ssl" {{ ($settings['email']['mail_encryption'] ?? env('MAIL_ENCRYPTION')) == 'ssl' ? 'selected' : '' }}>SSL</option>
                                                        <option value="null" {{ ($settings['email']['mail_encryption'] ?? env('MAIL_ENCRYPTION')) == null ? 'selected' : '' }}>None</option>
                                                    </select>
                                                    <div class="form-text">The encryption protocol to use.</div>
                                                    @if(isset($settings['email']['mail_encryption']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current encryption: <strong>{{ $settings['email']['mail_encryption'] ?: 'None' }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mail_host" class="form-label">Mail Host</label>
                                                    <input type="text" class="form-control" id="mail_host" name="mail_host" value="{{ $settings['email']['mail_host'] ?? env('MAIL_HOST') }}">
                                                    <div class="form-text">The hostname of your mail server (e.g., smtp.gmail.com).</div>
                                                    @if(isset($settings['email']['mail_host']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current host: <strong>{{ $settings['email']['mail_host'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mail_port" class="form-label">Mail Port</label>
                                                    <input type="number" class="form-control" id="mail_port" name="mail_port" value="{{ $settings['email']['mail_port'] ?? env('MAIL_PORT') }}">
                                                    <div class="form-text">The port of your mail server (e.g., 587 for TLS, 465 for SSL).</div>
                                                    @if(isset($settings['email']['mail_port']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current port: <strong>{{ $settings['email']['mail_port'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Authentication</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mail_username" class="form-label">Mail Username</label>
                                                    <input type="text" class="form-control" id="mail_username" name="mail_username" value="{{ $settings['email']['mail_username'] ?? env('MAIL_USERNAME') }}">
                                                    <div class="form-text">The username for your mail server.</div>
                                                    @if(isset($settings['email']['mail_username']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current username: <strong>{{ $settings['email']['mail_username'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mail_password" class="form-label">Mail Password</label>
                                                    <div class="input-group">
                                                        <input type="password" class="form-control" id="mail_password" name="mail_password" placeholder="Leave empty to keep current password">
                                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                    <div class="form-text">The password for your mail server. Leave empty to keep the current password.</div>
                                                    @if(isset($settings['email']['mail_password']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Password is set <i class="fas fa-check-circle text-success"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Sender Information</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mail_from_address" class="form-label">From Address</label>
                                                    <input type="email" class="form-control" id="mail_from_address" name="mail_from_address" value="{{ $settings['email']['mail_from_address'] ?? env('MAIL_FROM_ADDRESS') }}" required>
                                                    <div class="form-text">The email address that will be used to send emails.</div>
                                                    @if(isset($settings['email']['mail_from_address']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current address: <strong>{{ $settings['email']['mail_from_address'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mail_from_name" class="form-label">From Name</label>
                                                    <input type="text" class="form-control" id="mail_from_name" name="mail_from_name" value="{{ $settings['email']['mail_from_name'] ?? env('MAIL_FROM_NAME') }}" required>
                                                    <div class="form-text">The name that will be used to send emails.</div>
                                                    @if(isset($settings['email']['mail_from_name']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current name: <strong>{{ $settings['email']['mail_from_name'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Email Settings
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Payment Settings -->
                            <div class="tab-pane fade" id="payment" role="tabpanel" aria-labelledby="payment-tab">
                                <h5 class="settings-section-title">
                                    <i class="fas fa-credit-card me-2"></i>Payment Settings
                                </h5>
                                <p class="text-muted mb-4">Configure your payment gateway and subscription pricing settings.</p>

                                <form action="{{ route('admin.settings.update.payment') }}" method="POST">
                                    @csrf
                                    <div class="settings-section">
                                        <h6 class="mb-3">Stripe Configuration</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="stripe_key" class="form-label">Stripe Publishable Key</label>
                                                    <input type="text" class="form-control" id="stripe_key" name="stripe_key" value="{{ $settings['payment']['stripe_key'] ?? env('STRIPE_KEY') }}" required>
                                                    <div class="form-text">Your Stripe publishable key.</div>
                                                    @if(isset($settings['payment']['stripe_key']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current key: <strong>{{ Str::limit($settings['payment']['stripe_key'], 20) }}...</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="stripe_secret" class="form-label">Stripe Secret Key</label>
                                                    <input type="text" class="form-control" id="stripe_secret" name="stripe_secret" value="{{ $settings['payment']['stripe_secret'] ?? env('STRIPE_SECRET') }}" required>
                                                    <div class="form-text">Your Stripe secret key.</div>
                                                    @if(isset($settings['payment']['stripe_secret']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current secret: <strong>{{ Str::limit($settings['payment']['stripe_secret'], 20) }}...</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row g-3">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label for="stripe_webhook_secret" class="form-label">Stripe Webhook Secret</label>
                                                    <input type="text" class="form-control" id="stripe_webhook_secret" name="stripe_webhook_secret" value="{{ $settings['payment']['stripe_webhook_secret'] ?? env('STRIPE_WEBHOOK_SECRET') }}">
                                                    <div class="form-text">Your Stripe webhook secret for handling events.</div>
                                                    @if(isset($settings['payment']['stripe_webhook_secret']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Webhook secret is set <i class="fas fa-check-circle text-success"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Subscription Configuration</h6>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="stripe_price_base_monthly" class="form-label">Base Tier Monthly Price ID</label>
                                                    <input type="text" class="form-control" id="stripe_price_base_monthly" name="stripe_price_base_monthly" value="{{ $settings['payment']['stripe_price_base_monthly'] ?? env('STRIPE_PRICE_BASE_MONTHLY') }}" required>
                                                    <div class="form-text">Stripe price ID for the base tier monthly subscription.</div>
                                                    @if(isset($settings['payment']['stripe_price_base_monthly']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current ID: <strong>{{ $settings['payment']['stripe_price_base_monthly'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="mb-3">
                                                    <label for="stripe_price_base_yearly" class="form-label">Base Tier Yearly Price ID</label>
                                                    <input type="text" class="form-control" id="stripe_price_base_yearly" name="stripe_price_base_yearly" value="{{ $settings['payment']['stripe_price_base_yearly'] ?? env('STRIPE_PRICE_BASE_YEARLY') }}">
                                                    <div class="form-text">Stripe price ID for the base tier yearly subscription.</div>
                                                    @if(isset($settings['payment']['stripe_price_base_yearly']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current ID: <strong>{{ $settings['payment']['stripe_price_base_yearly'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="stripe_price_premium_monthly" class="form-label">Premium Tier Monthly Price ID</label>
                                                    <input type="text" class="form-control" id="stripe_price_premium_monthly" name="stripe_price_premium_monthly" value="{{ $settings['payment']['stripe_price_premium_monthly'] ?? env('STRIPE_PRICE_PREMIUM_MONTHLY') }}" required>
                                                    <div class="form-text">Stripe price ID for the premium tier monthly subscription.</div>
                                                    @if(isset($settings['payment']['stripe_price_premium_monthly']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current ID: <strong>{{ $settings['payment']['stripe_price_premium_monthly'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="mb-3">
                                                    <label for="stripe_price_premium_yearly" class="form-label">Premium Tier Yearly Price ID</label>
                                                    <input type="text" class="form-control" id="stripe_price_premium_yearly" name="stripe_price_premium_yearly" value="{{ $settings['payment']['stripe_price_premium_yearly'] ?? env('STRIPE_PRICE_PREMIUM_YEARLY') }}">
                                                    <div class="form-text">Stripe price ID for the premium tier yearly subscription.</div>
                                                    @if(isset($settings['payment']['stripe_price_premium_yearly']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current ID: <strong>{{ $settings['payment']['stripe_price_premium_yearly'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Pricing Configuration</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="subscription_trial_days" class="form-label">Default Trial Period (Days)</label>
                                                    <input type="number" class="form-control" id="subscription_trial_days" name="subscription_trial_days" value="{{ $settings['payment']['subscription_trial_days'] ?? env('SUBSCRIPTION_TRIAL_DAYS', 7) }}" min="0" required>
                                                    <div class="form-text">Default number of days for the free trial period for new users.</div>
                                                    @if(isset($settings['payment']['subscription_trial_days']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current days: <strong>{{ $settings['payment']['subscription_trial_days'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="mb-3">
                                                    <label for="subscription_base_monthly_price" class="form-label">Base Tier Monthly Price</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">$</span>
                                                        <input type="number" class="form-control" id="subscription_base_monthly_price" name="subscription_base_monthly_price" value="{{ $settings['payment']['subscription_base_monthly_price'] ?? env('SUBSCRIPTION_BASE_MONTHLY_PRICE', 5.00) }}" step="0.01" min="0" required>
                                                    </div>
                                                    <div class="form-text">Price for the base tier monthly subscription.</div>
                                                    @if(isset($settings['payment']['subscription_base_monthly_price']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current price: <strong>${{ $settings['payment']['subscription_base_monthly_price'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="mb-3">
                                                    <label for="subscription_base_yearly_price" class="form-label">Base Tier Yearly Price</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">$</span>
                                                        <input type="number" class="form-control" id="subscription_base_yearly_price" name="subscription_base_yearly_price" value="{{ $settings['payment']['subscription_base_yearly_price'] ?? env('SUBSCRIPTION_BASE_YEARLY_PRICE', 50.00) }}" step="0.01" min="0" required>
                                                    </div>
                                                    <div class="form-text">Price for the base tier yearly subscription.</div>
                                                    @if(isset($settings['payment']['subscription_base_yearly_price']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current price: <strong>${{ $settings['payment']['subscription_base_yearly_price'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="subscription_premium_monthly_price" class="form-label">Premium Tier Monthly Price</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">$</span>
                                                        <input type="number" class="form-control" id="subscription_premium_monthly_price" name="subscription_premium_monthly_price" value="{{ $settings['payment']['subscription_premium_monthly_price'] ?? env('SUBSCRIPTION_PREMIUM_MONTHLY_PRICE', 10.00) }}" step="0.01" min="0" required>
                                                    </div>
                                                    <div class="form-text">Price for the premium tier monthly subscription.</div>
                                                    @if(isset($settings['payment']['subscription_premium_monthly_price']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current price: <strong>${{ $settings['payment']['subscription_premium_monthly_price'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="mb-3">
                                                    <label for="subscription_premium_yearly_price" class="form-label">Premium Tier Yearly Price</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">$</span>
                                                        <input type="number" class="form-control" id="subscription_premium_yearly_price" name="subscription_premium_yearly_price" value="{{ $settings['payment']['subscription_premium_yearly_price'] ?? env('SUBSCRIPTION_PREMIUM_YEARLY_PRICE', 100.00) }}" step="0.01" min="0" required>
                                                    </div>
                                                    <div class="form-text">Price for the premium tier yearly subscription.</div>
                                                    @if(isset($settings['payment']['subscription_premium_yearly_price']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current price: <strong>${{ $settings['payment']['subscription_premium_yearly_price'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Payment Settings
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Plaid Settings -->
                            <div class="tab-pane fade" id="plaid" role="tabpanel" aria-labelledby="plaid-tab">
                                <h5 class="settings-section-title">
                                    <i class="fas fa-university me-2"></i>Plaid Settings
                                </h5>
                                <p class="text-muted mb-4">Configure your Plaid API settings for bank account linking and payment processing.</p>

                                <form action="{{ route('admin.settings.update.plaid') }}" method="POST">
                                    @csrf
                                    <div class="settings-section">
                                        <h6 class="mb-3">Plaid API Configuration</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="plaid_client_id" class="form-label">Client ID</label>
                                                    <input type="text" class="form-control" id="plaid_client_id" name="plaid_client_id" value="{{ $settings['plaid']['plaid_client_id'] ?? env('PLAID_CLIENT_ID') }}" required>
                                                    <div class="form-text">Your Plaid client ID.</div>
                                                    @if(isset($settings['plaid']['plaid_client_id']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current ID: <strong>{{ Str::limit($settings['plaid']['plaid_client_id'], 20) }}...</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="plaid_secret" class="form-label">Secret</label>
                                                    <input type="text" class="form-control" id="plaid_secret" name="plaid_secret" value="{{ $settings['plaid']['plaid_secret'] ?? env('PLAID_SECRET') }}" required>
                                                    <div class="form-text">Your Plaid secret key.</div>
                                                    @if(isset($settings['plaid']['plaid_secret']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current secret: <strong>{{ Str::limit($settings['plaid']['plaid_secret'], 20) }}...</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="plaid_environment" class="form-label">Environment</label>
                                                    <select class="form-select" id="plaid_environment" name="plaid_environment" required>
                                                        <option value="sandbox" {{ ($settings['plaid']['plaid_environment'] ?? env('PLAID_ENVIRONMENT')) == 'sandbox' ? 'selected' : '' }}>Sandbox</option>
                                                        <option value="development" {{ ($settings['plaid']['plaid_environment'] ?? env('PLAID_ENVIRONMENT')) == 'development' ? 'selected' : '' }}>Development</option>
                                                        <option value="production" {{ ($settings['plaid']['plaid_environment'] ?? env('PLAID_ENVIRONMENT')) == 'production' ? 'selected' : '' }}>Production</option>
                                                    </select>
                                                    <div class="form-text">The Plaid API environment to use.</div>
                                                    @if(isset($settings['plaid']['plaid_environment']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current environment: <strong>{{ ucfirst($settings['plaid']['plaid_environment']) }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="plaid_client_name" class="form-label">Client Name</label>
                                                    <input type="text" class="form-control" id="plaid_client_name" name="plaid_client_name" value="{{ $settings['plaid']['plaid_client_name'] ?? env('PLAID_CLIENT_NAME', 'PocketWatch') }}" required>
                                                    <div class="form-text">The name to display in the Plaid Link interface.</div>
                                                    @if(isset($settings['plaid']['plaid_client_name']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current name: <strong>{{ $settings['plaid']['plaid_client_name'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Plaid Products & Configuration</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="plaid_products" class="form-label">Products</label>
                                                    <input type="text" class="form-control" id="plaid_products" name="plaid_products" value="{{ $settings['plaid']['plaid_products'] ?? env('PLAID_PRODUCTS', 'auth,transactions,payment_initiation') }}" required>
                                                    <div class="form-text">Comma-separated list of Plaid products to use (e.g., auth,transactions,payment_initiation).</div>
                                                    @if(isset($settings['plaid']['plaid_products']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current products: <strong>{{ $settings['plaid']['plaid_products'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="plaid_country_codes" class="form-label">Country Codes</label>
                                                    <input type="text" class="form-control" id="plaid_country_codes" name="plaid_country_codes" value="{{ $settings['plaid']['plaid_country_codes'] ?? env('PLAID_COUNTRY_CODES', 'US,CA') }}" required>
                                                    <div class="form-text">Comma-separated list of country codes (e.g., US,CA).</div>
                                                    @if(isset($settings['plaid']['plaid_country_codes']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current countries: <strong>{{ $settings['plaid']['plaid_country_codes'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="plaid_language" class="form-label">Language</label>
                                                    <input type="text" class="form-control" id="plaid_language" name="plaid_language" value="{{ $settings['plaid']['plaid_language'] ?? env('PLAID_LANGUAGE', 'en') }}" required>
                                                    <div class="form-text">Language code for the Plaid Link interface (e.g., en).</div>
                                                    @if(isset($settings['plaid']['plaid_language']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current language: <strong>{{ $settings['plaid']['plaid_language'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="plaid_webhook" class="form-label">Webhook URL</label>
                                                    <input type="url" class="form-control" id="plaid_webhook" name="plaid_webhook" value="{{ $settings['plaid']['plaid_webhook'] ?? env('PLAID_WEBHOOK') }}">
                                                    <div class="form-text">URL for Plaid to send webhook notifications (e.g., https://yourdomain.com/api/webhook/plaid).</div>
                                                    @if(isset($settings['plaid']['plaid_webhook']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current webhook: <strong>{{ $settings['plaid']['plaid_webhook'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Plaid Settings
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Welcome Page Settings -->
                            <div class="tab-pane fade" id="welcome" role="tabpanel" aria-labelledby="welcome-tab">
                                <h5 class="settings-section-title">
                                    <i class="fas fa-home me-2"></i>Welcome Page Settings
                                </h5>
                                <p class="text-muted mb-4">Configure the content and appearance of your welcome page.</p>

                                <form action="{{ route('admin.settings.update.welcome') }}" method="POST" enctype="multipart/form-data">
                                    @csrf

                                    <div class="settings-section">
                                        <h6 class="mb-3">Navbar Settings</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="navbar_brand" class="form-label">Navbar Brand Text</label>
                                                    <input type="text" class="form-control" id="navbar_brand" name="navbar_brand" value="{{ $settings['welcome']['navbar_brand'] ?? 'PocketWatch' }}">
                                                    <div class="form-text">Text to display in the navbar when no logo is present.</div>
                                                    @if(isset($settings['welcome']['navbar_brand']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current value: <strong>{{ $settings['welcome']['navbar_brand'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Navbar Links</label>
                                                    <div class="navbar-links-container">
                                                        @php
                                                            $navbarLinks = $settings['welcome']['navbar_links'] ?? [
                                                                ['name' => 'Features', 'url' => '#features'],
                                                                ['name' => 'Testimonials', 'url' => '#testimonials'],
                                                                ['name' => 'Packages', 'url' => '#packages']
                                                            ];
                                                        @endphp

                                                        @foreach($navbarLinks as $index => $link)
                                                        <div class="input-group mb-2">
                                                            <input type="text" class="form-control" name="navbar_links[{{ $index }}][name]" placeholder="Link Name" value="{{ $link['name'] }}">
                                                            <input type="text" class="form-control" name="navbar_links[{{ $index }}][url]" placeholder="Link URL" value="{{ $link['url'] }}">
                                                            <button class="btn btn-outline-danger remove-link" type="button"><i class="fas fa-times"></i></button>
                                                        </div>
                                                        @endforeach
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-primary mt-2 add-navbar-link">
                                                        <i class="fas fa-plus me-1"></i>Add Link
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Slider Settings</h6>
                                        <div class="sliders-container">
                                            @php
                                                $sliders = $settings['welcome']['welcome_sliders'] ?? [
                                                    [
                                                        'image' => 'https://via.placeholder.com/1200x400/000000/39FF14?text=Welcome+to+PocketWatch',
                                                        'title' => 'Innovate Your Future',
                                                        'description' => 'Bold design and seamless experience'
                                                    ],
                                                    [
                                                        'image' => 'https://via.placeholder.com/1200x400/111111/39FF14?text=Explore+Our+Features',
                                                        'title' => 'Next-Level Tools',
                                                        'description' => 'Powerful tools to grow your brand'
                                                    ]
                                                ];
                                            @endphp

                                            @foreach($sliders as $index => $slider)
                                            <div class="card mb-3 slider-item">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">Slide {{ $index + 1 }}</h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-slider"><i class="fas fa-trash"></i></button>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row g-3">
                                                        <div class="col-md-12">
                                                            <div class="mb-3">
                                                                <label class="form-label">Image URL</label>
                                                                <input type="text" class="form-control" name="welcome_sliders[{{ $index }}][image]" value="{{ $slider['image'] }}">
                                                                <div class="form-text">URL to the slider image (1200x400 recommended).</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Title</label>
                                                                <input type="text" class="form-control" name="welcome_sliders[{{ $index }}][title]" value="{{ $slider['title'] }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Description</label>
                                                                <input type="text" class="form-control" name="welcome_sliders[{{ $index }}][description]" value="{{ $slider['description'] }}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2 add-slider">
                                            <i class="fas fa-plus me-1"></i>Add Slide
                                        </button>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Features Section</h6>
                                        <div class="row g-3 mb-3">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label for="features_title" class="form-label">Section Title</label>
                                                    <input type="text" class="form-control" id="features_title" name="features_title" value="{{ $settings['welcome']['features_title'] ?? 'Features' }}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="features-container">
                                            @php
                                                $features = $settings['welcome']['welcome_features'] ?? [
                                                    [
                                                        'icon' => 'bi-bar-chart',
                                                        'title' => 'Real-Time Analytics',
                                                        'description' => 'Track performance and insights live with our advanced analytics dashboard.'
                                                    ],
                                                    [
                                                        'icon' => 'bi-paint-bucket',
                                                        'title' => 'Customizable Interface',
                                                        'description' => 'Tailor the design to suit your needs with our flexible customization options.'
                                                    ],
                                                    [
                                                        'icon' => 'bi-headset',
                                                        'title' => '24/7 Support',
                                                        'description' => 'We\'re here for you anytime, anywhere with our dedicated support team.'
                                                    ]
                                                ];
                                            @endphp

                                            @foreach($features as $index => $feature)
                                            <div class="card mb-3 feature-item">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">Feature {{ $index + 1 }}</h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-feature"><i class="fas fa-trash"></i></button>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row g-3">
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">Icon</label>
                                                                <input type="text" class="form-control" name="welcome_features[{{ $index }}][icon]" value="{{ $feature['icon'] }}">
                                                                <div class="form-text">Bootstrap icon class (e.g., bi-bar-chart).</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-8">
                                                            <div class="mb-3">
                                                                <label class="form-label">Title</label>
                                                                <input type="text" class="form-control" name="welcome_features[{{ $index }}][title]" value="{{ $feature['title'] }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="mb-3">
                                                                <label class="form-label">Description</label>
                                                                <textarea class="form-control" name="welcome_features[{{ $index }}][description]" rows="2">{{ $feature['description'] }}</textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2 add-feature">
                                            <i class="fas fa-plus me-1"></i>Add Feature
                                        </button>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Testimonials Section</h6>
                                        <div class="row g-3 mb-3">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label for="testimonials_title" class="form-label">Section Title</label>
                                                    <input type="text" class="form-control" id="testimonials_title" name="testimonials_title" value="{{ $settings['welcome']['testimonials_title'] ?? 'Testimonials' }}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="testimonials-container">
                                            @php
                                                $testimonials = $settings['welcome']['welcome_testimonials'] ?? [
                                                    [
                                                        'avatar' => 'https://via.placeholder.com/40x40',
                                                        'content' => 'PocketWatch gave my startup the edge it needed. Love the aesthetics and support!',
                                                        'name' => 'Jane Doe',
                                                        'position' => 'Startup CEO'
                                                    ],
                                                    [
                                                        'avatar' => 'https://via.placeholder.com/40x40',
                                                        'content' => 'Super clean, super fast. Perfect for our eCommerce business.',
                                                        'name' => 'John Smith',
                                                        'position' => 'eCommerce Director'
                                                    ]
                                                ];
                                            @endphp

                                            @foreach($testimonials as $index => $testimonial)
                                            <div class="card mb-3 testimonial-item">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">Testimonial {{ $index + 1 }}</h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-testimonial"><i class="fas fa-trash"></i></button>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row g-3">
                                                        <div class="col-md-12">
                                                            <div class="mb-3">
                                                                <label class="form-label">Content</label>
                                                                <textarea class="form-control" name="welcome_testimonials[{{ $index }}][content]" rows="2">{{ $testimonial['content'] }}</textarea>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">Avatar URL</label>
                                                                <input type="text" class="form-control" name="welcome_testimonials[{{ $index }}][avatar]" value="{{ $testimonial['avatar'] }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">Name</label>
                                                                <input type="text" class="form-control" name="welcome_testimonials[{{ $index }}][name]" value="{{ $testimonial['name'] }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">Position</label>
                                                                <input type="text" class="form-control" name="welcome_testimonials[{{ $index }}][position]" value="{{ $testimonial['position'] }}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2 add-testimonial">
                                            <i class="fas fa-plus me-1"></i>Add Testimonial
                                        </button>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Packages Section</h6>
                                        <div class="row g-3 mb-3">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label for="packages_title" class="form-label">Section Title</label>
                                                    <input type="text" class="form-control" id="packages_title" name="packages_title" value="{{ $settings['welcome']['packages_title'] ?? 'Packages' }}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="packages-container">
                                            @php
                                                $packages = $settings['welcome']['welcome_packages'] ?? [
                                                    [
                                                        'title' => 'Starter',
                                                        'price' => '$5 / month',
                                                        'popular' => false,
                                                        'features' => [
                                                            'Basic Tools',
                                                            '1 User',
                                                            'Email Support'
                                                        ],
                                                        'button_text' => 'Select Plan',
                                                        'button_url' => '#'
                                                    ],
                                                    [
                                                        'title' => 'Professional',
                                                        'price' => '$15 / month',
                                                        'popular' => true,
                                                        'features' => [
                                                            'All Starter Features',
                                                            '5 Users',
                                                            'Chat Support'
                                                        ],
                                                        'button_text' => 'Select Plan',
                                                        'button_url' => '#'
                                                    ],
                                                    [
                                                        'title' => 'Enterprise',
                                                        'price' => '$30 / month',
                                                        'popular' => false,
                                                        'features' => [
                                                            'All Pro Features',
                                                            'Unlimited Users',
                                                            'Phone & Priority Support'
                                                        ],
                                                        'button_text' => 'Select Plan',
                                                        'button_url' => '#'
                                                    ]
                                                ];
                                            @endphp

                                            @foreach($packages as $index => $package)
                                            <div class="card mb-3 package-item">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">Package {{ $index + 1 }}</h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-package"><i class="fas fa-trash"></i></button>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row g-3">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Title</label>
                                                                <input type="text" class="form-control" name="welcome_packages[{{ $index }}][title]" value="{{ $package['title'] }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Price</label>
                                                                <input type="text" class="form-control" name="welcome_packages[{{ $index }}][price]" value="{{ $package['price'] }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="form-check mb-3">
                                                                <input class="form-check-input" type="checkbox" name="welcome_packages[{{ $index }}][popular]" id="popular_{{ $index }}" value="1" {{ $package['popular'] ? 'checked' : '' }}>
                                                                <label class="form-check-label" for="popular_{{ $index }}">
                                                                    Mark as Popular
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="mb-3">
                                                                <label class="form-label">Features (one per line)</label>
                                                                <textarea class="form-control" name="welcome_packages[{{ $index }}][features_text]" rows="3">{{ implode("\n", $package['features']) }}</textarea>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Button Text</label>
                                                                <input type="text" class="form-control" name="welcome_packages[{{ $index }}][button_text]" value="{{ $package['button_text'] }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Button URL</label>
                                                                <input type="text" class="form-control" name="welcome_packages[{{ $index }}][button_url]" value="{{ $package['button_url'] }}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2 add-package">
                                            <i class="fas fa-plus me-1"></i>Add Package
                                        </button>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Social Links</h6>
                                        <div class="social-links-container">
                                            @php
                                                $socialLinks = $settings['welcome']['social_links'] ?? [
                                                    ['icon' => 'bi-facebook', 'url' => '#'],
                                                    ['icon' => 'bi-twitter', 'url' => '#'],
                                                    ['icon' => 'bi-instagram', 'url' => '#'],
                                                    ['icon' => 'bi-github', 'url' => '#']
                                                ];
                                            @endphp

                                            @foreach($socialLinks as $index => $link)
                                            <div class="input-group mb-2">
                                                <input type="text" class="form-control" name="social_links[{{ $index }}][icon]" placeholder="Icon Class" value="{{ $link['icon'] }}">
                                                <input type="text" class="form-control" name="social_links[{{ $index }}][url]" placeholder="URL" value="{{ $link['url'] }}">
                                                <button class="btn btn-outline-danger remove-social" type="button"><i class="fas fa-times"></i></button>
                                            </div>
                                            @endforeach
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2 add-social-link">
                                            <i class="fas fa-plus me-1"></i>Add Social Link
                                        </button>
                                        <div class="form-text mt-2">Use Bootstrap Icons classes (e.g., bi-facebook, bi-twitter, etc.)</div>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Theme Colors</h6>
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="primary_color" class="form-label">Primary Color</label>
                                                    <input type="color" class="form-control form-control-color w-100" id="primary_color" name="primary_color" value="{{ $settings['welcome']['primary_color'] ?? '#39FF14' }}">
                                                    <div class="form-text">Main accent color for buttons and highlights.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="background_color" class="form-label">Background Color</label>
                                                    <input type="color" class="form-control form-control-color w-100" id="background_color" name="background_color" value="{{ $settings['welcome']['background_color'] ?? '#0a0a0a' }}">
                                                    <div class="form-text">Page background color.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="text_color" class="form-label">Text Color</label>
                                                    <input type="color" class="form-control form-control-color w-100" id="text_color" name="text_color" value="{{ $settings['welcome']['text_color'] ?? '#39FF14' }}">
                                                    <div class="form-text">Main text color.</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Welcome Page Settings
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- System Settings -->
                            <div class="tab-pane fade" id="system" role="tabpanel" aria-labelledby="system-tab">
                                <h5 class="settings-section-title">
                                    <i class="fas fa-server me-2"></i>System Settings
                                </h5>
                                <p class="text-muted mb-4">Configure system-wide settings like timezone, date formats, and currency.</p>

                                <form action="{{ route('admin.settings.update.system') }}" method="POST">
                                    @csrf
                                    <div class="settings-section">
                                        <h6 class="mb-3">Regional Settings</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="timezone" class="form-label">Timezone</label>
                                                    <select class="form-select" id="timezone" name="timezone">
                                                        @foreach($timezones as $tz)
                                                            <option value="{{ $tz }}" {{ ($settings['system']['timezone'] ?? config('app.timezone')) == $tz ? 'selected' : '' }}>{{ $tz }}</option>
                                                        @endforeach
                                                    </select>
                                                    <div class="form-text">Select the timezone for your application.</div>
                                                    @if(isset($settings['system']['timezone']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current timezone: <strong>{{ $settings['system']['timezone'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="mb-3">
                                                    <label for="date_format" class="form-label">Date Format</label>
                                                    <select class="form-select" id="date_format" name="date_format">
                                                        <option value="Y-m-d" {{ ($settings['system']['date_format'] ?? 'Y-m-d') == 'Y-m-d' ? 'selected' : '' }}>2023-12-31 (Y-m-d)</option>
                                                        <option value="m/d/Y" {{ ($settings['system']['date_format'] ?? 'Y-m-d') == 'm/d/Y' ? 'selected' : '' }}>12/31/2023 (m/d/Y)</option>
                                                        <option value="d/m/Y" {{ ($settings['system']['date_format'] ?? 'Y-m-d') == 'd/m/Y' ? 'selected' : '' }}>31/12/2023 (d/m/Y)</option>
                                                        <option value="d.m.Y" {{ ($settings['system']['date_format'] ?? 'Y-m-d') == 'd.m.Y' ? 'selected' : '' }}>31.12.2023 (d.m.Y)</option>
                                                        <option value="F j, Y" {{ ($settings['system']['date_format'] ?? 'Y-m-d') == 'F j, Y' ? 'selected' : '' }}>December 31, 2023 (F j, Y)</option>
                                                    </select>
                                                    <div class="form-text">Select the date format for your application.</div>
                                                    @if(isset($settings['system']['date_format']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current format: <strong>{{ $settings['system']['date_format'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="time_format" class="form-label">Time Format</label>
                                                    <select class="form-select" id="time_format" name="time_format">
                                                        <option value="H:i" {{ ($settings['system']['time_format'] ?? 'H:i') == 'H:i' ? 'selected' : '' }}>24-hour (14:30)</option>
                                                        <option value="h:i A" {{ ($settings['system']['time_format'] ?? 'H:i') == 'h:i A' ? 'selected' : '' }}>12-hour (02:30 PM)</option>
                                                    </select>
                                                    <div class="form-text">Select the time format for your application.</div>
                                                    @if(isset($settings['system']['time_format']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current format: <strong>{{ $settings['system']['time_format'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="mb-3">
                                                    <label for="currency" class="form-label">Default Currency</label>
                                                    <select class="form-select" id="currency" name="currency">
                                                        <option value="USD" {{ ($settings['system']['currency'] ?? 'USD') == 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                                        <option value="EUR" {{ ($settings['system']['currency'] ?? 'USD') == 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                                        <option value="GBP" {{ ($settings['system']['currency'] ?? 'USD') == 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                                        <option value="CAD" {{ ($settings['system']['currency'] ?? 'USD') == 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                                                        <option value="AUD" {{ ($settings['system']['currency'] ?? 'USD') == 'AUD' ? 'selected' : '' }}>AUD - Australian Dollar</option>
                                                        <option value="JPY" {{ ($settings['system']['currency'] ?? 'USD') == 'JPY' ? 'selected' : '' }}>JPY - Japanese Yen</option>
                                                    </select>
                                                    <div class="form-text">Select the default currency for your application.</div>
                                                    @if(isset($settings['system']['currency']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current currency: <strong>{{ $settings['system']['currency'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-section">
                                        <h6 class="mb-3">Additional Settings</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                                    <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" value="{{ $settings['system']['currency_symbol'] ?? '$' }}" required>
                                                    <div class="form-text">The symbol to use for the selected currency.</div>
                                                    @if(isset($settings['system']['currency_symbol']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current symbol: <strong>{{ $settings['system']['currency_symbol'] }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3 form-check form-switch mt-4">
                                                    <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" {{ isset($settings['system']['maintenance_mode']) && $settings['system']['maintenance_mode'] ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="maintenance_mode">Maintenance Mode</label>
                                                    <div class="form-text">Enable maintenance mode to make the site unavailable to users.</div>
                                                    @if(isset($settings['system']['maintenance_mode']))
                                                        <div class="current-value">
                                                            <i class="fas fa-info-circle me-1"></i> Current status:
                                                            <strong>{{ $settings['system']['maintenance_mode'] ? 'Enabled' : 'Disabled' }}</strong>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="settings-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save System Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password toggle functionality
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('mail_password');

        if (togglePassword && passwordInput) {
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                // Toggle icon
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        }

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.forEach(function(tooltipTriggerEl) {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Welcome page settings - Add navbar link
        $('.add-navbar-link').on('click', function() {
            const index = $('.navbar-links-container .input-group').length;
            const html = `
                <div class="input-group mb-2">
                    <input type="text" class="form-control" name="navbar_links[${index}][name]" placeholder="Link Name">
                    <input type="text" class="form-control" name="navbar_links[${index}][url]" placeholder="Link URL">
                    <button class="btn btn-outline-danger remove-link" type="button"><i class="fas fa-times"></i></button>
                </div>
            `;
            $('.navbar-links-container').append(html);
        });

        // Remove navbar link
        $(document).on('click', '.remove-link', function() {
            $(this).closest('.input-group').remove();
        });

        // Add slider
        $('.add-slider').on('click', function() {
            const index = $('.slider-item').length;
            const html = `
                <div class="card mb-3 slider-item">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Slide ${index + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-slider"><i class="fas fa-trash"></i></button>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Image URL</label>
                                    <input type="text" class="form-control" name="welcome_sliders[${index}][image]" value="https://via.placeholder.com/1200x400/000000/39FF14?text=New+Slide">
                                    <div class="form-text">URL to the slider image (1200x400 recommended).</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Title</label>
                                    <input type="text" class="form-control" name="welcome_sliders[${index}][title]" value="New Slide Title">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <input type="text" class="form-control" name="welcome_sliders[${index}][description]" value="New slide description goes here">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('.sliders-container').append(html);
        });

        // Remove slider
        $(document).on('click', '.remove-slider', function() {
            $(this).closest('.slider-item').remove();
        });

        // Add feature
        $('.add-feature').on('click', function() {
            const index = $('.feature-item').length;
            const html = `
                <div class="card mb-3 feature-item">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Feature ${index + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-feature"><i class="fas fa-trash"></i></button>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Icon</label>
                                    <input type="text" class="form-control" name="welcome_features[${index}][icon]" value="bi-star">
                                    <div class="form-text">Bootstrap icon class (e.g., bi-bar-chart).</div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Title</label>
                                    <input type="text" class="form-control" name="welcome_features[${index}][title]" value="New Feature">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea class="form-control" name="welcome_features[${index}][description]" rows="2">Describe your new feature here.</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('.features-container').append(html);
        });

        // Remove feature
        $(document).on('click', '.remove-feature', function() {
            $(this).closest('.feature-item').remove();
        });

        // Add testimonial
        $('.add-testimonial').on('click', function() {
            const index = $('.testimonial-item').length;
            const html = `
                <div class="card mb-3 testimonial-item">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Testimonial ${index + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-testimonial"><i class="fas fa-trash"></i></button>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Content</label>
                                    <textarea class="form-control" name="welcome_testimonials[${index}][content]" rows="2">Add your testimonial content here.</textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Avatar URL</label>
                                    <input type="text" class="form-control" name="welcome_testimonials[${index}][avatar]" value="https://via.placeholder.com/40x40">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Name</label>
                                    <input type="text" class="form-control" name="welcome_testimonials[${index}][name]" value="Customer Name">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Position</label>
                                    <input type="text" class="form-control" name="welcome_testimonials[${index}][position]" value="Customer Position">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('.testimonials-container').append(html);
        });

        // Remove testimonial
        $(document).on('click', '.remove-testimonial', function() {
            $(this).closest('.testimonial-item').remove();
        });

        // Add package
        $('.add-package').on('click', function() {
            const index = $('.package-item').length;
            const html = `
                <div class="card mb-3 package-item">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Package ${index + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-package"><i class="fas fa-trash"></i></button>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Title</label>
                                    <input type="text" class="form-control" name="welcome_packages[${index}][title]" value="New Package">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Price</label>
                                    <input type="text" class="form-control" name="welcome_packages[${index}][price]" value="$XX / month">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="welcome_packages[${index}][popular]" id="popular_${index}" value="1">
                                    <label class="form-check-label" for="popular_${index}">
                                        Mark as Popular
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Features (one per line)</label>
                                    <textarea class="form-control" name="welcome_packages[${index}][features_text]" rows="3">Feature 1
Feature 2
Feature 3</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Button Text</label>
                                    <input type="text" class="form-control" name="welcome_packages[${index}][button_text]" value="Select Plan">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Button URL</label>
                                    <input type="text" class="form-control" name="welcome_packages[${index}][button_url]" value="#">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('.packages-container').append(html);
        });

        // Remove package
        $(document).on('click', '.remove-package', function() {
            $(this).closest('.package-item').remove();
        });

        // Add social link
        $('.add-social-link').on('click', function() {
            const index = $('.social-links-container .input-group').length;
            const html = `
                <div class="input-group mb-2">
                    <input type="text" class="form-control" name="social_links[${index}][icon]" placeholder="Icon Class" value="bi-link">
                    <input type="text" class="form-control" name="social_links[${index}][url]" placeholder="URL" value="#">
                    <button class="btn btn-outline-danger remove-social" type="button"><i class="fas fa-times"></i></button>
                </div>
            `;
            $('.social-links-container').append(html);
        });

        // Remove social link
        $(document).on('click', '.remove-social', function() {
            $(this).closest('.input-group').remove();
        });
    });
</script>
@endpush
