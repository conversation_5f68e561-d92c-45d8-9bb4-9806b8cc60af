# 🎯 PocketWatch API - Complete Endpoints Summary

## 📋 **API Overview**

**Base URL:** `http://127.0.0.1:8000/api`
**Version:** v3.1 - Simple Stripe Integration
**Authentication:** Bearer Token
**Content-Type:** `application/json`

---

## 🔐 **Authentication Endpoints**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `POST` | `/register` | ❌ | Register new user account |
| `POST` | `/login` | ❌ | Login with email/password |
| `POST` | `/logout` | ✅ | Logout and invalidate token |
| `GET` | `/me` | ✅ | Get user profile information |
| `GET` | `/auth/google` | ❌ | Get Google OAuth redirect URL |
| `GET` | `/auth/google/callback` | ❌ | Handle Google OAuth callback |
| `POST` | `/auth/google/token` | ❌ | Mobile Google ID token auth |
| `POST` | `/auth/google/unlink` | ✅ | Unlink Google account |

### **Key Parameters:**
- **Register:** `name`, `email`, `password`, `password_confirmation`
- **Login:** `email`, `password`
- **Google Token:** `id_token`

---

## 💳 **Simple Stripe Package Endpoints**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/packages-simple` | ✅ | Get dynamic packages with pricing |
| `POST` | `/packages-simple/stripe-url` | ✅ | Generate Stripe redirect URL |
| `GET` | `/payment-simple-success` | ✅ | Handle successful payment |
| `GET` | `/payment-simple-cancel` | ✅ | Handle cancelled payment |

### **🚀 Simple Blade-Based Flow:**
1. **Get Packages** → API returns Base ($9.99) & Premium ($19.99)
2. **Generate URL** → Creates Stripe redirect with user_id, plan_id, price
3. **Blade View** → Beautiful payment page with package details
4. **Stripe Payment** → Direct Stripe checkout
5. **Trial Activation** → 7-day trial starts automatically

### **Package IDs:**
- `base_monthly` - $9.99/month (3-level nesting, 10 bins)
- `premium_monthly` - $19.99/month (unlimited nesting, unlimited bins)

### **Key Parameters:**
- **Generate URL:** `package_id` (base_monthly, premium_monthly)
- **Stripe URL:** Contains user_id, plan_id, price, currency, plan_name
- **Success:** `user_id`, `plan_id`, `price`, `session_id` (optional)

---

## 📦 **Package Endpoints (Legacy)**

> **⚠️ Note:** Complex Stripe API endpoints have been disabled in favor of the simple Blade-based approach above.

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/packages` | ✅ | Get available packages |
| `POST` | `/packages/purchase` | ✅ | Purchase package (real) |
| `POST` | `/packages/purchase-test` | ✅ | Purchase package (test) |
| `GET` | `/packages-checkout` | ✅ | Get packages for direct checkout |
| `POST` | `/packages/{id}/checkout` | ✅ | Create direct Stripe session |

---

## 🗂️ **Bins Management Endpoints**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/bins` | ✅ | Get all user bins (paginated) |
| `POST` | `/bins` | ✅ | Create new bin |
| `GET` | `/bins/{id}` | ✅ | Get specific bin details |
| `PUT` | `/bins/{id}` | ✅ | Update bin |
| `DELETE` | `/bins/{id}` | ✅ | Delete bin |
| `GET` | `/bins/{id}/transactions` | ✅ | Get bin transactions |

### **Key Parameters:**
- **Create Bin:** `name`, `threshold`, `category?`, `description?`
- **Query Filters:** `page`, `per_page`, `category`, `status`

---

## 🏗️ **Sub-Bins Endpoints (Hierarchical)**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/bins/{binId}/sub-bins` | ✅ | Get sub-bins for a bin |
| `POST` | `/bins/{binId}/sub-bins` | ✅ | Create sub-bin |
| `GET` | `/bins/{binId}/sub-bins-hierarchy` | ✅ | Get hierarchical structure |
| `POST` | `/bins/{binId}/sub-bins/{parentId}/nested` | ✅ | Create nested sub-bin |
| `PUT` | `/sub-bins/{id}` | ✅ | Update sub-bin |
| `DELETE` | `/sub-bins/{id}` | ✅ | Delete sub-bin |

### **Key Parameters:**
- **Create Sub-Bin:** `name`, `threshold`, `parent_sub_bin_id?`
- **Nesting Limits:** Base (3 levels), Premium (unlimited)

---

## 💰 **Transaction Endpoints**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/transactions` | ✅ | Get all transactions (paginated) |
| `POST` | `/transactions` | ✅ | Create new transaction |
| `GET` | `/transactions/{id}` | ✅ | Get transaction details |
| `PUT` | `/transactions/{id}` | ✅ | Update transaction |
| `DELETE` | `/transactions/{id}` | ✅ | Delete transaction |
| `POST` | `/transactions/bulk` | ✅ | Create multiple transactions |

### **Key Parameters:**
- **Create Transaction:** `amount`, `description`, `bin_id`, `sub_bin_id?`, `category?`, `date?`
- **Query Filters:** `page`, `per_page`, `bin_id`, `category`, `date_from`, `date_to`

---

## 🪙 **Crypto Wallet Endpoints**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/crypto/wallets` | ✅ | Get user crypto wallets |
| `POST` | `/crypto/wallets` | ✅ | Add new crypto wallet |
| `GET` | `/crypto/wallets/{id}` | ✅ | Get wallet details |
| `PUT` | `/crypto/wallets/{id}` | ✅ | Update wallet |
| `DELETE` | `/crypto/wallets/{id}` | ✅ | Delete wallet |
| `GET` | `/crypto/wallets/{id}/balance` | ✅ | Get wallet balance |
| `POST` | `/crypto/wallets/{id}/sync` | ✅ | Sync wallet transactions |

### **Key Parameters:**
- **Create Wallet:** `name`, `address`, `currency`, `network?`
- **Supported Currencies:** BTC, ETH, LTC, ADA, DOT, etc.

---

## 📊 **Reports Endpoints**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/reports` | ✅ | Get available reports |
| `POST` | `/reports/generate` | ✅ | Generate new report |
| `GET` | `/reports/{id}` | ✅ | Get report details |
| `GET` | `/reports/{id}/download` | ✅ | Download report file |
| `POST` | `/reports/{id}/regenerate` | ✅ | Regenerate report |
| `DELETE` | `/reports/{id}` | ✅ | Delete report |

### **Report Types:**
- `spending_summary` - Monthly spending breakdown
- `bin_analysis` - Bin usage analysis
- `transaction_history` - Detailed transaction report
- `crypto_portfolio` - Crypto holdings report

---

## 🔗 **Webhook Endpoints**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `POST` | `/webhook/stripe-checkout` | ❌ | Stripe checkout webhooks |
| `POST` | `/webhook/stripe` | ❌ | Legacy Stripe webhooks |
| `POST` | `/webhook/plaid` | ❌ | Plaid bank webhooks |

### **Webhook Events:**
- **Stripe:** `checkout.session.completed`, `customer.subscription.updated`
- **Plaid:** `TRANSACTIONS`, `ACCOUNTS`, `ITEM`

---

## 🔧 **Subscription Management (Legacy)**

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/subscriptions` | ✅ | Get user subscriptions |
| `POST` | `/subscriptions/select-plan` | ✅ | Select subscription plan |
| `POST` | `/subscriptions/add-payment-method` | ✅ | Add payment method |
| `POST` | `/subscriptions/cancel` | ✅ | Cancel subscription |
| `GET` | `/subscriptions/plans` | ✅ | Get available plans |

---

## 📈 **Response Format**

### **Success Response:**
```json
{
    "success": true,
    "message": "Operation successful",
    "data": {
        // Response data
    }
}
```

### **Error Response:**
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field": ["Validation error message"]
    }
}
```

---

## 🔑 **Authentication Header**

```
Authorization: Bearer {your_token_here}
```

---

## 📊 **HTTP Status Codes**

| Code | Description |
|------|-------------|
| `200` | Success |
| `201` | Created |
| `400` | Bad Request |
| `401` | Unauthorized |
| `403` | Forbidden |
| `404` | Not Found |
| `422` | Validation Error |
| `500` | Server Error |

---

## 🎯 **Quick Start Examples**

### **1. Register & Login:**
```bash
# Register
curl -X POST http://127.0.0.1:8000/api/register \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","email":"<EMAIL>","password":"password123","password_confirmation":"password123"}'

# Login
curl -X POST http://127.0.0.1:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### **2. Browse & Purchase Package:**
```bash
# Get packages
curl -X GET http://127.0.0.1:8000/api/packages-gui \
  -H "Authorization: Bearer YOUR_TOKEN"

# Confirm package
curl -X GET http://127.0.0.1:8000/api/packages-gui/base_monthly/confirm \
  -H "Authorization: Bearer YOUR_TOKEN"

# Create checkout
curl -X POST http://127.0.0.1:8000/api/packages-gui/base_monthly/checkout \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"confirmed":true,"terms_accepted":true}'
```

### **3. Create Bin & Sub-Bin:**
```bash
# Create bin
curl -X POST http://127.0.0.1:8000/api/bins \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Groceries","threshold":500,"category":"groceries"}'

# Create sub-bin
curl -X POST http://127.0.0.1:8000/api/bins/1/sub-bins \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Organic Food","threshold":200}'
```

### **4. Add Transaction:**
```bash
curl -X POST http://127.0.0.1:8000/api/transactions \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"amount":45.50,"description":"Whole Foods","bin_id":1,"category":"groceries"}'
```

---

## 🌟 **Key Features**

✅ **GUI-Based Checkout** - Smooth Stripe integration with confirmation page
✅ **Google OAuth** - One-click authentication
✅ **Hierarchical Sub-Bins** - Unlimited nesting for Premium users
✅ **Crypto Integration** - Multi-currency wallet support
✅ **Real-time Webhooks** - Automatic subscription management
✅ **Comprehensive Reports** - Detailed financial analytics
✅ **Mobile-Friendly** - Responsive API design
✅ **7-Day Free Trial** - Automatic trial management

---

**🎯 PocketWatch API v3.0 - Complete Financial Management Platform**
