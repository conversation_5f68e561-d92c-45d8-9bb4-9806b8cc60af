@extends('admin.layouts.app')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Crypto Wallets</h1>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.crypto-wallets.index') }}" method="GET" class="row g-3">
                <div class="col-md-5">
                    <label for="user_id" class="form-label">User</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">All Users</option>
                        @foreach(\App\Models\User::orderBy('name')->get() as $user)
                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-5">
                    <label for="network" class="form-label">Blockchain Network</label>
                    <select class="form-select" id="network" name="network">
                        <option value="">All Networks</option>
                        <option value="ethereum" {{ request('network') == 'ethereum' ? 'selected' : '' }}>Ethereum</option>
                        <option value="binance" {{ request('network') == 'binance' ? 'selected' : '' }}>Binance</option>
                        <option value="polygon" {{ request('network') == 'polygon' ? 'selected' : '' }}>Polygon</option>
                        <option value="avalanche" {{ request('network') == 'avalanche' ? 'selected' : '' }}>Avalanche</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Crypto Wallets Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">All Crypto Wallets</h6>
            <div>
                <span class="badge bg-primary">Total: {{ $wallets->total() }}</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Name</th>
                            <th>Network</th>
                            <th>Wallet Address</th>
                            <th>Total Value</th>
                            <th>Assets</th>
                            <th>Last Synced</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($wallets as $wallet)
                            <tr>
                                <td>{{ $wallet->id }}</td>
                                <td>
                                    <a href="{{ route('admin.users.show', $wallet->user_id) }}">
                                        {{ $wallet->user->name }}
                                    </a>
                                </td>
                                <td>{{ $wallet->wallet_name }}</td>
                                <td>
                                    @php
                                        $networkColors = [
                                            'ethereum' => 'primary',
                                            'binance' => 'warning',
                                            'polygon' => 'info',
                                            'avalanche' => 'danger'
                                        ];
                                        $color = $networkColors[$wallet->blockchain_network] ?? 'secondary';
                                    @endphp
                                    <span class="badge bg-{{ $color }}">
                                        {{ ucfirst($wallet->blockchain_network) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="text-monospace">
                                            {{ substr($wallet->wallet_address, 0, 6) }}...{{ substr($wallet->wallet_address, -4) }}
                                        </span>
                                        <button class="btn btn-sm btn-link p-0 ms-2" 
                                                onclick="navigator.clipboard.writeText('{{ $wallet->wallet_address }}'); alert('Address copied!');">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>${{ number_format($wallet->total_value_usd, 2) }}</td>
                                <td>
                                    @if(is_array($wallet->assets) && count($wallet->assets) > 0)
                                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#assetsModal{{ $wallet->id }}">
                                            {{ count($wallet->assets) }} Assets
                                        </button>
                                        
                                        <!-- Assets Modal -->
                                        <div class="modal fade" id="assetsModal{{ $wallet->id }}" tabindex="-1" aria-labelledby="assetsModalLabel{{ $wallet->id }}" aria-hidden="true">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="assetsModalLabel{{ $wallet->id }}">
                                                            Assets for {{ $wallet->wallet_name }}
                                                        </h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="table-responsive">
                                                            <table class="table table-hover">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Token</th>
                                                                        <th>Symbol</th>
                                                                        <th>Balance</th>
                                                                        <th>Price</th>
                                                                        <th>Value</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    @foreach($wallet->assets as $asset)
                                                                        <tr>
                                                                            <td>{{ $asset['name'] }}</td>
                                                                            <td>{{ $asset['symbol'] }}</td>
                                                                            <td>{{ $asset['balance'] }}</td>
                                                                            <td>${{ number_format($asset['price_usd'], 2) }}</td>
                                                                            <td>${{ number_format($asset['value_usd'], 2) }}</td>
                                                                        </tr>
                                                                    @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        <span class="text-muted">No assets</span>
                                    @endif
                                </td>
                                <td>
                                    @if($wallet->last_synced_at)
                                        {{ $wallet->last_synced_at->format('M d, Y H:i') }}
                                    @else
                                        <span class="text-muted">Never</span>
                                    @endif
                                </td>
                                <td>
                                    @if($wallet->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-danger">Inactive</span>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">No crypto wallets found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $wallets->withQueryString()->links() }}
            </div>
        </div>
    </div>
@endsection
