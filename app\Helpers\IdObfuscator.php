<?php

namespace App\Helpers;

use App\Services\IdObfuscationService;
use Illuminate\Support\Facades\App;

class IdObfuscator
{
    /**
     * Obfuscate an ID for use in views.
     *
     * @param  mixed  $id
     * @param  string  $type
     * @return string
     */
    public static function obfuscate($id, $type = 'default')
    {
        return App::make(IdObfuscationService::class)->obfuscate($id, $type);
    }
    
    /**
     * Deobfuscate an ID from views.
     *
     * @param  string  $obfuscatedId
     * @param  string|null  $expectedType
     * @return mixed
     */
    public static function deobfuscate($obfuscatedId, $expectedType = null)
    {
        return App::make(IdObfuscationService::class)->deobfuscate($obfuscatedId, $expectedType);
    }
    
    /**
     * Generate a route URL with an obfuscated ID.
     *
     * @param  string  $name
     * @param  mixed  $id
     * @param  string  $type
     * @param  array  $parameters
     * @param  bool  $absolute
     * @return string
     */
    public static function route($name, $id, $type = 'default', $parameters = [], $absolute = true)
    {
        $obfuscatedId = static::obfuscate($id, $type);
        
        return route($name, array_merge([$obfuscatedId], $parameters), $absolute);
    }
    
    /**
     * Generate a secure resource token.
     *
     * @param  mixed  $id
     * @param  string  $type
     * @param  int  $expiresInMinutes
     * @return string
     */
    public static function generateToken($id, $type, $expiresInMinutes = 60)
    {
        return App::make(IdObfuscationService::class)->generateResourceToken($id, $type, $expiresInMinutes);
    }
    
    /**
     * Validate a secure resource token.
     *
     * @param  string  $token
     * @param  string  $expectedType
     * @return mixed
     */
    public static function validateToken($token, $expectedType)
    {
        return App::make(IdObfuscationService::class)->validateResourceToken($token, $expectedType);
    }
}
