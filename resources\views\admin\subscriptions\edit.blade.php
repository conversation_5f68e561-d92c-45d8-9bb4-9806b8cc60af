@extends('admin.layouts.app')

@section('title', 'Edit Subscription')
@section('subtitle', 'Update subscription details')

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('admin.subscriptions.index') }}">Subscriptions</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.subscriptions.show', $subscription->id) }}">{{ $subscription->id }}</a></li>
    <li class="breadcrumb-item">Edit</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Edit Subscription</h6>
                    <span class="badge {{ $subscription->stripe_status === 'active' ? 'bg-success' : ($subscription->stripe_status === 'trialing' ? 'bg-info' : 'bg-secondary') }}">
                        {{ ucfirst($subscription->stripe_status) }}
                    </span>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.subscriptions.update', $subscription->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="mb-3">
                            <label class="form-label">User</label>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    {{ substr($subscription->user->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ $subscription->user->name }}</div>
                                    <div class="text-muted">{{ $subscription->user->email }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="subscription_tier" class="form-label">Subscription Tier <span class="text-danger">*</span></label>
                                <select class="form-select @error('subscription_tier') is-invalid @enderror" id="subscription_tier" name="subscription_tier" required>
                                    @foreach($tierOptions as $tier)
                                        <option value="{{ $tier }}" {{ old('subscription_tier', $subscription->subscription_tier) == $tier ? 'selected' : '' }}>
                                            {{ ucfirst($tier) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('subscription_tier')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="billing_cycle" class="form-label">Billing Cycle <span class="text-danger">*</span></label>
                                <select class="form-select @error('billing_cycle') is-invalid @enderror" id="billing_cycle" name="billing_cycle" required>
                                    @foreach($billingCycleOptions as $cycle)
                                        <option value="{{ $cycle }}" {{ old('billing_cycle', $subscription->billing_cycle) == $cycle ? 'selected' : '' }}>
                                            {{ ucfirst($cycle) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('billing_cycle')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_notification" name="send_notification" value="1" {{ old('send_notification') ? 'checked' : '' }}>
                                <label class="form-check-label" for="send_notification">
                                    Send notification to user
                                </label>
                            </div>
                            <div class="form-text">If checked, the user will receive an email notification about the subscription update.</div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('admin.subscriptions.show', $subscription->id) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Back to Details
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Update Subscription
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">Danger Zone</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card h-100 border-danger">
                                <div class="card-body">
                                    <h5 class="card-title">Cancel Subscription</h5>
                                    <p class="card-text">This will cancel the subscription at the end of the current billing period.</p>
                                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                        <i class="fas fa-ban me-1"></i> Cancel Subscription
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="card h-100 border-danger">
                                <div class="card-body">
                                    <h5 class="card-title">Delete Subscription</h5>
                                    <p class="card-text">This will permanently delete the subscription record and cancel it in Stripe.</p>
                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                        <i class="fas fa-trash-alt me-1"></i> Delete Permanently
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="text-muted mb-1">Subscription ID</div>
                        <div class="fw-bold">{{ $subscription->id }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Stripe ID</div>
                        <div class="fw-bold">
                            @if($subscription->stripe_id)
                                <span class="text-monospace">{{ $subscription->stripe_id }}</span>
                                <button class="btn btn-sm btn-link p-0 ms-2" 
                                        onclick="navigator.clipboard.writeText('{{ $subscription->stripe_id }}'); alert('ID copied!');">
                                    <i class="fas fa-copy"></i>
                                </button>
                            @else
                                <span class="text-muted">N/A</span>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Current Price</div>
                        <div class="fw-bold">${{ number_format($subscription->price, 2) }} / {{ $subscription->billing_cycle }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Created On</div>
                        <div class="fw-bold">{{ $subscription->created_at->format('M d, Y H:i') }}</div>
                    </div>
                    
                    @if($subscription->trial_ends_at)
                        <div class="mb-3">
                            <div class="text-muted mb-1">Trial Ends</div>
                            <div class="fw-bold">
                                {{ $subscription->trial_ends_at->format('M d, Y') }}
                                @if($subscription->onTrial())
                                    <span class="badge badge-soft-info ms-1">
                                        {{ now()->diffInDays($subscription->trial_ends_at) }} days left
                                    </span>
                                @endif
                            </div>
                        </div>
                    @endif
                    
                    @if($subscription->ends_at)
                        <div class="mb-3">
                            <div class="text-muted mb-1">Ends On</div>
                            <div class="fw-bold {{ $subscription->ends_at->isPast() ? 'text-danger' : 'text-warning' }}">
                                {{ $subscription->ends_at->format('M d, Y') }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Pricing Information</h6>
                </div>
                <div class="card-body">
                    <div class="pricing-info mb-4">
                        <h5 class="mb-3">Base Tier</h5>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Monthly:</span>
                            <span class="fw-bold">$5.00</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Yearly:</span>
                            <span class="fw-bold">$50.00 <span class="text-success">(Save $10)</span></span>
                        </div>
                    </div>
                    
                    <div class="pricing-info mb-4">
                        <h5 class="mb-3">Premium Tier</h5>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Monthly:</span>
                            <span class="fw-bold">$10.00</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Yearly:</span>
                            <span class="fw-bold">$100.00 <span class="text-success">(Save $20)</span></span>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> Changing the subscription tier or billing cycle will update the subscription in Stripe and may result in immediate charges or credits.
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cancel Modal -->
    <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.subscriptions.cancel', $subscription->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="cancelModalLabel">Cancel Subscription</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to cancel this subscription?</p>
                        <p class="text-muted">The subscription will remain active until the end of the current billing period.</p>
                        
                        <div class="mb-3">
                            <label for="cancel_reason" class="form-label">Cancellation Reason</label>
                            <input type="text" class="form-control" id="cancel_reason" name="cancel_reason" placeholder="Optional">
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="send_notification_cancel" name="send_notification" value="1" checked>
                            <label class="form-check-label" for="send_notification_cancel">
                                Send notification to user
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-danger">Cancel Subscription</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.subscriptions.destroy', $subscription->id) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">Delete Subscription</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This action cannot be undone.
                        </div>
                        <p>Are you sure you want to permanently delete this subscription?</p>
                        <p class="text-muted">This will cancel the subscription in Stripe and remove it from the database.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-danger">Delete Permanently</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Update price display when tier or billing cycle changes
        $('#subscription_tier, #billing_cycle').change(function() {
            updatePriceDisplay();
        });
        
        function updatePriceDisplay() {
            const tier = $('#subscription_tier').val();
            const cycle = $('#billing_cycle').val();
            
            // Highlight the selected pricing option
            $('.pricing-info').removeClass('bg-light');
            $(`.pricing-info:eq(${tier === 'premium' ? 1 : 0})`).addClass('bg-light');
            
            // Bold the selected billing cycle
            $('.pricing-info .d-flex').removeClass('fw-bold bg-light');
            $(`.pricing-info:eq(${tier === 'premium' ? 1 : 0}) .d-flex:eq(${cycle === 'yearly' ? 1 : 0})`).addClass('fw-bold bg-light');
        }
        
        // Initialize on page load
        updatePriceDisplay();
    });
</script>
@endpush
