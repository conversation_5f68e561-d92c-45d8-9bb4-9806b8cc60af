<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AvatarHelper
{
    /**
     * Get a safe avatar URL that handles missing files.
     *
     * @param string|null $avatarPath
     * @param string $userName
     * @return string
     */
    public static function getSafeAvatarUrl($avatarPath, $userName = 'User')
    {
        // If no avatar path provided, return default
        if (!$avatarPath) {
            return self::getDefaultAvatarUrl($userName);
        }

        // If it's a Google avatar URL, return as is
        if (Str::contains($avatarPath, ['googleusercontent.com', 'googleapis.com'])) {
            return $avatarPath;
        }

        // If it's a full URL, check if the file exists
        if (Str::startsWith($avatarPath, ['http://', 'https://'])) {
            // For local URLs, check if file exists in public folder
            if (Str::contains($avatarPath, [url('/'), config('app.url')])) {
                $relativePath = str_replace(url('/'), '', $avatarPath);

                // Check if file exists in public directory
                if (file_exists(public_path($relativePath))) {
                    return $avatarPath;
                } else {
                    // File doesn't exist, return default
                    return self::getDefaultAvatarUrl($userName);
                }
            } else {
                // External URL (like Google), return as-is
                return $avatarPath;
            }
        }

        // If it's a relative path, check if file exists in public folder
        if (file_exists(public_path($avatarPath))) {
            return url($avatarPath);
        }

        // Check if it's in avatars directory
        if (file_exists(public_path('avatars/' . $avatarPath))) {
            return url('avatars/' . $avatarPath);
        }

        // Default fallback
        return self::getDefaultAvatarUrl($userName);
    }

    /**
     * Get default avatar URL.
     *
     * @param string $userName
     * @return string
     */
    public static function getDefaultAvatarUrl($userName = 'User')
    {
        // Create a simple avatar using UI Avatars service
        $initials = self::getInitials($userName);
        $backgroundColor = self::getColorFromName($userName);

        return "https://ui-avatars.com/api/?name=" . urlencode($initials) .
               "&background=" . $backgroundColor .
               "&color=ffffff&size=200&font-size=0.6&bold=true";
    }

    /**
     * Get initials from name.
     *
     * @param string $name
     * @return string
     */
    public static function getInitials($name)
    {
        $words = explode(' ', trim($name));
        $initials = '';

        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
                if (strlen($initials) >= 2) break;
            }
        }

        return $initials ?: 'U';
    }

    /**
     * Generate a consistent color from name.
     *
     * @param string $name
     * @return string
     */
    public static function getColorFromName($name)
    {
        $colors = [
            '3498db', '9b59b6', 'e74c3c', 'f39c12',
            '2ecc71', '1abc9c', '34495e', 'e67e22',
            '95a5a6', 'f1c40f', 'd35400', '8e44ad'
        ];

        $hash = crc32($name);
        $index = abs($hash) % count($colors);

        return $colors[$index];
    }

    /**
     * Create a default avatar file if it doesn't exist.
     *
     * @param string $userName
     * @param string $userEmail
     * @return string
     */
    public static function createDefaultAvatar($userName, $userEmail = null)
    {
        $initials = self::getInitials($userName);
        $backgroundColor = self::getColorFromName($userName);

        // Generate a unique filename
        $filename = 'default_' . md5($userEmail ?: $userName) . '.png';
        $path = 'public/avatars/' . $filename;

        // Check if default avatar already exists
        if (Storage::exists($path)) {
            return Storage::url($path);
        }

        // Create avatar directory if it doesn't exist
        if (!Storage::exists('public/avatars')) {
            Storage::makeDirectory('public/avatars');
        }

        // For now, return the UI Avatars URL
        // In a production environment, you might want to download and store the image
        return self::getDefaultAvatarUrl($userName);
    }

    /**
     * Clean up invalid avatar paths from database.
     *
     * @return int Number of cleaned records
     */
    public static function cleanupInvalidAvatars()
    {
        $users = \App\Models\User::whereNotNull('avatar')->get();
        $cleaned = 0;

        foreach ($users as $user) {
            $avatarPath = $user->avatar;

            // Skip Google avatars and UI avatars
            if (Str::contains($avatarPath, ['googleusercontent.com', 'googleapis.com', 'ui-avatars.com'])) {
                continue;
            }

            // Check if local file exists in public folder
            $relativePath = str_replace(url('/'), '', $avatarPath);

            if (!file_exists(public_path($relativePath))) {
                // File doesn't exist, set to null so default will be used
                $user->avatar = null;
                $user->save();
                $cleaned++;
            }
        }

        return $cleaned;
    }
}
