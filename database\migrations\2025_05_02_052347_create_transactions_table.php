<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('bin_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('sub_bin_id')->nullable()->constrained()->nullOnDelete();
            $table->string('transaction_type')->comment('income, expense, transfer');
            $table->decimal('amount', 15, 2);
            $table->string('currency', 10)->default('USD');
            $table->text('description')->nullable();
            $table->string('category')->nullable();
            $table->string('payment_method')->nullable();
            $table->string('status')->default('completed');
            $table->string('reference_id')->nullable();
            $table->string('source')->default('manual')->comment('manual, plaid, stripe, etc.');
            $table->json('metadata')->nullable();
            $table->timestamp('transaction_date');
            $table->time('transaction_time')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
