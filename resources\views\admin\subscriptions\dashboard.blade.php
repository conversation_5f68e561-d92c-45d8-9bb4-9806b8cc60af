@extends('admin.layouts.app')

@section('title', 'Subscription Dashboard')
@section('subtitle', 'Overview of subscription metrics and performance')

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('admin.subscriptions.index') }}">Subscriptions</a></li>
    <li class="breadcrumb-item">Dashboard</li>
@endsection

@section('content')
    <!-- Subscription Stats -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Monthly Recurring Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($monthlyRevenue, 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Active Subscriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $activeSubscriptions }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Premium Subscribers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $premiumSubscriptions }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Trial Subscriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $trialSubscriptions }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Subscription Growth Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Growth</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Time Range:</div>
                            <a class="dropdown-item active" href="#">Last 30 Days</a>
                            <a class="dropdown-item" href="#">Last 90 Days</a>
                            <a class="dropdown-item" href="#">Last Year</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="subscriptionGrowthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Distribution -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="subscriptionDistributionChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="me-2">
                            <i class="fas fa-circle text-secondary"></i> Base (Monthly)
                        </span>
                        <span class="me-2">
                            <i class="fas fa-circle text-info"></i> Base (Yearly)
                        </span>
                        <span class="me-2">
                            <i class="fas fa-circle text-success"></i> Premium (Monthly)
                        </span>
                        <span class="me-2">
                            <i class="fas fa-circle text-primary"></i> Premium (Yearly)
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Subscriptions -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Subscriptions</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Plan</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentSubscriptions as $subscription)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-2">
                                                    {{ substr($subscription->user->name, 0, 1) }}
                                                </div>
                                                <div>{{ $subscription->user->name }}</div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge {{ $subscription->subscription_tier == 'premium' ? 'bg-success' : 'bg-secondary' }}">
                                                {{ ucfirst($subscription->subscription_tier) }}
                                            </span>
                                        </td>
                                        <td>{{ $subscription->created_at->format('M d, Y') }}</td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'active' => 'success',
                                                    'trialing' => 'info',
                                                    'canceled' => 'danger',
                                                    'incomplete' => 'warning',
                                                    'past_due' => 'danger',
                                                    'unpaid' => 'danger',
                                                    'incomplete_expired' => 'danger',
                                                    'paused' => 'warning',
                                                    'pending' => 'secondary'
                                                ];
                                                $color = $statusColors[$subscription->stripe_status] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $color }}">
                                                {{ ucfirst($subscription->stripe_status) }}
                                            </span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center">No recent subscriptions</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-sm btn-primary">View All</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Trials Ending -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Upcoming Trial Expirations</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Plan</th>
                                    <th>Trial Ends</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($upcomingTrialEnds as $subscription)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-2">
                                                    {{ substr($subscription->user->name, 0, 1) }}
                                                </div>
                                                <div>{{ $subscription->user->name }}</div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge {{ $subscription->subscription_tier == 'premium' ? 'bg-success' : 'bg-secondary' }}">
                                                {{ ucfirst($subscription->subscription_tier) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-{{ now()->diffInDays($subscription->trial_ends_at) < 3 ? 'danger' : 'warning' }}">
                                                {{ $subscription->trial_ends_at->format('M d, Y') }}
                                                <small>({{ now()->diffInDays($subscription->trial_ends_at) }} days)</small>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.subscriptions.show', $subscription->id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center">No upcoming trial expirations</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Subscription Growth Chart
    var ctx = document.getElementById("subscriptionGrowthChart");
    var myLineChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: {!! json_encode($growthChart['labels']) !!},
            datasets: [{
                label: "Base",
                lineTension: 0.3,
                backgroundColor: "rgba(78, 115, 223, 0.05)",
                borderColor: "rgba(108, 117, 125, 1)",
                pointRadius: 3,
                pointBackgroundColor: "rgba(108, 117, 125, 1)",
                pointBorderColor: "rgba(108, 117, 125, 1)",
                pointHoverRadius: 3,
                pointHoverBackgroundColor: "rgba(108, 117, 125, 1)",
                pointHoverBorderColor: "rgba(108, 117, 125, 1)",
                pointHitRadius: 10,
                pointBorderWidth: 2,
                data: {!! json_encode($growthChart['base']) !!},
            },
            {
                label: "Premium",
                lineTension: 0.3,
                backgroundColor: "rgba(46, 139, 87, 0.05)",
                borderColor: "rgba(46, 139, 87, 1)",
                pointRadius: 3,
                pointBackgroundColor: "rgba(46, 139, 87, 1)",
                pointBorderColor: "rgba(46, 139, 87, 1)",
                pointHoverRadius: 3,
                pointHoverBackgroundColor: "rgba(46, 139, 87, 1)",
                pointHoverBorderColor: "rgba(46, 139, 87, 1)",
                pointHitRadius: 10,
                pointBorderWidth: 2,
                data: {!! json_encode($growthChart['premium']) !!},
            }],
        },
        options: {
            maintainAspectRatio: false,
            layout: {
                padding: {
                    left: 10,
                    right: 25,
                    top: 25,
                    bottom: 0
                }
            },
            scales: {
                xAxes: [{
                    time: {
                        unit: 'date'
                    },
                    gridLines: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        maxTicksLimit: 7
                    }
                }],
                yAxes: [{
                    ticks: {
                        maxTicksLimit: 5,
                        padding: 10,
                        beginAtZero: true
                    },
                    gridLines: {
                        color: "rgb(234, 236, 244)",
                        zeroLineColor: "rgb(234, 236, 244)",
                        drawBorder: false,
                        borderDash: [2],
                        zeroLineBorderDash: [2]
                    }
                }],
            },
            legend: {
                display: true
            },
            tooltips: {
                backgroundColor: "rgb(255,255,255)",
                bodyFontColor: "#858796",
                titleMarginBottom: 10,
                titleFontColor: '#6e707e',
                titleFontSize: 14,
                borderColor: '#dddfeb',
                borderWidth: 1,
                xPadding: 15,
                yPadding: 15,
                displayColors: false,
                intersect: false,
                mode: 'index',
                caretPadding: 10,
            }
        }
    });

    // Subscription Distribution Chart
    var ctx2 = document.getElementById("subscriptionDistributionChart");
    var myPieChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: ["Base (Monthly)", "Base (Yearly)", "Premium (Monthly)", "Premium (Yearly)"],
            datasets: [{
                data: [
                    {{ $distributionChart['base_monthly'] }}, 
                    {{ $distributionChart['base_yearly'] }}, 
                    {{ $distributionChart['premium_monthly'] }}, 
                    {{ $distributionChart['premium_yearly'] }}
                ],
                backgroundColor: ['#6c757d', '#17a2b8', '#28a745', '#2E8B57'],
                hoverBackgroundColor: ['#5a6268', '#138496', '#218838', '#1D5E40'],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            maintainAspectRatio: false,
            tooltips: {
                backgroundColor: "rgb(255,255,255)",
                bodyFontColor: "#858796",
                borderColor: '#dddfeb',
                borderWidth: 1,
                xPadding: 15,
                yPadding: 15,
                displayColors: false,
                caretPadding: 10,
            },
            legend: {
                display: false
            },
            cutoutPercentage: 70,
        },
    });
</script>
@endpush
