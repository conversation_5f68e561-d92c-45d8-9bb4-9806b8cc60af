<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
        }
        .verification-code {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 8px;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 30px 0;
            font-family: 'Courier New', monospace;
        }
        .message {
            font-size: 16px;
            line-height: 1.6;
            color: #4a5568;
            margin-bottom: 20px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #718096;
            text-align: center;
        }
        .button {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 0;
        }
        .highlight {
            color: #667eea;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">💰 {{ config('app.name') }}</div>
            <h1 class="title">Verify Your Email Address</h1>
        </div>

        <div class="message">
            Hello <strong>{{ $user->name }}</strong>,
        </div>

        <div class="message">
            Thank you for registering with {{ config('app.name') }}! To complete your account setup and start managing your finances, please verify your email address using the verification code below:
        </div>

        <div class="verification-code">
            {{ $verificationCode }}
        </div>

        <div class="message">
            Enter this <span class="highlight">6-digit code</span> in the verification screen to activate your account. This code will expire in <span class="highlight">{{ $expiryMinutes }} minutes</span>.
        </div>

        <div class="warning">
            <strong>⚠️ Security Notice:</strong><br>
            • This code is valid for {{ $expiryMinutes }} minutes only<br>
            • Don't share this code with anyone<br>
            • If you didn't request this verification, please ignore this email<br>
            • You have {{ 5 - $attempts }} verification attempts remaining
        </div>

        <div class="message">
            Once verified, you'll be able to:
            <ul>
                <li>✅ Create and manage your financial bins</li>
                <li>✅ Track your income and expenses</li>
                <li>✅ Set up unlimited sub-bins (Premium)</li>
                <li>✅ Access detailed financial reports</li>
                <li>✅ Connect your crypto wallets</li>
            </ul>
        </div>

        <div class="footer">
            <p>
                <strong>{{ config('app.name') }}</strong><br>
                Your Personal Finance Management Platform
            </p>
            <p>
                If you're having trouble with verification, please contact our support team.<br>
                This email was sent to {{ $user->email }}
            </p>
            <p style="font-size: 12px; color: #a0aec0;">
                © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
