@extends('admin.layouts.app')

@section('title', 'Create Subscription')
@section('subtitle', 'Create a new subscription for a user')

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('admin.subscriptions.index') }}">Subscriptions</a></li>
    <li class="breadcrumb-item">Create</li>
@endsection

@section('content')
    <!-- Admin Note -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Note:</strong> Admin users do not require subscriptions. Only regular users can have subscriptions.
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Details</h6>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.subscriptions.store') }}" method="POST">
                        @csrf

                        <div class="mb-3">
                            <label for="user_id" class="form-label">User <span class="text-danger">*</span></label>
                            <select class="form-select @error('user_id') is-invalid @enderror" id="user_id" name="user_id" required>
                                <option value="">Select User</option>
                                @foreach($users as $user)
                                    @if(!$user->is_admin)
                                        <option value="{{ $user->uuid }}" {{ old('user_id') == $user->uuid ? 'selected' : '' }}>
                                            {{ $user->name }} ({{ $user->email }})
                                        </option>
                                    @endif
                                @endforeach
                            </select>
                            @error('user_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Select the user who will own this subscription.</div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="subscription_tier" class="form-label">Subscription Tier <span class="text-danger">*</span></label>
                                <select class="form-select @error('subscription_tier') is-invalid @enderror" id="subscription_tier" name="subscription_tier" required>
                                    @foreach($tierOptions as $tier)
                                        <option value="{{ $tier }}" {{ old('subscription_tier', 'base') == $tier ? 'selected' : '' }}>
                                            {{ ucfirst($tier) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('subscription_tier')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="billing_cycle" class="form-label">Billing Cycle <span class="text-danger">*</span></label>
                                <select class="form-select @error('billing_cycle') is-invalid @enderror" id="billing_cycle" name="billing_cycle" required>
                                    @foreach($billingCycleOptions as $cycle)
                                        <option value="{{ $cycle }}" {{ old('billing_cycle', 'monthly') == $cycle ? 'selected' : '' }}>
                                            {{ ucfirst($cycle) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('billing_cycle')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="trial_days" class="form-label">Trial Period (Days) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('trial_days') is-invalid @enderror" id="trial_days" name="trial_days" value="{{ old('trial_days', config('services.subscription.trial_days', 7)) }}" min="0">
                            @error('trial_days')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Number of days for the free trial period. Default is {{ config('services.subscription.trial_days', 7) }} days. Set to 0 to skip trial.</div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_notification" name="send_notification" value="1" {{ old('send_notification') ? 'checked' : '' }}>
                                <label class="form-check-label" for="send_notification">
                                    Send notification to user
                                </label>
                            </div>
                            <div class="form-text">If checked, the user will receive an email notification about their new subscription.</div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Back to Subscriptions
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i> Create Subscription
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Pricing Information</h6>
                </div>
                <div class="card-body">
                    <div class="pricing-info mb-4">
                        <h5 class="mb-3">Base Tier</h5>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Monthly:</span>
                            <span class="fw-bold">$5.00</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Yearly:</span>
                            <span class="fw-bold">$50.00 <span class="text-success">(Save $10)</span></span>
                        </div>
                    </div>

                    <div class="pricing-info mb-4">
                        <h5 class="mb-3">Premium Tier</h5>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Monthly:</span>
                            <span class="fw-bold">$10.00</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Yearly:</span>
                            <span class="fw-bold">$100.00 <span class="text-success">(Save $20)</span></span>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> Creating a subscription will immediately create a subscription in Stripe and attach it to the selected user.
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Features Comparison</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Feature</th>
                                    <th class="text-center">Base</th>
                                    <th class="text-center">Premium</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Financial Bins</td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                </tr>
                                <tr>
                                    <td>Auto Categorization</td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                </tr>
                                <tr>
                                    <td>Sub-bin Levels</td>
                                    <td class="text-center">3</td>
                                    <td class="text-center">Unlimited</td>
                                </tr>
                                <tr>
                                    <td>Crypto Scanner</td>
                                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                </tr>
                                <tr>
                                    <td>Advanced AI Suggestions</td>
                                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Update price display when tier or billing cycle changes
        $('#subscription_tier, #billing_cycle').change(function() {
            updatePriceDisplay();
        });

        function updatePriceDisplay() {
            const tier = $('#subscription_tier').val();
            const cycle = $('#billing_cycle').val();

            // Highlight the selected pricing option
            $('.pricing-info').removeClass('bg-light');
            $(`.pricing-info:eq(${tier === 'premium' ? 1 : 0})`).addClass('bg-light');

            // Bold the selected billing cycle
            $('.pricing-info .d-flex').removeClass('fw-bold bg-light');
            $(`.pricing-info:eq(${tier === 'premium' ? 1 : 0}) .d-flex:eq(${cycle === 'yearly' ? 1 : 0})`).addClass('fw-bold bg-light');
        }

        // Initialize on page load
        updatePriceDisplay();
    });
</script>
@endpush
