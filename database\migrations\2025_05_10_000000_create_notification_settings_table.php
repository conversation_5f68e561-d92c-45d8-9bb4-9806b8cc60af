<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->boolean('email_login')->default(true);
            $table->boolean('email_profile_updates')->default(true);
            $table->boolean('email_password_changes')->default(true);
            $table->boolean('email_bin_operations')->default(true);
            $table->boolean('email_transaction_operations')->default(true);
            $table->boolean('email_subscription_updates')->default(true);
            $table->boolean('email_threshold_alerts')->default(true);
            $table->boolean('email_renewal_reminders')->default(true);
            $table->boolean('email_marketing')->default(false);
            $table->boolean('email_registration')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_settings');
    }
};
