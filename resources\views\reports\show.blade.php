@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-between align-items-center mb-4">
        <div class="col-auto">
            <h1 class="h3 mb-0">Report Details</h1>
            <p class="text-muted">View and manage your report</p>
        </div>
        <div class="col-auto">
            <a href="{{ route('reports.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">Report Information</h5>
                    <hr>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Name</label>
                        <p class="fw-bold">{{ $report->name }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Type</label>
                        <p>
                            <span class="badge bg-{{ $report->type == 'transaction' ? 'primary' : ($report->type == 'bin' ? 'success' : 'info') }}">
                                {{ ucfirst($report->type) }}
                            </span>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Format</label>
                        <p>
                            <span class="badge bg-secondary">{{ strtoupper($report->format) }}</span>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Time Period</label>
                        <p>
                            <span class="badge bg-light text-dark">{{ ucfirst($report->period_type) }}</span>
                            @if($report->period_type == 'custom')
                                <br>
                                <small class="text-muted">
                                    {{ $report->start_date->format('M d, Y') }} - {{ $report->end_date->format('M d, Y') }}
                                </small>
                            @endif
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Status</label>
                        <p>
                            @if($report->status == 'completed')
                                <span class="badge bg-success">Completed</span>
                            @elseif($report->status == 'pending')
                                <span class="badge bg-warning">Pending</span>
                            @elseif($report->status == 'processing')
                                <span class="badge bg-info">Processing</span>
                            @else
                                <span class="badge bg-danger">Failed</span>
                            @endif
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Last Generated</label>
                        <p>
                            @if($report->last_generated_at)
                                {{ $report->last_generated_at->format('M d, Y H:i') }}
                            @else
                                <span class="text-muted">Never</span>
                            @endif
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Recurring</label>
                        <p>
                            @if($report->is_recurring)
                                <span class="badge bg-success">
                                    {{ ucfirst($report->schedule) }}
                                </span>
                                <br>
                                <small class="text-muted">
                                    Next: {{ $report->next_run_at ? $report->next_run_at->format('M d, Y') : 'N/A' }}
                                </small>
                            @else
                                <span class="badge bg-secondary">No</span>
                            @endif
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">Filters</h5>
                    <hr>
                    
                    @if($report->filters)
                        <div class="mb-3">
                            <label class="form-label text-muted">Bin</label>
                            <p>
                                @if(!empty($report->filters['bin_id']))
                                    {{ \App\Models\Bin::find($report->filters['bin_id'])->name ?? 'Unknown Bin' }}
                                @else
                                    <span class="text-muted">All Bins</span>
                                @endif
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Sub-Bin</label>
                            <p>
                                @if(!empty($report->filters['sub_bin_id']))
                                    {{ \App\Models\SubBin::find($report->filters['sub_bin_id'])->name ?? 'Unknown Sub-Bin' }}
                                @else
                                    <span class="text-muted">All Sub-Bins</span>
                                @endif
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Transaction Type</label>
                            <p>
                                @if(!empty($report->filters['transaction_type']))
                                    <span class="badge bg-{{ $report->filters['transaction_type'] == 'income' ? 'success' : 'danger' }}">
                                        {{ ucfirst($report->filters['transaction_type']) }}
                                    </span>
                                @else
                                    <span class="text-muted">All Types</span>
                                @endif
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Amount Range</label>
                            <p>
                                @if(!empty($report->filters['min_amount']) || !empty($report->filters['max_amount']))
                                    @if(!empty($report->filters['min_amount']) && !empty($report->filters['max_amount']))
                                        {{ number_format($report->filters['min_amount'], 2) }} - {{ number_format($report->filters['max_amount'], 2) }}
                                    @elseif(!empty($report->filters['min_amount']))
                                        Min: {{ number_format($report->filters['min_amount'], 2) }}
                                    @else
                                        Max: {{ number_format($report->filters['max_amount'], 2) }}
                                    @endif
                                @else
                                    <span class="text-muted">No Amount Filter</span>
                                @endif
                            </p>
                        </div>
                    @else
                        <p class="text-muted">No filters applied</p>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="card-title mb-0">Report Actions</h5>
                        
                        <div class="btn-group">
                            @if($report->status == 'completed')
                                <a href="{{ route('reports.download', $report) }}" class="btn btn-primary">
                                    <i class="fas fa-download me-2"></i>Download Report
                                </a>
                            @endif
                            
                            <form action="{{ route('reports.regenerate', $report) }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-outline-primary ms-2">
                                    <i class="fas fa-sync-alt me-2"></i>Regenerate Report
                                </button>
                            </form>
                            
                            <form action="{{ route('reports.destroy', $report) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger ms-2" onclick="return confirm('Are you sure you want to delete this report?')">
                                    <i class="fas fa-trash me-2"></i>Delete Report
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="alert alert-{{ $report->status == 'completed' ? 'success' : ($report->status == 'pending' || $report->status == 'processing' ? 'info' : 'danger') }}">
                        @if($report->status == 'completed')
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Report is ready!</strong> You can download it using the button above.
                        @elseif($report->status == 'pending')
                            <i class="fas fa-clock me-2"></i>
                            <strong>Report is pending.</strong> It will be generated shortly.
                        @elseif($report->status == 'processing')
                            <i class="fas fa-spinner fa-spin me-2"></i>
                            <strong>Report is being generated.</strong> This may take a few moments.
                        @else
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Report generation failed.</strong> Please try regenerating the report.
                        @endif
                    </div>
                    
                    @if($report->status == 'completed')
                        <div class="card bg-light mt-4">
                            <div class="card-body">
                                <h6 class="card-title">Report Preview</h6>
                                <p class="text-muted small">This is a preview of your report. Download the full report for complete details.</p>
                                
                                <div class="text-center py-5">
                                    <i class="fas fa-file-{{ $report->format == 'pdf' ? 'pdf' : 'csv' }} fa-4x text-primary mb-3"></i>
                                    <h5>{{ $report->name }}</h5>
                                    <p class="text-muted">{{ ucfirst($report->format) }} Report</p>
                                    <a href="{{ route('reports.download', $report) }}" class="btn btn-sm btn-primary mt-3">
                                        <i class="fas fa-download me-2"></i>Download
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
