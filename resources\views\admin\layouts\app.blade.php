<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name') }} - Admin Panel</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #32704e; /* Sea Green */
            --secondary-color: #3CB371; /* Medium Sea Green */
            --accent-color: #66CDAA; /* Medium Aquamarine */
            --light-color: #E8F5E9; /* Light Mint - slightly more subtle */
            --dark-color: #1D5E40; /* Dark Green */
            --sidebar-width: 220px;
            --sidebar-mini-width: 70px;
            --header-height: 60px;
            --body-bg: #ffffff;
            --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            --transition-speed: 0.25s;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--body-bg);
            color: #333;
            overflow-x: hidden;
        }

        /* Header Styles */
        .admin-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
            height: var(--header-height);
            position: fixed;
            top: 0;
            right: 0;
            left: 0;
            z-index: 1030;
            box-shadow: var(--card-shadow);
            display: flex;
            align-items: center;
            transition: all var(--transition-speed) ease;
            padding: 0;
        }

        .header-brand {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .header-brand:hover {
            color: var(--dark-color);
            text-decoration: none;
        }

        .header-search {
            max-width: 400px;
            position: relative;
        }

        .header-search .form-control {
            padding-left: 40px;
            border-radius: 20px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .header-search .form-control:focus {
            background-color: #fff;
            box-shadow: 0 0 0 0.25rem rgba(46, 139, 87, 0.25);
            border-color: var(--accent-color);
        }

        .header-search .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .user-dropdown .dropdown-toggle {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background: transparent;
            border: none;
            color: #495057;
            font-weight: 500;
        }

        .user-dropdown .dropdown-toggle:after {
            margin-left: 0.5rem;
        }

        .user-dropdown .dropdown-toggle:focus {
            box-shadow: none;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            color: #495057;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: var(--light-color);
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-divider {
            margin: 0.5rem 0;
            border-top: 1px solid #e9ecef;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--light-color);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 0.5rem;
        }

        /* Sidebar Styles */
        .admin-sidebar {
            position: fixed;
            top: var(--header-height);
            left: 0;
            bottom: 0;
            width: var(--sidebar-width);
            background-color: white;
            border-right: 1px solid rgba(0, 0, 0, 0.08);
            overflow-y: auto;
            transition: all var(--transition-speed) ease;
            z-index: 1020;
            box-shadow: var(--card-shadow);
            scrollbar-width: thin;
            scrollbar-color: var(--accent-color) transparent;
        }

        body.sidebar-mini .admin-sidebar {
            width: var(--sidebar-mini-width);
        }

        body.sidebar-mini .admin-content,
        body.sidebar-mini .admin-footer {
            margin-left: var(--sidebar-mini-width);
        }

        .admin-sidebar::-webkit-scrollbar {
            width: 3px;
        }

        .admin-sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .admin-sidebar::-webkit-scrollbar-thumb {
            background-color: var(--accent-color);
            border-radius: 10px;
        }

        .sidebar-menu {
            padding: 0.75rem 0;
        }

        .sidebar-menu .nav-link {
            color: #495057;
            padding: 0.6rem 1rem;
            font-weight: 500;
            border-left: 3px solid transparent;
            transition: all var(--transition-speed) ease;
            display: flex;
            align-items: center;
            margin: 0.1rem 0;
            border-radius: 0 4px 4px 0;
            font-size: 0.9rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-menu .nav-link:hover {
            background-color: var(--light-color);
            color: var(--primary-color);
            transform: translateX(3px);
        }

        .sidebar-menu .nav-link.active {
            color: var(--primary-color);
            background-color: var(--light-color);
            border-left-color: var(--primary-color);
            font-weight: 600;
        }

        .sidebar-menu .nav-link i {
            margin-right: 10px;
            width: 18px;
            text-align: center;
            font-size: 1rem;
            opacity: 0.8;
            transition: all var(--transition-speed) ease;
            flex-shrink: 0;
        }

        body.sidebar-mini .sidebar-menu .nav-link i {
            margin-right: 0;
            width: 100%;
            font-size: 1.1rem;
        }

        body.sidebar-mini .sidebar-menu .nav-link span,
        body.sidebar-mini .sidebar-heading,
        body.sidebar-mini .sidebar-profile-details {
            display: none;
        }

        .sidebar-menu .nav-link:hover i {
            transform: scale(1.1);
            opacity: 1;
        }

        .sidebar-menu .nav-link.active i {
            opacity: 1;
        }

        .sidebar-heading {
            padding: 0.8rem 1rem 0.3rem;
            font-size: 0.7rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #6c757d;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        .sidebar-toggle-mini {
            position: absolute;
            bottom: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            background-color: var(--light-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--primary-color);
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 10;
            transition: all var(--transition-speed) ease;
        }

        .sidebar-toggle-mini:hover {
            background-color: var(--primary-color);
            color: white;
            transform: rotate(180deg);
        }

        /* Content Area Styles */
        .admin-content {
            margin-left: var(--sidebar-width);
            margin-top: var(--header-height);
            padding: 1.5rem;
            min-height: calc(100vh - var(--header-height) - 50px);
            transition: all var(--transition-speed) ease;
            background-color: var(--body-bg);
        }

        /* Footer Styles */
        .admin-footer {
            margin-left: var(--sidebar-width);
            padding: 1rem 1.5rem;
            background-color: white;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            color: #6c757d;
            font-size: 0.8rem;
            height: 50px;
            transition: all var(--transition-speed) ease;
            position: sticky;
            bottom: 0;
            z-index: 1010;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            margin-bottom: 1.5rem;
            transition: all var(--transition-speed) ease;
            overflow: hidden;
            background-color: white;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-weight: 600;
            padding: 1.2rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-header .card-title {
            margin-bottom: 0;
            font-size: 1.1rem;
            color: var(--dark-color);
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-footer {
            background-color: white;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.5rem;
        }

        /* Stat Card Styles */
        .stat-card {
            border-radius: 12px;
            padding: 1.8rem;
            height: 100%;
            background-color: white;
            position: relative;
            overflow: hidden;
            transition: all var(--transition-speed) ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .stat-card .stat-icon {
            position: absolute;
            top: 1.2rem;
            right: 1.2rem;
            font-size: 2.8rem;
            opacity: 0.15;
            color: var(--primary-color);
            transition: all var(--transition-speed) ease;
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
            opacity: 0.2;
        }

        .stat-card .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            transition: all var(--transition-speed) ease;
        }

        .stat-card:hover .stat-value {
            transform: scale(1.05);
        }

        .stat-card .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }

        /* Button Styles */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--dark-color);
            border-color: var(--dark-color);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Table Styles */
        .table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            margin-bottom: 0;
        }

        .table thead th {
            background-color: white;
            color: var(--dark-color);
            font-weight: 600;
            border-bottom: 2px solid var(--primary-color);
            padding: 1rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            white-space: nowrap;
        }

        .table tbody td {
            padding: 1rem;
            vertical-align: middle;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr {
            transition: all var(--transition-speed) ease;
        }

        .table tbody tr:hover {
            background-color: var(--light-color);
            transform: scale(1.01);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
            z-index: 1;
            position: relative;
        }

        .table-responsive {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            background-color: white;
        }

        /* Badge Styles */
        .badge-primary {
            background-color: var(--primary-color);
        }

        .badge-success {
            background-color: #28a745;
        }

        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .badge-danger {
            background-color: #dc3545;
        }

        /* Loading Overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        body.loading {
            overflow: hidden;
        }

        /* Badges */
        .badge {
            font-weight: 500;
            padding: 0.35em 0.65em;
            border-radius: 0.25rem;
        }

        .badge-soft-primary {
            background-color: rgba(46, 139, 87, 0.1);
            color: var(--primary-color);
        }

        .badge-soft-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .badge-soft-warning {
            background-color: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .badge-soft-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .badge-soft-info {
            background-color: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
        }

        /* New Badge Styles */
        .bg-success-soft {
            background-color: rgba(40, 167, 69, 0.1) !important;
            color: #28a745 !important;
        }

        .bg-danger-soft {
            background-color: rgba(220, 53, 69, 0.1) !important;
            color: #dc3545 !important;
        }

        .bg-warning-soft {
            background-color: rgba(255, 193, 7, 0.1) !important;
            color: #ffc107 !important;
        }

        .bg-info-soft {
            background-color: rgba(23, 162, 184, 0.1) !important;
            color: #17a2b8 !important;
        }

        .bg-primary-soft {
            background-color: rgba(46, 139, 87, 0.1) !important;
            color: var(--primary-color) !important;
        }

        /* Pagination */
        .pagination {
            margin-bottom: 0;
            padding: 1rem 0;
            display: flex;
            justify-content: center;
        }

        .pagination .page-item .page-link {
            color: var(--primary-color);
            padding: 0.6rem 1rem;
            border-radius: 8px;
            margin: 0 0.3rem;
            border: none;
            font-weight: 500;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(46, 139, 87, 0.2);
        }

        .pagination .page-item .page-link:hover {
            background-color: var(--light-color);
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .pagination .page-item.disabled .page-link {
            color: #adb5bd;
            background-color: #f8f9fa;
            box-shadow: none;
        }

        /* Breadcrumb */
        .breadcrumb {
            background-color: transparent;
            padding: 0;
            margin-bottom: 1.5rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            font-size: 1.2rem;
            line-height: 1;
            vertical-align: middle;
        }

        .breadcrumb-item a {
            color: var(--primary-color);
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                width: var(--sidebar-width) !important;
            }

            .admin-sidebar.show {
                transform: translateX(0);
            }

            .admin-content, .admin-footer {
                margin-left: 0 !important;
            }

            .sidebar-toggle {
                display: block !important;
            }

            .user-dropdown .dropdown-toggle span {
                display: none;
            }

            body.sidebar-mini .sidebar-menu .nav-link span {
                display: inline;
            }

            body.sidebar-mini .sidebar-heading {
                display: block;
            }

            .sidebar-toggle-mini {
                display: none;
            }
        }

        /* Print Styles */
        @media print {
            .admin-header, .admin-sidebar, .admin-footer {
                display: none !important;
            }

            .admin-content {
                margin: 0 !important;
                padding: 0 !important;
            }

            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Header -->
    <header class="admin-header">
        <div class="container-fluid">
            <div class="row align-items-center" style="height: var(--header-height);">
                <div class="col-auto">
                    <div class="d-flex align-items-center">
                        <button class="btn sidebar-toggle d-md-none me-2" type="button">
                            <i class="fas fa-bars"></i>
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="header-brand text-decoration-none d-flex align-items-center">
                            @if(setting('logo'))
                                <img src="{{ asset('settings/logo.png') }}"  alt="" height="40" class="me-2">
                                {{-- <span>PocketWatch Admin</span> --}}
                            @else
                                <i class="fas fa-wallet me-2"></i>PocketWatch Admin
                            @endif
                        </a>
                    </div>
                </div>

                <div class="col d-none d-md-block">
                    <div class="header-search mx-auto">
                        <form action="{{ route('admin.search') }}" method="GET">
                            <div class="position-relative">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" class="form-control" placeholder="Search users, transactions..." name="query">
                            </div>
                        </form>
                    </div>
                </div>

                <div class="col-auto">
                    <div class="d-flex align-items-center">
                        <!-- Notifications -->
                        <div class="dropdown me-3">
                            <button class="btn position-relative" type="button" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    3
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="notificationsDropdown" style="width: 300px;">
                                <h6 class="dropdown-header">Notifications</h6>
                                <a class="dropdown-item d-flex align-items-center py-2" href="#">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-plus text-primary"></i>
                                    </div>
                                    <div class="ms-3">
                                        <p class="mb-0">New user registered</p>
                                        <small class="text-muted">5 minutes ago</small>
                                    </div>
                                </a>
                                <a class="dropdown-item d-flex align-items-center py-2" href="#">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-credit-card text-success"></i>
                                    </div>
                                    <div class="ms-3">
                                        <p class="mb-0">New subscription purchased</p>
                                        <small class="text-muted">15 minutes ago</small>
                                    </div>
                                </a>
                                <a class="dropdown-item d-flex align-items-center py-2" href="#">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                    </div>
                                    <div class="ms-3">
                                        <p class="mb-0">System alert: Low disk space</p>
                                        <small class="text-muted">1 hour ago</small>
                                    </div>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-center" href="#">View all notifications</a>
                            </div>
                        </div>

                        <!-- User Dropdown -->
                        <div class="dropdown user-dropdown">
                            <button class="btn dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                @if(Auth::user()->avatar)
                                    <img src="{{ Auth::user()->avatar }}" alt="{{ Auth::user()->name }}" class="rounded-circle me-1" width="36" height="36">
                                @else
                                    <div class="user-avatar">
                                        {{ substr(Auth::user()->name, 0, 1) }}
                                    </div>
                                @endif
                                <span class="d-none d-md-inline">{{ Auth::user()->name }}</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown">
                                <li class="dropdown-header">
                                    <div class="d-flex flex-column">
                                        <span class="fw-bold">{{ Auth::user()->name }}</span>
                                        <small class="text-muted">{{ Auth::user()->email }}</small>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.settings.index') }}"><i class="fas fa-cog me-2"></i>Settings</a></li>
                                <li><a class="dropdown-item" href="{{ route('home') }}"><i class="fas fa-home me-2"></i>Back to Site</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="{{ route('logout') }}"
                                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </a>
                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-menu">
            <!-- User Profile Section -->
            <div class="d-flex align-items-center p-2 mb-2">
                <div class="position-relative">
                    @if(Auth::user()->avatar)
                        <img src="{{ Auth::user()->avatar }}" alt="{{ Auth::user()->name }}" class="rounded-circle shadow-sm" width="40" height="40">
                    @else
                        <div class="user-avatar" style="width: 40px; height: 40px; font-size: 1rem;">
                            {{ substr(Auth::user()->name, 0, 1) }}
                        </div>
                    @endif
                    <span class="position-absolute bottom-0 end-0 bg-success rounded-circle p-1" style="width: 10px; height: 10px; border: 2px solid white;"></span>
                </div>
                <div class="ms-2 sidebar-profile-details">
                    <h6 class="mb-0 fw-bold fs-6">{{ Auth::user()->name }}</h6>
                    <p class="text-muted small mb-0" style="font-size: 0.7rem;">Administrator</p>
                </div>
            </div>

            <div class="sidebar-heading">Dashboard</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Overview">
                        <i class="fas fa-tachometer-alt"></i> <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.analytics') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Analytics">
                        <i class="fas fa-chart-line"></i> <span>Analytics</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-heading">User Management</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}" href="{{ route('admin.users.index') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="All Users">
                        <i class="fas fa-users"></i> <span>All Users</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.users.create') ? 'active' : '' }}" href="{{ route('admin.users.create') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Add New User">
                        <i class="fas fa-user-plus"></i> <span>Add New User</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-heading">Financial</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.transactions.*') ? 'active' : '' }}" href="{{ route('admin.transactions.index') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Transactions">
                        <i class="fas fa-exchange-alt"></i> <span>Transactions</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.subscriptions.index') ? 'active' : '' }}" href="{{ route('admin.subscriptions.index') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Subscriptions">
                        <i class="fas fa-crown"></i> <span>Subscriptions</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.subscriptions.packages') ? 'active' : '' }}" href="{{ route('admin.subscriptions.packages') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Subscription Packages">
                        <i class="fas fa-tags"></i> <span>Packages</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.subscriptions.dashboard') ? 'active' : '' }}" href="{{ route('admin.subscriptions.dashboard') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Subscription Dashboard">
                        <i class="fas fa-chart-pie"></i> <span>Sub Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.bins.*') ? 'active' : '' }}" href="{{ route('admin.bins.index') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Financial Bins">
                        <i class="fas fa-box"></i> <span>Financial Bins</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.crypto-wallets.*') ? 'active' : '' }}" href="{{ route('admin.crypto-wallets.index') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Crypto Wallets">
                        <i class="fab fa-bitcoin"></i> <span>Crypto Wallets</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-heading">System</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}" href="{{ route('admin.settings.index') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Settings">
                        <i class="fas fa-cog"></i> <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings.email') ? 'active' : '' }}" href="{{ route('admin.settings.index') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Email Settings">
                        <i class="fas fa-envelope"></i> <span>Email Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings.payment') ? 'active' : '' }}" href="{{ route('admin.settings.index') }}" data-bs-toggle="tooltip" data-bs-placement="right" title="Payment Settings">
                        <i class="fas fa-credit-card"></i> <span>Payment Settings</span>
                    </a>
                </li>
            </ul>

            <div class="mt-3 px-2 sidebar-profile-details">
                <div class="card bg-light border-0 shadow-sm">
                    <div class="card-body p-2">
                        <h6 class="mb-1 fw-bold fs-6"><i class="fas fa-question-circle text-primary me-1"></i> Need Help?</h6>
                        <p class="small mb-2" style="font-size: 0.7rem;">Check our documentation for help.</p>
                        <a href="#" class="btn btn-sm btn-primary w-100 py-1">View Docs</a>
                    </div>
                </div>
            </div>

            <!-- Sidebar Toggle Button -->
            <button class="sidebar-toggle-mini" id="sidebarToggleMini" title="Toggle Sidebar Size">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>
    </aside>

    <!-- Content -->
    <main class="admin-content">
        <div class="container-fluid">
            <!-- Breadcrumb -->
            {{-- <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb bg-transparent p-0">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}" class="text-decoration-none"><i class="fas fa-home"></i></a></li>
                    @yield('breadcrumbs')
                    <li class="breadcrumb-item active" aria-current="page">@yield('title', 'Dashboard')</li>
                </ol>
            </nav> --}}

            <!-- Page Title -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                    @yield('actions')
            </div>

            <!-- Alerts -->
            {{-- @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show shadow-sm border-0" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-2"></i>
                        <div>{{ session('success') }}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show shadow-sm border-0" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <div>{{ session('error') }}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('warning'))
                <div class="alert alert-warning alert-dismissible fade show shadow-sm border-0" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>{{ session('warning') }}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif --}}

            @if (session('info'))
                <div class="alert alert-info alert-dismissible fade show shadow-sm border-0" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>{{ session('info') }}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <!-- Main Content -->
            @yield('content')
        </div>
    </main>

    <!-- Footer -->
    <footer class="admin-footer">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    &copy; {{ date('Y') }} <span class="fw-bold text-primary">{{ config('app.name') }}</span>. All rights reserved.
                </div>
                <div class="d-flex align-items-center">
                    <a href="#" class="text-muted me-3 small text-decoration-none">Privacy Policy</a>
                    <a href="#" class="text-muted me-3 small text-decoration-none">Terms of Service</a>
                    <span class="badge bg-light text-dark">v1.0.0</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Sidebar toggle for mobile
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const sidebar = document.querySelector('.admin-sidebar');
            const content = document.querySelector('.admin-content');
            const footer = document.querySelector('.admin-footer');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 768 && sidebar.classList.contains('show') &&
                    !sidebar.contains(event.target) && !sidebarToggle.contains(event.target)) {
                    sidebar.classList.remove('show');
                }
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });

            // Mini sidebar toggle
            const sidebarToggleMini = document.getElementById('sidebarToggleMini');
            if (sidebarToggleMini) {
                // Check if mini sidebar preference is stored
                const isMiniSidebar = localStorage.getItem('miniSidebar') === 'true';

                // Apply mini sidebar if preference exists
                if (isMiniSidebar) {
                    document.body.classList.add('sidebar-mini');
                    sidebarToggleMini.querySelector('i').classList.remove('fa-chevron-left');
                    sidebarToggleMini.querySelector('i').classList.add('fa-chevron-right');
                }

                // Toggle mini sidebar on click
                sidebarToggleMini.addEventListener('click', function() {
                    document.body.classList.toggle('sidebar-mini');
                    const isMini = document.body.classList.contains('sidebar-mini');

                    // Update icon
                    if (isMini) {
                        this.querySelector('i').classList.remove('fa-chevron-left');
                        this.querySelector('i').classList.add('fa-chevron-right');
                    } else {
                        this.querySelector('i').classList.remove('fa-chevron-right');
                        this.querySelector('i').classList.add('fa-chevron-left');
                    }

                    // Save preference to localStorage
                    localStorage.setItem('miniSidebar', isMini);

                    // Reinitialize tooltips for mini sidebar
                    if (isMini) {
                        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                        tooltipTriggerList.forEach(tooltipTriggerEl => {
                            const tooltip = bootstrap.Tooltip.getInstance(tooltipTriggerEl);
                            if (tooltip) {
                                tooltip.dispose();
                            }
                            new bootstrap.Tooltip(tooltipTriggerEl);
                        });
                    }
                });
            }

            // Add active class to parent menu items when child is active
            const activeLinks = document.querySelectorAll('.sidebar-menu .nav-link.active');
            activeLinks.forEach(function(link) {
                const parentDropdown = link.closest('.nav-item.dropdown');
                if (parentDropdown) {
                    parentDropdown.querySelector('.dropdown-toggle').classList.add('active');
                }
            });

            // Confirm delete actions
            const deleteButtons = document.querySelectorAll('[data-confirm]');
            deleteButtons.forEach(function(button) {
                button.addEventListener('click', function(e) {
                    if (!confirm(this.dataset.confirm || 'Are you sure you want to delete this item?')) {
                        e.preventDefault();
                    }
                });
            });
        });

        // Function to show loading spinner
        function showLoading() {
            const loadingEl = document.createElement('div');
            loadingEl.classList.add('loading-overlay');
            loadingEl.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            `;
            document.body.appendChild(loadingEl);
            document.body.classList.add('loading');
        }

        // Function to hide loading spinner
        function hideLoading() {
            const loadingEl = document.querySelector('.loading-overlay');
            if (loadingEl) {
                loadingEl.remove();
                document.body.classList.remove('loading');
            }
        }
    </script>

    @stack('scripts')
</body>
</html>
