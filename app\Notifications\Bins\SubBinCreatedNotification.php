<?php

namespace App\Notifications\Bins;

use App\Models\SubBin;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class SubBinCreatedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The sub-bin instance.
     *
     * @var \App\Models\SubBin
     */
    protected $subBin;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\SubBin  $subBin
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_bin_operations';

    public function __construct(SubBin $subBin)
    {
        $this->subBin = $subBin;

        $this->subject = 'New Sub-Bin Created';
        $this->title = 'New Sub-Bin Created';
        $this->content = 'You have successfully created a new sub-bin in your PocketWatch account. 
                         Sub-bins help you further organize your finances within a main financial bin.';
        
        $this->detailsTitle = 'Sub-Bin Details';
        $this->details = [
            'Name' => $this->subBin->name,
            'Description' => $this->subBin->description ?? 'No description',
            'Parent Bin' => $this->subBin->bin->name,
            'Minimum Threshold' => $this->formatCurrency($this->subBin->threshold_min, $this->subBin->currency),
        ];
        
        if ($this->subBin->threshold_max) {
            $this->details['Maximum Threshold'] = $this->formatCurrency($this->subBin->threshold_max, $this->subBin->currency);
        }
        
        $this->actionText = 'View Sub-Bin';
        $this->actionUrl = url('/bins/' . $this->subBin->bin_id . '/sub-bins/' . $this->subBin->id);
        
        $this->closing = 'Start adding transactions to make the most of your financial organization.';
        $this->signature = 'The PocketWatch Team';
    }
    
    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);
        
        return $symbol . number_format($value, 2);
    }
    
    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];
        
        return $symbols[$currency] ?? $currency;
    }
}
