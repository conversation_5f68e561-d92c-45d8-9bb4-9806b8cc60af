<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class CalculateCumulativeBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'balance:calculate {--user-id= : Calculate for specific user ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate cumulative balance for all users or a specific user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Starting cumulative balance calculation...');

        $userId = $this->option('user-id');

        if ($userId) {
            // Calculate for specific user
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found.");
                return 1;
            }

            $this->calculateForUser($user);
        } else {
            // Calculate for all users
            $users = User::all();
            $this->info("Found {$users->count()} users to process.");

            $progressBar = $this->output->createProgressBar($users->count());
            $progressBar->start();

            foreach ($users as $user) {
                $this->calculateForUser($user);
                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine();
        }

        $this->info('✅ Cumulative balance calculation completed!');
        return 0;
    }

    /**
     * Calculate cumulative balance for a specific user.
     *
     * @param User $user
     * @return void
     */
    private function calculateForUser(User $user)
    {
        $oldBalance = $user->cumulative_balance ?? 0;
        $newBalance = $user->calculateCumulativeBalance();

        if ($this->option('user-id')) {
            $this->info("User: {$user->name} (ID: {$user->id})");
            $this->info("  Old Balance: {$oldBalance}");
            $this->info("  New Balance: {$newBalance}");
            $this->info("  Difference: " . ($newBalance - $oldBalance));
        }
    }
}
