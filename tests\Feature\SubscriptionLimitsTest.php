<?php

namespace Tests\Feature;

use App\Models\Bin;
use App\Models\SubBin;
use App\Models\User;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SubscriptionLimitsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test that base tier users can only create 3 bins.
     *
     * @return void
     */
    public function test_base_tier_user_can_only_create_three_bins()
    {
        // Create a base tier user
        $user = User::factory()->create([
            'subscription_tier' => 'base',
        ]);

        // Create a token for the user
        $token = $user->createToken('test-token')->plainTextToken;

        // Create 3 bins (should succeed)
        for ($i = 0; $i < 3; $i++) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson('/api/bins', [
                'name' => 'Test Bin ' . ($i + 1),
                'description' => 'Test Description',
                'threshold_min' => 100,
                'threshold_max' => 1000,
                'currency' => 'USD',
            ]);

            $response->assertStatus(201);
        }

        // Try to create a 4th bin (should fail)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/bins', [
            'name' => 'Test Bin 4',
            'description' => 'Test Description',
            'threshold_min' => 100,
            'threshold_max' => 1000,
            'currency' => 'USD',
        ]);

        $response->assertStatus(403)
            ->assertJsonPath('error', 'subscription_limit_reached')
            ->assertJsonPath('remaining_bins', 0)
            ->assertJsonPath('max_bins', SubscriptionService::MAX_BINS_BASE_TIER);
    }

    /**
     * Test that premium tier users can create unlimited bins.
     *
     * @return void
     */
    public function test_premium_tier_user_can_create_unlimited_bins()
    {
        // Create a premium tier user
        $user = User::factory()->create([
            'subscription_tier' => 'premium',
        ]);

        // Create a token for the user
        $token = $user->createToken('test-token')->plainTextToken;

        // Create 5 bins (should succeed for premium user)
        for ($i = 0; $i < 5; $i++) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson('/api/bins', [
                'name' => 'Test Bin ' . ($i + 1),
                'description' => 'Test Description',
                'threshold_min' => 100,
                'threshold_max' => 1000,
                'currency' => 'USD',
            ]);

            $response->assertStatus(201);
        }
    }

    /**
     * Test that base tier users can only create 3 sub-bins per bin.
     *
     * @return void
     */
    public function test_base_tier_user_can_only_create_three_sub_bins_per_bin()
    {
        // Create a base tier user
        $user = User::factory()->create([
            'subscription_tier' => 'base',
        ]);

        // Create a bin for the user
        $bin = Bin::create([
            'user_id' => $user->id,
            'name' => 'Test Bin',
            'description' => 'Test Description',
            'threshold_min' => 100,
            'threshold_max' => 1000,
            'current_amount' => 0,
            'currency' => 'USD',
        ]);

        // Create a token for the user
        $token = $user->createToken('test-token')->plainTextToken;

        // Create 3 sub-bins (should succeed)
        for ($i = 0; $i < 3; $i++) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson("/api/bins/{$bin->id}/sub-bins", [
                'name' => 'Test Sub-Bin ' . ($i + 1),
                'description' => 'Test Description',
                'threshold_min' => 50,
                'threshold_max' => 500,
                'currency' => 'USD',
            ]);

            $response->assertStatus(201);
        }

        // Try to create a 4th sub-bin (should fail)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson("/api/bins/{$bin->id}/sub-bins", [
            'name' => 'Test Sub-Bin 4',
            'description' => 'Test Description',
            'threshold_min' => 50,
            'threshold_max' => 500,
            'currency' => 'USD',
        ]);

        $response->assertStatus(403)
            ->assertJsonPath('error', 'subscription_limit_reached')
            ->assertJsonPath('remaining_sub_bins', 0)
            ->assertJsonPath('max_sub_bins', SubscriptionService::MAX_SUB_BINS_PER_BIN_BASE_TIER);
    }
}
