<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch API - Simple Stripe Integration</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #667eea;
            --primary-dark: #5a6fd8;
            --secondary: #764ba2;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
            --info: #17a2b8;
            --light: #f8f9fa;
            --dark: #343a40;
            --white: #ffffff;
            --border: #e9ecef;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--white);
            border-radius: 16px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .version-badge {
            display: inline-block;
            background: var(--success);
            color: var(--white);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .api-section {
            background: var(--white);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
        }

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .flow-info {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid var(--success);
        }

        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .flow-step {
            background: rgba(255, 255, 255, 0.8);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .step-number {
            background: var(--success);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto 10px;
        }

        .endpoint-card {
            border: 2px solid var(--border);
            border-radius: 12px;
            margin-bottom: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .endpoint-card:hover {
            border-color: var(--primary);
            box-shadow: var(--shadow);
        }

        .endpoint-header {
            background: var(--light);
            padding: 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 15px;
        }

        .endpoint-method {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .method-badge {
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .method-get { background: var(--success); color: var(--white); }
        .method-post { background: var(--info); color: var(--white); }

        .endpoint-url {
            font-family: 'JetBrains Mono', monospace;
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--dark);
        }

        .auth-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .auth-required { background: #ffe6e6; color: var(--danger); }
        .auth-none { background: #d4edda; color: var(--success); }

        .endpoint-body {
            padding: 25px;
        }

        .endpoint-description {
            font-size: 1.1rem;
            margin-bottom: 20px;
            color: #555;
        }

        .params-section {
            margin-bottom: 25px;
        }

        .params-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .param-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            border-left: 4px solid var(--primary);
        }

        .param-name {
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .param-type {
            display: inline-block;
            background: var(--primary);
            color: var(--white);
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-left: 10px;
        }

        .param-required {
            background: var(--danger);
            color: var(--white);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7rem;
            margin-left: 8px;
        }

        .param-description {
            color: #666;
            margin-top: 8px;
        }

        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            margin: 15px 0;
        }

        .copy-button {
            position: relative;
            float: right;
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            margin-bottom: 10px;
        }

        .status-codes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .status-code {
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }

        .status-200 { background: #d4edda; color: var(--success); }
        .status-201 { background: #d4edda; color: var(--success); }
        .status-400 { background: #f8d7da; color: var(--danger); }
        .status-401 { background: #f8d7da; color: var(--danger); }
        .status-404 { background: #f8d7da; color: var(--danger); }
        .status-422 { background: #fff3cd; color: #856404; }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .comparison-card {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .old-flow {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
        }

        .new-flow {
            background: #d4edda;
            border: 2px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-wallet"></i> PocketWatch API</h1>
            <p>Simple Stripe Integration Documentation</p>
            <span class="version-badge">v3.1 - Simple Blade-Based Stripe</span>
        </div>

        <!-- Flow Comparison -->
        <div class="api-section">
            <h2 class="section-title">
                <i class="fas fa-exchange-alt"></i> Simple vs Complex Flow
            </h2>

            <div class="comparison-grid">
                <div class="comparison-card old-flow">
                    <h3>❌ Old Complex Flow</h3>
                    <ol style="text-align: left; margin: 15px 0;">
                        <li>Complex Stripe API calls</li>
                        <li>Session management</li>
                        <li>Webhook handling</li>
                        <li>Multiple controllers</li>
                        <li>Error-prone integration</li>
                    </ol>
                </div>
                <div class="comparison-card new-flow">
                    <h3>✅ New Simple Flow</h3>
                    <ol style="text-align: left; margin: 15px 0;">
                        <li>Get packages via API</li>
                        <li>Generate Stripe URL</li>
                        <li>Redirect to Blade view</li>
                        <li>Direct Stripe payment</li>
                        <li>Trial activation</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Simple Flow Info -->
        <div class="api-section">
            <div class="flow-info">
                <h3 style="color: var(--success); margin-bottom: 15px;">🚀 Direct Stripe Checkout → Profile API</h3>
                <p style="margin-bottom: 15px; color: #155724;">
                    <strong>Perfect Integration:</strong> Stripe checkout redirects to profile API with payment success data and new authentication token!
                </p>
                <div class="flow-steps">
                    <div class="flow-step">
                        <div class="step-number">1</div>
                        <strong>Get Packages</strong><br>
                        API returns dynamic packages
                    </div>
                    <div class="flow-step">
                        <div class="step-number">2</div>
                        <strong>Create Session</strong><br>
                        Generate Stripe Checkout session
                    </div>
                    <div class="flow-step">
                        <div class="step-number">3</div>
                        <strong>Stripe Checkout</strong><br>
                        Professional payment page
                    </div>
                    <div class="flow-step">
                        <div class="step-number">4</div>
                        <strong>Profile API</strong><br>
                        Returns profile + payment success
                    </div>
                </div>
            </div>
        </div>

        <!-- API Endpoints -->
        <div class="api-section">
            <h2 class="section-title">
                <i class="fas fa-code"></i> Simple Stripe API Endpoints
            </h2>

            <!-- Get Packages -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <div class="endpoint-method">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api/packages-simple</span>
                    </div>
                    <span class="auth-badge auth-required">Auth Required</span>
                </div>
                <div class="endpoint-body">
                    <div class="endpoint-description">
                        Get all available packages with dynamic pricing and features.
                    </div>

                    <div class="params-section">
                        <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                        <button class="copy-button" onclick="copyCode('get-packages-simple')">Copy</button>
                        <pre class="code-block" id="get-packages-simple">{
    "success": true,
    "data": {
        "packages": [
            {
                "id": "base_monthly",
                "name": "Base Monthly",
                "price": 9.99,
                "features": ["Financial bins", "Transaction categorization", "Basic insights"]
            },
            {
                "id": "premium_monthly",
                "name": "Premium Monthly",
                "price": 19.99,
                "features": ["All Base features", "Unlimited sub-bins", "Crypto integration"]
            }
        ],
        "user": {"id": 1, "name": "John Doe", "email": "<EMAIL>"}
    }
}</pre>
                    </div>
                </div>
            </div>

            <!-- Generate Stripe URL -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <div class="endpoint-method">
                        <span class="method-badge method-post">POST</span>
                        <span class="endpoint-url">/api/packages-simple/stripe-url</span>
                    </div>
                    <span class="auth-badge auth-required">Auth Required</span>
                </div>
                <div class="endpoint-body">
                    <div class="endpoint-description">
                        Create Stripe Checkout session and return professional payment URL.
                    </div>

                    <div class="params-section">
                        <h4 class="params-title"><i class="fas fa-cog"></i> Request</h4>
                        <div class="param-item">
                            <div class="param-name">package_id <span class="param-type">string</span> <span class="param-required">required</span></div>
                            <div class="param-description">Package ID (base_monthly, premium_monthly)</div>
                        </div>
                    </div>

                    <div class="params-section">
                        <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                        <button class="copy-button" onclick="copyCode('generate-stripe-url')">Copy</button>
                        <pre class="code-block" id="generate-stripe-url">{
    "success": true,
    "data": {
        "stripe_checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
        "session_id": "cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
        "package": {
            "id": "base_monthly",
            "name": "Base Monthly",
            "price": 9.99
        },
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>"
        },
        "payment_details": {
            "amount": 9.99,
            "currency": "USD",
            "description": "Base Monthly - Monthly Subscription",
            "trial_days": 7
        }
    },
    "message": "Redirect user to stripe_checkout_url for payment"
}</pre>
                    </div>
                </div>
            </div>

            <!-- Payment Success Response -->
            <div class="endpoint-card">
                <div class="endpoint-header">
                    <div class="endpoint-method">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api/payment-simple-success</span>
                    </div>
                    <span class="auth-badge auth-none">No Auth Required</span>
                </div>
                <div class="endpoint-body">
                    <div class="endpoint-description">
                        Handle successful payment and return profile data with payment success information and new authentication token.
                    </div>

                    <div class="params-section">
                        <h4 class="params-title"><i class="fas fa-reply"></i> Response</h4>
                        <button class="copy-button" onclick="copyCode('payment-success-profile')">Copy</button>
                        <pre class="code-block" id="payment-success-profile">{
    "success": true,
    "payment_success": true,
    "message": "Payment successful! Your 7-day trial has started.",
    "user": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "John Doe",
        "email": "<EMAIL>",
        "subscription_tier": "trial",
        "trial_started_at": "2024-12-19T10:00:00Z",
        "trial_expired": false,
        "trial_days_remaining": 7
    },
    "token": "2|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "subscription": {
        "id": 1,
        "name": "Base Monthly",
        "tier": "trial",
        "price": 9.99,
        "trial_ends_at": "2024-12-26T10:00:00Z"
    },
    "trial_info": {
        "trial_started": true,
        "trial_days": 7,
        "trial_ends_at": "2024-12-26T10:00:00Z",
        "plan": "base_monthly"
    }
}</pre>

                        <div class="status-codes">
                            <div class="status-code status-200">200 - Success</div>
                            <div class="status-code status-400">400 - Missing Parameters</div>
                            <div class="status-code status-404">404 - User/Package Not Found</div>
                            <div class="status-code status-500">500 - Processing Error</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing Guide -->
        <div class="api-section">
            <h2 class="section-title">
                <i class="fas fa-vial"></i> Testing Guide
            </h2>

            <div style="background: #e8f5e8; padding: 20px; border-radius: 12px; margin-bottom: 25px; border-left: 4px solid #28a745;">
                <h3 style="color: #28a745; margin-bottom: 15px;">🧪 Complete Testing Flow</h3>
                <ol style="color: #155724; margin: 15px 0; padding-left: 20px;">
                    <li><strong>Open Test Page:</strong> <code>simple_stripe_test.html</code></li>
                    <li><strong>Login:</strong> Use <code><EMAIL></code> / <code>password123</code></li>
                    <li><strong>Select Package:</strong> Choose Base ($9.99) or Premium ($19.99)</li>
                    <li><strong>Create Session:</strong> API creates Stripe Checkout session</li>
                    <li><strong>Stripe Checkout:</strong> Professional Stripe payment page with trial</li>
                    <li><strong>Pay with Stripe:</strong> Use test card <code>************** 4242</code></li>
                    <li><strong>Profile API Response:</strong> Returns profile data with payment success and new token</li>
                </ol>
            </div>

            <div class="params-section">
                <h4 class="params-title"><i class="fas fa-code"></i> Example API Calls</h4>
                <button class="copy-button" onclick="copyCode('api-examples')">Copy</button>
                <pre class="code-block" id="api-examples"># 1. Get packages
curl -X GET "http://127.0.0.1:8000/api/packages-simple" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. Generate Stripe URL
curl -X POST "http://127.0.0.1:8000/api/packages-simple/stripe-url" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"package_id": "base_monthly"}'</pre>
            </div>
        </div>

        <!-- Footer -->
        <div class="api-section" style="text-align: center; background: linear-gradient(135deg, var(--primary), var(--secondary)); color: white;">
            <h3>🎯 PocketWatch API v3.1</h3>
            <p>Simple Stripe Integration Documentation | Last updated: December 19, 2024</p>
            <p>Features: Blade-Based Stripe + Google OAuth + Hierarchical Sub-Bins</p>
        </div>
    </div>

    <script>
        // Copy code functionality
        function copyCode(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;

            navigator.clipboard.writeText(text).then(() => {
                const button = element.previousElementSibling;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = 'var(--success)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'var(--primary)';
                }, 2000);
            });
        }

        console.log('🎯 Simple Stripe API Documentation loaded!');
        console.log('📋 Flow: Get Packages → Create Session → Stripe Checkout → Profile API Response');
    </script>
</body>
</html>