<?php

namespace App\Jobs;

use App\Models\CryptoWallet;
use App\Services\BinnitAiService;
use App\Services\MoralisService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncCryptoWalletData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The crypto wallet instance.
     *
     * @var \App\Models\CryptoWallet
     */
    protected $wallet;

    /**
     * Create a new job instance.
     *
     * @param  \App\Models\CryptoWallet  $wallet
     * @return void
     */
    public function __construct(CryptoWallet $wallet)
    {
        $this->wallet = $wallet;
    }

    /**
     * Execute the job.
     *
     * @param  \App\Services\MoralisService  $moralisService
     * @param  \App\Services\BinnitAiService  $binnitAiService
     * @return void
     */
    public function handle(MoralisService $moralisService, BinnitAiService $binnitAiService)
    {
        try {
            Log::info('Starting crypto wallet sync', [
                'wallet_id' => $this->wallet->id,
                'wallet_address' => $this->wallet->wallet_address,
            ]);

            // Get token balances from Moralis API
            $tokenBalances = $moralisService->getTokenBalances(
                $this->wallet->wallet_address,
                $this->wallet->blockchain_network
            );

            // Update wallet with fetched data
            if ($tokenBalances) {
                $this->wallet->assets = $tokenBalances;
                $this->wallet->last_synced_at = now();
                $this->wallet->save();

                Log::info('Crypto wallet sync completed', [
                    'wallet_id' => $this->wallet->id,
                ]);

                // Generate AI advice
                $this->generateAiAdvice($binnitAiService);
            } else {
                Log::warning('No token balances found for wallet', [
                    'wallet_id' => $this->wallet->id,
                    'wallet_address' => $this->wallet->wallet_address,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error syncing crypto wallet data', [
                'wallet_id' => $this->wallet->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate AI advice for the wallet.
     *
     * @param  \App\Services\BinnitAiService  $binnitAiService
     * @return void
     */
    protected function generateAiAdvice(BinnitAiService $binnitAiService): void
    {
        try {
            // Get advice from Binnit AI
            $advice = $binnitAiService->getInvestmentAdvice($this->wallet);

            // Update wallet with AI advice
            if ($advice) {
                $this->wallet->ai_advice = $advice;
                $this->wallet->save();

                Log::info('AI advice generated for crypto wallet', [
                    'wallet_id' => $this->wallet->id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error generating AI advice for crypto wallet', [
                'wallet_id' => $this->wallet->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
