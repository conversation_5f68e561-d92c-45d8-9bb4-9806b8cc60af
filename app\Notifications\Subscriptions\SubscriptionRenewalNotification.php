<?php

namespace App\Notifications\Subscriptions;

use App\Models\Subscription;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class SubscriptionRenewalNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The subscription instance.
     *
     * @var \App\Models\Subscription
     */
    protected $subscription;

    /**
     * The renewal date.
     *
     * @var \Illuminate\Support\Carbon
     */
    protected $renewalDate;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Subscription  $subscription
     * @param  \Illuminate\Support\Carbon  $renewalDate
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_renewal_reminders';

    public function __construct(Subscription $subscription, $renewalDate)
    {
        $this->subscription = $subscription;
        $this->renewalDate = $renewalDate;

        $this->subject = 'Your PocketWatch Subscription Will Renew Soon';
        $this->title = 'Subscription Renewal Reminder';
        $this->content = 'This is a friendly reminder that your PocketWatch ' . ucfirst($this->subscription->subscription_tier) . 
                         ' subscription will automatically renew soon. No action is required if you wish to continue your subscription.';
        
        $this->detailsTitle = 'Renewal Details';
        $this->details = [
            'Plan' => ucfirst($this->subscription->subscription_tier) . ' Tier',
            'Billing Cycle' => ucfirst($this->subscription->billing_cycle),
            'Price' => $this->formatCurrency($this->subscription->price, $this->subscription->currency),
            'Renewal Date' => $this->renewalDate->format('F j, Y'),
        ];
        
        $this->actionText = 'Manage Subscription';
        $this->actionUrl = url('/subscriptions');
        
        $this->closing = 'If you wish to make changes to your subscription, please do so before the renewal date.';
        $this->signature = 'The PocketWatch Team';
    }
    
    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);
        
        return $symbol . number_format($value, 2) . '/' . ($this->subscription->billing_cycle === 'monthly' ? 'month' : 'year');
    }
    
    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];
        
        return $symbols[$currency] ?? $currency;
    }
}
