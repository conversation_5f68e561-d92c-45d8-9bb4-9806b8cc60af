<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SimpleStripeController extends Controller
{
    /**
     * Get packages for simple purchase flow.
     */
    public function getPackages(Request $request)
    {
        $user = $request->user();

        $packages = [
            [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'description' => 'Essential financial management tools',
                'price' => 5.00,
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Basic insights',
                    'Hierarchical sub-bins (3 levels)',
                    'Bank account linking'
                ],
                'limits' => [
                    'max_bins' => 3,
                    'max_sub_bins_per_bin' => 3,
                    'max_nesting_depth' => 3
                ],
                'popular' => false
            ],
            [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'description' => 'Advanced financial management with premium features',
                'price' => 10.00,
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'features' => [
                    'All Base features',
                    'Unlimited hierarchical sub-bins',
                    'Crypto wallet integration',
                    'Advanced AI insights',
                    'Priority notifications',
                    'Advanced reporting'
                ],
                'limits' => [
                    'max_bins' => 'unlimited',
                    'max_sub_bins_per_bin' => 'unlimited',
                    'max_nesting_depth' => 'unlimited'
                ],
                'popular' => true
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'packages' => $packages,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'current_tier' => $user->subscription_tier
                ],
                'stripe_redirect_url' => route('stripe.redirect'),
                'currency' => 'USD'
            ]
        ]);
    }

    /**
     * Generate Stripe Checkout Session and return URL.
     */
    public function generateStripeUrl(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_id' => 'required|string|in:base_monthly,premium_monthly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid package selected',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $packageId = $request->package_id;
        $packageDetails = $this->getPackageDetails($packageId);

        if (!$packageDetails) {
            return response()->json([
                'success' => false,
                'message' => 'Package not found'
            ], 404);
        }

        // Create Stripe Checkout Session
        try {
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

            $session = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => $packageDetails['name'],
                            'description' => 'Monthly subscription with 7-day free trial',
                            'images' => ['https://via.placeholder.com/300x200?text=PocketWatch'],
                        ],
                        'unit_amount' => $packageDetails['price'] * 100, // Convert to cents
                        'recurring' => [
                            'interval' => 'month',
                        ],
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => url('/api/payment-simple-success') . '?user_id=' . $user->id . '&plan_id=' . $packageId . '&price=' . $packageDetails['price'] . '&session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('stripe.cancel', [
                    'user_id' => $user->id,
                    'plan_id' => $packageId
                ]),
                'customer_email' => $user->email,
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $packageId,
                    'plan_name' => $packageDetails['name'],
                    'trial_days' => '7',
                ],
                'subscription_data' => [
                    'trial_period_days' => 7,
                    'metadata' => [
                        'user_id' => $user->id,
                        'plan_id' => $packageId,
                    ],
                ],
                'allow_promotion_codes' => true,
                'billing_address_collection' => 'auto',
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'stripe_checkout_url' => $session->url,
                    'session_id' => $session->id,
                    'package' => $packageDetails,
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email
                    ],
                    'payment_details' => [
                        'amount' => $packageDetails['price'],
                        'currency' => 'USD',
                        'description' => $packageDetails['name'] . ' - Monthly Subscription',
                        'trial_days' => 7
                    ]
                ],
                'message' => 'Redirect user to stripe_checkout_url for payment'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create Stripe session: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle successful payment callback and redirect to profile API.
     */
    public function handleSuccess(Request $request)
    {
        $userId = $request->query('user_id');
        $planId = $request->query('plan_id');
        $price = $request->query('price');
        $sessionId = $request->query('session_id');

        if (!$userId || !$planId || !$price) {
            return response()->json([
                'success' => false,
                'message' => 'Missing required parameters'
            ], 400);
        }

        $user = User::find($userId);
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }

        $packageDetails = $this->getPackageDetails($planId);
        if (!$packageDetails) {
            return response()->json([
                'success' => false,
                'message' => 'Package not found'
            ], 404);
        }

        try {
            // Create subscription record
            $subscription = Subscription::create([
                'user_id' => $userId,
                'name' => $packageDetails['name'],
                'stripe_id' => $sessionId ?: 'manual_' . time(),
                'stripe_status' => 'active',
                'stripe_price' => 'price_' . $planId,
                'subscription_tier' => $packageDetails['tier'],
                'billing_cycle' => $packageDetails['billing_cycle'],
                'price' => $price,
                'currency' => 'USD',
                'features' => $packageDetails['features'],
                'trial_ends_at' => now()->addDays(7),
            ]);

            // Update user
            $user->update([
                'subscription_tier' => 'trial',
                'trial_started_at' => now(),
            ]);

            // Create a token for the user to access their profile
            $token = $user->createToken('payment_success_token')->plainTextToken;

            // Return profile data with payment success information
            return response()->json([
                'success' => true,
                'payment_success' => true,
                'message' => 'Payment successful! Your 7-day trial has started.',
                'user' => [
                    'id' => $user->id,
                    'uuid' => $user->uuid,
                    'name' => $user->name,
                    'email' => $user->email,
                    'subscription_tier' => $user->subscription_tier,
                    'trial_started_at' => $user->trial_started_at->toISOString(),
                    'trial_expired' => false,
                    'trial_days_remaining' => 7
                ],
                'token' => $token,
                'token_type' => 'Bearer',
                'subscription' => [
                    'id' => $subscription->id,
                    'name' => $subscription->name,
                    'tier' => $subscription->subscription_tier,
                    'price' => $subscription->price,
                    'trial_ends_at' => $subscription->trial_ends_at->toISOString()
                ],
                'trial_info' => [
                    'trial_started' => true,
                    'trial_days' => 7,
                    'trial_ends_at' => $subscription->trial_ends_at->toISOString(),
                    'plan' => $planId
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle cancelled payment.
     */
    public function handleCancel(Request $request)
    {
        $userId = $request->query('user_id');
        $planId = $request->query('plan_id');

        return response()->json([
            'success' => false,
            'message' => 'Payment was cancelled by user',
            'data' => [
                'user_id' => $userId,
                'plan_id' => $planId,
                'cancelled_at' => now()->toISOString(),
                'next_steps' => [
                    'view_packages' => '/api/packages-simple',
                    'try_again' => 'Select a package to try payment again'
                ]
            ]
        ]);
    }

    /**
     * Get package details by ID.
     */
    private function getPackageDetails($packageId)
    {
        $packages = [
            'base_monthly' => [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'tier' => 'base',
                'billing_cycle' => 'monthly',
                'price' => 5.00,
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Basic insights',
                    'Hierarchical sub-bins (3 levels)'
                ]
            ],
            'premium_monthly' => [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'tier' => 'premium',
                'billing_cycle' => 'monthly',
                'price' => 10.00,
                'features' => [
                    'All Base features',
                    'Unlimited hierarchical sub-bins',
                    'Crypto wallet integration',
                    'Advanced AI insights'
                ]
            ]
        ];

        return $packages[$packageId] ?? null;
    }
}
