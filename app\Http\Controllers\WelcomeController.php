<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;

class WelcomeController extends Controller
{
    /**
     * Display the welcome page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get welcome page settings
        $settings = [
            'site_name' => setting('app_name', 'PocketWatch'),
            'site_description' => setting('app_description', 'Your personal finance management tool'),
            'logo' => setting('logo'),
            'favicon' => setting('favicon'),
            'footer_text' => setting('footer_text', '&copy; ' . date('Y') . ' PocketWatch. All rights reserved.'),

            // Navbar settings
            'navbar_brand' => setting('navbar_brand', 'PocketWatch'),
            'navbar_links' => setting('navbar_links', [
                ['name' => 'Features', 'url' => '#features'],
                ['name' => 'Testimonials', 'url' => '#testimonials'],
                ['name' => 'Pricing', 'url' => '#packages']
            ]),

            // Slider settings
            'sliders' => setting('welcome_sliders', [
                [
                    'image' => 'https://images.unsplash.com/photo-1579621970795-87facc2f976d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=600&q=80',
                    'title' => 'Take Control of Your Finances',
                    'description' => 'Track, manage, and optimize your financial life with our powerful tools'
                ],
                [
                    'image' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=600&q=80',
                    'title' => 'Smart Financial Planning',
                    'description' => 'Make informed decisions with our advanced analytics and insights'
                ],
                [
                    'image' => 'https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=600&q=80',
                    'title' => 'Secure & Private',
                    'description' => 'Your financial data is protected with enterprise-grade security'
                ]
            ]),

            // Features settings
            'features_title' => setting('features_title', 'Features'),
            'features' => setting('welcome_features', [
                [
                    'icon' => 'bi-graph-up',
                    'title' => 'Financial Dashboard',
                    'description' => 'Get a comprehensive view of your finances with our intuitive dashboard that tracks income, expenses, and investments in real-time.'
                ],
                [
                    'icon' => 'bi-wallet2',
                    'title' => 'Wallet Management',
                    'description' => 'Securely manage multiple wallets and accounts in one place, with automatic categorization and transaction tracking.'
                ],
                [
                    'icon' => 'bi-shield-check',
                    'title' => 'Secure Transactions',
                    'description' => 'All your financial data is protected with bank-level encryption and advanced security protocols.'
                ],
                [
                    'icon' => 'bi-bell',
                    'title' => 'Smart Alerts',
                    'description' => 'Receive timely notifications about unusual spending, upcoming bills, and investment opportunities.'
                ],
                [
                    'icon' => 'bi-bar-chart',
                    'title' => 'Advanced Analytics',
                    'description' => 'Gain valuable insights with detailed reports and visualizations that help you understand your spending habits.'
                ],
                [
                    'icon' => 'bi-headset',
                    'title' => 'Premium Support',
                    'description' => 'Our dedicated support team is available 24/7 to help you with any questions or issues you might have.'
                ]
            ]),

            // Testimonials settings
            'testimonials_title' => setting('testimonials_title', 'Testimonials'),
            'testimonials' => setting('welcome_testimonials', [
                [
                    'avatar' => 'https://randomuser.me/api/portraits/women/32.jpg',
                    'content' => 'PocketWatch has completely transformed how I manage my finances. The dashboard gives me a clear picture of my spending habits, and the alerts have helped me avoid unnecessary fees. Highly recommended!',
                    'name' => 'Sarah Johnson',
                    'position' => 'Marketing Executive'
                ],
                [
                    'avatar' => 'https://randomuser.me/api/portraits/men/45.jpg',
                    'content' => 'As a small business owner, keeping track of expenses was always a challenge. PocketWatch has simplified everything with its intuitive interface and powerful reporting tools. The customer support is exceptional too!',
                    'name' => 'Michael Chen',
                    'position' => 'Small Business Owner'
                ],
                [
                    'avatar' => 'https://randomuser.me/api/portraits/women/68.jpg',
                    'content' => 'I\'ve tried several financial apps, but PocketWatch stands out with its security features and ease of use. The ability to manage multiple accounts in one place has saved me so much time.',
                    'name' => 'Emily Rodriguez',
                    'position' => 'Financial Analyst'
                ],
                [
                    'avatar' => 'https://randomuser.me/api/portraits/men/22.jpg',
                    'content' => 'The investment tracking feature in PocketWatch has been a game-changer for me. I can now easily monitor my portfolio performance and make informed decisions. Worth every penny!',
                    'name' => 'David Wilson',
                    'position' => 'Investment Consultant'
                ]
            ]),

            // Packages settings
            'packages_title' => setting('packages_title', 'Pricing Plans'),
            'packages' => $this->getRealSubscriptionPackages(),

            // Social links
            'social_links' => setting('social_links', [
                ['icon' => 'bi-facebook', 'url' => '#'],
                ['icon' => 'bi-twitter', 'url' => '#'],
                ['icon' => 'bi-instagram', 'url' => '#'],
                ['icon' => 'bi-github', 'url' => '#']
            ]),

            // Theme settings
            'primary_color' => setting('primary_color', '#39FF14'),
            'background_color' => setting('background_color', '#0a0a0a'),
            'text_color' => setting('text_color', '#39FF14')
        ];

        return view('welcome', compact('settings'));
    }

    /**
     * Get real subscription packages from the system configuration.
     *
     * @return array
     */
    protected function getRealSubscriptionPackages()
    {
        // Get trial days
        $trialDays = config('services.subscription.trial_days', 7);

        // Get pricing information
        $pricing = [
            'base' => [
                'monthly' => config('services.subscription.base_monthly_price', 5.00),
                'yearly' => config('services.subscription.base_yearly_price', 50.00),
            ],
            'premium' => [
                'monthly' => config('services.subscription.premium_monthly_price', 10.00),
                'yearly' => config('services.subscription.premium_yearly_price', 100.00),
            ],
        ];

        // Get features for each tier
        $baseFeatures = $this->getFeatures('base');
        $premiumFeatures = $this->getFeatures('premium');

        // Format packages for display
        $packages = [
            [
                'title' => 'Base',
                'price' => '$' . number_format($pricing['base']['monthly'], 2) . ' / month',
                'popular' => false,
                'features' => $this->formatFeaturesForDisplay($baseFeatures),
                'button_text' => 'Start Free Trial',
                'button_url' => route('admin.login')
            ],
            [
                'title' => 'Premium',
                'price' => '$' . number_format($pricing['premium']['monthly'], 2) . ' / month',
                'popular' => true,
                'features' => $this->formatFeaturesForDisplay($premiumFeatures),
                'button_text' => 'Start Free Trial',
                'button_url' => route('admin.login')
            ],
            [
                'title' => 'Base (Annual)',
                'price' => '$' . number_format($pricing['base']['yearly'], 2) . ' / year',
                'popular' => false,
                'features' => array_merge(
                    $this->formatFeaturesForDisplay($baseFeatures),
                    ['Save with annual billing']
                ),
                'button_text' => 'Start Free Trial',
                'button_url' => route('admin.login')
            ]
        ];

        // Use custom packages from settings if available
        $customPackages = setting('welcome_packages');
        if (!empty($customPackages)) {
            return $customPackages;
        }

        return $packages;
    }

    /**
     * Get the features based on tier.
     *
     * @param  string  $tier
     * @return array
     */
    protected function getFeatures($tier)
    {
        $features = [
            'base' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => 3,
                'crypto_scanner' => false,
                'unlimited_sub_bins' => false,
                'priority_notifications' => false,
                'advanced_ai_suggestions' => false,
            ],
            'premium' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => -1, // Unlimited
                'crypto_scanner' => true,
                'unlimited_sub_bins' => true,
                'priority_notifications' => true,
                'advanced_ai_suggestions' => true,
            ],
        ];

        return $features[$tier] ?? $features['base'];
    }

    /**
     * Format features for display in the welcome page.
     *
     * @param  array  $features
     * @return array
     */
    protected function formatFeaturesForDisplay($features)
    {
        $displayFeatures = [];

        // Feature labels for display
        $featureLabels = [
            'secure_login' => 'Secure Login',
            'bank_account_linking' => 'Bank Account Linking',
            'total_balance_view' => 'Total Balance View',
            'balance_toggle' => 'Balance Toggle',
            'financial_bins' => 'Financial Bins',
            'auto_categorization' => 'Auto Categorization',
            'manual_bin_editing' => 'Manual Bin Editing',
            'graphical_insights' => 'Graphical Insights',
            'recent_transactions' => 'Recent Transactions',
            'chatbot_access' => 'Chatbot Access',
            'notifications' => 'Notifications',
            'max_sub_bin_levels' => 'Sub-Bin Levels',
            'crypto_scanner' => 'Crypto Scanner',
            'unlimited_sub_bins' => 'Unlimited Sub-Bins',
            'priority_notifications' => 'Priority Notifications',
            'advanced_ai_suggestions' => 'Advanced AI Suggestions',
        ];

        // Add enabled features to display list
        foreach ($features as $key => $enabled) {
            if ($enabled === true) {
                if (isset($featureLabels[$key])) {
                    $displayFeatures[] = $featureLabels[$key];
                }
            } elseif ($key === 'max_sub_bin_levels') {
                if ($enabled === -1) {
                    $displayFeatures[] = 'Unlimited Sub-Bin Levels';
                } else {
                    $displayFeatures[] = "Up to {$enabled} Sub-Bin Levels";
                }
            }
        }

        // Add trial information
        $trialDays = config('services.subscription.trial_days', 7);
        $displayFeatures[] = "{$trialDays}-Day Free Trial";

        return $displayFeatures;
    }
}
