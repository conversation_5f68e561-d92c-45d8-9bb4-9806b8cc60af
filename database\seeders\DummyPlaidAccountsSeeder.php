<?php

namespace Database\Seeders;

use App\Models\PlaidAccount;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class DummyPlaidAccountsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users
        $users = User::all();

        foreach ($users as $user) {
            // Create dummy checking account
            PlaidAccount::create([
                'uuid' => Str::uuid(),
                'user_id' => $user->id,
                'institution_id' => 'ins_1',
                'institution_name' => 'Chase Bank',
                'account_id' => 'acc_' . Str::random(10),
                'account_name' => 'Checking Account',
                'account_type' => 'depository',
                'account_subtype' => 'checking',
                'account_mask' => '1234',
                'access_token' => 'access-sandbox-' . Str::random(10),
                'item_id' => 'item-sandbox-' . Str::random(10),
                'is_default' => true,
                'is_payment_enabled' => true,
                'metadata' => [
                    'balances' => [
                        'available' => 1000.00,
                        'current' => 1050.00,
                        'iso_currency_code' => 'USD',
                    ],
                ],
                'last_synced_at' => now(),
            ]);

            // Create dummy savings account
            PlaidAccount::create([
                'uuid' => Str::uuid(),
                'user_id' => $user->id,
                'institution_id' => 'ins_2',
                'institution_name' => 'Bank of America',
                'account_id' => 'acc_' . Str::random(10),
                'account_name' => 'Savings Account',
                'account_type' => 'depository',
                'account_subtype' => 'savings',
                'account_mask' => '5678',
                'access_token' => 'access-sandbox-' . Str::random(10),
                'item_id' => 'item-sandbox-' . Str::random(10),
                'is_default' => false,
                'is_payment_enabled' => true,
                'metadata' => [
                    'balances' => [
                        'available' => 5000.00,
                        'current' => 5000.00,
                        'iso_currency_code' => 'USD',
                    ],
                ],
                'last_synced_at' => now(),
            ]);

            // Create dummy credit card account
            PlaidAccount::create([
                'uuid' => Str::uuid(),
                'user_id' => $user->id,
                'institution_id' => 'ins_3',
                'institution_name' => 'American Express',
                'account_id' => 'acc_' . Str::random(10),
                'account_name' => 'Credit Card',
                'account_type' => 'credit',
                'account_subtype' => 'credit card',
                'account_mask' => '9012',
                'access_token' => 'access-sandbox-' . Str::random(10),
                'item_id' => 'item-sandbox-' . Str::random(10),
                'is_default' => false,
                'is_payment_enabled' => false,
                'metadata' => [
                    'balances' => [
                        'available' => 5000.00,
                        'current' => -1500.00,
                        'iso_currency_code' => 'USD',
                    ],
                ],
                'last_synced_at' => now(),
            ]);
        }

        $this->command->info('Dummy Plaid accounts created successfully!');
    }
}
