<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'PocketWatch') }}</title>

    <!-- Favicon -->
    @if(setting('favicon'))
    <link rel="icon" href="{{ asset('settings/fav.png') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ asset('settings/fav.png') }}" type="image/x-icon">
    @endif

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Theme CSS -->
    <link href="{{ asset('css/theme.css') }}" rel="stylesheet">

    <style>
        :root {
            --primary-color: #32704e; /* Sea Green */
            --secondary-color: #3CB371; /* Medium Sea Green */
            --accent-color: #66CDAA; /* Medium Aquamarine */
            --light-color: #E0FFF0; /* Light Mint */
            --dark-color: #1D5E40; /* Dark Green */
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        .navbar-brand {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar-brand:hover {
            color: var(--dark-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--dark-color);
            border-color: var(--dark-color);
        }

        .btn-link {
            color: var(--primary-color);
        }

        .btn-link:hover {
            color: var(--dark-color);
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-weight: 600;
            padding: 1rem 1.5rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(46, 139, 87, 0.25);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Subscription styles */
        .pricing-card {
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
        }

        .pricing-card.popular {
            border: 2px solid var(--primary-color);
            transform: scale(1.02);
        }

        .pricing-card.popular:hover {
            transform: translateY(-5px) scale(1.02);
        }

        .pricing-price {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .pricing-price small {
            font-size: 1rem;
            color: #6c757d;
            font-weight: 400;
        }

        .form-switch .form-check-input {
            width: 3em;
            height: 1.5em;
            cursor: pointer;
        }

        .accordion-button:not(.collapsed) {
            background-color: rgba(46, 139, 87, 0.1);
            color: var(--primary-color);
        }

        .accordion-button:focus {
            border-color: rgba(46, 139, 87, 0.25);
            box-shadow: 0 0 0 0.25rem rgba(46, 139, 87, 0.25);
        }

        /* Override Bootstrap styles */
        .btn-success {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-success:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-outline-success {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-success:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .bg-success {
            background-color: var(--primary-color) !important;
        }

        .text-success {
            color: var(--primary-color) !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <nav class="navbar navbar-expand-md navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="{{ url('/') }}">
                    <i class="fas fa-wallet me-2"></i>{{ config('app.name', 'PocketWatch') }}
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav me-auto">
                        @auth
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('home') }}">
                                    <i class="fas fa-home me-1"></i>{{ __('Dashboard') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('subscriptions.index') }}">
                                    <i class="fas fa-crown me-1"></i>{{ __('Subscription') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('reports.index') }}">
                                    <i class="fas fa-file-alt me-1"></i>{{ __('Reports') }}
                                </a>
                            </li>
                            @if(Auth::user()->is_admin)
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('admin.dashboard') }}">
                                        <i class="fas fa-tachometer-alt me-1"></i>{{ __('Admin Panel') }}
                                    </a>
                                </li>
                            @endif
                        @endauth
                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('login') }}">
                                        <i class="fas fa-sign-in-alt me-1"></i>{{ __('Login') }}
                                    </a>
                                </li>
                            @endif

                            {{-- @if (Route::has('register'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('register') }}">
                                        <i class="fas fa-user-plus me-1"></i>{{ __('Register') }}
                                    </a>
                                </li>
                            @endif --}}
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    @if(Auth::user()->avatar)
                                        <img src="{{ Auth::user()->avatar }}" alt="{{ Auth::user()->name }}" class="rounded-circle me-1" width="24" height="24">
                                    @else
                                        <i class="fas fa-user-circle me-1"></i>
                                    @endif
                                    {{ Auth::user()->name }}
                                    @if(Auth::user()->is_admin)
                                        <span class="badge bg-dark ms-1">Admin</span>
                                    @endif
                                </a>

                                <div class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="{{ route('home') }}">
                                        <i class="fas fa-home me-2"></i>{{ __('Dashboard') }}
                                    </a>
                                    <a class="dropdown-item" href="{{ route('subscriptions.index') }}">
                                        <i class="fas fa-crown me-2"></i>{{ __('Manage Subscription') }}
                                    </a>
                                    <a class="dropdown-item" href="{{ route('reports.index') }}">
                                        <i class="fas fa-file-alt me-2"></i>{{ __('Reports') }}
                                    </a>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-user me-2"></i>{{ __('Profile') }}
                                    </a>
                                    <a class="dropdown-item" href="{{ route('settings.notifications') }}">
                                        <i class="fas fa-cog me-2"></i>{{ __('Notification Settings') }}
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="{{ route('logout') }}"
                                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i>{{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <main class="py-4">
            @yield('content')
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>
