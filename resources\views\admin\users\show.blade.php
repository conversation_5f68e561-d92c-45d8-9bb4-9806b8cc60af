@extends('admin.layouts.app')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">User Details</h1>
        <div>
            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Back to Users
            </a>
            <form action="{{ route('admin.users.toggle-status', $user->uuid) }}" method="POST" class="d-inline">
                @csrf
                <button type="submit" class="btn {{ $user->is_active ? 'btn-warning' : 'btn-success' }}"
                        onclick="return confirm('Are you sure you want to {{ $user->is_active ? 'deactivate' : 'activate' }} this user?')">
                    <i class="fas {{ $user->is_active ? 'fa-ban' : 'fa-check' }} me-1"></i>
                    {{ $user->is_active ? 'Deactivate' : 'Activate' }} User
                </button>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- User Profile Card -->
        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Information</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        @if($user->avatar)
                            <img src="{{ $user->avatar }}" alt="{{ $user->name }}" class="img-profile rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        @else
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto" style="width: 150px; height: 150px;">
                                <i class="fas fa-user fa-4x text-secondary"></i>
                            </div>
                        @endif
                        <h4 class="mt-3">{{ $user->name }}</h4>
                        <p class="text-muted">
                            {{ $user->email }}
                            @if($user->is_admin)
                                <span class="badge bg-dark ms-1">Admin</span>
                            @endif
                        </p>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Status:</span>
                            <span class="badge {{ $user->is_active ? 'bg-success' : 'bg-danger' }}">
                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Subscription:</span>
                            @if($user->subscription_tier == 'premium')
                                <span class="badge bg-success">
                                    <i class="fas fa-crown me-1"></i> Premium
                                </span>
                            @elseif($user->subscription_tier == 'trial')
                                <span class="badge bg-info">
                                    <i class="fas fa-star me-1"></i> Trial
                                </span>
                            @else
                                <span class="badge bg-secondary">
                                    <i class="fas fa-user me-1"></i> Base
                                </span>
                            @endif
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Registered:</span>
                            <span>{{ $user->created_at->format('M d, Y') }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Last Login:</span>
                            <span>
                                @if($user->last_login_at)
                                    {{ $user->last_login_at->format('M d, Y H:i') }}
                                @else
                                    <span class="text-muted">Never</span>
                                @endif
                            </span>
                        </div>
                        @if($user->phone_number)
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">Phone:</span>
                                <span>
                                    @if($user->country_code)
                                        +{{ $user->country_code }}
                                    @endif
                                    {{ $user->phone_number }}
                                </span>
                            </div>
                        @endif
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Cumulative Balance:</span>
                            <span class="fw-bold">
                                ${{ number_format($user->cumulative_balance, 2) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Details</h6>
                </div>
                <div class="card-body">
                    @if($user->subscriptions->count() > 0)
                        @foreach($user->subscriptions as $subscription)
                            <div class="mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">Plan:</span>
                                    <span class="fw-bold">
                                        @if($user->subscription_tier == 'trial' && $subscription->stripe_status == 'trialing')
                                            Free Trial
                                        @else
                                            {{ $subscription->name }}
                                        @endif
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">Status:</span>
                                    <span class="badge
                                        @if($subscription->stripe_status == 'trialing')
                                            bg-info
                                        @elseif($subscription->stripe_status == 'active')
                                            bg-success
                                        @elseif($subscription->stripe_status == 'canceled')
                                            bg-danger
                                        @else
                                            bg-secondary
                                        @endif
                                    ">
                                        @if($subscription->stripe_status == 'trialing')
                                            <i class="fas fa-star me-1"></i> Trial
                                        @else
                                            {{ ucfirst($subscription->stripe_status) }}
                                        @endif
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">Price:</span>
                                    <span>
                                        @if($user->subscription_tier == 'trial' && $subscription->stripe_status == 'trialing')
                                            Free / 7 Days
                                        @else
                                            ${{ number_format($subscription->price, 2) }}/{{ $subscription->billing_cycle }}
                                        @endif
                                    </span>
                                </div>
                                @if($subscription->trial_ends_at)
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-muted">Trial Ends:</span>
                                        <span>{{ $subscription->trial_ends_at->format('M d, Y') }}</span>
                                    </div>
                                @endif
                                @if($subscription->ends_at)
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-muted">Ends At:</span>
                                        <span>{{ $subscription->ends_at->format('M d, Y') }}</span>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <p>No subscription data available</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-xl-8">
            <!-- Bins Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Bins</h6>
                    <span class="badge bg-primary">{{ $user->bins->count() }}</span>
                </div>
                <div class="card-body">
                    @if($user->bins->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No.</th>
                                        <th>Name</th>
                                        <th>Current Amount</th>
                                        <th>Threshold</th>
                                        <th>Status</th>
                                        <th>Sub-Bins</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->bins as $bin)
                                        <tr>
                                            <td>{{ $loop->iteration }}</td>
                                            <td>{{ $bin->name }}</td>
                                            <td>${{ number_format($bin->current_amount, 2) }}</td>
                                            <td>${{ number_format($bin->threshold_min, 2) }} -
                                                @if($bin->threshold_max)
                                                    ${{ number_format($bin->threshold_max, 2) }}
                                                @else
                                                    ∞
                                                @endif
                                            </td>
                                            <td>
                                                @if($bin->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td>{{ $bin->subBins->count() }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <p>No bins created yet</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Transactions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Transactions</h6>
                    <a href="{{ route('admin.transactions.index', ['user_id' => $user->uuid]) }}" class="btn btn-sm btn-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    @if($user->transactions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No.</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Bin</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->transactions->take(5) as $transaction)
                                        <tr>
                                            <td>{{ $loop->iteration }}</td>
                                            <td>
                                                @if($transaction->transaction_type == 'income')
                                                    <span class="badge bg-success">Income</span>
                                                @elseif($transaction->transaction_type == 'expense')
                                                    <span class="badge bg-danger">Expense</span>
                                                @else
                                                    <span class="badge bg-info">Transfer</span>
                                                @endif
                                            </td>
                                            <td>${{ number_format($transaction->amount, 2) }}</td>
                                            <td>
                                                @if($transaction->bin)
                                                    {{ $transaction->bin->name }}
                                                    @if($transaction->sub_bin_id)
                                                        <span class="text-muted">
                                                            ({{ $transaction->subBin->name }})
                                                        </span>
                                                    @endif
                                                @else
                                                    <span class="text-muted">N/A</span>
                                                @endif
                                            </td>
                                            <td>{{ $transaction->transaction_date->format('M d, Y') }}</td>
                                            <td>
                                                <span class="badge bg-success">{{ ucfirst($transaction->status) }}</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                            <p>No transactions found</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Crypto Wallets Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Crypto Wallets</h6>
                    <a href="{{ route('admin.crypto-wallets.index', ['user_id' => $user->uuid]) }}" class="btn btn-sm btn-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    @if($user->cryptoWallets->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No.</th>
                                        <th>Name</th>
                                        <th>Network</th>
                                        <th>Address</th>
                                        <th>Value</th>
                                        <th>Last Synced</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->cryptoWallets as $wallet)
                                        <tr>
                                            <td>{{ $loop->iteration }}</td>
                                            <td>{{ $wallet->wallet_name }}</td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    {{ ucfirst($wallet->blockchain_network) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-monospace">
                                                    {{ substr($wallet->wallet_address, 0, 6) }}...{{ substr($wallet->wallet_address, -4) }}
                                                </span>
                                            </td>
                                            <td>${{ number_format($wallet->total_value_usd, 2) }}</td>
                                            <td>
                                                @if($wallet->last_synced_at)
                                                    {{ $wallet->last_synced_at->format('M d, Y H:i') }}
                                                @else
                                                    <span class="text-muted">Never</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fab fa-bitcoin fa-3x text-muted mb-3"></i>
                            <p>No crypto wallets found</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
