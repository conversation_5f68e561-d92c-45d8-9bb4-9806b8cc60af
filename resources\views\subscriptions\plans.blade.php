@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10 text-center">
            <h2 class="fw-bold mb-3" style="color: var(--primary-color);">Choose Your Perfect Plan</h2>
            <p class="text-muted mb-5">Select the plan that best fits your financial management needs. All plans include a 7-day free trial.</p>

            <div class="billing-toggle-container d-inline-flex align-items-center bg-light p-2 rounded-pill mb-5">
                <span class="me-3 {{ $billingCycle == 'monthly' ? 'fw-bold' : 'text-muted' }}">Monthly</span>
                <div class="form-check form-switch mx-2 mb-0">
                    <input class="form-check-input" type="checkbox" id="billingToggle" {{ $billingCycle == 'yearly' ? 'checked' : '' }}
                        onchange="window.location.href='{{ route('subscriptions.plans') }}?billing={{ $billingCycle == 'yearly' ? 'monthly' : 'yearly' }}'">
                    <label class="form-check-label" for="billingToggle"></label>
                </div>
                <span class="ms-3 {{ $billingCycle == 'yearly' ? 'fw-bold' : 'text-muted' }}">
                    Yearly <span class="badge bg-success ms-1">Save 17%</span>
                </span>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <!-- Base Plan -->
        <div class="col-lg-5 col-md-6 mb-4">
            <div class="card pricing-card h-100">
                <div class="card-header text-center py-4 bg-white">
                    <h3 class="mb-0">Base Plan</h3>
                </div>
                <div class="card-body d-flex flex-column">
                    <div class="pricing-header text-center">
                        <div class="pricing-price mb-3">
                            <span class="currency">$</span>
                            <span class="amount">{{ $billingCycle == 'monthly' ? '5' : '50' }}</span>
                            <span class="period">/ {{ $billingCycle == 'monthly' ? 'month' : 'year' }}</span>
                        </div>
                        @if($billingCycle == 'yearly')
                            <div class="savings-badge mb-3">
                                <span class="badge bg-success bg-opacity-10 text-success px-3 py-2">Save $10 with annual billing</span>
                            </div>
                        @endif
                        <p class="text-muted">Essential financial management tools</p>
                    </div>

                    <div class="pricing-features mt-4 flex-grow-1">
                        <h6 class="fw-bold mb-3">What's included:</h6>
                        <ul class="feature-list">
                            <li><i class="fas fa-check text-success me-2"></i> Secure user registration and login</li>
                            <li><i class="fas fa-check text-success me-2"></i> Link bank accounts via Plaid</li>
                            <li><i class="fas fa-check text-success me-2"></i> View total balance on dashboard</li>
                            <li><i class="fas fa-check text-success me-2"></i> Hide/Show balance with eye toggle</li>
                            <li><i class="fas fa-check text-success me-2"></i> Create financial bins with thresholds</li>
                            <li><i class="fas fa-check text-success me-2"></i> Automatic transaction categorization</li>
                            <li><i class="fas fa-check text-success me-2"></i> View graphical insights</li>
                            <li><i class="fas fa-check text-success me-2"></i> View recent transactions</li>
                            <li><i class="fas fa-check text-success me-2"></i> Chat with Binnit chatbot</li>
                            <li><i class="fas fa-check text-success me-2"></i> Notification system</li>
                            <li><i class="fas fa-check text-success me-2"></i> Up to 3 levels of sub-bins</li>
                            <li><i class="fas fa-times text-danger me-2"></i> Crypto scanner</li>
                            <li><i class="fas fa-times text-danger me-2"></i> Unlimited sub-bins</li>
                            <li><i class="fas fa-times text-danger me-2"></i> Priority notifications</li>
                            <li><i class="fas fa-times text-danger me-2"></i> Advanced AI suggestions</li>
                        </ul>
                    </div>

                    <div class="pricing-action mt-4">
                        <a href="{{ route('subscriptions.checkout', ['tier' => 'base', 'billing' => $billingCycle]) }}" class="btn btn-outline-primary btn-lg w-100">
                            Start Free Trial
                        </a>
                        <div class="text-center mt-3">
                            <small class="text-muted">7-day free trial, cancel anytime</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Plan -->
        <div class="col-lg-5 col-md-6 mb-4">
            <div class="card pricing-card premium-card h-100">
                <div class="popular-badge">Most Popular</div>
                <div class="card-header text-center py-4 bg-primary text-white">
                    <h3 class="mb-0">Premium Plan</h3>
                </div>
                <div class="card-body d-flex flex-column">
                    <div class="pricing-header text-center">
                        <div class="pricing-price mb-3">
                            <span class="currency">$</span>
                            <span class="amount">{{ $billingCycle == 'monthly' ? '10' : '100' }}</span>
                            <span class="period">/ {{ $billingCycle == 'monthly' ? 'month' : 'year' }}</span>
                        </div>
                        @if($billingCycle == 'yearly')
                            <div class="savings-badge mb-3">
                                <span class="badge bg-success bg-opacity-10 text-success px-3 py-2">Save $20 with annual billing</span>
                            </div>
                        @endif
                        <p class="text-muted">Advanced features for power users</p>
                    </div>

                    <div class="pricing-features mt-4 flex-grow-1">
                        <h6 class="fw-bold mb-3">Everything in Base, plus:</h6>
                        <ul class="feature-list premium-features">
                            <li><i class="fas fa-check text-success me-2"></i> <strong>Unlimited levels of sub-bins</strong></li>
                            <li><i class="fas fa-check text-success me-2"></i> <strong>Crypto scanner</strong></li>
                            <li><i class="fas fa-check text-success me-2"></i> <strong>Priority notifications</strong></li>
                            <li><i class="fas fa-check text-success me-2"></i> <strong>Advanced AI suggestions</strong></li>
                        </ul>

                        <div class="premium-divider my-4"></div>

                        <h6 class="fw-bold mb-3">Base features included:</h6>
                        <ul class="feature-list base-features">
                            <li><i class="fas fa-check text-success me-2"></i> Secure user registration and login</li>
                            <li><i class="fas fa-check text-success me-2"></i> Link bank accounts via Plaid</li>
                            <li><i class="fas fa-check text-success me-2"></i> View total balance on dashboard</li>
                            <li><i class="fas fa-check text-success me-2"></i> And all other Base Plan features</li>
                        </ul>
                    </div>

                    <div class="pricing-action mt-4">
                        <a href="{{ route('subscriptions.checkout', ['tier' => 'premium', 'billing' => $billingCycle]) }}" class="btn btn-primary btn-lg w-100">
                            Start Free Trial
                        </a>
                        <div class="text-center mt-3">
                            <small class="text-muted">7-day free trial, cancel anytime</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center mt-4">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="fw-bold mb-4">Frequently Asked Questions</h5>

                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqOne">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                    How does the 7-day free trial work?
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="faqOne" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    All new subscriptions start with a 7-day free trial. You'll have full access to all features of your chosen plan during this period. No credit card is required to start the trial, and you can cancel at any time before the trial ends to avoid being charged.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    Can I switch between plans?
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="faqTwo" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes, you can upgrade from Base to Premium at any time. When you upgrade, you'll be charged the prorated difference for the remainder of your current billing cycle. Downgrades will take effect at the end of your current billing cycle.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    How do I cancel my subscription?
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="faqThree" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    You can cancel your subscription at any time from your subscription management page. After cancellation, your subscription will remain active until the end of your current billing period, and you won't be charged again.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqFour">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                    What payment methods do you accept?
                                </button>
                            </h2>
                            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="faqFour" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    We accept all major credit and debit cards, including Visa, Mastercard, American Express, and Discover. Payment processing is securely handled by Stripe.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Pricing Cards */
    .pricing-card {
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 16px;
        transition: all 0.3s ease;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .pricing-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .pricing-card .card-header {
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Premium Card */
    .premium-card {
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .popular-badge {
        position: absolute;
        top: 15px;
        right: -35px;
        background: var(--primary-color);
        color: white;
        padding: 5px 40px;
        font-size: 0.8rem;
        font-weight: 600;
        transform: rotate(45deg);
        z-index: 1;
    }

    /* Pricing Elements */
    .pricing-price {
        position: relative;
        display: inline-block;
    }

    .pricing-price .currency {
        position: relative;
        top: -15px;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .pricing-price .amount {
        font-size: 3.5rem;
        font-weight: 700;
        color: var(--primary-color);
        line-height: 1;
    }

    .pricing-price .period {
        font-size: 1rem;
        color: #6c757d;
        font-weight: 400;
    }

    /* Feature Lists */
    .feature-list {
        list-style-type: none;
        padding-left: 0;
    }

    .feature-list li {
        padding: 8px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .premium-features li {
        font-weight: 500;
    }

    .premium-divider {
        height: 1px;
        background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
    }

    /* Billing Toggle */
    .billing-toggle-container {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .form-switch .form-check-input {
        width: 3em;
        height: 1.5em;
        cursor: pointer;
    }

    /* FAQ Accordion */
    .accordion-button:not(.collapsed) {
        background-color: rgba(46, 139, 87, 0.1);
        color: var(--primary-color);
    }

    .accordion-button:focus {
        border-color: rgba(46, 139, 87, 0.25);
        box-shadow: 0 0 0 0.25rem rgba(46, 139, 87, 0.25);
    }

    .accordion-item {
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 10px;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .accordion-button {
        font-weight: 500;
    }
</style>
@endsection
