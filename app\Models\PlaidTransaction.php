<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlaidTransaction extends Model
{
    use HasFactory, SoftDeletes, HasUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'plaid_account_id',
        'transaction_id',
        'category_id',
        'category',
        'transaction_type',
        'name',
        'amount',
        'iso_currency_code',
        'unofficial_currency_code',
        'date',
        'pending',
        'account_owner',
        'location',
        'payment_meta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'category' => 'json',
        'amount' => 'decimal:2',
        'date' => 'date',
        'pending' => 'boolean',
        'location' => 'json',
        'payment_meta' => 'json',
    ];

    /**
     * Get the user that owns the transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the account that owns the transaction.
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(PlaidAccount::class, 'plaid_account_id');
    }
}
