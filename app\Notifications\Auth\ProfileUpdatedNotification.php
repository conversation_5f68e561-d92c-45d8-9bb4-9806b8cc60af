<?php

namespace App\Notifications\Auth;

use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProfileUpdatedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The timestamp when the profile was updated.
     *
     * @var \Illuminate\Support\Carbon
     */
    protected $timestamp;

    /**
     * The fields that were updated.
     *
     * @var array
     */
    protected $updatedFields;

    /**
     * Create a new notification instance.
     *
     * @param  array  $updatedFields
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_profile_updates';

    public function __construct(array $updatedFields = [])
    {
        $this->timestamp = now();
        $this->updatedFields = $updatedFields;

        $this->subject = 'Your PocketWatch Profile Has Been Updated';
        $this->title = 'Profile Updated';
        $this->content = 'Your PocketWatch account profile has been successfully updated. If you made these changes, 
                         you can safely ignore this email.';
        
        $this->detailsTitle = 'Update Details';
        
        $details = [
            'Date & Time' => $this->timestamp->format('F j, Y, g:i a'),
        ];
        
        if (!empty($this->updatedFields)) {
            $details['Updated Fields'] = implode(', ', $this->updatedFields);
        }
        
        $this->details = $details;
        
        $this->actionText = 'View Profile';
        $this->actionUrl = url('/profile');
        
        $this->closing = 'If you did not update your profile, please contact our support team immediately.';
        $this->signature = 'The PocketWatch Security Team';
    }
}
