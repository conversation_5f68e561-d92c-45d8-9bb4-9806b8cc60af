<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Email verification code fields
            $table->string('email_verification_code', 6)->nullable()->after('email_verified_at');
            $table->timestamp('email_verification_code_expires_at')->nullable()->after('email_verification_code');
            
            // Password reset code fields
            $table->string('password_reset_code', 6)->nullable()->after('email_verification_code_expires_at');
            $table->timestamp('password_reset_code_expires_at')->nullable()->after('password_reset_code');
            
            // Track verification attempts
            $table->integer('email_verification_attempts')->default(0)->after('password_reset_code_expires_at');
            $table->integer('password_reset_attempts')->default(0)->after('email_verification_attempts');
            
            // Last code sent timestamps to prevent spam
            $table->timestamp('last_verification_code_sent_at')->nullable()->after('password_reset_attempts');
            $table->timestamp('last_password_reset_code_sent_at')->nullable()->after('last_verification_code_sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'email_verification_code',
                'email_verification_code_expires_at',
                'password_reset_code',
                'password_reset_code_expires_at',
                'email_verification_attempts',
                'password_reset_attempts',
                'last_verification_code_sent_at',
                'last_password_reset_code_sent_at',
            ]);
        });
    }
};
