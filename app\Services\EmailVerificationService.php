<?php

namespace App\Services;

use App\Models\User;
use App\Mail\EmailVerificationMail;
use App\Mail\PasswordResetMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class EmailVerificationService
{
    const MAX_ATTEMPTS = 5;
    const CODE_LENGTH = 6;
    const RATE_LIMIT_MINUTES = 1; // Minimum time between code requests

    /**
     * Generate a random 6-digit verification code
     */
    public function generateVerificationCode(): string
    {
        return str_pad(random_int(0, 999999), self::CODE_LENGTH, '0', STR_PAD_LEFT);
    }

    /**
     * Send email verification code
     */
    public function sendEmailVerificationCode(User $user): array
    {
        try {
            // Check rate limiting
            if ($this->isRateLimited($user, 'email_verification')) {
                return [
                    'success' => false,
                    'message' => 'Please wait before requesting another verification code.',
                    'error_code' => 'RATE_LIMITED'
                ];
            }

            // Check if user has exceeded max attempts
            if (($user->email_verification_attempts ?? 0) >= self::MAX_ATTEMPTS) {
                return [
                    'success' => false,
                    'message' => 'Maximum verification attempts exceeded. Please contact support.',
                    'error_code' => 'MAX_ATTEMPTS_EXCEEDED'
                ];
            }

            // Generate new verification code
            $verificationCode = $this->generateVerificationCode();
            $expiryMinutes = config('app.email_verification_code_expiry', 15);
            $expiresAt = Carbon::now()->addMinutes($expiryMinutes);

            // Update user with new verification code
            $user->update([
                'email_verification_code' => $verificationCode,
                'email_verification_code_expires_at' => $expiresAt,
                'last_verification_code_sent_at' => Carbon::now(),
            ]);

            // Send email
            Mail::to($user->email)->send(
                new EmailVerificationMail(
                    $user,
                    $verificationCode,
                    $expiryMinutes,
                    $user->email_verification_attempts ?? 0
                )
            );

            Log::info('Email verification code sent', [
                'user_id' => $user->id,
                'email' => $user->email,
                'expires_at' => $expiresAt
            ]);

            return [
                'success' => true,
                'message' => 'Verification code sent to your email address.',
                'expires_at' => $expiresAt,
                'attempts_remaining' => self::MAX_ATTEMPTS - ($user->email_verification_attempts ?? 0)
            ];

        } catch (\Exception $e) {
            Log::error('Failed to send email verification code', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send verification code. Please try again.',
                'error_code' => 'SEND_FAILED'
            ];
        }
    }

    /**
     * Verify email verification code
     */
    public function verifyEmailCode(User $user, string $code): array
    {
        try {
            // Check if code exists and hasn't expired
            if (!$user->email_verification_code || 
                Carbon::now()->isAfter($user->email_verification_code_expires_at)) {
                return [
                    'success' => false,
                    'message' => 'Verification code has expired. Please request a new one.',
                    'error_code' => 'CODE_EXPIRED'
                ];
            }

            // Check if code matches
            if ($user->email_verification_code !== $code) {
                // Increment attempts
                $user->increment('email_verification_attempts');

                $attemptsRemaining = self::MAX_ATTEMPTS - ($user->email_verification_attempts ?? 0);

                if ($attemptsRemaining <= 0) {
                    // Clear the code if max attempts reached
                    $user->update([
                        'email_verification_code' => null,
                        'email_verification_code_expires_at' => null,
                    ]);

                    return [
                        'success' => false,
                        'message' => 'Maximum verification attempts exceeded. Please request a new code.',
                        'error_code' => 'MAX_ATTEMPTS_EXCEEDED'
                    ];
                }

                return [
                    'success' => false,
                    'message' => 'Invalid verification code.',
                    'error_code' => 'INVALID_CODE',
                    'attempts_remaining' => $attemptsRemaining
                ];
            }

            // Code is valid - verify the email
            $user->update([
                'email_verified_at' => Carbon::now(),
                'email_verification_code' => null,
                'email_verification_code_expires_at' => null,
                'email_verification_attempts' => 0,
            ]);

            Log::info('Email verified successfully', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            return [
                'success' => true,
                'message' => 'Email verified successfully!',
                'user' => $user->fresh()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to verify email code', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Verification failed. Please try again.',
                'error_code' => 'VERIFICATION_FAILED'
            ];
        }
    }

    /**
     * Send password reset code
     */
    public function sendPasswordResetCode(User $user): array
    {
        try {
            // Check rate limiting
            if ($this->isRateLimited($user, 'password_reset')) {
                return [
                    'success' => false,
                    'message' => 'Please wait before requesting another reset code.',
                    'error_code' => 'RATE_LIMITED'
                ];
            }

            // Check if user has exceeded max attempts
            if (($user->password_reset_attempts ?? 0) >= self::MAX_ATTEMPTS) {
                return [
                    'success' => false,
                    'message' => 'Maximum reset attempts exceeded. Please contact support.',
                    'error_code' => 'MAX_ATTEMPTS_EXCEEDED'
                ];
            }

            // Generate new reset code
            $resetCode = $this->generateVerificationCode();
            $expiryMinutes = config('app.password_reset_code_expiry', 15);
            $expiresAt = Carbon::now()->addMinutes($expiryMinutes);

            // Update user with new reset code
            $user->update([
                'password_reset_code' => $resetCode,
                'password_reset_code_expires_at' => $expiresAt,
                'last_password_reset_code_sent_at' => Carbon::now(),
            ]);

            // Send email
            Mail::to($user->email)->send(
                new PasswordResetMail(
                    $user,
                    $resetCode,
                    $expiryMinutes,
                    $user->password_reset_attempts ?? 0
                )
            );

            Log::info('Password reset code sent', [
                'user_id' => $user->id,
                'email' => $user->email,
                'expires_at' => $expiresAt
            ]);

            return [
                'success' => true,
                'message' => 'Password reset code sent to your email address.',
                'expires_at' => $expiresAt,
                'attempts_remaining' => self::MAX_ATTEMPTS - ($user->password_reset_attempts ?? 0)
            ];

        } catch (\Exception $e) {
            Log::error('Failed to send password reset code', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send reset code. Please try again.',
                'error_code' => 'SEND_FAILED'
            ];
        }
    }

    /**
     * Verify password reset code
     */
    public function verifyPasswordResetCode(User $user, string $code): array
    {
        try {
            // Check if code exists and hasn't expired
            if (!$user->password_reset_code || 
                Carbon::now()->isAfter($user->password_reset_code_expires_at)) {
                return [
                    'success' => false,
                    'message' => 'Reset code has expired. Please request a new one.',
                    'error_code' => 'CODE_EXPIRED'
                ];
            }

            // Check if code matches
            if ($user->password_reset_code !== $code) {
                // Increment attempts
                $user->increment('password_reset_attempts');

                $attemptsRemaining = self::MAX_ATTEMPTS - ($user->password_reset_attempts ?? 0);

                if ($attemptsRemaining <= 0) {
                    // Clear the code if max attempts reached
                    $user->update([
                        'password_reset_code' => null,
                        'password_reset_code_expires_at' => null,
                    ]);

                    return [
                        'success' => false,
                        'message' => 'Maximum reset attempts exceeded. Please request a new code.',
                        'error_code' => 'MAX_ATTEMPTS_EXCEEDED'
                    ];
                }

                return [
                    'success' => false,
                    'message' => 'Invalid reset code.',
                    'error_code' => 'INVALID_CODE',
                    'attempts_remaining' => $attemptsRemaining
                ];
            }

            // Code is valid
            Log::info('Password reset code verified', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            return [
                'success' => true,
                'message' => 'Reset code verified. You can now set a new password.',
                'reset_token' => $user->password_reset_code // Use this for the next step
            ];

        } catch (\Exception $e) {
            Log::error('Failed to verify password reset code', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Verification failed. Please try again.',
                'error_code' => 'VERIFICATION_FAILED'
            ];
        }
    }

    /**
     * Complete password reset
     */
    public function resetPassword(User $user, string $code, string $newPassword): array
    {
        try {
            // Verify the code first
            $verificationResult = $this->verifyPasswordResetCode($user, $code);
            
            if (!$verificationResult['success']) {
                return $verificationResult;
            }

            // Update password and clear reset code
            $user->update([
                'password' => bcrypt($newPassword),
                'password_reset_code' => null,
                'password_reset_code_expires_at' => null,
                'password_reset_attempts' => 0,
            ]);

            Log::info('Password reset completed', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            return [
                'success' => true,
                'message' => 'Password reset successfully!',
                'user' => $user->fresh()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to reset password', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Password reset failed. Please try again.',
                'error_code' => 'RESET_FAILED'
            ];
        }
    }

    /**
     * Check if user is rate limited for sending codes
     */
    private function isRateLimited(User $user, string $type): bool
    {
        $lastSentField = $type === 'email_verification' 
            ? 'last_verification_code_sent_at' 
            : 'last_password_reset_code_sent_at';

        if (!$user->$lastSentField) {
            return false;
        }

        return Carbon::now()->diffInMinutes($user->$lastSentField) < self::RATE_LIMIT_MINUTES;
    }
}
