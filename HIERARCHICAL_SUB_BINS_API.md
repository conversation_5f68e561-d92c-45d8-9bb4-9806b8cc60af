# 🏗️ Hierarchical Sub-Bins API Documentation

## Overview

The hierarchical sub-bin system allows users to create unlimited levels of nested sub-bins, creating a pyramid/tree-like structure for better organization of their financial goals.

## 🌳 Structure

```
Bin (Root)
├── Sub-Bin Level 1
│   ├── Sub-Bin Level 2
│   │   ├── Sub-Bin Level 3
│   │   └── Sub-Bin Level 3
│   └── Sub-Bin Level 2
└── Sub-Bin Level 1
    └── Sub-Bin Level 2
        └── Sub-Bin Level 3
```

## 🔒 Subscription Limits

- **Base Tier**: Maximum 5 levels deep
- **Premium Tier**: Maximum 10 levels deep

## 📡 API Endpoints

### 1. Create Sub-Bin (Direct or Nested)

**Endpoint:** `POST /api/bins/{binId}/sub-bins`

**Description:** Create a sub-bin directly under a bin OR nested under another sub-bin

**Parameters:**
```json
{
  "name": "Emergency Fund Level 2",
  "type": "income|expenditure",
  "description": "Secondary emergency fund",
  "threshold_min": 100.00,
  "threshold_max": 1000.00,
  "currency": "USD",
  "parent_sub_bin_id": 123  // Optional: ID of parent sub-bin for nesting
}
```

**Response:**
```json
{
  "message": "Nested sub-bin created successfully under Emergency Fund",
  "sub_bin": {
    "id": 456,
    "uuid": "uuid-string",
    "name": "Emergency Fund Level 2",
    "bin_id": 1,
    "parent_sub_bin_id": 123,
    "depth_level": 2,
    "path": "123/456",
    "current_amount": 0.00,
    "threshold_min": 100.00,
    "threshold_max": 1000.00,
    "currency": "USD",
    "is_active": true
  },
  "hierarchy_info": {
    "depth_level": 2,
    "path": "123/456",
    "is_nested": true,
    "parent_sub_bin": {
      "id": 123,
      "name": "Emergency Fund",
      "depth_level": 1
    },
    "max_depth": 5
  }
}
```

### 2. Create Nested Sub-Bin (Specific Endpoint)

**Endpoint:** `POST /api/bins/{binId}/sub-bins/{parentSubBinId}/nested`

**Description:** Create a sub-bin specifically under another sub-bin

**Parameters:**
```json
{
  "name": "Vacation Savings - Europe",
  "type": "expenditure",
  "description": "Specific savings for Europe trip",
  "threshold_min": 500.00,
  "threshold_max": 3000.00,
  "currency": "USD"
}
```

### 3. Get Hierarchical Tree Structure

**Endpoint:** `GET /api/bins/{binId}/sub-bins-hierarchy`

**Description:** Get the complete hierarchical tree structure for a bin

**Response:**
```json
{
  "bin": {
    "id": 1,
    "name": "Savings",
    "uuid": "bin-uuid"
  },
  "hierarchy": [
    {
      "id": 123,
      "uuid": "sub-bin-uuid",
      "name": "Emergency Fund",
      "type": "expenditure",
      "current_amount": 1500.00,
      "depth_level": 1,
      "path": "123",
      "children": [
        {
          "id": 456,
          "name": "Emergency Fund Level 2",
          "depth_level": 2,
          "path": "123/456",
          "children": [],
          "children_count": 0,
          "has_children": false
        }
      ],
      "children_count": 1,
      "has_children": true
    }
  ],
  "total_sub_bins": 5,
  "max_depth_allowed": 5
}
```

### 4. Get Sub-Bin Details with Hierarchy

**Endpoint:** `GET /api/bins/{binId}/sub-bins/{id}`

**Description:** Get detailed information about a specific sub-bin including its position in the hierarchy

**Response:**
```json
{
  "sub_bin": {
    "id": 456,
    "name": "Emergency Fund Level 2",
    "parent_sub_bin_id": 123,
    "depth_level": 2,
    "path": "123/456"
  },
  "hierarchy_info": {
    "depth_level": 2,
    "path": "123/456",
    "is_nested": true,
    "has_children": false,
    "children_count": 0,
    "parent_sub_bin": {
      "id": 123,
      "name": "Emergency Fund",
      "depth_level": 1
    },
    "ancestors": [
      {
        "id": 123,
        "name": "Emergency Fund",
        "depth_level": 1
      }
    ]
  },
  "child_sub_bins": []
}
```

## 🔧 Usage Examples

### Example 1: Create a Basic Sub-Bin

```bash
curl -X POST /api/bins/1/sub-bins \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Emergency Fund",
    "type": "expenditure",
    "threshold_min": 1000,
    "threshold_max": 5000
  }'
```

### Example 2: Create a Nested Sub-Bin

```bash
curl -X POST /api/bins/1/sub-bins \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Emergency Fund - Medical",
    "type": "expenditure",
    "parent_sub_bin_id": 123,
    "threshold_min": 500,
    "threshold_max": 2000
  }'
```

### Example 3: Get Hierarchy Tree

```bash
curl -X GET /api/bins/1/sub-bins-hierarchy \
  -H "Authorization: Bearer your-token"
```

## 🚨 Error Responses

### Maximum Depth Reached
```json
{
  "message": "Maximum nesting depth of 5 levels reached",
  "error": "max_depth_reached",
  "current_depth": 5,
  "max_depth": 5
}
```

### Parent Sub-Bin Not Found
```json
{
  "message": "Parent sub-bin not found or does not belong to this bin"
}
```

### Subscription Limit Reached
```json
{
  "message": "Upgrade to Premium to create more sub-bins",
  "error": "subscription_limit_reached",
  "remaining_sub_bins": 0,
  "max_sub_bins": 10,
  "subscription_tier": "base"
}
```

## 🎯 Key Features

✅ **Unlimited Nesting**: Create sub-bins under sub-bins  
✅ **Automatic Path Tracking**: Each sub-bin knows its full path  
✅ **Depth Level Management**: Automatic depth calculation  
✅ **Subscription-Based Limits**: Different limits for different tiers  
✅ **Tree Structure API**: Get complete hierarchy in one call  
✅ **Ancestor/Descendant Relationships**: Easy navigation up and down the tree  
✅ **Cascade Deletion**: Deleting a parent removes all children  
✅ **Performance Optimized**: Indexed queries for fast retrieval  

## 🔄 Migration

The system automatically migrates existing sub-bins to the new hierarchical structure with `depth_level = 1` and appropriate path values.
