<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class RetryFailedNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:retry-failed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry all failed notification jobs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Retrying failed notification jobs...');
        
        // Get all failed jobs related to notifications
        $failedJobs = DB::table('failed_jobs')
            ->where('payload', 'like', '%SendQueuedNotifications%')
            ->get();
        
        $this->info("Found {$failedJobs->count()} failed notification jobs");
        
        if ($failedJobs->count() === 0) {
            return Command::SUCCESS;
        }
        
        // Retry each failed job
        foreach ($failedJobs as $job) {
            $this->info("Retrying job {$job->uuid}...");
            Artisan::call('queue:retry', ['id' => $job->uuid]);
        }
        
        $this->info('All failed notification jobs have been retried');
        
        return Command::SUCCESS;
    }
}
