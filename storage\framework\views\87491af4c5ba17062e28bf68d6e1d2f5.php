<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plaid Integration Test - PocketWatch</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .status-success { border-left-color: #28a745; }
        .status-error { border-left-color: #dc3545; }
        .status-warning { border-left-color: #ffc107; }
        .test-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .config-item {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
        }
        .plaid-link-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .plaid-link-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        .plaid-link-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary">
                        <i class="fas fa-university"></i> Plaid Integration Test
                    </h1>
                    <p class="lead text-muted">Comprehensive testing suite for Plaid banking integration</p>
                </div>
            </div>
        </div>

        <!-- Configuration Check -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cog"></i> Configuration Check</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Environment Variables:</h6>
                                <div class="config-item">
                                    <strong>PLAID_CLIENT_ID:</strong> 
                                    <span class="text-success"><?php echo e(config('services.plaid.client_id') ? '✓ Set' : '✗ Missing'); ?></span>
                                </div>
                                <div class="config-item">
                                    <strong>PLAID_SECRET:</strong> 
                                    <span class="text-success"><?php echo e(config('services.plaid.secret') ? '✓ Set' : '✗ Missing'); ?></span>
                                </div>
                                <div class="config-item">
                                    <strong>PLAID_ENVIRONMENT:</strong> 
                                    <span class="badge bg-info"><?php echo e(config('services.plaid.environment', 'Not Set')); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Additional Settings:</h6>
                                <div class="config-item">
                                    <strong>CLIENT_NAME:</strong> <?php echo e(config('services.plaid.client_name', 'Not Set')); ?>

                                </div>
                                <div class="config-item">
                                    <strong>PRODUCTS:</strong> <?php echo e(config('services.plaid.products', 'Not Set')); ?>

                                </div>
                                <div class="config-item">
                                    <strong>COUNTRY_CODES:</strong> <?php echo e(config('services.plaid.country_codes', 'Not Set')); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Suite -->
        <div class="row">
            <!-- API Connection Test -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="api-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plug"></i> API Connection Test
                            <span class="float-end" id="api-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Test basic connectivity to Plaid API</p>
                        <button class="btn btn-primary" onclick="testApiConnection()">
                            <i class="fas fa-play"></i> Test Connection
                        </button>
                        <div id="api-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Link Token Test -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="token-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-key"></i> Link Token Generation
                            <span class="float-end" id="token-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Generate a Plaid Link token for account linking</p>
                        <button class="btn btn-success" onclick="testLinkToken()">
                            <i class="fas fa-key"></i> Generate Token
                        </button>
                        <div id="token-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Plaid Link Integration -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="link-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-link"></i> Plaid Link Integration
                            <span class="float-end" id="link-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Test the complete Plaid Link flow</p>
                        <button class="plaid-link-button" id="link-account-btn" onclick="initializePlaidLink()" disabled>
                            <i class="fas fa-university"></i> Connect Bank Account
                        </button>
                        <div id="link-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="accounts-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> Account Information
                            <span class="float-end" id="accounts-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Retrieve connected account information</p>
                        <button class="btn btn-info" onclick="testAccountInfo()" disabled id="accounts-btn">
                            <i class="fas fa-info-circle"></i> Get Accounts
                        </button>
                        <div id="accounts-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Transactions Test -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="transactions-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exchange-alt"></i> Transactions
                            <span class="float-end" id="transactions-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Retrieve recent transactions (last 30 days)</p>
                        <button class="btn btn-warning" onclick="testTransactions()" disabled id="transactions-btn">
                            <i class="fas fa-history"></i> Get Transactions
                        </button>
                        <div id="transactions-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Clear Session -->
            <div class="col-md-6 mb-4">
                <div class="card test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trash"></i> Clear Test Data
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Clear stored test session data</p>
                        <button class="btn btn-danger" onclick="clearTestSession()">
                            <i class="fas fa-broom"></i> Clear Session
                        </button>
                        <div id="clear-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results Summary -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> Test Results Summary</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-summary">
                            <p class="text-muted">Run tests to see results summary...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plaid Link SDK -->
    <script src="https://cdn.plaid.com/link/v2/stable/link-initialize.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let linkToken = null;
        let plaidHandler = null;
        let testResults = {
            api: null,
            token: null,
            link: null,
            accounts: null,
            transactions: null
        };

        // Set up CSRF token for all AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Test API Connection
        async function testApiConnection() {
            updateStatus('api', 'loading', 'Testing...');
            
            try {
                const response = await fetch('/test/plaid/api-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('api', 'success', 'Connected');
                    testResults.api = true;
                } else {
                    updateStatus('api', 'error', 'Failed');
                    testResults.api = false;
                }
                
                displayResult('api-result', data);
                updateSummary();
                
            } catch (error) {
                updateStatus('api', 'error', 'Error');
                testResults.api = false;
                displayResult('api-result', { error: error.message });
                updateSummary();
            }
        }

        // Test Link Token Generation
        async function testLinkToken() {
            updateStatus('token', 'loading', 'Generating...');
            
            try {
                const response = await fetch('/test/plaid/link-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const data = await response.json();
                
                if (data.success && data.link_token) {
                    updateStatus('token', 'success', 'Generated');
                    testResults.token = true;
                    linkToken = data.link_token;
                    document.getElementById('link-account-btn').disabled = false;
                } else {
                    updateStatus('token', 'error', 'Failed');
                    testResults.token = false;
                }
                
                displayResult('token-result', data);
                updateSummary();
                
            } catch (error) {
                updateStatus('token', 'error', 'Error');
                testResults.token = false;
                displayResult('token-result', { error: error.message });
                updateSummary();
            }
        }

        // Initialize Plaid Link
        function initializePlaidLink() {
            if (!linkToken) {
                alert('Please generate a link token first!');
                return;
            }

            updateStatus('link', 'loading', 'Initializing...');

            plaidHandler = Plaid.create({
                token: linkToken,
                onSuccess: function(public_token, metadata) {
                    updateStatus('link', 'success', 'Connected');
                    testResults.link = true;
                    
                    displayResult('link-result', {
                        success: true,
                        public_token: public_token,
                        metadata: metadata,
                        message: 'Successfully connected to bank account!'
                    });
                    
                    // Exchange public token
                    exchangePublicToken(public_token, metadata);
                    updateSummary();
                },
                onExit: function(err, metadata) {
                    if (err != null) {
                        updateStatus('link', 'error', 'Failed');
                        testResults.link = false;
                        displayResult('link-result', { error: err, metadata: metadata });
                    } else {
                        updateStatus('link', 'warning', 'Cancelled');
                        displayResult('link-result', { message: 'User cancelled', metadata: metadata });
                    }
                    updateSummary();
                },
                onEvent: function(eventName, metadata) {
                    console.log('Plaid Link Event:', eventName, metadata);
                }
            });

            plaidHandler.open();
        }

        // Exchange Public Token
        async function exchangePublicToken(publicToken, metadata) {
            try {
                const response = await fetch('/test/plaid/exchange-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({
                        public_token: publicToken,
                        metadata: metadata
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('accounts-btn').disabled = false;
                    document.getElementById('transactions-btn').disabled = false;
                    displayResult('link-result', {
                        ...data,
                        message: 'Public token exchanged successfully!'
                    });
                }

            } catch (error) {
                console.error('Token exchange error:', error);
            }
        }

        // Test Account Information
        async function testAccountInfo() {
            updateStatus('accounts', 'loading', 'Fetching...');
            
            try {
                const response = await fetch('/test/plaid/accounts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('accounts', 'success', 'Retrieved');
                    testResults.accounts = true;
                } else {
                    updateStatus('accounts', 'error', 'Failed');
                    testResults.accounts = false;
                }
                
                displayResult('accounts-result', data);
                updateSummary();
                
            } catch (error) {
                updateStatus('accounts', 'error', 'Error');
                testResults.accounts = false;
                displayResult('accounts-result', { error: error.message });
                updateSummary();
            }
        }

        // Helper Functions
        function updateStatus(test, status, text) {
            const statusElement = document.getElementById(`${test}-status`);
            const cardElement = document.getElementById(`${test}-test-card`);
            
            // Remove existing status classes
            cardElement.classList.remove('status-success', 'status-error', 'status-warning');
            
            switch(status) {
                case 'loading':
                    statusElement.innerHTML = `<div class="loading"></div> ${text}`;
                    break;
                case 'success':
                    statusElement.innerHTML = `<i class="fas fa-check text-success"></i> ${text}`;
                    cardElement.classList.add('status-success');
                    break;
                case 'error':
                    statusElement.innerHTML = `<i class="fas fa-times text-danger"></i> ${text}`;
                    cardElement.classList.add('status-error');
                    break;
                case 'warning':
                    statusElement.innerHTML = `<i class="fas fa-exclamation text-warning"></i> ${text}`;
                    cardElement.classList.add('status-warning');
                    break;
            }
        }

        function displayResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        function updateSummary() {
            const summary = document.getElementById('test-summary');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(result => result === true).length;
            const failed = Object.values(testResults).filter(result => result === false).length;
            const pending = Object.values(testResults).filter(result => result === null).length;
            
            summary.innerHTML = `
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">${total}</h4>
                        <p>Total Tests</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">${passed}</h4>
                        <p>Passed</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-danger">${failed}</h4>
                        <p>Failed</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">${pending}</h4>
                        <p>Pending</p>
                    </div>
                </div>
            `;
        }

        // Test Transactions
        async function testTransactions() {
            updateStatus('transactions', 'loading', 'Fetching...');

            try {
                const response = await fetch('/test/plaid/transactions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });

                const data = await response.json();

                if (data.success) {
                    updateStatus('transactions', 'success', 'Retrieved');
                    testResults.transactions = true;
                } else {
                    updateStatus('transactions', 'error', 'Failed');
                    testResults.transactions = false;
                }

                displayResult('transactions-result', data);
                updateSummary();

            } catch (error) {
                updateStatus('transactions', 'error', 'Error');
                testResults.transactions = false;
                displayResult('transactions-result', { error: error.message });
                updateSummary();
            }
        }

        // Clear Test Session
        async function clearTestSession() {
            try {
                const response = await fetch('/test/plaid/clear-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Reset UI state
                    document.getElementById('link-account-btn').disabled = true;
                    document.getElementById('accounts-btn').disabled = true;
                    document.getElementById('transactions-btn').disabled = true;
                    linkToken = null;

                    // Reset test results
                    testResults.link = null;
                    testResults.accounts = null;
                    testResults.transactions = null;

                    // Update statuses
                    updateStatus('link', 'warning', 'Reset');
                    updateStatus('accounts', 'warning', 'Reset');
                    updateStatus('transactions', 'warning', 'Reset');

                    displayResult('clear-result', {
                        success: true,
                        message: 'Test session cleared successfully'
                    });

                    updateSummary();
                }

            } catch (error) {
                displayResult('clear-result', { error: error.message });
            }
        }

        // Initialize summary on page load
        updateSummary();
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\PocketWatch\resources\views/test/plaid-test.blade.php ENDPATH**/ ?>