<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\NotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationSettingController extends Controller
{
    /**
     * Display the user's notification settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $settings = $request->user()->getNotificationSettings();

        return response()->json([
            'settings' => $settings,
        ]);
    }

    /**
     * Update the user's notification settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_login' => 'sometimes|boolean',
            'email_profile_updates' => 'sometimes|boolean',
            'email_password_changes' => 'sometimes|boolean',
            'email_bin_operations' => 'sometimes|boolean',
            'email_transaction_operations' => 'sometimes|boolean',
            'email_subscription_updates' => 'sometimes|boolean',
            'email_threshold_alerts' => 'sometimes|boolean',
            'email_renewal_reminders' => 'sometimes|boolean',
            'email_marketing' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $settings = $request->user()->getNotificationSettings();
        $settings->update($request->only([
            'email_login',
            'email_profile_updates',
            'email_password_changes',
            'email_bin_operations',
            'email_transaction_operations',
            'email_subscription_updates',
            'email_threshold_alerts',
            'email_renewal_reminders',
            'email_marketing',
        ]));

        return response()->json([
            'message' => 'Notification settings updated successfully',
            'settings' => $settings,
        ]);
    }
}
