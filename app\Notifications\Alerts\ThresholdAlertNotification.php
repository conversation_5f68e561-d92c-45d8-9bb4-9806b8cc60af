<?php

namespace App\Notifications\Alerts;

use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class ThresholdAlertNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_threshold_alerts';

    /**
     * The bin or sub-bin name.
     *
     * @var string
     */
    protected $binName;

    /**
     * The bin or sub-bin type (bin or sub-bin).
     *
     * @var string
     */
    protected $binType;

    /**
     * The current amount.
     *
     * @var float
     */
    protected $currentAmount;

    /**
     * The threshold amount.
     *
     * @var float
     */
    protected $thresholdAmount;

    /**
     * The threshold type (min or max).
     *
     * @var string
     */
    protected $thresholdType;

    /**
     * The currency.
     *
     * @var string
     */
    protected $currency;

    /**
     * Create a new notification instance.
     *
     * @param  string  $binName
     * @param  string  $binType
     * @param  float  $currentAmount
     * @param  float  $thresholdAmount
     * @param  string  $thresholdType
     * @param  string  $currency
     * @return void
     */

    public function __construct($binName, $binType, $currentAmount, $thresholdAmount, $thresholdType, $currency = 'USD')
    {
        $this->binName = $binName;
        $this->binType = $binType;
        $this->currentAmount = $currentAmount;
        $this->thresholdAmount = $thresholdAmount;
        $this->thresholdType = $thresholdType;
        $this->currency = $currency;

        $alertType = $thresholdType === 'min' ? 'Below Minimum' : 'Above Maximum';

        $this->subject = $alertType . ' Threshold Alert for ' . $binName;
        $this->title = $alertType . ' Threshold Alert';

        if ($thresholdType === 'min') {
            $this->content = 'Your ' . $binType . ' "' . $binName . '" has fallen below the minimum threshold amount.
                             This may require your attention to ensure you have sufficient funds.';
        } else {
            $this->content = 'Your ' . $binType . ' "' . $binName . '" has exceeded the maximum threshold amount.
                             You may want to consider redistributing funds or making planned expenditures.';
        }

        $this->detailsTitle = 'Alert Details';
        $this->details = [
            'Name' => $this->binName,
            'Type' => ucfirst($this->binType),
            'Current Amount' => $this->formatCurrency($this->currentAmount, $this->currency),
            $thresholdType === 'min' ? 'Minimum Threshold' : 'Maximum Threshold' => $this->formatCurrency($this->thresholdAmount, $this->currency),
            'Difference' => $this->formatCurrency(abs($this->currentAmount - $this->thresholdAmount), $this->currency),
        ];

        $this->actionText = 'View Details';
        $this->actionUrl = url('/' . ($this->binType === 'bin' ? 'bins/' : 'sub-bins/') . $this->binName);

        $this->closing = 'Monitor your financial bins regularly to maintain optimal financial health.';
        $this->signature = 'The PocketWatch Team';
    }

    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);

        return $symbol . number_format($value, 2);
    }

    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];

        return $symbols[$currency] ?? $currency;
    }
}
