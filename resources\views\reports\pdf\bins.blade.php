<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Bins Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .header p {
            font-size: 14px;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .summary {
            margin-top: 30px;
            border-top: 2px solid #ddd;
            padding-top: 20px;
        }
        .summary table {
            width: 50%;
            margin-left: auto;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .sub-bin {
            padding-left: 20px;
        }
        .active {
            color: #28a745;
        }
        .inactive {
            color: #dc3545;
        }
        .income {
            color: #28a745;
        }
        .expenditure {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Bins Report</h1>
        <p>{{ $report->name }}</p>
        <p>Generated: {{ now()->format('M d, Y H:i:s') }}</p>
    </div>

    @if($bins->count() > 0)
        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Description</th>
                    <th>Current Amount</th>
                    <th>Threshold</th>
                    <th>Currency</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($bins as $bin)
                    <tr>
                        <td><strong>{{ $bin->name }}</strong></td>
                        <td class="{{ $bin->type }}">{{ ucfirst($bin->type) }}</td>
                        <td>{{ $bin->description }}</td>
                        <td class="text-right">{{ number_format($bin->current_amount, 2) }}</td>
                        <td>
                            {{ number_format($bin->threshold_min, 2) }} - 
                            {{ $bin->threshold_max ? number_format($bin->threshold_max, 2) : '∞' }}
                        </td>
                        <td>{{ $bin->currency }}</td>
                        <td class="{{ $bin->is_active ? 'active' : 'inactive' }}">
                            {{ $bin->is_active ? 'Active' : 'Inactive' }}
                        </td>
                    </tr>
                    @foreach($bin->subBins as $subBin)
                        <tr>
                            <td class="sub-bin">-- {{ $subBin->name }}</td>
                            <td class="{{ $subBin->type }}">{{ ucfirst($subBin->type) }}</td>
                            <td>{{ $subBin->description }}</td>
                            <td class="text-right">{{ number_format($subBin->current_amount, 2) }}</td>
                            <td>
                                {{ number_format($subBin->threshold_min, 2) }} - 
                                {{ $subBin->threshold_max ? number_format($subBin->threshold_max, 2) : '∞' }}
                            </td>
                            <td>{{ $subBin->currency }}</td>
                            <td class="{{ $subBin->is_active ? 'active' : 'inactive' }}">
                                {{ $subBin->is_active ? 'Active' : 'Inactive' }}
                            </td>
                        </tr>
                    @endforeach
                @endforeach
            </tbody>
        </table>
    @else
        <p class="text-center">No bins found.</p>
    @endif

    <div class="summary">
        <h2>Summary</h2>
        <table>
            <tr>
                <th>Total Bins</th>
                <td class="text-right">{{ $total_bins }}</td>
            </tr>
            <tr>
                <th>Total Sub-Bins</th>
                <td class="text-right">{{ $total_sub_bins }}</td>
            </tr>
            <tr>
                <th>Total Amount</th>
                <td class="text-right">{{ $bins->first()->currency ?? 'USD' }} {{ number_format($total_amount, 2) }}</td>
            </tr>
        </table>
    </div>

    <div class="footer">
        <p>PocketWatch Financial Management System</p>
        <p>This report was generated automatically. Please contact support if you have any questions.</p>
    </div>
</body>
</html>
