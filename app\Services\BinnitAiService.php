<?php

namespace App\Services;

use App\Models\CryptoWallet;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BinnitAiService
{
    /**
     * The Binnit AI API base URL.
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * The Binnit AI API key.
     *
     * @var string
     */
    protected $apiKey;

    /**
     * Create a new Binnit AI service instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->baseUrl = config('services.binnit_ai.url', 'https://api.binnit.ai');
        $this->apiKey = config('services.binnit_ai.key');
    }

    /**
     * Get investment advice for the given wallet.
     *
     * @param CryptoWallet $wallet
     * @return array|null
     */
    public function getInvestmentAdvice(CryptoWallet $wallet): ?array
    {
        try {
            // Extract tokens from wallet assets
            $tokens = [];
            foreach ($wallet->assets as $asset) {
                $tokens[$asset['symbol']] = (float) $asset['balance'];
            }

            // Call Binnit AI API
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post("{$this->baseUrl}/crypto/analyze", [
                'wallet' => $wallet->wallet_address,
                'tokens' => $tokens,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Failed to get investment advice from Binnit AI', [
                'wallet_id' => $wallet->id,
                'wallet_address' => $wallet->wallet_address,
                'error' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception when getting investment advice from Binnit AI', [
                'wallet_id' => $wallet->id,
                'wallet_address' => $wallet->wallet_address,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }
}
