<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use App\Models\User;
use App\Notifications\Subscriptions\SubscriptionRenewalNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class SendSubscriptionRenewalReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:send-renewal-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send renewal reminders to users whose subscriptions are about to renew';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get subscriptions that will renew in the next 3 days
        $renewalDate = Carbon::now()->addDays(3);
        
        // For monthly subscriptions, we need to find those that started around 27-30 days ago
        // For yearly subscriptions, we need to find those that started around 362-365 days ago
        
        $this->info('Looking for subscriptions that will renew around ' . $renewalDate->format('Y-m-d'));
        
        // Get active subscriptions
        $subscriptions = Subscription::where('stripe_status', 'active')
            ->whereNull('ends_at')
            ->get();
        
        $count = 0;
        
        foreach ($subscriptions as $subscription) {
            // Skip if no user found
            if (!$subscription->user) {
                continue;
            }
            
            // Calculate next renewal date based on created_at and billing cycle
            $createdAt = Carbon::parse($subscription->created_at);
            $nextRenewalDate = null;
            
            if ($subscription->billing_cycle === 'monthly') {
                // Calculate next monthly renewal
                $nextRenewalDate = $createdAt->copy()->addMonthsNoOverflow(
                    Carbon::now()->diffInMonths($createdAt) + 1
                );
            } else {
                // Calculate next yearly renewal
                $nextRenewalDate = $createdAt->copy()->addYearsNoOverflow(
                    Carbon::now()->diffInYears($createdAt) + 1
                );
            }
            
            // Check if renewal date is within the 3-day window
            if ($nextRenewalDate->between(
                Carbon::now()->addDays(2),
                Carbon::now()->addDays(4)
            )) {
                // Send renewal notification
                $subscription->user->notify(new SubscriptionRenewalNotification(
                    $subscription,
                    $nextRenewalDate
                ));
                
                $this->info("Sent renewal reminder to {$subscription->user->email} for subscription {$subscription->id}");
                $count++;
            }
        }
        
        $this->info("Sent {$count} subscription renewal reminders");
        
        return Command::SUCCESS;
    }
}
