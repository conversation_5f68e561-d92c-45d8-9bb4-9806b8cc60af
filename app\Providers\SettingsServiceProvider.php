<?php

namespace App\Providers;

use App\Models\Setting;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only run if the settings table exists and we're not in the console
        if (Schema::hasTable('settings') && !$this->app->runningInConsole()) {
            // Load all settings
            $settings = Setting::all();

            // Apply settings to config
            foreach ($settings as $setting) {
                $this->applySettingToConfig($setting);
            }
        }
    }

    /**
     * Apply a setting to the configuration.
     *
     * @param \App\Models\Setting $setting
     * @return void
     */
    protected function applySettingToConfig(Setting $setting): void
    {
        // Map setting keys to config keys
        $configMap = [
            // General settings
            'app_name' => 'app.name',
            'app_description' => 'app.description',
            'logo' => 'app.logo',
            'favicon' => 'app.favicon',
            'footer_text' => 'app.footer_text',
            'header_text' => 'app.header_text',
            
            // Email settings
            'mail_mailer' => 'mail.default',
            'mail_host' => 'mail.mailers.smtp.host',
            'mail_port' => 'mail.mailers.smtp.port',
            'mail_username' => 'mail.mailers.smtp.username',
            'mail_password' => 'mail.mailers.smtp.password',
            'mail_encryption' => 'mail.mailers.smtp.encryption',
            'mail_from_address' => 'mail.from.address',
            'mail_from_name' => 'mail.from.name',
            
            // Payment settings
            'stripe_key' => 'services.stripe.key',
            'stripe_secret' => 'services.stripe.secret',
            'stripe_webhook_secret' => 'services.stripe.webhook.secret',
            'stripe_price_base_monthly' => 'services.stripe.prices.base_monthly',
            'stripe_price_premium_monthly' => 'services.stripe.prices.premium_monthly',
            'stripe_price_base_yearly' => 'services.stripe.prices.base_yearly',
            'stripe_price_premium_yearly' => 'services.stripe.prices.premium_yearly',
            
            // System settings
            'timezone' => 'app.timezone',
            'date_format' => 'app.date_format',
            'time_format' => 'app.time_format',
            'currency' => 'app.currency',
            'currency_symbol' => 'app.currency_symbol',
            'maintenance_mode' => 'app.maintenance_mode',
        ];

        // If the setting key exists in our map, apply it to the config
        if (isset($configMap[$setting->key])) {
            Config::set($configMap[$setting->key], $setting->value);
        }
    }
}
