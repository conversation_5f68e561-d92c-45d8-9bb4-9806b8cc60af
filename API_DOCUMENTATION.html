<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketWatch API Documentation</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f8fafc;
            color: #222;
        }

        .api-nav {
            margin-bottom: 32px;
        }

        .api-nav .nav-link {
            color: #4f46e5;
            font-weight: 500;
        }

        .api-nav .nav-link.active,
        .api-nav .nav-link:hover {
            color: #fff;
            background: #4f46e5;
        }

        .endpoint {
            margin-bottom: 32px;
        }

        .endpoint .card-header {
            background: #e0e7ff;
        }

        .method {
            font-size: 0.98em;
            font-weight: bold;
            padding: 2px 10px;
            border-radius: 3px;
            margin-right: 10px;
            color: #fff;
        }

        .method-get {
            background: #22c55e;
        }

        .method-post {
            background: #2563eb;
        }

        .method-put {
            background: #f59e42;
        }

        .method-delete {
            background: #ef4444;
        }

        .endpoint-url {
            font-family: monospace;
            font-size: 1.08em;
            color: #222;
            background: #e0e7ff;
            padding: 2px 7px;
            border-radius: 3px;
            margin-right: 4px;
        }

        pre.code {
            background: #23272f;
            color: #e0e7ff;
            border-radius: 5px;
            padding: 10px;
            font-size: 0.98em;
            overflow-x: auto;
            border: 1px solid #1e293b;
            margin-bottom: 0;
        }

        .status {
            display: inline-block;
            font-size: 0.93em;
            font-weight: 600;
            margin-right: 8px;
            padding: 2px 8px;
            border-radius: 3px;
        }

        .status-200 {
            background: #22c55e;
            color: #fff;
        }

        .status-201 {
            background: #2563eb;
            color: #fff;
        }

        .status-400,
        .status-422 {
            background: #f59e42;
            color: #fff;
        }

        .status-401,
        .status-403 {
            background: #ef4444;
            color: #fff;
        }

        .status-404 {
            background: #a1a1aa;
            color: #fff;
        }

        .status-500 {
            background: #222;
            color: #fff;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="text-center my-4">PocketWatch API Documentation</h1>
        <div class="alert alert-info text-center" role="alert">
            All endpoints require authentication unless noted. <strong>All paths below omit the /api prefix.</strong>
        </div>
        <!-- Navigation (Bootstrap) -->
        <ul class="nav nav-pills api-nav justify-content-center mb-4">
            <li class="nav-item"><a class="nav-link" href="#auth">Authentication</a></li>
            <li class="nav-item"><a class="nav-link" href="#email-verification">Email Verification</a></li>
            <li class="nav-item"><a class="nav-link" href="#password-reset">Password Reset</a></li>
            <li class="nav-item"><a class="nav-link" href="#google-oauth">Google OAuth</a></li>
            <li class="nav-item"><a class="nav-link" href="#user">User Profile</a></li>
            <li class="nav-item"><a class="nav-link" href="#bins">Bins</a></li>
            <li class="nav-item"><a class="nav-link" href="#sub-bins">Sub-Bins</a></li>
            <li class="nav-item"><a class="nav-link" href="#transactions">Transactions</a></li>
            <li class="nav-item"><a class="nav-link" href="#subscriptions">Subscriptions</a></li>
            <li class="nav-item"><a class="nav-link" href="#crypto-wallets">Crypto Wallets</a></li>
            <li class="nav-item"><a class="nav-link" href="#notification-settings">Notifications</a></li>
            <li class="nav-item"><a class="nav-link" href="#plaid">Plaid</a></li>
            <li class="nav-item"><a class="nav-link" href="#plaid-payment">Plaid Payment</a></li>
            <li class="nav-item"><a class="nav-link" href="#reports">Reports</a></li>
            <li class="nav-item"><a class="nav-link" href="#webhooks">Webhooks</a></li>
            <li class="nav-item"><a class="nav-link" href="#packages">Packages</a></li>
        </ul>
        <h2 id="auth">Authentication</h2>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/register</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Register a new user account.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Request Parameters</div>
                    <ul class="param-list">
                        <li><b>name</b> (string, required)</li>
                        <li><b>email</b> (string, required)</li>
                        <li><b>password</b> (string, required)</li>
                        <li><b>password_confirmation</b> (string, required)</li>
                        <li><b>country_code</b> (string, required)</li>
                        <li><b>phone_number</b> (string, required)</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "message": "User registered successfully",
    "user": { ... },
    "token": "..."
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-201">201 Created</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/login</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Authenticate user and receive access token.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Request Parameters</div>
                    <ul class="param-list">
                        <li><b>email</b> (string, required)</li>
                        <li><b>password</b> (string, required)</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "message": "Login successful",
    "token": "..."
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/forgot-password</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Send password reset email to user.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/reset-password</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Reset user password using reset token.</div>
            </div>
        </div>
        <h2 id="email-verification">Email Verification</h2>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/email/send-verification-code</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Send email verification code.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/email/verify</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Verify email with code.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/email/resend-verification-code</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Resend email verification code.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/email/check-verification-status</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Check if email is verified.</div>
            </div>
        </div>
        <h2 id="password-reset">Password Reset</h2>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/password/send-reset-code</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Send password reset code to user email.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/password/verify-reset-code</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Verify password reset code.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/password/reset-with-code</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Reset password using code.</div>
            </div>
        </div>
        <h2 id="google-oauth">Google OAuth</h2>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/auth/google</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Redirect to Google for OAuth login.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/auth/google/callback</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Handle Google OAuth callback.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/auth/google/token</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Login with Google token.</div>
            </div>
        </div>
        <h2 id="user">User Profile & Authenticated Actions</h2>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/me</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get current authenticated user info.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/logout</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Logout the authenticated user.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-put">PUT</span>
                <span class="endpoint-url">/profile</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Update user profile.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-put">PUT</span>
                <span class="endpoint-url">/password</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Change user password.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/avatar</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Update user avatar.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/auth/google/unlink</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Unlink Google account from user.</div>
            </div>
        </div>
        <h2 id="bins">Bins</h2>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/bins</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get all bins for the user.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/bins/income</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get all income bins for the user.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/bins/expense</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get all expense bins for the user.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/bins</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Create a new bin.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/bins/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get a bin by ID.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-put">PUT</span>
                <span class="endpoint-url">/bins/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Update a bin by ID.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-delete">DELETE</span>
                <span class="endpoint-url">/bins/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Delete a bin by ID.</div>
            </div>
        </div>
        <h2 id="sub-bins">Sub-Bins</h2>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/bins/{binId}/sub-bins</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get all sub-bins for a bin.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/bins/{binId}/sub-bins</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Create a sub-bin for a bin.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/bins/{binId}/sub-bins/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get a sub-bin by ID.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-put">PUT</span>
                <span class="endpoint-url">/bins/{binId}/sub-bins/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Update a sub-bin by ID.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-delete">DELETE</span>
                <span class="endpoint-url">/bins/{binId}/sub-bins/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Delete a sub-bin by ID.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/bins/{binId}/sub-bins-hierarchy</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get hierarchical sub-bin structure for a bin.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/bins/{binId}/sub-bins/{parentSubBinId}/nested</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Create a nested sub-bin under a parent sub-bin.</div>
            </div>
        </div>
        <h2 id="transactions">Transactions</h2>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/transactions</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get all transactions for the user.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/transactions</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Create a new transaction.</div>
            </div>
        </div>
        <div class="endpoint card">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/transactions/stats</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc">Get transaction statistics for the user.</div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/transactions/by-bin/{binId}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Get transactions for a specific bin.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>binId</b> (integer, required) - The ID of the bin</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "transactions": [ ... ]
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/transactions/by-category/{category}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Get transactions by category.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>category</b> (string, required) - The category name</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "transactions": [ ... ]
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/transactions/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Get a transaction by ID.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>id</b> (integer, required) - The transaction ID</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "transaction": { ... }
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-put">PUT</span>
                <span class="endpoint-url">/transactions/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Update a transaction by ID.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>id</b> (integer, required) - The transaction ID</li>
                    </ul>
                    <div class="params-title mb-1 mt-2">Request Body</div>
                    <ul class="param-list">
                        <li><b>amount</b> (number, optional)</li>
                        <li><b>description</b> (string, optional)</li>
                        <li><b>category</b> (string, optional)</li>
                        <li><b>bin_id</b> (integer, optional)</li>
                        <li><b>date</b> (string, optional, format: YYYY-MM-DD)</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "transaction": { ... }
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-delete">DELETE</span>
                <span class="endpoint-url">/transactions/{id}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Delete a transaction by ID.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>id</b> (integer, required) - The transaction ID</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "message": "Transaction deleted successfully."
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                    </div>
                </div>
            </div>
        </div>
        <h2 id="subscriptions">Subscriptions</h2>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/subscriptions</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Get all subscriptions for the user.</div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "subscriptions": [ ... ]
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Create a new subscription.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Request Body</div>
                    <ul class="param-list">
                        <li><b>plan_id</b> (string, required) - The ID of the subscription plan</li>
                        <li><b>payment_method_id</b> (string, required) - The payment method identifier</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "subscription": { ... }
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-201">201 Created</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions/select-plan</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Select a subscription plan.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Request Body</div>
                    <ul class="param-list">
                        <li><b>plan_id</b> (string, required) - The ID of the plan to select</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "message": "Plan selected successfully."
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions/add-payment-method</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Add payment method and start trial.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Request Body</div>
                    <ul class="param-list">
                        <li><b>payment_method_id</b> (string, required) - The payment method identifier</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "trial": true
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions/start-trial-test</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Start a trial subscription (test).</div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "trial": true
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions/cancel</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Cancel a subscription.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Request Body</div>
                    <ul class="param-list">
                        <li><b>subscription_id</b> (string, required) - The subscription identifier</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "message": "Subscription cancelled."
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions/connect-stripe</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Connect Stripe for billing.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Request Body</div>
                    <ul class="param-list">
                        <li><b>stripe_account_id</b> (string, required) - The Stripe account identifier</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "message": "Stripe connected."
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions/setup-auto-billing</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Setup automatic billing for subscription.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Request Body</div>
                    <ul class="param-list">
                        <li><b>subscription_id</b> (string, required) - The subscription identifier</li>
                        <li><b>auto_billing</b> (boolean, required) - Enable or disable auto billing</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "auto_billing": true
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/subscriptions/plans</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Get available subscription plans.</div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "plans": [ ... ]
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/subscriptions/history</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Get subscription history for the user.</div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "history": [ ... ]
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-get">GET</span>
                <span class="endpoint-url">/subscriptions/{uuid}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Get a subscription by UUID.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>uuid</b> (string, required) - The subscription UUID</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "subscription": { ... }
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-put">PUT</span>
                <span class="endpoint-url">/subscriptions/{uuid}</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Update a subscription by UUID.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>uuid</b> (string, required) - The subscription UUID</li>
                    </ul>
                    <div class="params-title mb-1 mt-2">Request Body</div>
                    <ul class="param-list">
                        <li><b>plan_id</b> (string, optional)</li>
                        <li><b>status</b> (string, optional)</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "subscription": { ... }
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-put">PUT</span>
                <span class="endpoint-url">/subscriptions/{uuid}/change-cycle</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Change billing cycle for a subscription.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>uuid</b> (string, required) - The subscription UUID</li>
                    </ul>
                    <div class="params-title mb-1 mt-2">Request Body</div>
                    <ul class="param-list">
                        <li><b>cycle</b> (string, required) - The new billing cycle (e.g., monthly, yearly)</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "cycle": "monthly"
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                        <span class="status status-422">422 Validation Error</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions/{uuid}/pause</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Pause a subscription.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                        <li><b>uuid</b> (string, required) - The subscription UUID</li>
                    </ul>
                </div>
                <div class="mb-2">
                    <div class="response-title mb-1">Response</div>
                    <pre class="code">{
    "success": true,
    "message": "Subscription paused."
}</pre>
                    <div class="status-codes mt-2">
                        <span class="status status-200">200 OK</span>
                        <span class="status status-401">401 Unauthorized</span>
                        <span class="status status-404">404 Not Found</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card endpoint mb-4">
            <div class="card-header">
                <span class="method method-post">POST</span>
                <span class="endpoint-url">/subscriptions/{uuid}/resume</span>
            </div>
            <div class="card-body">
                <div class="endpoint-desc mb-2">Resume a paused subscription.</div>
                <div class="mb-2">
                    <div class="params-title mb-1">Path Parameters</div>
                    <ul class="param-list">
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Delete a report by ID.</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-get">GET</span>
                    <span class="endpoint-url">/reports/{id}/download</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Download a report by ID.</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-post">POST</span>
                    <span class="endpoint-url">/reports/{id}/regenerate</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Regenerate a report by ID.</div>
                </div>
            </div>
            <h2 id="webhooks">Webhooks</h2>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-post">POST</span>
                    <span class="endpoint-url">/webhook/stripe</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Handle Stripe webhook events for subscription and payment updates.</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-post">POST</span>
                    <span class="endpoint-url">/webhook/plaid</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Handle Plaid webhook events.</div>
                </div>
            </div>
            <h2 id="packages">Packages (Legacy & Simple Stripe)</h2>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-get">GET</span>
                    <span class="endpoint-url">/packages</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Get available packages (legacy).</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-post">POST</span>
                    <span class="endpoint-url">/packages/purchase</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Purchase a package (legacy).</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-post">POST</span>
                    <span class="endpoint-url">/packages/purchase-test</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Test purchase of a package (legacy).</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-get">GET</span>
                    <span class="endpoint-url">/packages-simple</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Get available packages (simple Stripe integration).</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-post">POST</span>
                    <span class="endpoint-url">/packages-simple/stripe-url</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Generate Stripe URL for simple package purchase.</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-get">GET</span>
                    <span class="endpoint-url">/payment-simple-cancel</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Handle Stripe payment cancel (simple Stripe integration).</div>
                </div>
            </div>
            <div class="endpoint card">
                <div class="card-header">
                    <span class="method method-get">GET</span>
                    <span class="endpoint-url">/payment-simple-success</span>
                </div>
                <div class="card-body">
                    <div class="endpoint-desc">Handle Stripe payment success (simple Stripe integration).</div>
                </div>
            </div>
        </div>
</body>

</html>