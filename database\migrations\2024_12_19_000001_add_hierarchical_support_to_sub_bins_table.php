<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sub_bins', function (Blueprint $table) {
            // Add parent sub-bin relationship for hierarchical structure
            $table->foreignId('parent_sub_bin_id')->nullable()->after('bin_id')->constrained('sub_bins')->onDelete('cascade');
            
            // Add depth level for easier querying and validation
            $table->integer('depth_level')->default(1)->after('parent_sub_bin_id')->comment('1 = direct child of bin, 2+ = nested sub-bin');
            
            // Add path for easier tree operations (stores the full path like "1/5/12")
            $table->string('path')->nullable()->after('depth_level')->comment('Full path from root bin to this sub-bin');
            
            // Add index for better performance on hierarchical queries
            $table->index(['bin_id', 'parent_sub_bin_id']);
            $table->index(['depth_level']);
            $table->index(['path']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sub_bins', function (Blueprint $table) {
            $table->dropForeign(['parent_sub_bin_id']);
            $table->dropIndex(['bin_id', 'parent_sub_bin_id']);
            $table->dropIndex(['depth_level']);
            $table->dropIndex(['path']);
            $table->dropColumn(['parent_sub_bin_id', 'depth_level', 'path']);
        });
    }
};
