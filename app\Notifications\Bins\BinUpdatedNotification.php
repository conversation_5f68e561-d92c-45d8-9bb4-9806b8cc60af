<?php

namespace App\Notifications\Bins;

use App\Models\Bin;
use App\Notifications\BaseNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class BinUpdatedNotification extends BaseNotification implements ShouldQueue
{
    /**
     * The bin instance.
     *
     * @var \App\Models\Bin
     */
    protected $bin;

    /**
     * The fields that were updated.
     *
     * @var array
     */
    protected $updatedFields;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Bin  $bin
     * @param  array  $updatedFields
     * @return void
     */
    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_bin_operations';

    public function __construct(Bin $bin, array $updatedFields = [])
    {
        $this->bin = $bin;
        $this->updatedFields = $updatedFields;

        $this->subject = 'Financial Bin Updated';
        $this->title = 'Bin Updated';
        $this->content = 'Your financial bin has been successfully updated in your PocketWatch account.';
        
        $this->detailsTitle = 'Updated Bin Details';
        $this->details = [
            'Name' => $this->bin->name,
            'Description' => $this->bin->description ?? 'No description',
            'Minimum Threshold' => $this->formatCurrency($this->bin->threshold_min, $this->bin->currency),
        ];
        
        if ($this->bin->threshold_max) {
            $this->details['Maximum Threshold'] = $this->formatCurrency($this->bin->threshold_max, $this->bin->currency);
        }
        
        if (!empty($this->updatedFields)) {
            $this->details['Updated Fields'] = implode(', ', $this->updatedFields);
        }
        
        $this->actionText = 'View Bin';
        $this->actionUrl = url('/bins/' . $this->bin->id);
        
        $this->closing = 'Keep your financial bins up to date for better financial management.';
        $this->signature = 'The PocketWatch Team';
    }
    
    /**
     * Format currency value.
     *
     * @param  float  $value
     * @param  string  $currency
     * @return string
     */
    protected function formatCurrency($value, $currency = 'USD')
    {
        $currency = $currency ?? 'USD';
        $symbol = $this->getCurrencySymbol($currency);
        
        return $symbol . number_format($value, 2);
    }
    
    /**
     * Get currency symbol.
     *
     * @param  string  $currency
     * @return string
     */
    protected function getCurrencySymbol($currency)
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
        ];
        
        return $symbols[$currency] ?? $currency;
    }
}
