<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check what columns exist in the users table
        $columns = Schema::getColumnListing('users');
        $this->command->info('Available columns in users table: ' . implode(', ', $columns));

        // Create admin user with only basic fields
        $adminData = [
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ];

        // Add optional fields if they exist
        if (in_array('is_admin', $columns)) {
            $adminData['is_admin'] = true;
        }
        if (in_array('is_active', $columns)) {
            $adminData['is_active'] = true;
        }
        if (in_array('subscription_tier', $columns)) {
            $adminData['subscription_tier'] = 'none';
        }

        User::create($adminData);

        // Create regular user (for trial system testing)
        $userData = [
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ];

        // Add optional fields if they exist
        if (in_array('is_admin', $columns)) {
            $userData['is_admin'] = false;
        }
        if (in_array('is_active', $columns)) {
            $userData['is_active'] = true;
        }
        if (in_array('subscription_tier', $columns)) {
            $userData['subscription_tier'] = 'none'; // Ready for trial system
        }

        User::create($userData);

        // Create base subscriber (for testing subscriptions)
        $baseData = [
            'name' => 'Base Subscriber',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ];

        // Add optional fields if they exist
        if (in_array('is_admin', $columns)) {
            $baseData['is_admin'] = false;
        }
        if (in_array('is_active', $columns)) {
            $baseData['is_active'] = true;
        }
        if (in_array('subscription_tier', $columns)) {
            $baseData['subscription_tier'] = 'base'; // Has base subscription
        }

        User::create($baseData);

        // Create premium subscriber (for testing premium features)
        $premiumData = [
            'name' => 'Premium Subscriber',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ];

        // Add optional fields if they exist
        if (in_array('is_admin', $columns)) {
            $premiumData['is_admin'] = false;
        }
        if (in_array('is_active', $columns)) {
            $premiumData['is_active'] = true;
        }
        if (in_array('subscription_tier', $columns)) {
            $premiumData['subscription_tier'] = 'premium'; // Has premium subscription
        }

        User::create($premiumData);

        // Output success message
        $this->command->info('Users created successfully with available columns!');
    }
}
