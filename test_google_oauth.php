<?php

/**
 * Simple Google OAuth Test Script for PocketWatch API
 * 
 * This script tests the Google OAuth endpoints without needing Postman
 * Run with: php test_google_oauth.php
 */

// Configuration
$baseUrl = 'http://127.0.0.1:8000/api';
$testEmail = '<EMAIL>'; // Change this to your test email

echo "🔐 PocketWatch Google OAuth Test Script\n";
echo "=====================================\n\n";

// Test 1: Get Google OAuth URL
echo "Test 1: Getting Google OAuth URL...\n";
$response = makeRequest('GET', $baseUrl . '/auth/google');

if ($response && isset($response['success']) && $response['success']) {
    echo "✅ SUCCESS: Google OAuth URL generated\n";
    echo "🔗 Redirect URL: " . $response['redirect_url'] . "\n\n";
    
    echo "📋 Next Steps:\n";
    echo "1. Copy the URL above\n";
    echo "2. Open it in your browser\n";
    echo "3. Sign in with Google\n";
    echo "4. Copy the 'code' parameter from the callback URL\n";
    echo "5. Run: php test_google_oauth.php AUTHORIZATION_CODE\n\n";
} else {
    echo "❌ FAILED: Could not generate Google OAuth URL\n";
    if ($response) {
        echo "Error: " . json_encode($response, JSON_PRETTY_PRINT) . "\n";
    }
    echo "\n";
}

// Test 2: Handle OAuth callback (if authorization code provided)
if (isset($argv[1])) {
    $authCode = $argv[1];
    echo "Test 2: Testing OAuth callback with authorization code...\n";
    
    $response = makeRequest('GET', $baseUrl . '/auth/google/callback?code=' . urlencode($authCode));
    
    if ($response && isset($response['success']) && $response['success']) {
        echo "✅ SUCCESS: User authenticated via Google\n";
        echo "👤 User: " . $response['user']['name'] . " (" . $response['user']['email'] . ")\n";
        echo "🔑 Token: " . substr($response['token'], 0, 20) . "...\n";
        echo "📧 Email Verified: " . ($response['user']['email_verified_at'] ? 'Yes' : 'No') . "\n";
        echo "🎯 Subscription Tier: " . $response['user']['subscription_tier'] . "\n\n";
        
        // Save token for further testing
        $token = $response['token'];
        
        // Test 3: Check user profile
        echo "Test 3: Checking user profile...\n";
        $profileResponse = makeRequest('GET', $baseUrl . '/me', [], $token);
        
        if ($profileResponse && isset($profileResponse['id'])) {
            echo "✅ SUCCESS: Profile retrieved\n";
            echo "👤 Name: " . $profileResponse['name'] . "\n";
            echo "📧 Email: " . $profileResponse['email'] . "\n";
            echo "🔗 Provider: " . $profileResponse['provider'] . "\n\n";
        } else {
            echo "❌ FAILED: Could not retrieve profile\n";
            if ($profileResponse) {
                echo "Error: " . json_encode($profileResponse, JSON_PRETTY_PRINT) . "\n";
            }
            echo "\n";
        }
        
        // Test 4: View packages
        echo "Test 4: Viewing available packages...\n";
        $packagesResponse = makeRequest('GET', $baseUrl . '/packages', [], $token);
        
        if ($packagesResponse && isset($packagesResponse['success']) && $packagesResponse['success']) {
            echo "✅ SUCCESS: Packages retrieved\n";
            echo "📦 Available packages: " . count($packagesResponse['data']['packages']) . "\n";
            foreach ($packagesResponse['data']['packages'] as $package) {
                echo "   - " . $package['name'] . " ($" . $package['price'] . "/month)\n";
            }
            echo "\n";
        } else {
            echo "❌ FAILED: Could not retrieve packages\n";
            if ($packagesResponse) {
                echo "Error: " . json_encode($packagesResponse, JSON_PRETTY_PRINT) . "\n";
            }
            echo "\n";
        }
        
        // Test 5: Purchase package (test mode)
        echo "Test 5: Purchasing base package (test mode)...\n";
        $purchaseResponse = makeRequest('POST', $baseUrl . '/packages/purchase-test', [
            'package_id' => 'base_monthly'
        ], $token);
        
        if ($purchaseResponse && isset($purchaseResponse['success']) && $purchaseResponse['success']) {
            echo "✅ SUCCESS: Package purchased\n";
            echo "📦 Package: " . $purchaseResponse['data']['subscription']['name'] . "\n";
            echo "💰 Price: $" . $purchaseResponse['data']['subscription']['price'] . "\n";
            echo "🎯 Tier: " . $purchaseResponse['data']['subscription']['subscription_tier'] . "\n";
            echo "⏰ Trial ends: " . $purchaseResponse['data']['subscription']['trial_ends_at'] . "\n\n";
        } else {
            echo "❌ FAILED: Could not purchase package\n";
            if ($purchaseResponse) {
                echo "Error: " . json_encode($purchaseResponse, JSON_PRETTY_PRINT) . "\n";
            }
            echo "\n";
        }
        
        // Test 6: Check updated profile
        echo "Test 6: Checking updated profile after purchase...\n";
        $updatedProfileResponse = makeRequest('GET', $baseUrl . '/me', [], $token);
        
        if ($updatedProfileResponse && isset($updatedProfileResponse['id'])) {
            echo "✅ SUCCESS: Updated profile retrieved\n";
            echo "🎯 Subscription Tier: " . $updatedProfileResponse['subscription_tier'] . "\n";
            echo "⏰ Trial Started: " . ($updatedProfileResponse['trial_started_at'] ?: 'Not started') . "\n";
            echo "📅 Trial Days Remaining: " . ($updatedProfileResponse['trial_days_remaining'] ?: 'N/A') . "\n\n";
        } else {
            echo "❌ FAILED: Could not retrieve updated profile\n";
            echo "\n";
        }
        
        echo "🎉 Google OAuth Integration Test Complete!\n";
        echo "✅ All tests passed - Google authentication is working correctly.\n\n";
        
    } else {
        echo "❌ FAILED: OAuth callback failed\n";
        if ($response) {
            echo "Error: " . json_encode($response, JSON_PRETTY_PRINT) . "\n";
        }
        echo "\n";
    }
}

// Test configuration
echo "🔧 Configuration Check:\n";
echo "Base URL: $baseUrl\n";
echo "Make sure your Laravel server is running: php artisan serve\n";
echo "Make sure Google OAuth is configured in your .env file\n\n";

/**
 * Make HTTP request
 */
function makeRequest($method, $url, $data = [], $token = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if (!empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_error($ch)) {
        echo "CURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return null;
    }
    
    curl_close($ch);
    
    $decoded = json_decode($response, true);
    
    if ($httpCode >= 400) {
        echo "HTTP Error $httpCode: $response\n";
    }
    
    return $decoded;
}

echo "📚 For more detailed testing, see: Google_OAuth_Testing_Guide.md\n";
echo "🌐 For API documentation, open: PocketWatch_API_Documentation.html\n";
?>
