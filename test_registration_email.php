<?php

// This script tests the registration email functionality

// Load the Lara<PERSON> application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Notifications\Auth\WelcomeNotification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

// Output header
echo "=== PocketWatch Registration Email Test ===\n\n";

// Check mail configuration
echo "Mail Configuration:\n";
echo "- Driver: " . config('mail.default') . "\n";
echo "- Host: " . config('mail.mailers.smtp.host') . "\n";
echo "- Port: " . config('mail.mailers.smtp.port') . "\n";
echo "- Username: " . config('mail.mailers.smtp.username') . "\n";
echo "- From Address: " . config('mail.from.address') . "\n";
echo "- From Name: " . config('mail.from.name') . "\n\n";

// Create a test user
$testEmail = 'test_' . time() . '@example.com';
echo "Creating test user with email: {$testEmail}\n";

try {
    // Create the user
    $user = User::create([
        'name' => 'Test User',
        'email' => $testEmail,
        'password' => Hash::make('password'),
        'country_code' => '+1',
        'phone_number' => '1234567890',
        'subscription_tier' => 'trial',
    ]);
    
    echo "User created successfully with ID: {$user->id}\n";
    
    // Send welcome notification directly
    echo "Sending welcome notification...\n";
    $user->notify(new WelcomeNotification());
    
    echo "Welcome notification queued successfully.\n";
    
    // Process the queue
    echo "Processing the queue...\n";
    Illuminate\Support\Facades\Artisan::call('queue:work', [
        '--stop-when-empty' => true,
        '--tries' => 3
    ]);
    
    $output = Illuminate\Support\Facades\Artisan::output();
    echo $output . "\n";
    
    echo "Test completed successfully!\n";
} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Test ===\n";
