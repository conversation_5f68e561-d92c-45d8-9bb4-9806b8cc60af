<?php

/**
 * Test script for updated Sub-Bin Store API
 * Tests the new flexible threshold management
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\Bin;
use App\Models\SubBin;

echo "🧪 Testing Updated Sub-Bin Store API\n";
echo "===================================\n\n";

// Find or create a test user
$testUser = User::where('email', '<EMAIL>')->first();
if (!$testUser) {
    $testUser = User::create([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'email_verified_at' => now(),
    ]);
    echo "✅ Created test user: {$testUser->name}\n\n";
} else {
    echo "✅ Using existing test user: {$testUser->name}\n\n";
}

// Create a test bin
$testBin = Bin::create([
    'user_id' => $testUser->id,
    'name' => 'Test Parent Bin',
    'type' => 'income',
    'description' => 'Test bin for sub-bin creation',
    'threshold_max_limit' => 10000.00,
    'threshold_max_warning' => 8000.00,
    'current_amount' => 0.00,
    'currency' => 'USD',
    'is_active' => true,
]);

echo "📦 Created test bin: {$testBin->name} (ID: {$testBin->id})\n";
echo "💰 Bin type: {$testBin->type}\n\n";

// Test 1: Create sub-bin without any threshold limits (new feature)
echo "🧪 Test 1: Create Sub-Bin Without Threshold Limits\n";
echo "---------------------------------------------------\n";

$subBinData1 = [
    'name' => 'Emergency Fund - No Limits',
    // type will be auto-inherited from parent bin
    'description' => 'Sub-bin without any threshold limits',
    'currency' => 'USD',
];

try {
    $subBin1 = new SubBin($subBinData1);
    $subBin1->bin_id = $testBin->id;
    $subBin1->current_amount = 0;
    
    // Auto-select parent category
    $subBin1->type = $testBin->type;
    
    // Set default threshold values (new logic)
    $subBin1->threshold_max_limit = null; // User can set as they want
    $subBin1->threshold_max_warning = null; // No default warning
    
    $subBin1->depth_level = 1;
    $subBin1->save();
    
    echo "✅ Sub-bin created successfully without limits\n";
    echo "📊 Details:\n";
    echo "   - Name: {$subBin1->name}\n";
    echo "   - Type: {$subBin1->type} (inherited from parent)\n";
    echo "   - Max Limit: " . ($subBin1->threshold_max_limit ?? 'null (no limit)') . "\n";
    echo "   - Max Warning: " . ($subBin1->threshold_max_warning ?? 'null (no warning)') . "\n";
    echo "   - Current Amount: {$subBin1->current_amount}\n";
    echo "   - Depth Level: {$subBin1->depth_level}\n";
    
} catch (Exception $e) {
    echo "❌ Error creating sub-bin without limits: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Create sub-bin with custom threshold limits
echo "🧪 Test 2: Create Sub-Bin With Custom Threshold Limits\n";
echo "------------------------------------------------------\n";

$subBinData2 = [
    'name' => 'Vacation Fund - With Limits',
    'type' => 'income', // Explicitly set type
    'description' => 'Sub-bin with custom threshold limits',
    'threshold_max_limit' => 5000.00,
    'threshold_max_warning' => 4000.00,
    'currency' => 'USD',
];

try {
    $subBin2 = new SubBin($subBinData2);
    $subBin2->bin_id = $testBin->id;
    $subBin2->current_amount = 0;
    $subBin2->depth_level = 1;
    $subBin2->save();
    
    echo "✅ Sub-bin created successfully with custom limits\n";
    echo "📊 Details:\n";
    echo "   - Name: {$subBin2->name}\n";
    echo "   - Type: {$subBin2->type}\n";
    echo "   - Max Limit: {$subBin2->threshold_max_limit}\n";
    echo "   - Max Warning: {$subBin2->threshold_max_warning}\n";
    echo "   - Current Amount: {$subBin2->current_amount}\n";
    echo "   - Depth Level: {$subBin2->depth_level}\n";
    
} catch (Exception $e) {
    echo "❌ Error creating sub-bin with limits: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Create nested sub-bin without limits
echo "🧪 Test 3: Create Nested Sub-Bin Without Limits\n";
echo "------------------------------------------------\n";

$subBinData3 = [
    'name' => 'Medical Emergency - Nested',
    // type will be auto-inherited from parent sub-bin
    'description' => 'Nested sub-bin without limits',
    'currency' => 'USD',
];

try {
    $subBin3 = new SubBin($subBinData3);
    $subBin3->bin_id = $testBin->id;
    $subBin3->parent_sub_bin_id = $subBin1->id; // Nested under first sub-bin
    $subBin3->current_amount = 0;
    
    // Auto-select parent category from parent sub-bin
    $subBin3->type = $subBin1->type;
    
    // Set default threshold values
    $subBin3->threshold_max_limit = null;
    $subBin3->threshold_max_warning = null;
    
    $subBin3->depth_level = $subBin1->depth_level + 1;
    $subBin3->save();
    
    echo "✅ Nested sub-bin created successfully without limits\n";
    echo "📊 Details:\n";
    echo "   - Name: {$subBin3->name}\n";
    echo "   - Type: {$subBin3->type} (inherited from parent sub-bin)\n";
    echo "   - Parent Sub-Bin: {$subBin1->name}\n";
    echo "   - Max Limit: " . ($subBin3->threshold_max_limit ?? 'null (no limit)') . "\n";
    echo "   - Max Warning: " . ($subBin3->threshold_max_warning ?? 'null (no warning)') . "\n";
    echo "   - Current Amount: {$subBin3->current_amount}\n";
    echo "   - Depth Level: {$subBin3->depth_level}\n";
    
} catch (Exception $e) {
    echo "❌ Error creating nested sub-bin: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Verify cumulative balance calculation
echo "🧪 Test 4: Verify Cumulative Balance Calculation\n";
echo "------------------------------------------------\n";

$initialBalance = $testUser->calculateCumulativeBalance();
echo "💳 User cumulative balance: {$initialBalance}\n";

// Add some amounts to sub-bins
$subBin1->current_amount = 1000.00;
$subBin1->save();

$subBin2->current_amount = 2000.00;
$subBin2->save();

$subBin3->current_amount = 500.00;
$subBin3->save();

$newBalance = $testUser->calculateCumulativeBalance();
echo "💳 Updated cumulative balance: {$newBalance}\n";
echo "📈 Balance change: " . ($newBalance - $initialBalance) . "\n";

echo "\n";

// Test 5: Validation test - ensure nullable fields work
echo "🧪 Test 5: Validation Test - Nullable Fields\n";
echo "---------------------------------------------\n";

// Test creating sub-bin with only name (minimal required data)
$subBinData5 = [
    'name' => 'Minimal Sub-Bin',
];

try {
    $subBin5 = new SubBin($subBinData5);
    $subBin5->bin_id = $testBin->id;
    $subBin5->current_amount = 0;
    
    // Auto-select parent category
    $subBin5->type = $testBin->type;
    
    // All threshold fields should be nullable
    $subBin5->threshold_max_limit = null;
    $subBin5->threshold_max_warning = null;
    
    $subBin5->depth_level = 1;
    $subBin5->save();
    
    echo "✅ Minimal sub-bin created successfully\n";
    echo "📊 Details:\n";
    echo "   - Name: {$subBin5->name}\n";
    echo "   - Type: {$subBin5->type} (auto-inherited)\n";
    echo "   - Description: " . ($subBin5->description ?? 'null') . "\n";
    echo "   - Max Limit: " . ($subBin5->threshold_max_limit ?? 'null') . "\n";
    echo "   - Max Warning: " . ($subBin5->threshold_max_warning ?? 'null') . "\n";
    echo "   - Currency: {$subBin5->currency}\n";
    
} catch (Exception $e) {
    echo "❌ Error creating minimal sub-bin: " . $e->getMessage() . "\n";
}

echo "\n";

// Clean up test data
echo "🧹 Cleaning Up Test Data\n";
echo "------------------------\n";

try {
    // Delete sub-bins first (due to foreign key constraints)
    SubBin::where('bin_id', $testBin->id)->delete();
    echo "✅ Deleted test sub-bins\n";
    
    // Delete test bin
    $testBin->delete();
    echo "✅ Deleted test bin\n";
    
    // Recalculate user balance
    $finalBalance = $testUser->calculateCumulativeBalance();
    echo "💳 Final cumulative balance: {$finalBalance}\n";
    
} catch (Exception $e) {
    echo "❌ Error during cleanup: " . $e->getMessage() . "\n";
}

echo "\n🎯 Test Summary\n";
echo "===============\n";
echo "✅ Sub-bin creation without limits: Working\n";
echo "✅ Sub-bin creation with custom limits: Working\n";
echo "✅ Nested sub-bin creation: Working\n";
echo "✅ Parent category inheritance: Working\n";
echo "✅ Nullable threshold fields: Working\n";
echo "✅ Cumulative balance calculation: Working\n";
echo "✅ Minimal data validation: Working\n";

echo "\n🎉 All sub-bin store update tests completed successfully!\n";
echo "   The API now supports flexible threshold management:\n";
echo "   - Minimum value: 0 by default (current_amount)\n";
echo "   - Maximum value: User can set as they want (optional)\n";
echo "   - No required threshold limits for sub-bin creation\n";
