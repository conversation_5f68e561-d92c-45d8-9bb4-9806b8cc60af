<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Process;

class EnsureQueueIsRunning extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:ensure-running';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ensure the queue worker is running';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking if queue worker is running...');
        
        // Process any pending jobs
        $this->info('Processing pending jobs...');
        Artisan::call('queue:work', [
            '--stop-when-empty' => true,
            '--tries' => 3
        ]);
        
        $output = Artisan::output();
        $this->info($output);
        
        $this->info('Queue worker check completed.');
        
        return Command::SUCCESS;
    }
}
