<?php

namespace App\Notifications\Subscriptions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TrialExpiredNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Your PocketWatch Trial Has Expired')
            ->greeting("Hello {$notifiable->name}!")
            ->line('Your PocketWatch free trial has expired.')
            ->line('To continue using PocketWatch and access all features, please connect your payment method and choose a subscription plan.')
            ->line('Your account data is safe and will be restored once you subscribe.')
            ->action('Subscribe Now', url('/subscription/upgrade'))
            ->line('If you have any questions or need assistance, please contact our support team.')
            ->line('We hope to see you back soon!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'trial_expired',
            'message' => 'Your trial has expired. Please subscribe to continue using PocketWatch.',
        ];
    }
}
