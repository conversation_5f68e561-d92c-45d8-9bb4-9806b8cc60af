<?php

namespace App\Services;

use App\Models\Bin;
use App\Models\SubBin;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    /**
     * Maximum number of bins allowed for base/trial tier
     */
    const MAX_BINS_BASE_TIER = 3;

    /**
     * Maximum number of sub-bins per bin allowed for base/trial tier
     */
    const MAX_SUB_BINS_PER_BIN_BASE_TIER = 3;

    /**
     * Check if the user can create more bins based on their subscription tier
     *
     * @param User $user
     * @return bool
     */
    public function canCreateBin(User $user): bool
    {
        // Admin users have no restrictions
        if ($user->isAdmin()) {
            return true;
        }

        // Premium users have no restrictions
        if ($user->subscription_tier === 'premium') {
            return true;
        }

        // Base/trial tier users are limited to MAX_BINS_BASE_TIER bins
        $binCount = Bin::where('user_id', $user->id)->count();
        return $binCount < self::MAX_BINS_BASE_TIER;
    }

    /**
     * Check if the user can create more sub-bins for a specific bin based on their subscription tier
     *
     * @param User $user
     * @param int $binId
     * @return bool
     */
    public function canCreateSubBin(User $user, int $binId): bool
    {
        // Admin users have no restrictions
        if ($user->isAdmin()) {
            return true;
        }

        // Premium users have no restrictions
        if ($user->subscription_tier === 'premium') {
            return true;
        }

        // Base/trial tier users are limited to MAX_SUB_BINS_PER_BIN_BASE_TIER sub-bins per bin
        $subBinCount = SubBin::where('bin_id', $binId)->count();
        return $subBinCount < self::MAX_SUB_BINS_PER_BIN_BASE_TIER;
    }

    /**
     * Get the remaining bin count for the user
     *
     * @param User $user
     * @return int
     */
    public function getRemainingBinCount(User $user): int
    {
        if ($user->isAdmin() || $user->subscription_tier === 'premium') {
            return PHP_INT_MAX; // Unlimited
        }

        $binCount = Bin::where('user_id', $user->id)->count();
        return max(0, self::MAX_BINS_BASE_TIER - $binCount);
    }

    /**
     * Get the remaining sub-bin count for a specific bin
     *
     * @param User $user
     * @param int $binId
     * @return int
     */
    public function getRemainingSubBinCount(User $user, int $binId): int
    {
        if ($user->isAdmin() || $user->subscription_tier === 'premium') {
            return PHP_INT_MAX; // Unlimited
        }

        $subBinCount = SubBin::where('bin_id', $binId)->count();
        return max(0, self::MAX_SUB_BINS_PER_BIN_BASE_TIER - $subBinCount);
    }

    /**
     * Get the subscription upgrade message
     *
     * @param string $type
     * @return string
     */
    public function getUpgradeMessage(string $type = 'bin'): string
    {
        if ($type === 'bin') {
            return 'You have reached the maximum number of bins allowed for your current subscription. '
                . 'Please upgrade to Premium to create unlimited bins.';
        } else {
            return 'You have reached the maximum number of sub-bins allowed for your current subscription. '
                . 'Please upgrade to Premium to create unlimited sub-bins.';
        }
    }
}
