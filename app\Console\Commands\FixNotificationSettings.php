<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\NotificationSetting;
use Illuminate\Console\Command;

class FixNotificationSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:fix-settings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix notification settings for all users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing notification settings for all users...');
        
        $users = User::all();
        $count = 0;
        
        foreach ($users as $user) {
            $settings = $user->notificationSetting;
            
            if (!$settings) {
                // Create notification settings for user
                $settings = NotificationSetting::create([
                    'user_id' => $user->id,
                    'email_login' => true,
                    'email_profile_updates' => true,
                    'email_password_changes' => true,
                    'email_bin_operations' => true,
                    'email_transaction_operations' => true,
                    'email_subscription_updates' => true,
                    'email_threshold_alerts' => true,
                    'email_renewal_reminders' => true,
                    'email_marketing' => true,
                ]);
                
                $this->info("Created notification settings for user {$user->email}");
                $count++;
            } else {
                // Ensure all notification types are set
                $updated = false;
                $notificationTypes = [
                    'email_login',
                    'email_profile_updates',
                    'email_password_changes',
                    'email_bin_operations',
                    'email_transaction_operations',
                    'email_subscription_updates',
                    'email_threshold_alerts',
                    'email_renewal_reminders',
                    'email_marketing',
                ];
                
                foreach ($notificationTypes as $type) {
                    if (!isset($settings->$type)) {
                        $settings->$type = true;
                        $updated = true;
                    }
                }
                
                if ($updated) {
                    $settings->save();
                    $this->info("Updated notification settings for user {$user->email}");
                    $count++;
                }
            }
        }
        
        $this->info("Fixed notification settings for {$count} users");
        
        return Command::SUCCESS;
    }
}
