<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'group',
        'type',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'value' => 'json',
    ];

    /**
     * Get a setting value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        // Try to get from cache first
        $cacheKey = 'setting_' . $key;

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // If not in cache, get from database
        $setting = self::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        // Store in cache for future requests (cache for 24 hours)
        Cache::put($cacheKey, $setting->value, 60 * 24);

        return $setting->value;
    }

    /**
     * Set a setting value.
     *
     * @param string $key
     * @param mixed $value
     * @param string $group
     * @param string $type
     * @return \App\Models\Setting
     */
    public static function set(string $key, $value, string $group = 'general', string $type = 'string')
    {
        $setting = self::firstOrNew(['key' => $key]);
        $oldValue = $setting->exists ? $setting->value : null;

        $setting->value = $value;
        $setting->group = $group;
        $setting->type = $type;
        $setting->save();

        // Update cache
        $cacheKey = 'setting_' . $key;
        Cache::put($cacheKey, $value, 60 * 24);

        // Log the change if the setting_logs table exists
        if (Schema::hasTable('setting_logs')) {
            SettingLog::logChange($key, $oldValue, $value);
        }

        return $setting;
    }

    /**
     * Clear the settings cache.
     *
     * @return void
     */
    public static function clearCache()
    {
        $settings = self::all();

        foreach ($settings as $setting) {
            Cache::forget('setting_' . $setting->key);
        }
    }

    /**
     * Get all settings by group.
     *
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByGroup(string $group)
    {
        $cacheKey = 'settings_group_' . $group;

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $settings = self::where('group', $group)->get();

        // Cache for 24 hours
        Cache::put($cacheKey, $settings, 60 * 24);

        return $settings;
    }

    /**
     * Get all settings as an associative array.
     *
     * @return array
     */
    public static function getAllAsArray()
    {
        $cacheKey = 'settings_all_array';

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $settings = self::all();
        $result = [];

        foreach ($settings as $setting) {
            $result[$setting->key] = $setting->value;
        }

        // Cache for 24 hours
        Cache::put($cacheKey, $result, 60 * 24);

        return $result;
    }

    /**
     * Boot the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when a setting is updated
        static::saved(function ($setting) {
            // Clear specific setting cache
            Cache::forget('setting_' . $setting->key);

            // Clear group cache
            Cache::forget('settings_group_' . $setting->group);

            // Clear all settings cache
            Cache::forget('settings_all_array');
        });

        // Clear cache when a setting is deleted
        static::deleted(function ($setting) {
            // Clear specific setting cache
            Cache::forget('setting_' . $setting->key);

            // Clear group cache
            Cache::forget('settings_group_' . $setting->group);

            // Clear all settings cache
            Cache::forget('settings_all_array');
        });
    }
}
