<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;

class PlaidAccount extends Model
{
    use HasFactory, SoftDeletes, HasUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'institution_id',
        'institution_name',
        'account_id',
        'account_name',
        'account_type',
        'account_subtype',
        'account_mask',
        'access_token',
        'item_id',
        'is_default',
        'is_payment_enabled',
        'metadata',
        'last_synced_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_default' => 'boolean',
        'is_payment_enabled' => 'boolean',
        'metadata' => 'json',
        'last_synced_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'access_token',
        'item_id',
    ];

    /**
     * Get the user that owns the account.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the transactions for the account.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PlaidTransaction::class);
    }

    /**
     * Set the access token attribute with encryption.
     *
     * @param string $value
     * @return void
     */
    public function setAccessTokenAttribute($value): void
    {
        $this->attributes['access_token'] = $value ? Crypt::encryptString($value) : null;
    }

    /**
     * Get the access token attribute with decryption.
     *
     * @param string|null $value
     * @return string|null
     */
    public function getAccessTokenAttribute($value): ?string
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    /**
     * Set the item ID attribute with encryption.
     *
     * @param string $value
     * @return void
     */
    public function setItemIdAttribute($value): void
    {
        $this->attributes['item_id'] = $value ? Crypt::encryptString($value) : null;
    }

    /**
     * Get the item ID attribute with decryption.
     *
     * @param string|null $value
     * @return string|null
     */
    public function getItemIdAttribute($value): ?string
    {
        return $value ? Crypt::decryptString($value) : null;
    }
}
