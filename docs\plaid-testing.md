# Testing Plaid Integration in PocketWatch

This document provides instructions for testing the Plaid integration in PocketWatch without needing to connect to the actual Plaid API.

## Adding Dummy Plaid Accounts

There are several ways to add dummy Plaid accounts for testing:

### Method 1: Using the Web Interface

1. Log in to your PocketWatch account
2. Navigate to the Bank Accounts page
3. Click the "Add Dummy Accounts" button
4. Confirm the action when prompted

This will add three dummy accounts to your user:
- A checking account (payment-enabled, default)
- A savings account (payment-enabled)
- A credit card (not payment-enabled)

### Method 2: Using the API

You can add dummy accounts via the API:

```bash
curl -X POST http://127.0.0.1:8000/api/plaid/add-dummy-accounts \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Method 3: Using the Artisan Command

You can add dummy accounts for a specific user or all users using the Artisan command:

```bash
# Add dummy accounts for a specific user by ID
php artisan plaid:add-dummy-accounts 1

# Add dummy accounts for a specific user by email
php artisan plaid:add-dummy-accounts <EMAIL>

# Add dummy accounts for all users (interactive)
php artisan plaid:add-dummy-accounts
```

## Testing with Postman

A Postman collection is included for testing the Plaid API endpoints:

1. Import the collection from `public/PocketWatch_Plaid_API.postman_collection.json`
2. Create an environment with the following variables:
   - `base_url`: `http://127.0.0.1:8000`
   - `token`: (leave empty, will be filled automatically)
3. Run the "Login" request first to get a token
4. Run the "Add Dummy Accounts" request to add dummy accounts
5. Test the other endpoints as needed

## Available API Endpoints

### Plaid Accounts

- `GET /api/plaid/accounts` - Get all Plaid accounts
- `POST /api/plaid/add-dummy-accounts` - Add dummy Plaid accounts
- `GET /api/plaid/link-token` - Get a Plaid link token
- `POST /api/plaid/accounts/{id}/set-default` - Set a Plaid account as default
- `POST /api/plaid/accounts/{id}/toggle-payment` - Toggle payment capability
- `DELETE /api/plaid/accounts/{id}` - Delete a Plaid account

### Plaid Payments

- `GET /api/plaid-payment/accounts` - Get payment-enabled accounts
- `POST /api/plaid-payment/process` - Process a payment
- `GET /api/plaid-payment/status/{paymentId}` - Check payment status

## Testing Payment Processing

To test payment processing:

1. Add dummy accounts using one of the methods above
2. Get the ID of a payment-enabled account (checking or savings)
3. Make a POST request to `/api/plaid-payment/process` with:
   ```json
   {
       "account_id": 1,
       "package_id": "premium_monthly",
       "amount": 10.00
   }
   ```
4. Check the response for the payment ID
5. Check the payment status with `/api/plaid-payment/status/{paymentId}`

## Troubleshooting

If you encounter issues:

1. Check that your user has dummy accounts added
2. Verify that the account ID you're using exists and is payment-enabled
3. Check the Laravel logs for detailed error messages
4. Make sure your authentication token is valid

## Notes

- Dummy accounts are for testing only and do not connect to real bank accounts
- The payment processing is simulated and does not involve real money transfers
- In a production environment, you would need to use the actual Plaid API with valid credentials
