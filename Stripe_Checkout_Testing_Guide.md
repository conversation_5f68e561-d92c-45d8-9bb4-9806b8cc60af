# 🚀 PocketWatch GUI-Based Stripe Checkout Testing Guide

## 🎯 **GUI-Based Payment Flow (Recommended)**

### **New Improved Flow:**
1. User selects package → **Confirmation page with details**
2. User reviews and confirms → **Stripe checkout opens**
3. User pays on Stripe → **Automatic redirect back**
4. Trial starts automatically → **Done!**

### **Alternative Flows:**
- **Direct Flow:** User clicks package → Direct to Stripe (simple)
- **Legacy Flow:** Multi-step API calls (complex)

## 🎨 **GUI Flow Benefits:**
✅ **Better UX:** Users see exactly what they're buying
✅ **Trust Building:** Detailed confirmation page
✅ **Legal Compliance:** Terms acceptance
✅ **Reduced Abandonment:** Clear pricing and trial info
✅ **Professional Look:** Modern, responsive design

## 🔧 **Setup Requirements**

### **Step 1: Environment Configuration**

Add these to your `.env` file:

```env
# Stripe Configuration
STRIPE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Price IDs (create these in Stripe Dashboard)
STRIPE_PRICE_BASE_MONTHLY=price_your_base_monthly_id
STRIPE_PRICE_PREMIUM_MONTHLY=price_your_premium_monthly_id

# App URL for redirects
APP_URL=http://127.0.0.1:8000
```

### **Step 2: Create Stripe Products & Prices**

1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Switch to **Test Mode**
3. Go to **Products** → **Add Product**

**Base Monthly Product:**
- Name: "PocketWatch Base Monthly"
- Description: "Essential financial management tools"
- Price: $9.99 USD, Recurring monthly
- Copy the **Price ID** (starts with `price_`)

**Premium Monthly Product:**
- Name: "PocketWatch Premium Monthly"
- Description: "Advanced financial management with premium features"
- Price: $19.99 USD, Recurring monthly
- Copy the **Price ID**

### **Step 3: Configure Webhook**

1. Go to **Developers** → **Webhooks** → **Add endpoint**
2. Endpoint URL: `http://127.0.0.1:8000/api/webhook/stripe-checkout`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `invoice.payment_succeeded`
   - `customer.subscription.deleted`
4. Copy the **Webhook Secret**

## 🧪 **Testing Methods**

### **Method 1: GUI Test Page (Recommended)**

1. **Open the GUI test page:**
   ```
   file:///path/to/gui_package_selection.html
   ```

2. **Complete the flow:**
   - Sign in with test credentials or Google
   - Browse packages with detailed information
   - Click "Select Package" to see confirmation page
   - Review package details, pricing, and trial info
   - Check confirmation boxes
   - Click "Proceed to Secure Payment"
   - Complete payment on Stripe with test card

### **Method 2: Direct Checkout Test Page**

1. **Open the direct test page:**
   ```
   file:///path/to/stripe_checkout_test.html
   ```

2. **Login with credentials:**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Or use Google OAuth

3. **Select a package:**
   - Click "Buy Base Monthly" or "Buy Premium Monthly"
   - You'll be redirected to Stripe

4. **Test payment:**
   - Use test card: `4242 4242 4242 4242`
   - Any future expiry date
   - Any 3-digit CVC

### **Method 3: Postman Testing (GUI Flow)**

#### **Test 1: Get Packages for GUI**

**Request:**
```
GET http://127.0.0.1:8000/api/packages-gui
Authorization: Bearer YOUR_TOKEN
```

**Expected Response:**
```json
{
    "success": true,
    "data": {
        "packages": [
            {
                "id": "base_monthly",
                "name": "Base Monthly",
                "price": 9.99,
                "description": "Essential financial management tools for personal use",
                "features": [...],
                "popular": false,
                "recommended_for": "Individuals and small families"
            },
            {
                "id": "premium_monthly",
                "name": "Premium Monthly",
                "price": 19.99,
                "description": "Advanced financial management with premium features",
                "features": [...],
                "popular": true,
                "recommended_for": "Power users and businesses"
            }
        ],
        "user": {
            "id": 1,
            "name": "Test User",
            "email": "<EMAIL>"
        }
    }
}
```

#### **Test 2: Get Package Confirmation Details**

**Request:**
```
GET http://127.0.0.1:8000/api/packages-gui/base_monthly/confirm
Authorization: Bearer YOUR_TOKEN
```

**Expected Response:**
```json
{
    "success": true,
    "data": {
        "package": {
            "id": "base_monthly",
            "name": "Base Monthly",
            "price": 9.99,
            "features": [...],
            "description": "Essential financial management tools for personal use"
        },
        "user": {
            "id": 1,
            "name": "Test User",
            "email": "<EMAIL>"
        },
        "pricing": {
            "subtotal": 9.99,
            "tax": 0,
            "total": 9.99,
            "currency": "USD"
        },
        "trial_info": {
            "trial_days": 7,
            "trial_end_date": "2024-12-26",
            "first_billing_date": "2024-12-26",
            "trial_description": "Start your 7-day free trial today. You won't be charged until Dec 26, 2024."
        },
        "what_happens_next": [
            "You will be redirected to Stripe for secure payment",
            "Your 7-day free trial starts immediately",
            "Full access to all Base Monthly features",
            "Cancel anytime during trial period",
            "Automatic billing starts after trial ends"
        ]
    }
}
```

#### **Test 3: Confirm and Create Checkout**

**Request:**
```
POST http://127.0.0.1:8000/api/packages-gui/base_monthly/checkout
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json
```

**Body:**
```json
{
    "confirmed": true,
    "terms_accepted": true
}
```

**Expected Response:**
```json
{
    "success": true,
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_abc123...",
        "session_id": "cs_test_abc123...",
        "package": {
            "id": "base_monthly",
            "name": "Base Monthly",
            "price": 9.99
        },
        "confirmation": {
            "confirmed_at": "2024-12-19T10:00:00Z",
            "user_confirmed": true,
            "terms_accepted": true
        },
        "next_step": "redirect_to_stripe"
    },
    "message": "Package confirmed! Redirecting to Stripe for secure payment..."
}
```

### **Method 4: Postman Testing (Direct Flow)**

#### **Test 1: Get Packages with Checkout**

**Request:**
```
GET http://127.0.0.1:8000/api/packages-checkout
Authorization: Bearer YOUR_TOKEN
```

**Expected Response:**
```json
{
    "success": true,
    "data": {
        "packages": [
            {
                "id": "base_monthly",
                "name": "Base Monthly",
                "price": 9.99,
                "checkout_action": "create_checkout_session",
                "features": [...]
            },
            {
                "id": "premium_monthly",
                "name": "Premium Monthly",
                "price": 19.99,
                "checkout_action": "create_checkout_session",
                "features": [...]
            }
        ],
        "user_info": {
            "id": 1,
            "email": "<EMAIL>",
            "name": "Test User"
        }
    }
}
```

#### **Test 2: Create Checkout Session**

**Request:**
```
POST http://127.0.0.1:8000/api/packages/base_monthly/checkout
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json
```

**Expected Response:**
```json
{
    "success": true,
    "data": {
        "checkout_url": "https://checkout.stripe.com/pay/cs_test_abc123...",
        "session_id": "cs_test_abc123...",
        "package": {
            "id": "base_monthly",
            "name": "Base Monthly",
            "price": 9.99
        },
        "trial_info": {
            "trial_days": 7,
            "trial_start": "immediately_after_payment",
            "auto_billing_date": "2024-12-26"
        }
    },
    "message": "Checkout session created. Redirect user to checkout_url."
}
```

#### **Test 3: Handle Payment Success**

**Request:**
```
GET http://127.0.0.1:8000/api/payment-success?session_id=cs_test_abc123
Authorization: Bearer YOUR_TOKEN
```

**Expected Response:**
```json
{
    "success": true,
    "message": "Payment successful! Your 7-day trial has started.",
    "data": {
        "session_id": "cs_test_abc123",
        "subscription_id": "sub_abc123",
        "customer_id": "cus_abc123",
        "trial_started": true,
        "trial_ends_at": "2024-12-26T10:00:00Z",
        "package_name": "Base Monthly"
    }
}
```

### **Method 3: cURL Testing**

```bash
# 1. Get packages
curl -X GET "http://127.0.0.1:8000/api/packages-checkout" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. Create checkout session
curl -X POST "http://127.0.0.1:8000/api/packages/base_monthly/checkout" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 3. Check payment success
curl -X GET "http://127.0.0.1:8000/api/payment-success?session_id=cs_test_abc123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🎯 **Complete User Flow Testing**

### **Scenario 1: New User Registration + Purchase**

1. **Register:** `POST /api/register`
2. **Login:** `POST /api/login` (get token)
3. **View packages:** `GET /api/packages-checkout`
4. **Create checkout:** `POST /api/packages/base_monthly/checkout`
5. **Redirect to Stripe:** Open `checkout_url` in browser
6. **Pay with test card:** `4242 4242 4242 4242`
7. **Return to app:** Stripe redirects to success URL
8. **Verify trial:** `GET /api/me` (should show trial status)

### **Scenario 2: Google OAuth + Purchase**

1. **Google OAuth:** `GET /api/auth/google`
2. **Complete OAuth:** Sign in with Google
3. **View packages:** `GET /api/packages-checkout`
4. **Create checkout:** `POST /api/packages/premium_monthly/checkout`
5. **Pay with Stripe:** Complete payment
6. **Verify subscription:** Check user status

### **Scenario 3: Payment Cancellation**

1. **Create checkout session**
2. **Go to Stripe checkout**
3. **Click "Back" or close tab**
4. **Check cancel endpoint:** `GET /api/payment-cancel`

## 🔍 **Testing Stripe Test Cards**

### **Successful Payments:**
- **Visa:** `4242 4242 4242 4242`
- **Mastercard:** `5555 5555 5555 4444`
- **American Express:** `3782 822463 10005`

### **Failed Payments:**
- **Declined:** `4000 0000 0000 0002`
- **Insufficient funds:** `4000 0000 0000 9995`
- **Expired card:** `4000 0000 0000 0069`

### **3D Secure:**
- **Authentication required:** `4000 0025 0000 3155`

## 🚨 **Common Issues & Solutions**

### **Issue 1: Invalid Stripe Price ID**
```json
{
    "success": false,
    "message": "Failed to create checkout session: No such price: 'price_invalid'"
}
```
**Solution:** Check your `STRIPE_PRICE_BASE_MONTHLY` and `STRIPE_PRICE_PREMIUM_MONTHLY` in `.env`

### **Issue 2: Webhook Signature Verification Failed**
```
Invalid signature
```
**Solution:** Check your `STRIPE_WEBHOOK_SECRET` in `.env`

### **Issue 3: Redirect URI Issues**
```
Invalid redirect URL
```
**Solution:** Ensure `APP_URL` is correctly set in `.env`

### **Issue 4: Session Not Found**
```json
{
    "success": false,
    "message": "No session ID provided"
}
```
**Solution:** Ensure Stripe is redirecting with `session_id` parameter

## 🎉 **Success Indicators**

✅ **Checkout Session Created:** Returns valid Stripe checkout URL
✅ **Stripe Redirect:** User successfully redirected to Stripe
✅ **Payment Processing:** Test card payment completes
✅ **Webhook Received:** Stripe sends webhook to your endpoint
✅ **Trial Started:** User subscription tier changes to "trial"
✅ **Database Updated:** Subscription record created
✅ **User Access:** User can access protected features

## 📱 **Frontend Integration Example**

```javascript
// React/Vue/Angular example
async function buyPackage(packageId) {
    try {
        const response = await fetch(`/api/packages/${packageId}/checkout`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Redirect to Stripe
            window.location.href = data.data.checkout_url;
        }
    } catch (error) {
        console.error('Checkout failed:', error);
    }
}
```

## 🔄 **Webhook Testing**

Use Stripe CLI to test webhooks locally:

```bash
# Install Stripe CLI
# Forward webhooks to local server
stripe listen --forward-to localhost:8000/api/webhook/stripe-checkout

# Trigger test events
stripe trigger checkout.session.completed
stripe trigger customer.subscription.created
```

This comprehensive testing guide covers all aspects of the new smooth Stripe checkout integration! 🚀
