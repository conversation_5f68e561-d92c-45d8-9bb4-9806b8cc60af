<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plaid_transactions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('plaid_account_id');
            $table->foreign('plaid_account_id')->references('id')->on('plaid_accounts')->onDelete('cascade');
            $table->string('transaction_id')->unique();
            $table->string('category_id')->nullable();
            $table->json('category')->nullable();
            $table->string('transaction_type')->nullable();
            $table->string('name');
            $table->decimal('amount', 10, 2);
            $table->string('iso_currency_code')->nullable();
            $table->string('unofficial_currency_code')->nullable();
            $table->date('date');
            $table->boolean('pending')->default(false);
            $table->string('account_owner')->nullable();
            $table->json('location')->nullable();
            $table->json('payment_meta')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plaid_transactions');
    }
};
