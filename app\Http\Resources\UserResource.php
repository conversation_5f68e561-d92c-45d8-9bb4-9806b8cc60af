<?php

namespace App\Http\Resources;

class UserResource extends ApiResource
{
    /**
     * The resource type.
     *
     * @var string
     */
    protected $resourceType = 'user';

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $data = parent::toArray($request);

        // Remove sensitive fields
        unset($data['password']);
        unset($data['remember_token']);

        // Add computed fields
        $data['full_phone_number'] = $this->getFullPhoneNumber();
        $data['status_label'] = $this->getStatusLabel();
        $data['has_premium'] = $this->hasPremium();
        $data['is_trial'] = $this->isTrial();

        // Add trial information
        $data['trial_expired'] = $this->trialExpired();
        $data['trial_days_remaining'] = $this->getTrialDaysRemaining();
        $data['trial_expiration_date'] = $this->getTrialExpirationDate();

        return $data;
    }
}
