<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twilio Integration Test - PocketWatch</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .status-success { border-left-color: #28a745; }
        .status-error { border-left-color: #dc3545; }
        .status-warning { border-left-color: #ffc107; }
        .test-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .config-item {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
        }
        .phone-input {
            font-size: 16px;
            padding: 10px;
        }
        .test-numbers {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary">
                        <i class="fas fa-sms"></i> Twilio Integration Test
                    </h1>
                    <p class="lead text-muted">Comprehensive testing suite for Twilio SMS integration</p>
                </div>
            </div>
        </div>

        <!-- Configuration Check -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cog"></i> Configuration Check</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Environment Variables:</h6>
                                <div class="config-item">
                                    <strong>TWILIO_SID:</strong> 
                                    <span class="text-success">{{ config('services.twilio.sid') ? '✓ Set' : '✗ Missing' }}</span>
                                </div>
                                <div class="config-item">
                                    <strong>TWILIO_TOKEN:</strong> 
                                    <span class="text-success">{{ config('services.twilio.token') ? '✓ Set' : '✗ Missing' }}</span>
                                </div>
                                <div class="config-item">
                                    <strong>TWILIO_FROM:</strong> 
                                    <span class="badge bg-info">{{ config('services.twilio.from', 'Not Set') }}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Additional Settings:</h6>
                                <div class="config-item">
                                    <strong>VERIFY_SID:</strong> 
                                    <span class="text-success">{{ config('services.twilio.verify_sid') ? '✓ Set (Enhanced)' : '✗ Not Set (Basic)' }}</span>
                                </div>
                                <div class="config-item">
                                    <strong>SDK Status:</strong> 
                                    <span class="text-success">{{ class_exists('Twilio\Rest\Client') ? '✓ Installed' : '✗ Missing' }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary" onclick="testConnection()">
                                <i class="fas fa-plug"></i> Test Connection
                            </button>
                            <button class="btn btn-outline-info" onclick="getConfigStatus()">
                                <i class="fas fa-info-circle"></i> Detailed Config
                            </button>
                        </div>
                        <div id="connection-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Numbers Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="test-numbers">
                    <h6><i class="fas fa-info-circle"></i> Twilio Test Numbers (Sandbox)</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Valid Numbers:</strong><br>
                            <code>+15005550006</code> - Valid US number<br>
                            <code>+15005550009</code> - Cannot receive SMS
                        </div>
                        <div class="col-md-4">
                            <strong>Invalid Numbers:</strong><br>
                            <code>+15005550001</code> - Invalid number<br>
                            <code>+15005550007</code> - Blacklisted
                        </div>
                        <div class="col-md-4">
                            <strong>International:</strong><br>
                            <code>+447700900000</code> - UK test number<br>
                            <code>+33600000000</code> - France test number
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Suite -->
        <div class="row">
            <!-- Basic SMS Test -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="sms-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sms"></i> Basic SMS Test
                            <span class="float-end" id="sms-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="sms-phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control phone-input" id="sms-phone" 
                                   placeholder="+15005550006" value="+15005550006">
                        </div>
                        <div class="mb-3">
                            <label for="sms-message" class="form-label">Message</label>
                            <textarea class="form-control" id="sms-message" rows="3" 
                                      placeholder="Test message from PocketWatch">Hello from PocketWatch! This is a test SMS message.</textarea>
                            <div class="form-text">Max 1600 characters</div>
                        </div>
                        <button class="btn btn-primary" onclick="testSMS()">
                            <i class="fas fa-paper-plane"></i> Send SMS
                        </button>
                        <div id="sms-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Verification Code Test -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="verification-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-key"></i> Verification Code Test
                            <span class="float-end" id="verification-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="verify-phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control phone-input" id="verify-phone" 
                                   placeholder="+15005550006" value="+15005550006">
                        </div>
                        <div class="d-flex gap-2 mb-3">
                            <button class="btn btn-success" onclick="sendVerificationCode()">
                                <i class="fas fa-key"></i> Send Code
                            </button>
                            <input type="text" class="form-control" id="verify-code" 
                                   placeholder="Enter code" maxlength="6" style="max-width: 150px;">
                            <button class="btn btn-outline-success" onclick="verifyCode()" disabled id="verify-btn">
                                <i class="fas fa-check"></i> Verify
                            </button>
                        </div>
                        <div id="verification-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Bulk SMS Test -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="bulk-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> Bulk SMS Test
                            <span class="float-end" id="bulk-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="bulk-phones" class="form-label">Phone Numbers (one per line)</label>
                            <textarea class="form-control" id="bulk-phones" rows="4" 
                                      placeholder="+15005550006&#10;+15005550009&#10;+447700900000">+15005550006
+15005550009
+447700900000</textarea>
                            <div class="form-text">Max 5 numbers for testing</div>
                        </div>
                        <div class="mb-3">
                            <label for="bulk-message" class="form-label">Message</label>
                            <textarea class="form-control" id="bulk-message" rows="2" 
                                      placeholder="Bulk test message">Bulk SMS test from PocketWatch!</textarea>
                        </div>
                        <button class="btn btn-warning" onclick="testBulkSMS()">
                            <i class="fas fa-broadcast-tower"></i> Send Bulk SMS
                        </button>
                        <div id="bulk-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- SMS Status Check -->
            <div class="col-md-6 mb-4">
                <div class="card test-card" id="status-test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search"></i> SMS Status Check
                            <span class="float-end" id="status-check-status">
                                <i class="fas fa-clock text-warning"></i> Pending
                            </span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="message-sid" class="form-label">Message SID</label>
                            <input type="text" class="form-control" id="message-sid" 
                                   placeholder="SM1234567890abcdef1234567890abcdef">
                            <div class="form-text">Get this from SMS test results above</div>
                        </div>
                        <button class="btn btn-info" onclick="checkSMSStatus()">
                            <i class="fas fa-search"></i> Check Status
                        </button>
                        <div id="status-result" class="test-result" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results Summary -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> Test Results Summary</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-summary">
                            <p class="text-muted">Run tests to see results summary...</p>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-danger" onclick="clearSession()">
                                <i class="fas fa-broom"></i> Clear Test Session
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        let testResults = {
            connection: null,
            sms: null,
            verification: null,
            bulk: null,
            status: null
        };

        // Test Twilio connection
        async function testConnection() {
            updateStatus('connection', 'loading', 'Testing...');
            
            try {
                const response = await fetch('/test/twilio/connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    testResults.connection = true;
                } else {
                    testResults.connection = false;
                }
                
                displayResult('connection-result', data);
                updateSummary();
                
            } catch (error) {
                testResults.connection = false;
                displayResult('connection-result', { error: error.message });
                updateSummary();
            }
        }

        // Test SMS sending
        async function testSMS() {
            const phone = document.getElementById('sms-phone').value.trim();
            const message = document.getElementById('sms-message').value.trim();
            
            if (!phone || !message) {
                alert('Please enter both phone number and message');
                return;
            }

            updateStatus('sms', 'loading', 'Sending...');
            
            try {
                const response = await fetch('/test/twilio/sms', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ 
                        phone_number: phone,
                        message: message 
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('sms', 'success', 'Sent');
                    testResults.sms = true;
                    
                    // Auto-fill message SID for status check
                    if (data.sid) {
                        document.getElementById('message-sid').value = data.sid;
                    }
                } else {
                    updateStatus('sms', 'error', 'Failed');
                    testResults.sms = false;
                }
                
                displayResult('sms-result', data);
                updateSummary();
                
            } catch (error) {
                updateStatus('sms', 'error', 'Error');
                testResults.sms = false;
                displayResult('sms-result', { error: error.message });
                updateSummary();
            }
        }

        // Send verification code
        async function sendVerificationCode() {
            const phone = document.getElementById('verify-phone').value.trim();
            
            if (!phone) {
                alert('Please enter a phone number');
                return;
            }

            updateStatus('verification', 'loading', 'Sending...');
            
            try {
                const response = await fetch('/test/twilio/verification-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ phone_number: phone })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('verification', 'success', 'Code Sent');
                    document.getElementById('verify-btn').disabled = false;
                } else {
                    updateStatus('verification', 'error', 'Failed');
                }
                
                displayResult('verification-result', data);
                
            } catch (error) {
                updateStatus('verification', 'error', 'Error');
                displayResult('verification-result', { error: error.message });
            }
        }

        // Verify code
        async function verifyCode() {
            const code = document.getElementById('verify-code').value.trim();
            
            if (!code) {
                alert('Please enter the verification code');
                return;
            }

            updateStatus('verification', 'loading', 'Verifying...');
            
            try {
                const response = await fetch('/test/twilio/verify-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ code: code })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('verification', 'success', 'Verified');
                    testResults.verification = true;
                } else {
                    updateStatus('verification', 'error', 'Invalid');
                    testResults.verification = false;
                }
                
                displayResult('verification-result', data);
                updateSummary();
                
            } catch (error) {
                updateStatus('verification', 'error', 'Error');
                testResults.verification = false;
                displayResult('verification-result', { error: error.message });
                updateSummary();
            }
        }

        // Test bulk SMS
        async function testBulkSMS() {
            const phonesText = document.getElementById('bulk-phones').value.trim();
            const message = document.getElementById('bulk-message').value.trim();
            
            if (!phonesText || !message) {
                alert('Please enter phone numbers and message');
                return;
            }

            const phoneNumbers = phonesText.split('\n').map(p => p.trim()).filter(p => p);
            
            if (phoneNumbers.length === 0) {
                alert('Please enter at least one phone number');
                return;
            }

            if (phoneNumbers.length > 5) {
                alert('Maximum 5 phone numbers allowed for testing');
                return;
            }

            updateStatus('bulk', 'loading', 'Sending...');
            
            try {
                const response = await fetch('/test/twilio/multiple-numbers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ 
                        phone_numbers: phoneNumbers,
                        message: message 
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.summary.successful > 0) {
                    updateStatus('bulk', 'success', `${data.summary.successful}/${data.summary.total} Sent`);
                    testResults.bulk = true;
                } else {
                    updateStatus('bulk', 'error', 'Failed');
                    testResults.bulk = false;
                }
                
                displayResult('bulk-result', data);
                updateSummary();
                
            } catch (error) {
                updateStatus('bulk', 'error', 'Error');
                testResults.bulk = false;
                displayResult('bulk-result', { error: error.message });
                updateSummary();
            }
        }

        // Check SMS status
        async function checkSMSStatus() {
            const messageSid = document.getElementById('message-sid').value.trim();
            
            if (!messageSid) {
                alert('Please enter a message SID');
                return;
            }

            updateStatus('status-check', 'loading', 'Checking...');
            
            try {
                const response = await fetch('/test/twilio/sms-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ message_sid: messageSid })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('status-check', 'success', 'Retrieved');
                    testResults.status = true;
                } else {
                    updateStatus('status-check', 'error', 'Failed');
                    testResults.status = false;
                }
                
                displayResult('status-result', data);
                updateSummary();
                
            } catch (error) {
                updateStatus('status-check', 'error', 'Error');
                testResults.status = false;
                displayResult('status-result', { error: error.message });
                updateSummary();
            }
        }

        // Get detailed config status
        async function getConfigStatus() {
            try {
                const response = await fetch('/test/twilio/config', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const data = await response.json();
                displayResult('connection-result', data);
                
            } catch (error) {
                displayResult('connection-result', { error: error.message });
            }
        }

        // Clear test session
        async function clearSession() {
            try {
                const response = await fetch('/test/twilio/clear-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Reset UI
                    document.getElementById('verify-btn').disabled = true;
                    document.getElementById('verify-code').value = '';
                    
                    alert('Test session cleared successfully');
                }
                
            } catch (error) {
                alert('Failed to clear session: ' + error.message);
            }
        }

        // Helper Functions
        function updateStatus(test, status, text) {
            const statusElement = document.getElementById(`${test}-status`);
            const cardElement = document.getElementById(`${test}-test-card`);
            
            if (!statusElement || !cardElement) return;
            
            // Remove existing status classes
            cardElement.classList.remove('status-success', 'status-error', 'status-warning');
            
            switch(status) {
                case 'loading':
                    statusElement.innerHTML = `<div class="loading"></div> ${text}`;
                    break;
                case 'success':
                    statusElement.innerHTML = `<i class="fas fa-check text-success"></i> ${text}`;
                    cardElement.classList.add('status-success');
                    break;
                case 'error':
                    statusElement.innerHTML = `<i class="fas fa-times text-danger"></i> ${text}`;
                    cardElement.classList.add('status-error');
                    break;
                case 'warning':
                    statusElement.innerHTML = `<i class="fas fa-exclamation text-warning"></i> ${text}`;
                    cardElement.classList.add('status-warning');
                    break;
            }
        }

        function displayResult(elementId, data) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'block';
                element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
        }

        function updateSummary() {
            const summary = document.getElementById('test-summary');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(result => result === true).length;
            const failed = Object.values(testResults).filter(result => result === false).length;
            const pending = Object.values(testResults).filter(result => result === null).length;
            
            summary.innerHTML = `
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">${total}</h4>
                        <p>Total Tests</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">${passed}</h4>
                        <p>Passed</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-danger">${failed}</h4>
                        <p>Failed</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">${pending}</h4>
                        <p>Pending</p>
                    </div>
                </div>
            `;
        }

        // Auto-format phone number inputs
        document.querySelectorAll('.phone-input').forEach(input => {
            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^\d+]/g, '');
                if (!value.startsWith('+')) {
                    value = '+' + value;
                }
                e.target.value = value;
            });
        });

        // Initialize summary on page load
        updateSummary();
    </script>
</body>
</html>
