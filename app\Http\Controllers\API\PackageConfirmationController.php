<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class PackageConfirmationController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Get package details for confirmation page.
     */
    public function getPackageForConfirmation(Request $request, $packageId)
    {
        $validator = Validator::make(['package_id' => $packageId], [
            'package_id' => 'required|string|in:base_monthly,premium_monthly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid package selected',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $packageDetails = $this->getPackageDetails($packageId);

        if (!$packageDetails) {
            return response()->json([
                'success' => false,
                'message' => 'Package not found'
            ], 404);
        }

        // Calculate pricing details
        $subtotal = $packageDetails['price'];
        $tax = 0; // You can add tax calculation here
        $total = $subtotal + $tax;
        $trialEndDate = now()->addDays(7);
        $firstBillingDate = $trialEndDate;

        return response()->json([
            'success' => true,
            'data' => [
                'package' => $packageDetails,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'current_tier' => $user->subscription_tier
                ],
                'pricing' => [
                    'subtotal' => $subtotal,
                    'tax' => $tax,
                    'total' => $total,
                    'currency' => 'USD',
                    'billing_cycle' => $packageDetails['billing_cycle']
                ],
                'trial_info' => [
                    'trial_days' => 7,
                    'trial_start' => 'immediately_after_payment',
                    'trial_end_date' => $trialEndDate->toDateString(),
                    'first_billing_date' => $firstBillingDate->toDateString(),
                    'trial_description' => 'Start your 7-day free trial today. You won\'t be charged until ' . $firstBillingDate->format('M j, Y') . '.'
                ],
                'features_included' => $packageDetails['features'],
                'what_happens_next' => [
                    'You will be redirected to Stripe for secure payment',
                    'Your 7-day free trial starts immediately',
                    'Full access to all ' . $packageDetails['name'] . ' features',
                    'Cancel anytime during trial period',
                    'Automatic billing starts after trial ends'
                ]
            ]
        ]);
    }

    /**
     * Confirm package selection and create checkout session.
     */
    public function confirmAndCreateCheckout(Request $request, $packageId)
    {
        $validator = Validator::make(
            array_merge($request->all(), ['package_id' => $packageId]),
            [
                'package_id' => 'required|string|in:base_monthly,premium_monthly',
                'confirmed' => 'required|boolean|accepted',
                'terms_accepted' => 'required|boolean|accepted',
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $packageDetails = $this->getPackageDetails($packageId);

        if (!$packageDetails) {
            return response()->json([
                'success' => false,
                'message' => 'Package not found'
            ], 404);
        }

        try {
            // Create Stripe checkout session
            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price' => $packageDetails['stripe_price_id'],
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => config('app.url') . '/payment-success?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => config('app.url') . '/payment-cancel?package_id=' . $packageId,
                'customer_email' => $user->email,
                'client_reference_id' => $user->id,
                'subscription_data' => [
                    'trial_period_days' => 7,
                    'metadata' => [
                        'user_id' => $user->id,
                        'package_id' => $packageId,
                        'package_name' => $packageDetails['name'],
                        'confirmed_at' => now()->toISOString(),
                    ],
                ],
                'metadata' => [
                    'user_id' => $user->id,
                    'package_id' => $packageId,
                    'package_name' => $packageDetails['name'],
                    'user_email' => $user->email,
                    'confirmed_at' => now()->toISOString(),
                ],
                'allow_promotion_codes' => true,
                'billing_address_collection' => 'auto',
                'phone_number_collection' => [
                    'enabled' => true,
                ],
                'custom_text' => [
                    'submit' => [
                        'message' => 'Start your 7-day free trial for ' . $packageDetails['name'] . '. You won\'t be charged until ' . now()->addDays(7)->format('M j, Y') . '.'
                    ]
                ]
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'checkout_url' => $session->url,
                    'session_id' => $session->id,
                    'package' => $packageDetails,
                    'confirmation' => [
                        'confirmed_at' => now()->toISOString(),
                        'user_confirmed' => true,
                        'terms_accepted' => true
                    ],
                    'next_step' => 'redirect_to_stripe'
                ],
                'message' => 'Package confirmed! Redirecting to Stripe for secure payment...'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create checkout session: ' . $e->getMessage(),
                'error_code' => 'checkout_creation_failed'
            ], 500);
        }
    }

    /**
     * Get package details by ID.
     */
    private function getPackageDetails($packageId)
    {
        $packages = [
            'base_monthly' => [
                'id' => 'base_monthly',
                'name' => 'Base Monthly',
                'tier' => 'base',
                'billing_cycle' => 'monthly',
                'price' => 5.00,
                'original_price' => 5.00,
                'discount' => 0,
                'stripe_price_id' => env('STRIPE_PRICE_BASE_MONTHLY'),
                'description' => 'Essential financial management tools for personal use',
                'features' => [
                    'Financial bins with thresholds',
                    'Transaction categorization',
                    'Basic insights and analytics',
                    'Hierarchical sub-bins (3 levels deep)',
                    'Bank account linking',
                    'Monthly spending reports',
                    'Email notifications',
                    'Mobile app access'
                ],
                'limits' => [
                    'max_bins' => 10,
                    'max_sub_bins_per_bin' => 10,
                    'max_nesting_depth' => 3,
                    'max_transactions_per_month' => 1000
                ],
                'popular' => false,
                'recommended_for' => 'Individuals and small families'
            ],
            'premium_monthly' => [
                'id' => 'premium_monthly',
                'name' => 'Premium Monthly',
                'tier' => 'premium',
                'billing_cycle' => 'monthly',
                'price' => 10.00,
                'original_price' => 10.00,
                'discount' => 0,
                'stripe_price_id' => env('STRIPE_PRICE_PREMIUM_MONTHLY'),
                'description' => 'Advanced financial management with premium features',
                'features' => [
                    'All Base features included',
                    'Unlimited hierarchical sub-bins',
                    'Crypto wallet integration',
                    'Advanced AI insights and predictions',
                    'Priority notifications',
                    'Advanced reporting and analytics',
                    'Custom spending categories',
                    'Investment tracking',
                    'Multi-currency support',
                    'Priority customer support'
                ],
                'limits' => [
                    'max_bins' => 'unlimited',
                    'max_sub_bins_per_bin' => 'unlimited',
                    'max_nesting_depth' => 'unlimited',
                    'max_transactions_per_month' => 'unlimited'
                ],
                'popular' => true,
                'recommended_for' => 'Power users and businesses'
            ]
        ];

        return $packages[$packageId] ?? null;
    }

    /**
     * Get all packages for selection page.
     */
    public function getAllPackages(Request $request)
    {
        $user = $request->user();
        
        $packages = [
            $this->getPackageDetails('base_monthly'),
            $this->getPackageDetails('premium_monthly')
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'packages' => $packages,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'current_tier' => $user->subscription_tier
                ],
                'trial_info' => [
                    'trial_days' => 7,
                    'trial_description' => 'All packages include a 7-day free trial'
                ],
                'currency' => 'USD'
            ]
        ]);
    }
}
