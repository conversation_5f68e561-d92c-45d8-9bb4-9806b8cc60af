<?php

namespace App\Services;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;

class IdObfuscationService
{
    /**
     * The separator used in the hash.
     *
     * @var string
     */
    protected $separator = '.';

    /**
     * Obfuscate an ID.
     *
     * @param  string|int  $id
     * @param  string  $type
     * @return string
     */
    public function obfuscate($id, $type = 'default')
    {
        // Create a payload with the ID, type, and a timestamp
        $payload = [
            'id' => $id,
            'type' => $type,
            'timestamp' => now()->timestamp,
        ];

        // Encrypt the payload
        $encrypted = Crypt::encrypt($payload);

        // Create a hash of the encrypted payload
        $hash = hash_hmac('sha256', $encrypted, config('app.key'));

        // Take the first 8 characters of the hash
        $shortHash = substr($hash, 0, 8);

        // Encode the encrypted payload
        $encodedPayload = base64_encode($encrypted);

        // Return the hash and encoded payload
        return $shortHash . $this->separator . $encodedPayload;
    }

    /**
     * Deobfuscate an ID.
     *
     * @param  string  $obfuscatedId
     * @param  string|null  $expectedType
     * @return string|int|null
     */
    public function deobfuscate($obfuscatedId, $expectedType = null)
    {
        try {
            // Split the hash and encoded payload
            $parts = explode($this->separator, $obfuscatedId);

            if (count($parts) !== 2) {
                return null;
            }

            $shortHash = $parts[0];
            $encodedPayload = $parts[1];

            // Decode the payload
            $encrypted = base64_decode($encodedPayload);

            // Verify the hash
            $hash = hash_hmac('sha256', $encrypted, config('app.key'));
            $calculatedShortHash = substr($hash, 0, 8);

            if (!hash_equals($calculatedShortHash, $shortHash)) {
                return null;
            }

            // Decrypt the payload
            $payload = Crypt::decrypt($encrypted);

            // Verify the type if expected type is provided
            if ($expectedType !== null && $payload['type'] !== $expectedType) {
                return null;
            }

            return $payload['id'];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Generate a secure resource token.
     *
     * @param  string|int  $id
     * @param  string  $type
     * @param  int  $expiresInMinutes
     * @return string
     */
    public function generateResourceToken($id, $type, $expiresInMinutes = 60)
    {
        $payload = [
            'id' => $id,
            'type' => $type,
            'expires_at' => now()->addMinutes($expiresInMinutes)->timestamp,
            'nonce' => Str::random(16),
        ];

        return Crypt::encrypt($payload);
    }

    /**
     * Validate a resource token.
     *
     * @param  string  $token
     * @param  string  $expectedType
     * @return string|int|null
     */
    public function validateResourceToken($token, $expectedType)
    {
        try {
            $payload = Crypt::decrypt($token);

            // Check if token has expired
            if ($payload['expires_at'] < now()->timestamp) {
                return null;
            }

            // Verify the type
            if ($payload['type'] !== $expectedType) {
                return null;
            }

            return $payload['id'];
        } catch (\Exception $e) {
            return null;
        }
    }
}
