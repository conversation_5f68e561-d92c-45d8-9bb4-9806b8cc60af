<?php

namespace App\Http\Controllers;

use App\Models\PlaidAccount;
use App\Models\Subscription;
use App\Services\PlaidService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PlaidPaymentController extends Controller
{
    /**
     * The Plaid service instance.
     *
     * @var \App\Services\PlaidService
     */
    protected $plaidService;

    /**
     * Create a new controller instance.
     *
     * @param \App\Services\PlaidService $plaidService
     * @return void
     */
    public function __construct(PlaidService $plaidService)
    {
        $this->middleware('auth');
        $this->plaidService = $plaidService;
    }

    /**
     * Show the payment form.
     *
     * @return \Illuminate\View\View
     */
    public function showPaymentForm()
    {
        // Get user's payment-enabled Plaid accounts
        $accounts = Auth::user()->plaidAccounts()
            ->where('is_payment_enabled', true)
            ->get();
            
        // Get available subscription packages
        $packages = [
            [
                'id' => 'base_monthly',
                'name' => 'Base Tier (Monthly)',
                'price' => setting('subscription_base_monthly_price', 5.00),
                'description' => 'Basic features with monthly billing',
            ],
            [
                'id' => 'premium_monthly',
                'name' => 'Premium Tier (Monthly)',
                'price' => setting('subscription_premium_monthly_price', 10.00),
                'description' => 'Advanced features with monthly billing',
            ],
            [
                'id' => 'base_yearly',
                'name' => 'Base Tier (Yearly)',
                'price' => setting('subscription_base_yearly_price', 50.00),
                'description' => 'Basic features with yearly billing (save 16%)',
            ],
            [
                'id' => 'premium_yearly',
                'name' => 'Premium Tier (Yearly)',
                'price' => setting('subscription_premium_yearly_price', 100.00),
                'description' => 'Advanced features with yearly billing (save 16%)',
            ],
        ];
        
        return view('plaid.payment', compact('accounts', 'packages'));
    }

    /**
     * Get payment accounts for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentAccounts()
    {
        $accounts = Auth::user()->plaidAccounts()
            ->where('is_payment_enabled', true)
            ->get()
            ->map(function ($account) {
                return [
                    'id' => $account->id,
                    'uuid' => $account->uuid,
                    'institution_name' => $account->institution_name,
                    'account_name' => $account->account_name,
                    'account_type' => $account->account_type,
                    'account_mask' => $account->account_mask,
                ];
            });
            
        return response()->json([
            'success' => true,
            'accounts' => $accounts,
        ]);
    }

    /**
     * Process a payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function processPayment(Request $request)
    {
        $request->validate([
            'account_id' => 'required|exists:plaid_accounts,id',
            'package_id' => 'required|string',
            'amount' => 'required|numeric|min:1',
        ]);
        
        $account = PlaidAccount::where('user_id', Auth::id())
            ->where('id', $request->account_id)
            ->where('is_payment_enabled', true)
            ->firstOrFail();
            
        // In a real implementation, you would use Plaid's payment_initiation
        // product to create a payment. For this example, we'll simulate it.
        
        // Create a payment record
        $paymentId = Str::uuid();
        
        // Create or update subscription based on the package
        $packageParts = explode('_', $request->package_id);
        $tier = $packageParts[0] ?? 'base';
        $cycle = $packageParts[1] ?? 'monthly';
        
        $subscription = Subscription::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'status' => 'active',
            ],
            [
                'name' => ucfirst($tier) . ' Tier',
                'stripe_id' => null, // Not using Stripe
                'stripe_status' => 'active',
                'stripe_price' => null,
                'quantity' => 1,
                'trial_ends_at' => null,
                'ends_at' => null,
                'payment_method' => 'plaid',
                'payment_id' => $paymentId,
                'billing_cycle' => $cycle,
            ]
        );
        
        // Update user's subscription tier
        Auth::user()->update([
            'subscription_tier' => $tier,
        ]);
        
        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'payment_id' => $paymentId,
                'subscription' => [
                    'id' => $subscription->id,
                    'name' => $subscription->name,
                    'status' => $subscription->stripe_status,
                    'billing_cycle' => $subscription->billing_cycle,
                ],
            ]);
        }
        
        return redirect()->route('subscriptions.index')
            ->with('success', 'Payment processed successfully. Your subscription has been updated.');
    }

    /**
     * Get the status of a payment.
     *
     * @param  string  $paymentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentStatus($paymentId)
    {
        // In a real implementation, you would check the status of the payment
        // with Plaid's API. For this example, we'll simulate it.
        
        return response()->json([
            'success' => true,
            'status' => 'completed',
            'last_status_update' => now()->toIso8601String(),
        ]);
    }
}
