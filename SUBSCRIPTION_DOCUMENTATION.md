# PocketWatch Subscription Documentation

## Overview

PocketWatch offers two subscription tiers: Base and Premium. All new users receive a 7-day free trial to experience the full features of their chosen subscription tier before being charged.

## Subscription Tiers

### Base Tier ($5/month or $50/year)

- Secure user registration and login (email, password)
- Link bank accounts via Plaid (in production)
- View total balance on the dashboard
- Hide/Show balance with eye toggle
- Create financial bins (categories) with minimum and maximum thresholds
- Automatic transaction categorization via Binnit AI
- Edit bins manually
- View graphical insights (Income and Spending Tabs, Weekly/Monthly/Yearly filters)
- View 4 recent transactions in a scrollable carousel
- Chat with Binnit chatbot for financial advice
- Notification system (bell icon for alerts)
- 3 Levels of Sub-Bins maximum

### Premium Tier ($10/month or $100/year)

All Base Tier features, plus:
- Unlimited Sub-Bins (nested bins inside bins)
- Access to Crypto Scanner Page:
  - Connect crypto wallets
  - Receive crypto diversification advice
  - Portfolio rebalancing suggestions
- Priority notifications and personalized financial tips
- Access to advanced Binnit AI suggestions

## Free Trial

- All new subscriptions include a 7-day free trial
- Users can access all features of their chosen tier during the trial period
- No charge is made until the trial period ends
- Users can cancel at any time during the trial period without being charged

## Billing Cycles

- **Monthly**: Charged every month after the trial period ends
- **Yearly**: Charged annually after the trial period ends (save approximately 17% compared to monthly billing)

## Subscription Management

Users can manage their subscriptions through the app:
- View current subscription details
- Upgrade from Base to Premium tier
- Change billing cycle
- Cancel subscription

## API Endpoints

### Get All Subscriptions
- **URL**: `/api/subscriptions`
- **Method**: `GET`
- **Authentication**: Required
- **Response**: List of user's subscriptions

### Create Subscription
- **URL**: `/api/subscriptions`
- **Method**: `POST`
- **Authentication**: Required
- **Parameters**:
  - `subscription_tier` (required): Subscription tier (base, premium)
  - `billing_cycle` (required): Billing cycle (monthly, yearly)
- **Response**: Checkout URL for payment and subscription details

### Get Subscription
- **URL**: `/api/subscriptions/{id}`
- **Method**: `GET`
- **Authentication**: Required
- **URL Parameters**: `id` (required): Subscription ID
- **Response**: Subscription details

### Update Subscription
- **URL**: `/api/subscriptions/{id}`
- **Method**: `PUT`
- **Authentication**: Required
- **URL Parameters**: `id` (required): Subscription ID
- **Parameters**: `subscription_tier` (required): New subscription tier
- **Response**: Updated subscription details

### Cancel Subscription
- **URL**: `/api/subscriptions/{id}`
- **Method**: `DELETE`
- **Authentication**: Required
- **URL Parameters**: `id` (required): Subscription ID
- **Response**: Confirmation of cancellation

## Implementation Details

- Subscriptions are managed through Stripe
- All subscriptions include a 7-day free trial
- Subscription status is tracked in the database
- User's subscription tier is updated automatically based on their active subscription
- Feature access is controlled based on the user's subscription tier
