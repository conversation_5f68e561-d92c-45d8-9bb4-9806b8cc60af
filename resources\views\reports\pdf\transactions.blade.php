<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Transaction Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .header p {
            font-size: 14px;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .summary {
            margin-top: 30px;
            border-top: 2px solid #ddd;
            padding-top: 20px;
        }
        .summary table {
            width: 50%;
            margin-left: auto;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .income {
            color: #28a745;
        }
        .expense {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Transaction Report</h1>
        <p>{{ $report->name }}</p>
        <p>Period: {{ $report->start_date->format('M d, Y') }} - {{ $report->end_date->format('M d, Y') }}</p>
        <p>Generated: {{ now()->format('M d, Y H:i:s') }}</p>
    </div>

    @if($transactions->count() > 0)
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Description</th>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Bin</th>
                    <th>Category</th>
                </tr>
            </thead>
            <tbody>
                @foreach($transactions as $transaction)
                    <tr>
                        <td>{{ $transaction->transaction_date->format('Y-m-d') }}</td>
                        <td>{{ $transaction->description }}</td>
                        <td>{{ ucfirst($transaction->transaction_type) }}</td>
                        <td class="text-right {{ $transaction->transaction_type == 'income' ? 'income' : 'expense' }}">
                            {{ $transaction->currency }} {{ number_format($transaction->amount, 2) }}
                        </td>
                        <td>{{ $transaction->bin ? $transaction->bin->name : 'N/A' }}</td>
                        <td>{{ $transaction->category ?: 'N/A' }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <p class="text-center">No transactions found for the selected period.</p>
    @endif

    <div class="summary">
        <h2>Summary</h2>
        <table>
            <tr>
                <th>Total Income</th>
                <td class="text-right income">{{ $transactions->first()->currency ?? 'USD' }} {{ number_format($total_income, 2) }}</td>
            </tr>
            <tr>
                <th>Total Expense</th>
                <td class="text-right expense">{{ $transactions->first()->currency ?? 'USD' }} {{ number_format($total_expense, 2) }}</td>
            </tr>
            <tr>
                <th>Net Amount</th>
                <td class="text-right {{ $net_amount >= 0 ? 'income' : 'expense' }}">
                    {{ $transactions->first()->currency ?? 'USD' }} {{ number_format($net_amount, 2) }}
                </td>
            </tr>
        </table>
    </div>

    <div class="footer">
        <p>PocketWatch Financial Management System</p>
        <p>This report was generated automatically. Please contact support if you have any questions.</p>
    </div>
</body>
</html>
