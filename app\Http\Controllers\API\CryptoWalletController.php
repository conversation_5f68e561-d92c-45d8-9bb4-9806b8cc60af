<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\SyncCryptoWalletData;
use App\Models\CryptoWallet;
use App\Services\MoralisService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CryptoWalletController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $wallets = $request->user()->cryptoWallets;

        return response()->json([
            'wallets' => $wallets,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if user has premium subscription for crypto analysis
        $user = $request->user();
        if ($user->subscription_tier !== 'premium') {
            return response()->json([
                'message' => 'Crypto wallet analysis is only available for premium subscribers',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'wallet_address' => 'required|string',
            'wallet_name' => 'nullable|string|max:255',
            'blockchain_network' => 'required|string|in:ethereum,binance,polygon,avalanche',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if wallet already exists for this user
        $existingWallet = $user->cryptoWallets()
            ->where('wallet_address', $request->wallet_address)
            ->where('blockchain_network', $request->blockchain_network)
            ->first();

        if ($existingWallet) {
            return response()->json([
                'message' => 'Wallet already exists',
                'wallet' => $existingWallet,
            ], 400);
        }

        // Create new wallet
        $wallet = new CryptoWallet([
            'uuid' => (string) Str::uuid(),
            'user_id' => $user->id,
            'wallet_address' => $request->wallet_address,
            'wallet_name' => $request->wallet_name ?? 'My Wallet',
            'blockchain_network' => $request->blockchain_network,
            'assets' => [],
            'total_value_usd' => 0,
            'last_synced_at' => null,
            'ai_advice' => null,
        ]);
        $wallet->save();

        // Sync wallet data from blockchain
        $this->syncWalletData($wallet);

        return response()->json([
            'message' => 'Wallet added successfully',
            'wallet' => $wallet,
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, string $id)
    {
        $wallet = CryptoWallet::find($id);

        if (!$wallet || $wallet->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Wallet not found',
            ], 404);
        }

        return response()->json([
            'wallet' => $wallet,
        ]);
    }

    /**
     * Sync wallet data from blockchain.
     *
     * @param  \App\Models\CryptoWallet  $wallet
     * @return void
     */
    private function syncWalletData(CryptoWallet $wallet)
    {
        try {
            // Dispatch job to sync wallet data in the background
            SyncCryptoWalletData::dispatch($wallet);

            // For immediate feedback, update the last_synced_at timestamp
            $wallet->last_synced_at = now();
            $wallet->save();

            Log::info('Crypto wallet sync job dispatched', [
                'wallet_id' => $wallet->id,
                'wallet_address' => $wallet->wallet_address,
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Error dispatching wallet sync job: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $wallet = CryptoWallet::find($id);

        if (!$wallet || $wallet->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Wallet not found',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'wallet_name' => 'sometimes|required|string|max:255',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Update wallet
        $wallet->update($request->only(['wallet_name', 'is_active']));

        // Sync wallet data if requested
        if ($request->has('sync') && $request->sync) {
            $this->syncWalletData($wallet);
        }

        return response()->json([
            'message' => 'Wallet updated successfully',
            'wallet' => $wallet,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, string $id)
    {
        $wallet = CryptoWallet::find($id);

        if (!$wallet || $wallet->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Wallet not found',
            ], 404);
        }

        $wallet->delete();

        return response()->json([
            'message' => 'Wallet deleted successfully',
        ]);
    }

    /**
     * Sync wallet data manually.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function sync(Request $request, string $id)
    {
        $wallet = CryptoWallet::find($id);

        if (!$wallet || $wallet->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Wallet not found',
            ], 404);
        }

        // Check if user has premium subscription for crypto analysis
        $user = $request->user();
        if ($user->subscription_tier !== 'premium') {
            return response()->json([
                'message' => 'Crypto wallet analysis is only available for premium subscribers',
            ], 403);
        }

        // Sync wallet data
        $this->syncWalletData($wallet);

        return response()->json([
            'message' => 'Wallet synced successfully',
            'wallet' => $wallet,
        ]);
    }

    /**
     * Get wallet assets.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAssets(Request $request, string $id)
    {
        $wallet = CryptoWallet::find($id);

        if (!$wallet || $wallet->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Wallet not found',
            ], 404);
        }

        // Check if user has premium subscription for crypto analysis
        $user = $request->user();
        if ($user->subscription_tier !== 'premium') {
            return response()->json([
                'message' => 'Crypto wallet analysis is only available for premium subscribers',
            ], 403);
        }

        // Check if wallet needs syncing (last sync > 1 hour ago)
        if (!$wallet->last_synced_at || $wallet->last_synced_at->diffInHours(now()) > 1) {
            $this->syncWalletData($wallet);

            // Inform the client that a sync is in progress
            return response()->json([
                'wallet_address' => $wallet->wallet_address,
                'wallet_name' => $wallet->wallet_name,
                'blockchain_network' => $wallet->blockchain_network,
                'total_value_usd' => $wallet->total_value_usd,
                'last_synced_at' => $wallet->last_synced_at,
                'assets' => $wallet->assets,
                'sync_status' => 'in_progress',
                'message' => 'Wallet data sync is in progress. Refresh in a few moments for updated data.',
            ]);
        }

        return response()->json([
            'wallet_address' => $wallet->wallet_address,
            'wallet_name' => $wallet->wallet_name,
            'blockchain_network' => $wallet->blockchain_network,
            'total_value_usd' => $wallet->total_value_usd,
            'last_synced_at' => $wallet->last_synced_at,
            'assets' => $wallet->assets,
            'sync_status' => 'completed',
        ]);
    }

    /**
     * Get wallet transactions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @param  \App\Services\MoralisService  $moralisService
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransactions(Request $request, string $id, MoralisService $moralisService)
    {
        $wallet = CryptoWallet::find($id);

        if (!$wallet || $wallet->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Wallet not found',
            ], 404);
        }

        // Check if user has premium subscription for crypto analysis
        $user = $request->user();
        if ($user->subscription_tier !== 'premium') {
            return response()->json([
                'message' => 'Crypto wallet analysis is only available for premium subscribers',
            ], 403);
        }

        try {
            // Get transactions from Moralis API
            $transactions = $moralisService->getTransactions(
                $wallet->wallet_address,
                $wallet->blockchain_network,
                $request->input('limit', 10)
            );

            if (!$transactions) {
                return response()->json([
                    'message' => 'No transactions found or API unavailable',
                ], 404);
            }

            return response()->json([
                'wallet_address' => $wallet->wallet_address,
                'wallet_name' => $wallet->wallet_name,
                'blockchain_network' => $wallet->blockchain_network,
                'transactions' => $transactions,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting wallet transactions', [
                'wallet_id' => $wallet->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Error fetching transaction data: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Add a wallet by scanning a QR code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addFromQrCode(Request $request)
    {
        // Check if user has premium subscription for crypto analysis
        $user = $request->user();
        if ($user->subscription_tier !== 'premium') {
            return response()->json([
                'message' => 'Crypto wallet analysis is only available for premium subscribers',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'qr_data' => 'required|string',
            'wallet_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Parse the QR code data
        $qrData = $request->qr_data;

        // Extract wallet address and blockchain network from QR code
        // QR codes for crypto wallets typically follow formats like:
        // ethereum:0x123456789abcdef... or bitcoin:bc1q123456789...

        $walletData = $this->parseQrCodeData($qrData);

        if (!$walletData) {
            return response()->json([
                'message' => 'Invalid QR code format. Could not extract wallet information.',
            ], 422);
        }

        // Check if wallet already exists for this user
        $existingWallet = $user->cryptoWallets()
            ->where('wallet_address', $walletData['wallet_address'])
            ->where('blockchain_network', $walletData['blockchain_network'])
            ->first();

        if ($existingWallet) {
            return response()->json([
                'message' => 'Wallet already exists',
                'wallet' => $existingWallet,
            ], 400);
        }

        // Create new wallet
        $wallet = new CryptoWallet([
            'uuid' => (string) Str::uuid(),
            'user_id' => $user->id,
            'wallet_address' => $walletData['wallet_address'],
            'wallet_name' => $request->wallet_name ?? $walletData['suggested_name'],
            'blockchain_network' => $walletData['blockchain_network'],
            'assets' => [],
            'total_value_usd' => 0,
            'last_synced_at' => null,
            'ai_advice' => null,
        ]);
        $wallet->save();

        // Sync wallet data from blockchain
        $this->syncWalletData($wallet);

        return response()->json([
            'message' => 'Wallet added successfully from QR code',
            'wallet' => $wallet,
        ], 201);
    }

    /**
     * Parse QR code data to extract wallet information.
     *
     * @param  string  $qrData
     * @return array|null
     */
    private function parseQrCodeData(string $qrData): ?array
    {
        // Common QR code formats:
        // ethereum:0x123456789abcdef...
        // bitcoin:bc1q123456789...
        // Or just the address itself: 0x123456789abcdef...

        $supportedNetworks = [
            'ethereum' => ['prefix' => 'ethereum:', 'regex' => '/^(ethereum:)?(0x[a-fA-F0-9]{40})$/'],
            'binance' => ['prefix' => 'bnb:', 'regex' => '/^(bnb:)?(0x[a-fA-F0-9]{40})$/'],
            'polygon' => ['prefix' => 'polygon:', 'regex' => '/^(polygon:)?(0x[a-fA-F0-9]{40})$/'],
            'avalanche' => ['prefix' => 'avax:', 'regex' => '/^(avax:)?(0x[a-fA-F0-9]{40})$/'],
        ];

        foreach ($supportedNetworks as $network => $config) {
            // Check if the QR data matches this network's format
            if (preg_match($config['regex'], $qrData, $matches)) {
                // Extract the wallet address (should be in the last capture group)
                $walletAddress = end($matches);

                return [
                    'wallet_address' => $walletAddress,
                    'blockchain_network' => $network,
                    'suggested_name' => ucfirst($network) . ' Wallet',
                ];
            }
        }

        // If the QR data is just a plain Ethereum-style address without a prefix
        if (preg_match('/^0x[a-fA-F0-9]{40}$/', $qrData)) {
            return [
                'wallet_address' => $qrData,
                'blockchain_network' => 'ethereum', // Default to Ethereum
                'suggested_name' => 'Ethereum Wallet',
            ];
        }

        // If we couldn't parse the QR code data
        return null;
    }
}
