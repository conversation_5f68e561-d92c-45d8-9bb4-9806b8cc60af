<?php

namespace App\Http\Controllers;

use App\Models\NotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationSettingController extends Controller
{
    /**
     * Display the user's notification settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $settings = $request->user()->getNotificationSettings();

        return view('settings.notifications', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update the user's notification settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_login' => 'sometimes|boolean',
            'email_profile_updates' => 'sometimes|boolean',
            'email_password_changes' => 'sometimes|boolean',
            'email_bin_operations' => 'sometimes|boolean',
            'email_transaction_operations' => 'sometimes|boolean',
            'email_subscription_updates' => 'sometimes|boolean',
            'email_threshold_alerts' => 'sometimes|boolean',
            'email_renewal_reminders' => 'sometimes|boolean',
            'email_marketing' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->route('settings.notifications')
                ->withErrors($validator)
                ->withInput();
        }

        $settings = $request->user()->getNotificationSettings();
        
        // Convert checkbox inputs to boolean values
        $data = [];
        $fields = [
            'email_login',
            'email_profile_updates',
            'email_password_changes',
            'email_bin_operations',
            'email_transaction_operations',
            'email_subscription_updates',
            'email_threshold_alerts',
            'email_renewal_reminders',
            'email_marketing',
        ];
        
        foreach ($fields as $field) {
            $data[$field] = $request->has($field);
        }
        
        $settings->update($data);

        return redirect()->route('settings.notifications')
            ->with('success', 'Notification settings updated successfully');
    }
}
