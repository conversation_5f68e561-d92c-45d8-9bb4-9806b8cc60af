@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-university me-2"></i>Linked Bank Accounts
                    </h5>
                    <div class="btn-group">
                        <a href="{{ route('plaid.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Link New Account
                        </a>
                        <button id="addDummyAccounts" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-magic me-1"></i>Add Dummy Accounts
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if ($accounts->isEmpty())
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-university fa-4x text-muted"></i>
                            </div>
                            <h5>No Bank Accounts Linked</h5>
                            <p class="text-muted">Link your bank accounts to track transactions and make payments.</p>
                            <a href="{{ route('plaid.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Link Bank Account
                            </a>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Institution</th>
                                        <th>Account</th>
                                        <th>Type</th>
                                        <th>Last Synced</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($accounts as $account)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <i class="fas fa-university text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <div>{{ $account->institution_name }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>{{ $account->account_name }}</div>
                                                <small class="text-muted">••••{{ $account->account_mask }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    {{ ucfirst($account->account_type) }}
                                                    @if ($account->account_subtype)
                                                        - {{ ucfirst($account->account_subtype) }}
                                                    @endif
                                                </span>
                                            </td>
                                            <td>
                                                @if ($account->last_synced_at)
                                                    <small>{{ $account->last_synced_at->diffForHumans() }}</small>
                                                @else
                                                    <small class="text-muted">Never</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if ($account->is_default)
                                                    <span class="badge bg-success">Default</span>
                                                @endif

                                                @if ($account->is_payment_enabled)
                                                    <span class="badge bg-info">Payment Enabled</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        @if (!$account->is_default)
                                                            <li>
                                                                <form action="{{ route('plaid.set-default', $account->id) }}" method="POST">
                                                                    @csrf
                                                                    <button type="submit" class="dropdown-item">
                                                                        <i class="fas fa-star me-2 text-warning"></i>Set as Default
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        @endif

                                                        <li>
                                                            <form action="{{ route('plaid.toggle-payment', $account->id) }}" method="POST">
                                                                @csrf
                                                                <button type="submit" class="dropdown-item">
                                                                    @if ($account->is_payment_enabled)
                                                                        <i class="fas fa-toggle-off me-2 text-danger"></i>Disable Payments
                                                                    @else
                                                                        <i class="fas fa-toggle-on me-2 text-success"></i>Enable Payments
                                                                    @endif
                                                                </button>
                                                            </form>
                                                        </li>

                                                        <li><hr class="dropdown-divider"></li>

                                                        <li>
                                                            <form action="{{ route('plaid.destroy', $account->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to remove this account?');">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-trash-alt me-2"></i>Remove Account
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const addDummyAccountsBtn = document.getElementById('addDummyAccounts');

        if (addDummyAccountsBtn) {
            addDummyAccountsBtn.addEventListener('click', function() {
                if (confirm('This will add dummy Plaid accounts for testing purposes. Any existing accounts will be removed. Continue?')) {
                    // Show loading state
                    addDummyAccountsBtn.disabled = true;
                    addDummyAccountsBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';

                    // Get CSRF token
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                    // Make API request
                    fetch('/api/plaid/add-dummy-accounts', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        credentials: 'same-origin'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Reload the page to show the new accounts
                            window.location.reload();
                        } else {
                            alert('Error adding dummy accounts: ' + (data.message || 'Unknown error'));
                            addDummyAccountsBtn.disabled = false;
                            addDummyAccountsBtn.innerHTML = '<i class="fas fa-magic me-1"></i>Add Dummy Accounts';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error adding dummy accounts. See console for details.');
                        addDummyAccountsBtn.disabled = false;
                        addDummyAccountsBtn.innerHTML = '<i class="fas fa-magic me-1"></i>Add Dummy Accounts';
                    });
                }
            });
        }
    });
</script>
@endpush
