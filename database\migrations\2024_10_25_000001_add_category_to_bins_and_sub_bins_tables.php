<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only add category if tables exist
        if (Schema::hasTable('bins')) {
            Schema::table('bins', function (Blueprint $table) {
                $table->string('category')->default('expense')->comment('income, expense')->after('type');
            });
        }

        if (Schema::hasTable('sub_bins')) {
            Schema::table('sub_bins', function (Blueprint $table) {
                $table->string('category')->default('expense')->comment('income, expense')->after('type');
            });
        }

        // Update existing records to set category based on type
        if (Schema::hasTable('bins')) {
            DB::statement("UPDATE bins SET category = CASE WHEN type = 'income' THEN 'income' ELSE 'expense' END");
        }
        if (Schema::hasTable('sub_bins')) {
            DB::statement("UPDATE sub_bins SET category = CASE WHEN type = 'income' THEN 'income' ELSE 'expense' END");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('bins')) {
            Schema::table('bins', function (Blueprint $table) {
                $table->dropColumn('category');
            });
        }

        if (Schema::hasTable('sub_bins')) {
            Schema::table('sub_bins', function (Blueprint $table) {
                $table->dropColumn('category');
            });
        }
    }
};
