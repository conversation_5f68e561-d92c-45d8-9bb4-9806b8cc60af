<?php

// This script helps diagnose email sending issues in the PocketWatch application

// Load the <PERSON><PERSON> application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Mail\TestMail;

// Output header
echo "=== PocketWatch Email Diagnostics ===\n\n";

// Check mail configuration
echo "Mail Configuration:\n";
echo "- Driver: " . config('mail.default') . "\n";
echo "- Host: " . config('mail.mailers.smtp.host') . "\n";
echo "- Port: " . config('mail.mailers.smtp.port') . "\n";
echo "- Username: " . config('mail.mailers.smtp.username') . "\n";
echo "- From Address: " . config('mail.from.address') . "\n";
echo "- From Name: " . config('mail.from.name') . "\n\n";

// Check queue configuration
echo "Queue Configuration:\n";
echo "- Connection: " . config('queue.default') . "\n";
echo "- Driver: " . config('queue.connections.' . config('queue.default') . '.driver') . "\n\n";

// Check for pending jobs
$pendingJobs = DB::table('jobs')->count();
echo "Pending Jobs: {$pendingJobs}\n";

// Check for failed jobs
$failedJobs = DB::table('failed_jobs')->count();
echo "Failed Jobs: {$failedJobs}\n\n";

// Check if the mail driver is set to log
if (config('mail.default') === 'log') {
    echo "WARNING: Mail driver is set to 'log'. Emails will be written to log files instead of being sent.\n";
    echo "To send actual emails, update the MAIL_MAILER setting in your .env file.\n\n";
}

// Attempt to send a test email
echo "Attempting to send a test email...\n";
try {
    // Log the attempt
    Log::info('Sending test email from diagnostics script');
    
    // Send the email
    Mail::to('<EMAIL>')->send(new TestMail());
    
    echo "Test email sent successfully! Check your logs or email inbox.\n";
} catch (\Exception $e) {
    echo "Error sending test email: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Diagnostics ===\n";
