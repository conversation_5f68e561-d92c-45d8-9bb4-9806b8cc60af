<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

abstract class BaseNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The notification subject.
     *
     * @var string
     */
    protected $subject;

    /**
     * The notification title.
     *
     * @var string
     */
    protected $title;

    /**
     * The notification greeting.
     *
     * @var string
     */
    protected $greeting;

    /**
     * The notification content.
     *
     * @var string
     */
    protected $content;

    /**
     * The notification details title.
     *
     * @var string
     */
    protected $detailsTitle;

    /**
     * The notification details.
     *
     * @var array
     */
    protected $details = [];

    /**
     * The notification action text.
     *
     * @var string
     */
    protected $actionText;

    /**
     * The notification action URL.
     *
     * @var string
     */
    protected $actionUrl;

    /**
     * The notification closing.
     *
     * @var string
     */
    protected $closing;

    /**
     * The notification signature.
     *
     * @var string
     */
    protected $signature;

    /**
     * The notification type for preference checking.
     *
     * @var string
     */
    protected $notificationType = 'email_login';

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        // Check if the user has enabled this type of notification
        if (method_exists($notifiable, 'isNotificationEnabled') &&
            !$notifiable->isNotificationEnabled($this->notificationType)) {
            return [];
        }

        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $mail = (new MailMessage)
            ->subject($this->subject)
            ->view('emails.notification', [
                'subject' => $this->subject,
                'title' => $this->title,
                'greeting' => $this->getGreeting($notifiable),
                'content' => $this->content,
                'detailsTitle' => $this->detailsTitle,
                'details' => $this->details,
                'actionText' => $this->actionText,
                'actionUrl' => $this->actionUrl,
                'closing' => $this->closing,
                'signature' => $this->signature,
            ]);

        return $mail;
    }

    /**
     * Get the greeting for the notification.
     *
     * @param  mixed  $notifiable
     * @return string
     */
    protected function getGreeting($notifiable)
    {
        if ($this->greeting) {
            return $this->greeting;
        }

        $name = $notifiable->name ?? null;

        return $name ? "Hello, {$name}!" : "Hello!";
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'subject' => $this->subject,
            'content' => $this->content,
            'details' => $this->details,
            'action_url' => $this->actionUrl,
        ];
    }
}
