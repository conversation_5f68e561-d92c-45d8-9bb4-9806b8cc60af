<?php

namespace App\Helpers;

use App\Models\User;

class NotificationHelper
{
    /**
     * Check if a notification type is enabled for a user.
     *
     * @param  \App\Models\User  $user
     * @param  string  $type
     * @return bool
     */
    public static function isEnabled(User $user, string $type): bool
    {
        if (!$user) {
            return false;
        }
        
        return $user->isNotificationEnabled($type);
    }
    
    /**
     * Get notification type description.
     *
     * @param  string  $type
     * @return string
     */
    public static function getTypeDescription(string $type): string
    {
        $descriptions = [
            'email_login' => 'Login notifications',
            'email_profile_updates' => 'Profile update notifications',
            'email_password_changes' => 'Password change notifications',
            'email_bin_operations' => 'Bin operation notifications',
            'email_transaction_operations' => 'Transaction operation notifications',
            'email_subscription_updates' => 'Subscription update notifications',
            'email_threshold_alerts' => 'Threshold alert notifications',
            'email_renewal_reminders' => 'Subscription renewal reminders',
            'email_marketing' => 'Marketing emails',
        ];
        
        return $descriptions[$type] ?? 'Unknown notification type';
    }
}
