@extends('admin.layouts.app')

@push('styles')
<style>
    .hierarchy-viewer {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .sub-bin-item {
        padding: 0.5rem;
        margin: 0.25rem 0;
        border-left: 3px solid #007bff;
        background-color: white;
        border-radius: 0.25rem;
    }

    .sub-bin-item.depth-1 {
        margin-left: 0;
        border-left-color: #007bff;
    }

    .sub-bin-item.depth-2 {
        margin-left: 1rem;
        border-left-color: #28a745;
    }

    .sub-bin-item.depth-3 {
        margin-left: 2rem;
        border-left-color: #ffc107;
    }

    .sub-bin-item.depth-4 {
        margin-left: 3rem;
        border-left-color: #dc3545;
    }

    .sub-bin-item.depth-5 {
        margin-left: 4rem;
        border-left-color: #6f42c1;
    }

    .export-buttons .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .table-actions {
        white-space: nowrap;
    }

    .hierarchy-toggle {
        cursor: pointer;
        color: #007bff;
        text-decoration: none;
    }

    .hierarchy-toggle:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .collapse-content {
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
@endpush

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Bins Management</h1>
            <p class="text-muted mb-0">Manage user bins and view sub-bin hierarchies</p>
        </div>
        <div class="export-buttons">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success" onclick="exportData('csv')">
                    <i class="fas fa-file-csv me-1"></i> Export CSV
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="exportData('pdf')">
                    <i class="fas fa-file-pdf me-1"></i> Export PDF
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Search & Filters</h6>
            @if(request()->hasAny(['user_id', 'search']))
                <a href="{{ route('admin.bins.index') }}" class="btn btn-sm btn-outline-danger">
                    <i class="fas fa-times me-1"></i> Clear Filters
                </a>
            @endif
        </div>
        <div class="card-body">
            <form action="{{ route('admin.bins.index') }}" method="GET" class="row g-3" id="filterForm">
                <!-- Preserve per_page when filtering -->
                <input type="hidden" name="per_page" value="{{ request('per_page', 5) }}">

                <div class="col-md-4">
                    <label for="user_id" class="form-label fw-semibold">Filter by User</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">All Users</option>
                        @foreach(\App\Models\User::where('is_admin', false)->orderBy('name')->get() as $user)
                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                {{ $user->name }} ({{ $user->email }})
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="search" class="form-label fw-semibold">Search Bins</label>
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request('search') }}" placeholder="Bin name...">
                        @if(request('search'))
                            <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        @endif
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <div class="d-flex gap-2 w-100">
                        <button type="submit" class="btn btn-primary flex-fill">
                            <i class="fas fa-search me-1"></i> Search
                        </button>
                        @if(request()->hasAny(['user_id', 'search']))
                            <a href="{{ route('admin.bins.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i> Reset
                            </a>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bins Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center flex-wrap">
            <div class="d-flex align-items-center">
                <h6 class="m-0 font-weight-bold text-primary me-3">All Bins</h6>
                <span class="badge bg-primary">Total: {{ $bins->total() }}</span>
                @if(request()->hasAny(['user_id', 'search']))
                    <span class="badge bg-info ms-2">Filtered</span>
                @endif
            </div>
            <div class="d-flex align-items-center">
                <label for="per_page" class="form-label me-2 mb-0">Show:</label>
                <select class="form-select form-select-sm" id="per_page" name="per_page" onchange="changePerPage(this.value)" style="width: auto;">
                    <option value="5" {{ request('per_page', 5) == 5 ? 'selected' : '' }}>5</option>
                    <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10</option>
                    <option value="20" {{ request('per_page') == 20 ? 'selected' : '' }}>20</option>
                    <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                    <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                </select>
                <span class="ms-2 text-muted">per page</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-striped align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-center" style="width: 60px;">#</th>
                            <th scope="col">Bin Details</th>
                            <th scope="col">User</th>
                            <th scope="col">Financial Info</th>
                            <th scope="col">Status</th>
                            <th scope="col">Sub-Bins</th>
                            <th scope="col" class="text-center" style="width: 120px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($bins as $bin)
                            <tr>
                                <td class="text-center fw-bold">{{ $loop->iteration + ($bins->perPage() * ($bins->currentPage() - 1)) }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon me-3">
                                            <i class="fas fa-box text-primary fa-lg"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $bin->name }}</div>
                                            <div class="small text-muted">
                                                <i class="fas fa-tag me-1"></i>{{ ucfirst($bin->type) }}
                                                @if($bin->description)
                                                    <br><i class="fas fa-info-circle me-1"></i>{{ Str::limit($bin->description, 50) }}
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <a href="{{ route('admin.users.show', $bin->user->uuid) }}" class="fw-bold text-decoration-none">
                                                {{ $bin->user->name }}
                                            </a>
                                            <div class="small text-muted">{{ $bin->user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="financial-info">
                                        <div class="current-amount">
                                            <strong class="{{ $bin->current_amount < $bin->threshold_min ? 'text-danger' : 'text-success' }}">
                                                {{ $bin->currency }} {{ number_format($bin->current_amount, 2) }}
                                            </strong>
                                        </div>
                                        <div class="small text-muted">
                                            Threshold: {{ $bin->currency }} {{ number_format($bin->threshold_min, 2) }}
                                            @if($bin->threshold_max)
                                                - {{ $bin->currency }} {{ number_format($bin->threshold_max, 2) }}
                                            @else
                                                - ∞
                                            @endif
                                        </div>
                                        <div class="small text-muted">
                                            <i class="far fa-calendar-alt me-1"></i>{{ $bin->created_at->format('M d, Y') }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($bin->is_active)
                                        <span class="badge bg-success rounded-pill">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    @else
                                        <span class="badge bg-danger rounded-pill">
                                            <i class="fas fa-times-circle me-1"></i>Inactive
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="sub-bins-info">
                                        @if($bin->subBins->count() > 0)
                                            <span class="badge bg-info rounded-pill mb-1">
                                                {{ $bin->subBins->count() }} Sub-Bins
                                            </span>
                                            <br>
                                            <a href="#" class="hierarchy-toggle small" onclick="toggleHierarchy({{ $bin->id }})">
                                                <i class="fas fa-sitemap me-1"></i>View Hierarchy
                                            </a>
                                        @else
                                            <span class="badge bg-secondary rounded-pill">
                                                <i class="fas fa-minus me-1"></i>No Sub-Bins
                                            </span>
                                        @endif
                                    </div>
                                </td>
                                <td class="text-center table-actions">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewBinDetails({{ $bin->id }})" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @if($bin->subBins->count() > 0)
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="exportBinData({{ $bin->id }})" title="Export Bin Data">
                                                <i class="fas fa-download"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @if($bin->subBins->count() > 0)
                                <tr id="hierarchy-{{ $bin->id }}" class="collapse-row" style="display: none;">
                                    <td colspan="7">
                                        <div class="hierarchy-viewer collapse-content">
                                            <h6 class="mb-3">
                                                <i class="fas fa-sitemap text-primary me-2"></i>
                                                Sub-Bin Hierarchy for "{{ $bin->name }}"
                                            </h6>
                                            @include('admin.bins.partials.sub-bin-hierarchy', ['subBins' => $bin->subBins->whereNull('parent_sub_bin_id')])
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No bins found</h5>
                                        <p class="text-muted">Try adjusting your search or filter criteria</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination and Info -->
            @if($bins->hasPages() || $bins->count() > 0)
                <div class="d-flex justify-content-between align-items-center mt-4 flex-wrap">
                    <div class="mb-2 mb-md-0">
                        <span class="text-muted">
                            Showing {{ $bins->firstItem() ?? 0 }} to {{ $bins->lastItem() ?? 0 }}
                            of {{ $bins->total() }} results
                            @if(request()->hasAny(['user_id', 'search']))
                                (filtered from {{ \App\Models\Bin::count() }} total bins)
                            @endif
                        </span>
                    </div>
                    <div>
                        {{ $bins->withQueryString()->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Auto-submit on filter change
        document.getElementById('user_id').addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });

        // Auto-submit search on Enter key
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('filterForm').submit();
            }
        });
    });

    // Clear search function
    function clearSearch() {
        document.getElementById('search').value = '';
        document.getElementById('filterForm').submit();
    }

    // Change per page function
    function changePerPage(perPage) {
        const url = new URL(window.location);
        url.searchParams.set('per_page', perPage);
        url.searchParams.delete('page'); // Reset to first page when changing per_page
        window.location.href = url.toString();
    }

    // Toggle hierarchy viewer
    function toggleHierarchy(binId) {
        const hierarchyRow = document.getElementById('hierarchy-' + binId);
        const toggleLink = event.target.closest('.hierarchy-toggle');

        if (hierarchyRow.style.display === 'none') {
            hierarchyRow.style.display = 'table-row';
            toggleLink.innerHTML = '<i class="fas fa-sitemap me-1"></i>Hide Hierarchy';
        } else {
            hierarchyRow.style.display = 'none';
            toggleLink.innerHTML = '<i class="fas fa-sitemap me-1"></i>View Hierarchy';
        }
    }

    // Export data function
    function exportData(format) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Exporting...';
        button.disabled = true;

        const url = new URL(window.location);
        url.searchParams.set('export', format);

        // Create a temporary link to trigger download
        const link = document.createElement('a');
        link.href = url.toString();
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button after a delay
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 2000);
    }

    // View bin details
    function viewBinDetails(binId) {
        // Show loading state
        const button = event.target.closest('button');
        const originalHtml = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        // Fetch bin details via AJAX
        fetch(`{{ route('admin.bins.index') }}/${binId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showBinDetailsModal(data.bin);
            } else {
                alert('Error loading bin details');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading bin details');
        })
        .finally(() => {
            button.innerHTML = originalHtml;
            button.disabled = false;
        });
    }

    // Show bin details in modal
    function showBinDetailsModal(bin) {
        const modalHtml = `
            <div class="modal fade" id="binDetailsModal" tabindex="-1" aria-labelledby="binDetailsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="binDetailsModalLabel">
                                <i class="fas fa-box me-2"></i>Bin Details: ${bin.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Basic Information</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Name:</strong></td><td>${bin.name}</td></tr>
                                        <tr><td><strong>Type:</strong></td><td>${bin.type.charAt(0).toUpperCase() + bin.type.slice(1)}</td></tr>
                                        <tr><td><strong>Currency:</strong></td><td>${bin.currency}</td></tr>
                                        <tr><td><strong>Status:</strong></td><td>${bin.is_active ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>'}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">Financial Information</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Current Amount:</strong></td><td>${bin.currency} ${parseFloat(bin.current_amount).toLocaleString()}</td></tr>
                                        <tr><td><strong>Threshold Min:</strong></td><td>${bin.currency} ${parseFloat(bin.threshold_min).toLocaleString()}</td></tr>
                                        <tr><td><strong>Threshold Max:</strong></td><td>${bin.threshold_max ? bin.currency + ' ' + parseFloat(bin.threshold_max).toLocaleString() : 'Unlimited'}</td></tr>
                                    </table>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Owner Information</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Name:</strong></td><td>${bin.user.name}</td></tr>
                                        <tr><td><strong>Email:</strong></td><td>${bin.user.email}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">Statistics</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Sub-Bins:</strong></td><td>${bin.sub_bins_count}</td></tr>
                                        <tr><td><strong>Total Transactions:</strong></td><td>${bin.total_transactions}</td></tr>
                                    </table>
                                </div>
                            </div>
                            ${bin.description ? `
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="text-primary">Description</h6>
                                        <p class="text-muted">${bin.description}</p>
                                    </div>
                                </div>
                            ` : ''}
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-primary">Timestamps</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Created:</strong></td><td>${bin.created_at}</td></tr>
                                        <tr><td><strong>Updated:</strong></td><td>${bin.updated_at}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('binDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body and show
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('binDetailsModal'));
        modal.show();

        // Remove modal from DOM when hidden
        document.getElementById('binDetailsModal').addEventListener('hidden.bs.modal', function () {
            this.remove();
        });
    }

    // Export individual bin data
    function exportBinData(binId) {
        // Show loading state
        const button = event.target.closest('button');
        const originalHtml = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        const url = new URL(window.location);
        url.searchParams.set('export', 'csv');
        url.searchParams.set('bin_id', binId);

        // Create a temporary link to trigger download
        const link = document.createElement('a');
        link.href = url.toString();
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button after a delay
        setTimeout(() => {
            button.innerHTML = originalHtml;
            button.disabled = false;
        }, 2000);
    }

    // View sub-bin details
    function viewSubBinDetails(subBinId) {
        alert('View sub-bin details for ID: ' + subBinId + '\n\nThis would typically open a detailed view or modal.');
    }

    // Export sub-bin transactions
    function exportSubBinTransactions(subBinId) {
        alert('Export transactions for sub-bin ID: ' + subBinId + '\n\nThis would export all transactions for this sub-bin.');
    }

    // Toggle sub-bin children
    function toggleSubBinChildren(subBinId) {
        const childrenContainer = document.getElementById('children-' + subBinId);
        if (childrenContainer) {
            if (childrenContainer.style.display === 'none') {
                childrenContainer.style.display = 'block';
            } else {
                childrenContainer.style.display = 'none';
            }
        }
    }
</script>
@endpush
