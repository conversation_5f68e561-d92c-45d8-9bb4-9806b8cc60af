@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-university me-2"></i>Link Bank Account
                    </h5>
                </div>

                <div class="card-body">
                    <div class="text-center py-4">
                        <div class="mb-4">
                            <i class="fas fa-lock fa-3x text-primary"></i>
                        </div>
                        <h4>Securely Link Your Bank Account</h4>
                        <p class="text-muted mb-4">
                            We use Plaid to securely connect your bank account. Your credentials are never stored on our servers.
                        </p>
                        
                        <button id="linkButton" class="btn btn-primary btn-lg">
                            <i class="fas fa-link me-2"></i>Connect Your Bank
                        </button>
                    </div>
                    
                    <div class="mt-4">
                        <div class="row">
                            <div class="col-md-4 text-center mb-3">
                                <div class="mb-2">
                                    <i class="fas fa-shield-alt fa-2x text-success"></i>
                                </div>
                                <h6>Secure</h6>
                                <p class="small text-muted">Your banking credentials are never stored on our servers</p>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <div class="mb-2">
                                    <i class="fas fa-lock fa-2x text-success"></i>
                                </div>
                                <h6>Encrypted</h6>
                                <p class="small text-muted">All data is encrypted with bank-level security</p>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <div class="mb-2">
                                    <i class="fas fa-user-shield fa-2x text-success"></i>
                                </div>
                                <h6>Private</h6>
                                <p class="small text-muted">Your personal information is never sold or shared</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer text-center">
                    <a href="{{ route('plaid.index') }}" class="btn btn-link">
                        <i class="fas fa-arrow-left me-1"></i>Back to Accounts
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for submitting Plaid data -->
<form id="plaidLinkForm" action="{{ route('plaid.store') }}" method="POST" style="display: none;">
    @csrf
    <input type="hidden" name="public_token" id="public_token">
    <input type="hidden" name="metadata" id="metadata">
</form>

@endsection

@push('scripts')
<script src="https://cdn.plaid.com/link/v2/stable/link-initialize.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const linkToken = "{{ $linkToken }}";
        let handler;
        
        // Initialize Plaid Link
        const initializePlaidLink = () => {
            handler = Plaid.create({
                token: linkToken,
                onSuccess: function(public_token, metadata) {
                    // Submit form with Plaid data
                    document.getElementById('public_token').value = public_token;
                    document.getElementById('metadata').value = JSON.stringify(metadata);
                    document.getElementById('plaidLinkForm').submit();
                },
                onExit: function(err, metadata) {
                    if (err != null) {
                        console.error('Plaid Link error:', err);
                    }
                },
                onEvent: function(eventName, metadata) {
                    console.log('Plaid Link event:', eventName);
                }
            });
        };
        
        // Initialize Plaid Link when the page loads
        initializePlaidLink();
        
        // Open Plaid Link when the button is clicked
        document.getElementById('linkButton').addEventListener('click', function() {
            handler.open();
        });
    });
</script>
@endpush
