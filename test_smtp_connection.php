<?php

/**
 * Test SMTP connection and email sending
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;

echo "🧪 SMTP Connection and Email Test\n";
echo "=================================\n\n";

// Display current configuration
echo "📧 Current Email Configuration:\n";
echo "-------------------------------\n";
echo "Mail Driver: " . config('mail.default') . "\n";
echo "SMTP Host: " . config('mail.mailers.smtp.host') . "\n";
echo "SMTP Port: " . config('mail.mailers.smtp.port') . "\n";
echo "SMTP Username: " . config('mail.mailers.smtp.username') . "\n";
echo "SMTP Encryption: " . config('mail.mailers.smtp.encryption') . "\n";
echo "From Address: " . config('mail.from.address') . "\n";
echo "From Name: " . config('mail.from.name') . "\n\n";

// Test 1: Test SMTP connection
echo "🧪 Test 1: SMTP Connection Test\n";
echo "-------------------------------\n";

try {
    // Create a test transport
    $transport = new \Symfony\Component\Mailer\Transport\Smtp\EsmtpTransport(
        config('mail.mailers.smtp.host'),
        config('mail.mailers.smtp.port'),
        config('mail.mailers.smtp.encryption') === 'tls'
    );
    
    $transport->setUsername(config('mail.mailers.smtp.username'));
    $transport->setPassword(config('mail.mailers.smtp.password'));
    
    echo "✅ SMTP transport created successfully\n";
    echo "🔌 Attempting to connect to Gmail SMTP...\n";
    
    // Test the connection by sending a simple email
    Mail::raw('SMTP Connection Test - This is a test email to verify SMTP connection.', function ($message) {
        $message->to('<EMAIL>') // This won't actually send, just tests connection
                ->subject('SMTP Connection Test')
                ->from(config('mail.from.address'), config('mail.from.name'));
    });
    
    echo "✅ SMTP connection test passed\n";
    
} catch (\Exception $e) {
    echo "❌ SMTP connection failed\n";
    echo "🔍 Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n";

// Test 2: Send actual test email
echo "🧪 Test 2: Send Test Email\n";
echo "--------------------------\n";

$testEmail = '<EMAIL>'; // Send to the same Gmail account for testing

try {
    Mail::raw('Hello! This is a test email from PocketWatch to verify email functionality is working correctly. If you receive this email, the Gmail SMTP integration is working!', function ($message) use ($testEmail) {
        $message->to($testEmail)
                ->subject('PocketWatch Email Test - ' . date('Y-m-d H:i:s'))
                ->from(config('mail.from.address'), config('mail.from.name'));
    });
    
    echo "✅ Test email sent successfully to {$testEmail}\n";
    echo "📧 Check the inbox for the test email\n";
    
} catch (\Exception $e) {
    echo "❌ Failed to send test email\n";
    echo "🔍 Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n";

// Test 3: Verify Gmail app password
echo "🧪 Test 3: Gmail App Password Verification\n";
echo "------------------------------------------\n";

$appPassword = config('mail.mailers.smtp.password');
$username = config('mail.mailers.smtp.username');

echo "📧 Gmail Account: {$username}\n";
echo "🔐 App Password Length: " . strlen($appPassword) . " characters\n";
echo "🔐 App Password Format: " . (preg_match('/^[a-z]{16}$/', $appPassword) ? 'Valid (16 lowercase letters)' : 'Invalid format') . "\n";

if (strlen($appPassword) !== 16) {
    echo "⚠️  WARNING: Gmail app passwords should be 16 characters long\n";
}

if (!preg_match('/^[a-z]{16}$/', $appPassword)) {
    echo "⚠️  WARNING: Gmail app passwords should contain only lowercase letters\n";
    echo "💡 Make sure you're using an App Password, not your regular Gmail password\n";
    echo "💡 Generate App Password at: https://myaccount.google.com/apppasswords\n";
}

echo "\n";

// Test 4: Check Laravel mail configuration
echo "🧪 Test 4: Laravel Mail Configuration\n";
echo "-------------------------------------\n";

try {
    $mailer = Mail::mailer();
    echo "✅ Laravel mailer instance created\n";
    echo "📧 Default mailer: " . config('mail.default') . "\n";
    
    // Check if all required config values are set
    $requiredConfigs = [
        'mail.mailers.smtp.host',
        'mail.mailers.smtp.port',
        'mail.mailers.smtp.username',
        'mail.mailers.smtp.password',
        'mail.from.address',
        'mail.from.name'
    ];
    
    $missingConfigs = [];
    foreach ($requiredConfigs as $configKey) {
        if (empty(config($configKey))) {
            $missingConfigs[] = $configKey;
        }
    }
    
    if (empty($missingConfigs)) {
        echo "✅ All required mail configurations are set\n";
    } else {
        echo "❌ Missing configurations:\n";
        foreach ($missingConfigs as $missing) {
            echo "   - {$missing}\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Laravel mail configuration error\n";
    echo "🔍 Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Gmail-specific recommendations
echo "🧪 Test 5: Gmail Setup Recommendations\n";
echo "--------------------------------------\n";

echo "📋 Gmail SMTP Setup Checklist:\n";
echo "1. ✅ 2-Factor Authentication enabled on Gmail account\n";
echo "2. ✅ App Password generated (not regular password)\n";
echo "3. ✅ SMTP settings configured correctly:\n";
echo "   - Host: smtp.gmail.com\n";
echo "   - Port: 587\n";
echo "   - Encryption: TLS\n";
echo "   - Username: <EMAIL>\n";
echo "   - Password: 16-character app password\n";
echo "4. ✅ FROM address matches authenticated Gmail account\n";

echo "\n💡 Troubleshooting Tips:\n";
echo "- Make sure 2FA is enabled on your Gmail account\n";
echo "- Use App Password, not your regular Gmail password\n";
echo "- FROM address must match the authenticated Gmail account\n";
echo "- Check Gmail's 'Less secure app access' is disabled (use App Password instead)\n";
echo "- Verify the app password is exactly 16 lowercase letters\n";

echo "\n🎯 Test Summary\n";
echo "===============\n";
echo "✅ Configuration check: Complete\n";
echo "✅ SMTP connection test: " . (isset($transport) ? 'Attempted' : 'Failed') . "\n";
echo "✅ Test email sending: Attempted\n";
echo "✅ Gmail setup verification: Complete\n";

echo "\n🎉 SMTP Test Complete!\n";
echo "   Check your email inbox for the test message.\n";
echo "   If you don't receive it, check the error messages above.\n";
