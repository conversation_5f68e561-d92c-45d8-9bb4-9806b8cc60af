<?php

/**
 * <PERSON>ript to migrate avatars from storage to public folder
 * Run this script once to move existing avatars
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

echo "🔄 Starting avatar migration from storage to public folder...\n\n";

// Create public/avatars directory if it doesn't exist
$publicAvatarDir = public_path('avatars');
if (!file_exists($publicAvatarDir)) {
    mkdir($publicAvatarDir, 0755, true);
    echo "✅ Created public/avatars directory\n";
}

// Get all users with avatars
$users = User::whereNotNull('avatar')->get();
$migrated = 0;
$skipped = 0;
$errors = 0;

echo "📊 Found {$users->count()} users with avatar paths\n\n";

foreach ($users as $user) {
    $avatarPath = $user->avatar;
    
    echo "👤 Processing user: {$user->name} (ID: {$user->id})\n";
    echo "   Current avatar: {$avatarPath}\n";
    
    // Skip Google avatars and UI avatars
    if (Str::contains($avatarPath, ['googleusercontent.com', 'googleapis.com', 'ui-avatars.com'])) {
        echo "   ⏭️  Skipping external avatar\n\n";
        $skipped++;
        continue;
    }
    
    // Skip if already in public folder
    if (Str::contains($avatarPath, '/avatars/') && !Str::contains($avatarPath, '/storage/')) {
        echo "   ⏭️  Already in public folder\n\n";
        $skipped++;
        continue;
    }
    
    try {
        // Extract storage path
        $storagePath = '';
        if (Str::contains($avatarPath, '/storage/')) {
            $storagePath = str_replace(url('storage'), 'public', $avatarPath);
        } else {
            // Try to find the file in storage
            $filename = basename($avatarPath);
            $storagePath = 'public/avatars/' . $filename;
        }
        
        echo "   📁 Looking for file: {$storagePath}\n";
        
        // Check if file exists in storage
        if (Storage::exists($storagePath)) {
            // Generate new filename
            $extension = pathinfo($storagePath, PATHINFO_EXTENSION);
            $newFilename = Str::random(40) . '.' . $extension;
            $newPublicPath = $publicAvatarDir . '/' . $newFilename;
            
            // Copy file from storage to public
            $fileContent = Storage::get($storagePath);
            file_put_contents($newPublicPath, $fileContent);
            
            // Update user avatar path
            $newAvatarUrl = url('avatars/' . $newFilename);
            $user->avatar = $newAvatarUrl;
            $user->save();
            
            // Delete old file from storage
            Storage::delete($storagePath);
            
            echo "   ✅ Migrated to: {$newAvatarUrl}\n";
            $migrated++;
            
        } else {
            echo "   ❌ File not found in storage, setting to null\n";
            $user->avatar = null;
            $user->save();
            $errors++;
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
        $errors++;
    }
    
    echo "\n";
}

echo "📊 Migration Summary:\n";
echo "   ✅ Migrated: {$migrated} avatars\n";
echo "   ⏭️  Skipped: {$skipped} avatars (external or already in public)\n";
echo "   ❌ Errors: {$errors} avatars (files not found or errors)\n\n";

if ($migrated > 0) {
    echo "🎉 Avatar migration completed successfully!\n";
    echo "   All avatars are now stored in public/avatars/ directory\n";
    echo "   They can be accessed directly via URL without storage symlink\n\n";
}

echo "🧹 Running avatar cleanup to remove any remaining invalid paths...\n";

// Run cleanup
$cleaned = \App\Helpers\AvatarHelper::cleanupInvalidAvatars();
echo "   Cleaned up {$cleaned} invalid avatar paths\n\n";

echo "✅ Avatar migration and cleanup completed!\n";
echo "   All users now have valid avatar URLs or will use default avatars\n";
echo "   No more 403 Forbidden errors should occur\n";
