<?php

namespace App\Console\Commands;

use App\Jobs\SyncCryptoWalletData;
use App\Models\CryptoWallet;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncCryptoWallets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crypto:sync-wallets {--force : Force sync all wallets regardless of last sync time}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync all crypto wallets with blockchain data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting crypto wallet sync...');

        $query = CryptoWallet::query();

        // Only sync wallets that haven't been synced in the last 6 hours
        // unless --force flag is used
        if (!$this->option('force')) {
            $query->where(function ($q) {
                $q->whereNull('last_synced_at')
                  ->orWhere('last_synced_at', '<', now()->subHours(6));
            });
        }

        $wallets = $query->get();
        $count = $wallets->count();

        $this->info("Found {$count} wallets to sync");

        if ($count === 0) {
            $this->info('No wallets need syncing at this time');
            return Command::SUCCESS;
        }

        foreach ($wallets as $wallet) {
            try {
                // Dispatch job to sync wallet data
                SyncCryptoWalletData::dispatch($wallet);
                $this->info("Dispatched sync job for wallet {$wallet->id}");
            } catch (\Exception $e) {
                $this->error("Error dispatching sync job for wallet {$wallet->id}: {$e->getMessage()}");
                Log::error('Error dispatching crypto wallet sync job', [
                    'wallet_id' => $wallet->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $this->info('Crypto wallet sync jobs dispatched successfully');

        return Command::SUCCESS;
    }
}
