<?php

namespace App\Console\Commands;

use App\Models\Bin;
use App\Models\SubBin;
use App\Notifications\Alerts\ThresholdAlertNotification;
use Illuminate\Console\Command;

class CheckThresholdAlerts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:threshold-alerts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for bins and sub-bins that have crossed their threshold limits';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->checkBins();
        $this->checkSubBins();
        
        return Command::SUCCESS;
    }
    
    /**
     * Check bins for threshold alerts.
     */
    protected function checkBins()
    {
        $this->info('Checking bins for threshold alerts...');
        
        // Check for bins below minimum threshold
        $binsUnderMin = Bin::where('is_active', true)
            ->whereNotNull('threshold_min')
            ->whereRaw('current_amount < threshold_min')
            ->get();
            
        $this->info("Found {$binsUnderMin->count()} bins below minimum threshold");
        
        foreach ($binsUnderMin as $bin) {
            if (!$bin->user) {
                continue;
            }
            
            // Send notification
            $bin->user->notify(new ThresholdAlertNotification(
                $bin->name,
                'bin',
                $bin->current_amount,
                $bin->threshold_min,
                'min',
                $bin->currency
            ));
            
            $this->info("Sent minimum threshold alert for bin {$bin->name} to {$bin->user->email}");
        }
        
        // Check for bins above maximum threshold
        $binsOverMax = Bin::where('is_active', true)
            ->whereNotNull('threshold_max')
            ->whereRaw('current_amount > threshold_max')
            ->get();
            
        $this->info("Found {$binsOverMax->count()} bins above maximum threshold");
        
        foreach ($binsOverMax as $bin) {
            if (!$bin->user) {
                continue;
            }
            
            // Send notification
            $bin->user->notify(new ThresholdAlertNotification(
                $bin->name,
                'bin',
                $bin->current_amount,
                $bin->threshold_max,
                'max',
                $bin->currency
            ));
            
            $this->info("Sent maximum threshold alert for bin {$bin->name} to {$bin->user->email}");
        }
    }
    
    /**
     * Check sub-bins for threshold alerts.
     */
    protected function checkSubBins()
    {
        $this->info('Checking sub-bins for threshold alerts...');
        
        // Check for sub-bins below minimum threshold
        $subBinsUnderMin = SubBin::where('is_active', true)
            ->whereNotNull('threshold_min')
            ->whereRaw('current_amount < threshold_min')
            ->get();
            
        $this->info("Found {$subBinsUnderMin->count()} sub-bins below minimum threshold");
        
        foreach ($subBinsUnderMin as $subBin) {
            if (!$subBin->bin || !$subBin->bin->user) {
                continue;
            }
            
            // Send notification
            $subBin->bin->user->notify(new ThresholdAlertNotification(
                $subBin->name,
                'sub-bin',
                $subBin->current_amount,
                $subBin->threshold_min,
                'min',
                $subBin->currency
            ));
            
            $this->info("Sent minimum threshold alert for sub-bin {$subBin->name} to {$subBin->bin->user->email}");
        }
        
        // Check for sub-bins above maximum threshold
        $subBinsOverMax = SubBin::where('is_active', true)
            ->whereNotNull('threshold_max')
            ->whereRaw('current_amount > threshold_max')
            ->get();
            
        $this->info("Found {$subBinsOverMax->count()} sub-bins above maximum threshold");
        
        foreach ($subBinsOverMax as $subBin) {
            if (!$subBin->bin || !$subBin->bin->user) {
                continue;
            }
            
            // Send notification
            $subBin->bin->user->notify(new ThresholdAlertNotification(
                $subBin->name,
                'sub-bin',
                $subBin->current_amount,
                $subBin->threshold_max,
                'max',
                $subBin->currency
            ));
            
            $this->info("Sent maximum threshold alert for sub-bin {$subBin->name} to {$subBin->bin->user->email}");
        }
    }
}
