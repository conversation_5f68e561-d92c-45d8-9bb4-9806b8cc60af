<?php

namespace App\Notifications\Auth;

use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Notifications\Messages\MailMessage;

class CustomResetPasswordNotification extends ResetPassword
{
    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url(route('password.reset', [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ], false));

        // Use our custom password reset template
        return (new MailMessage)
            ->subject('Reset Your PocketWatch Password')
            ->view('emails.notification', [
                'subject' => 'Reset Your PocketWatch Password',
                'title' => 'Password Reset Request',
                'greeting' => 'Hello ' . $notifiable->name . ',',
                'content' => 'You are receiving this email because we received a password reset request for your account.
                             Click the button below to reset your password. This password reset link will expire in 60 minutes.',
                'detailsTitle' => 'Important Information',
                'details' => [
                    'Expires' => 'This link will expire in 60 minutes',
                    'Security Tip' => 'Choose a strong password that you don\'t use elsewhere',
                ],
                'actionText' => 'Reset Password',
                'actionUrl' => $url,
                'closing' => 'If you did not request a password reset, no further action is required.',
                'signature' => 'The PocketWatch Security Team',
            ]);
    }
}
