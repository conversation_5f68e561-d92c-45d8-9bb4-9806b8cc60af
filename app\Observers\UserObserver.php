<?php

namespace App\Observers;

use App\Models\Subscription;
use App\Models\User;
use App\Notifications\Subscriptions\SubscriptionCreatedNotification;
use Illuminate\Support\Facades\Log;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        // Skip creating subscription for admin users
        if ($user->is_admin) {
            return;
        }

        // Trial subscription will be created on first login
        // Send welcome notification
        try {
            $user->notify(new \App\Notifications\Auth\WelcomeNotification());

            Log::info('User registered successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send welcome notification for user: ' . $user->id, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get the features for a subscription tier.
     */
    private function getFeatures(string $tier): array
    {
        $features = [
            'base' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => 3,
                'crypto_scanner' => false,
                'unlimited_sub_bins' => false,
                'priority_notifications' => false,
                'advanced_ai_suggestions' => false,
            ],
            'premium' => [
                'secure_login' => true,
                'bank_account_linking' => true,
                'total_balance_view' => true,
                'balance_toggle' => true,
                'financial_bins' => true,
                'auto_categorization' => true,
                'manual_bin_editing' => true,
                'graphical_insights' => true,
                'recent_transactions' => true,
                'chatbot_access' => true,
                'notifications' => true,
                'max_sub_bin_levels' => -1, // Unlimited
                'crypto_scanner' => true,
                'unlimited_sub_bins' => true,
                'priority_notifications' => true,
                'advanced_ai_suggestions' => true,
            ],
        ];

        return $features[$tier] ?? $features['base'];
    }
}
