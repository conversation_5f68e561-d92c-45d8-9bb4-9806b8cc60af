<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubBin extends Model
{
    use HasFactory, SoftDeletes, HasUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'bin_id',
        'parent_sub_bin_id',
        'name',
        'type',
        'description',
        'threshold_max_limit',
        'threshold_max_warning',
        'current_amount',
        'currency',
        'is_active',
        'depth_level',
        'path',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'threshold_max_limit' => 'decimal:2',
        'threshold_max_warning' => 'decimal:2',
        'current_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'depth_level' => 'integer',
    ];

    /**
     * Get the bin that owns the sub-bin.
     */
    public function bin(): BelongsTo
    {
        return $this->belongsTo(Bin::class);
    }

    /**
     * Get the transactions for the sub-bin.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the parent sub-bin (for nested sub-bins).
     */
    public function parentSubBin(): BelongsTo
    {
        return $this->belongsTo(SubBin::class, 'parent_sub_bin_id');
    }

    /**
     * Get the child sub-bins (nested sub-bins under this sub-bin).
     */
    public function childSubBins(): HasMany
    {
        return $this->hasMany(SubBin::class, 'parent_sub_bin_id');
    }

    /**
     * Get all descendants (recursive children) of this sub-bin.
     */
    public function descendants(): HasMany
    {
        return $this->hasMany(SubBin::class, 'parent_sub_bin_id')->with('descendants');
    }

    /**
     * Get all ancestors (recursive parents) of this sub-bin.
     */
    public function ancestors()
    {
        $ancestors = collect();
        $current = $this->parentSubBin;

        while ($current) {
            $ancestors->push($current);
            $current = $current->parentSubBin;
        }

        return $ancestors;
    }

    /**
     * Get the root bin for this sub-bin (traverses up the hierarchy).
     */
    public function getRootBin()
    {
        return $this->bin;
    }

    /**
     * Check if this sub-bin is a direct child of a bin (not nested under another sub-bin).
     */
    public function isDirectChild(): bool
    {
        return $this->parent_sub_bin_id === null && $this->depth_level === 1;
    }

    /**
     * Get the parent category (from parent sub-bin or bin).
     * Used for defaulting sub-bin category to parent's category.
     */
    public function getParentCategory(): string
    {
        if ($this->parent_sub_bin_id) {
            // Get category from parent sub-bin
            return $this->parentSubBin->type ?? 'expense';
        } else {
            // Get category from parent bin
            return $this->bin->type ?? 'expense';
        }
    }

    /**
     * Check if this sub-bin has children.
     */
    public function hasChildren(): bool
    {
        return $this->childSubBins()->count() > 0;
    }

    /**
     * Get the full hierarchy path as an array.
     */
    public function getHierarchyPath(): array
    {
        if (empty($this->path)) {
            return [$this->id];
        }

        return array_map('intval', explode('/', $this->path));
    }

    /**
     * Update the path for this sub-bin and all its descendants.
     */
    public function updatePath(): void
    {
        if ($this->parent_sub_bin_id) {
            $parent = $this->parentSubBin;
            $this->path = $parent->path ? $parent->path . '/' . $this->id : (string) $this->id;
            $this->depth_level = $parent->depth_level + 1;
        } else {
            $this->path = (string) $this->id;
            $this->depth_level = 1;
        }

        $this->saveQuietly();

        // Update all descendants
        foreach ($this->childSubBins as $child) {
            $child->updatePath();
        }
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($subBin) {
            $subBin->updatePath();
        });

        static::updated(function ($subBin) {
            if ($subBin->isDirty('parent_sub_bin_id')) {
                $subBin->updatePath();
            }
        });
    }
}
