<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'stripe' => [
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook' => [
            'secret' => env('STRIPE_WEBHOOK_SECRET'),
            'tolerance' => env('STRIPE_WEBHOOK_TOLERANCE', 300),
        ],
        'prices' => [
            'base_monthly' => env('STRIPE_PRICE_BASE_MONTHLY'),
            'base_yearly' => env('STRIPE_PRICE_BASE_YEARLY'),
            'premium_monthly' => env('STRIPE_PRICE_PREMIUM_MONTHLY'),
            'premium_yearly' => env('STRIPE_PRICE_PREMIUM_YEARLY'),
        ],
    ],

    'subscription' => [
        'trial_days' => (int) env('SUBSCRIPTION_TRIAL_DAYS', 7),
        'prices' => [
            'base_monthly' => (float) env('SUBSCRIPTION_BASE_MONTHLY_PRICE', 5.00),
            'base_yearly' => (float) env('SUBSCRIPTION_BASE_YEARLY_PRICE', 50.00),
            'premium_monthly' => (float) env('SUBSCRIPTION_PREMIUM_MONTHLY_PRICE', 10.00),
            'premium_yearly' => (float) env('SUBSCRIPTION_PREMIUM_YEARLY_PRICE', 100.00),
        ],
    ],

    'moralis' => [
        'key' => env('MORALIS_API_KEY'),
        'url' => env('MORALIS_API_URL', 'https://deep-index.moralis.io/api/v2'),
    ],

    'binnit_ai' => [
        'key' => env('BINNIT_AI_API_KEY'),
        'url' => env('BINNIT_AI_API_URL', 'https://api.binnit.ai'),
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI', env('APP_URL').'/api/auth/google/callback'),
    ],

];
