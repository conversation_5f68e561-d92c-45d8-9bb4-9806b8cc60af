<!DOCTYPE html>
<html
    xmlns:o="urn:schemas-microsoft-com:office:office"
    xmlns:x="urn:schemas-microsoft-com:office:excel"
    xmlns="http://www.w3.org/TR/REC-html40"
>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="ProgId" content="Excel.Sheet" />
        <meta name="Generator" content="Microsoft Excel 15" />
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                margin: 30px;
                color: #333;
                line-height: 1.6;
            }
            h1 {
                color: #28a745;
                text-align: center;
                margin-bottom: 30px;
                font-weight: 600;
                font-size: 24pt;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 30px;
            }
            th {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                text-align: left;
                padding: 12px;
                border: 1px solid #ddd;
            }
            td {
                padding: 12px;
                border: 1px solid #ddd;
                vertical-align: top;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            .section {
                font-size: 18pt;
                font-weight: bold;
                color: #28a745;
                padding: 15px 0 10px 0;
                border-bottom: 2px solid #28a745;
                margin-top: 30px;
                margin-bottom: 15px;
            }
            .premium {
                color: #ffc107;
                font-weight: bold;
            }
            code {
                font-family: Consolas, 'Courier New', monospace;
                background-color: #f5f5f5;
                padding: 2px 4px;
                border-radius: 4px;
            }
            p {
                margin-bottom: 15px;
            }
            .info-box {
                background-color: #f8f9fa;
                border-left: 4px solid #28a745;
                padding: 15px;
                margin-bottom: 30px;
            }
            .footer {
                text-align: center;
                margin-top: 40px;
                color: #6c757d;
                font-style: italic;
            }
        </style>
    </head>
    <body>
        <h1>PocketWatch API Documentation</h1>

        <div class="info-box">
            <p><strong>Base URL:</strong> https://pocketwatch.nexusbridgellc.com/api</p>
            <p>
                <strong>Authentication:</strong> Most endpoints require a Bearer
                token in the Authorization header:
                <code>Authorization: Bearer {your_token}</code>
            </p>
            <p><strong>Note:</strong> This documentation is formatted for Excel compatibility.</p>
        </div>

        <div class="section">Authentication Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/register</td>
                    <td>POST</td>
                    <td>No</td>
                    <td>
                        name (required): User's full name email (required):
                        User's email address password (required): Password (min
                        8 characters) password_confirmation (required): Password
                        confirmation country_code (optional): Country calling
                        code phone_number (optional): Phone number
                    </td>
                    <td>
                        Register a new user. All new users start with a 7-day
                        free trial of their chosen subscription tier.
                    </td>
                </tr>
                <tr>
                    <td>/login</td>
                    <td>POST</td>
                    <td>No</td>
                    <td>
                        email (required): User's email address password
                        (required): Password
                    </td>
                    <td>Login and get authentication token.</td>
                </tr>
                <tr>
                    <td>/me</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>Get current user profile.</td>
                </tr>
                <tr>
                    <td>/logout</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>Logout and invalidate token.</td>
                </tr>
                <tr>
                    <td>/profile</td>
                    <td>PUT</td>
                    <td>Yes</td>
                    <td>
                        name (optional): User's full name email (optional):
                        User's email address country_code (optional): Country
                        calling code phone_number (optional): Phone number
                    </td>
                    <td>Update user profile.</td>
                </tr>
                <tr>
                    <td>/password</td>
                    <td>PUT</td>
                    <td>Yes</td>
                    <td>
                        current_password (required): Current password password
                        (required): New password (min 8 characters)
                        password_confirmation (required): New password
                        confirmation
                    </td>
                    <td>Change user password.</td>
                </tr>
                <tr>
                    <td>/avatar</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>
                        avatar (required): Image file (jpeg, png, jpg, gif, max
                        2MB)
                    </td>
                    <td>Update user avatar.</td>
                </tr>
                <tr>
                    <td>/forgot-password</td>
                    <td>POST</td>
                    <td>No</td>
                    <td>email (required): User's email address</td>
                    <td>Request password reset link.</td>
                </tr>
                <tr>
                    <td>/reset-password</td>
                    <td>POST</td>
                    <td>No</td>
                    <td>
                        email (required): User's email address token (required):
                        Reset token from email password (required): New password
                        (min 8 characters) password_confirmation (required): New
                        password confirmation
                    </td>
                    <td>Reset password with token.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Bin Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/bins</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>
                        page (optional): Page number per_page (optional): Items
                        per page
                    </td>
                    <td>Get all bins.</td>
                </tr>
                <tr>
                    <td>/bins</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>
                        name (required): Bin name description (optional): Bin
                        description threshold_min (required): Minimum threshold
                        amount threshold_max (optional): Maximum threshold
                        amount currency (optional): Currency code (default: USD)
                    </td>
                    <td>Create a new bin.</td>
                </tr>
                <tr>
                    <td>/bins/{id}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>id (required): Bin ID</td>
                    <td>Get bin details.</td>
                </tr>
                <tr>
                    <td>/bins/{id}</td>
                    <td>PUT</td>
                    <td>Yes</td>
                    <td>
                        id (required): Bin ID name (optional): Bin name
                        description (optional): Bin description threshold_min
                        (optional): Minimum threshold amount threshold_max
                        (optional): Maximum threshold amount currency
                        (optional): Currency code is_active (optional): Bin
                        status (boolean)
                    </td>
                    <td>Update bin.</td>
                </tr>
                <tr>
                    <td>/bins/{id}</td>
                    <td>DELETE</td>
                    <td>Yes</td>
                    <td>id (required): Bin ID</td>
                    <td>Delete bin.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Sub-Bin Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/bins/{binId}/sub-bins</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>binId (required): Parent bin ID</td>
                    <td>Get all sub-bins for a bin.</td>
                </tr>
                <tr>
                    <td>/bins/{binId}/sub-bins</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>
                        binId (required): Parent bin ID name (required): Sub-bin
                        name description (optional): Sub-bin description
                        threshold_min (required): Minimum threshold amount
                        threshold_max (optional): Maximum threshold amount
                        currency (optional): Currency code (default: USD)
                    </td>
                    <td>
                        Create a new sub-bin. Base tier users are limited to 3
                        levels of sub-bins, while Premium users have unlimited
                        sub-bin levels.
                    </td>
                </tr>
                <tr>
                    <td>/bins/{binId}/sub-bins/{id}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>
                        binId (required): Parent bin ID id (required): Sub-bin
                        ID
                    </td>
                    <td>Get sub-bin details.</td>
                </tr>
                <tr>
                    <td>/bins/{binId}/sub-bins/{id}</td>
                    <td>PUT</td>
                    <td>Yes</td>
                    <td>
                        binId (required): Parent bin ID id (required): Sub-bin
                        ID name (optional): Sub-bin name description (optional):
                        Sub-bin description threshold_min (optional): Minimum
                        threshold amount threshold_max (optional): Maximum
                        threshold amount currency (optional): Currency code
                        is_active (optional): Sub-bin status (boolean)
                    </td>
                    <td>Update sub-bin.</td>
                </tr>
                <tr>
                    <td>/bins/{binId}/sub-bins/{id}</td>
                    <td>DELETE</td>
                    <td>Yes</td>
                    <td>
                        binId (required): Parent bin ID id (required): Sub-bin
                        ID
                    </td>
                    <td>Delete sub-bin.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Transaction Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/transactions</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>
                        page (optional): Page number per_page (optional): Items
                        per page bin_id (optional): Filter by bin ID sub_bin_id
                        (optional): Filter by sub-bin ID type (optional): Filter
                        by transaction type (income, expense, transfer)
                        start_date (optional): Filter by start date end_date
                        (optional): Filter by end date category (optional):
                        Filter by category
                    </td>
                    <td>Get all transactions.</td>
                </tr>
                <tr>
                    <td>/transactions</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>
                        bin_id (optional): Bin ID sub_bin_id (optional): Sub-bin
                        ID transaction_type (required): Type (income, expense,
                        transfer) amount (required): Transaction amount currency
                        (optional): Currency code (default: USD) description
                        (optional): Transaction description category (optional):
                        Transaction category payment_method (optional): Payment
                        method transaction_date (required): Transaction date
                    </td>
                    <td>Create a new transaction.</td>
                </tr>
                <tr>
                    <td>/transactions/{id}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>id (required): Transaction ID</td>
                    <td>Get transaction details.</td>
                </tr>
                <tr>
                    <td>/transactions/{id}</td>
                    <td>PUT</td>
                    <td>Yes</td>
                    <td>
                        id (required): Transaction ID bin_id (optional): Bin ID
                        sub_bin_id (optional): Sub-bin ID transaction_type
                        (optional): Type (income, expense, transfer) amount
                        (optional): Transaction amount currency (optional):
                        Currency code description (optional): Transaction
                        description category (optional): Transaction category
                        payment_method (optional): Payment method
                        transaction_date (optional): Transaction date
                    </td>
                    <td>Update transaction.</td>
                </tr>
                <tr>
                    <td>/transactions/{id}</td>
                    <td>DELETE</td>
                    <td>Yes</td>
                    <td>id (required): Transaction ID</td>
                    <td>Delete transaction.</td>
                </tr>
                <tr>
                    <td>/transactions/stats</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>
                        period (optional): Period (day, week, month, year)
                        start_date (optional): Start date for custom period
                        end_date (optional): End date for custom period
                    </td>
                    <td>Get transaction statistics.</td>
                </tr>
                <tr>
                    <td>/transactions/by-bin/{binId}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>
                        binId (required): Bin ID start_date (optional): Filter
                        by start date end_date (optional): Filter by end date
                        type (optional): Filter by transaction type page
                        (optional): Page number per_page (optional): Items per
                        page
                    </td>
                    <td>Get transactions by bin.</td>
                </tr>
                <tr>
                    <td>/transactions/by-category/{category}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>
                        category (required): Category name start_date
                        (optional): Filter by start date end_date (optional):
                        Filter by end date type (optional): Filter by
                        transaction type page (optional): Page number per_page
                        (optional): Items per page
                    </td>
                    <td>Get transactions by category.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Subscription Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/subscriptions</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>Get all subscriptions.</td>
                </tr>
                <tr>
                    <td>/subscriptions</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>
                        subscription_tier (required): Subscription tier (base,
                        premium) billing_cycle (required): Billing cycle
                        (monthly, yearly)
                    </td>
                    <td>
                        Create a new subscription with 7-day free trial. Base
                        tier is $5/month or $50/year. Premium tier is $10/month
                        or $100/year.
                    </td>
                </tr>
                <tr>
                    <td>/subscriptions/{id}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>id (required): Subscription ID</td>
                    <td>Get subscription details.</td>
                </tr>
                <tr>
                    <td>/subscriptions/{id}</td>
                    <td>PUT</td>
                    <td>Yes</td>
                    <td>
                        id (required): Subscription ID subscription_tier
                        (required): New subscription tier
                    </td>
                    <td>Update subscription.</td>
                </tr>
                <tr>
                    <td>/subscriptions/{id}</td>
                    <td>DELETE</td>
                    <td>Yes</td>
                    <td>id (required): Subscription ID</td>
                    <td>Cancel subscription.</td>
                </tr>
                <tr>
                    <td>/subscriptions/plans</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>Get available subscription plans.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Crypto Wallet Endpoints (Premium Feature)</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/crypto-wallets</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>
                        Get all crypto wallets. Requires Premium subscription or
                        active trial.
                    </td>
                </tr>
                <tr>
                    <td>/crypto-wallets</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>
                        wallet_address (required): Blockchain wallet address
                        wallet_name (optional): Custom wallet name
                        blockchain_network (required): Network (ethereum,
                        binance, polygon, avalanche)
                    </td>
                    <td>
                        Create a new crypto wallet. Requires Premium
                        subscription or active trial.
                    </td>
                </tr>
                <tr>
                    <td>/crypto-wallets/{id}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>id (required): Wallet ID</td>
                    <td>
                        Get crypto wallet details. Requires Premium subscription
                        or active trial.
                    </td>
                </tr>
                <tr>
                    <td>/crypto-wallets/{id}</td>
                    <td>PUT</td>
                    <td>Yes</td>
                    <td>
                        id (required): Wallet ID wallet_name (optional): Custom
                        wallet name is_active (optional): Wallet status
                        (boolean)
                    </td>
                    <td>
                        Update crypto wallet. Requires Premium subscription or
                        active trial.
                    </td>
                </tr>
                <tr>
                    <td>/crypto-wallets/{id}</td>
                    <td>DELETE</td>
                    <td>Yes</td>
                    <td>id (required): Wallet ID</td>
                    <td>
                        Delete crypto wallet. Requires Premium subscription or
                        active trial.
                    </td>
                </tr>
                <tr>
                    <td>/crypto-wallets/{id}/assets</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>id (required): Wallet ID</td>
                    <td>
                        Get crypto wallet assets. Requires Premium subscription
                        or active trial.
                    </td>
                </tr>
                <tr>
                    <td>/crypto-wallets/{id}/transactions</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>id (required): Wallet ID</td>
                    <td>
                        Get crypto wallet transactions. Requires Premium
                        subscription or active trial.
                    </td>
                </tr>
                <tr>
                    <td>/crypto-wallets/{id}/sync</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>id (required): Wallet ID</td>
                    <td>
                        Sync crypto wallet with blockchain. Requires Premium
                        subscription or active trial.
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="section">Webhook Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/webhook/stripe</td>
                    <td>POST</td>
                    <td>No</td>
                    <td>
                        Stripe-Signature (header, required): Stripe webhook
                        signature
                    </td>
                    <td>Handle Stripe webhook events.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Subscription Tiers</div>

        <table>
            <thead>
                <tr>
                    <th>Feature</th>
                    <th>Base Tier ($5/month)</th>
                    <th>Premium Tier ($10/month)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Secure user registration and login</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Link bank accounts via Plaid</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>View total balance on dashboard</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Hide/Show balance with eye toggle</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Create financial bins with thresholds</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Automatic transaction categorization</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Edit bins manually</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>View graphical insights</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>View recent transactions carousel</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Chat with Binnit chatbot</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Notification system</td>
                    <td>Yes</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Sub-Bin Levels</td>
                    <td>Maximum 3 levels</td>
                    <td>Unlimited</td>
                </tr>
                <tr>
                    <td>Crypto Scanner Page</td>
                    <td>No</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Priority notifications</td>
                    <td>No</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Advanced Binnit AI suggestions</td>
                    <td>No</td>
                    <td>Yes</td>
                </tr>
                <tr>
                    <td>Free Trial</td>
                    <td colspan="2" style="text-align: center">
                        7 days for all new users
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="section">Reports Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/reports</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>
                        page (optional): Page number per_page (optional): Items
                        per page
                    </td>
                    <td>Get all reports for the authenticated user.</td>
                </tr>
                <tr>
                    <td>/reports</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>
                        name (required): Report name type (required): Report
                        type (transaction, bin, summary) format (required):
                        Report format (csv, pdf) period_type (required): Time
                        period (daily, weekly, monthly, custom) start_date
                        (required if period_type is custom): Start date end_date
                        (required if period_type is custom): End date bin_id
                        (optional): Filter by bin ID sub_bin_id (optional):
                        Filter by sub-bin ID transaction_type (optional): Filter
                        by transaction type (income, expense) min_amount
                        (optional): Filter by minimum amount max_amount
                        (optional): Filter by maximum amount is_recurring
                        (optional): Whether the report is recurring schedule
                        (required if is_recurring is true): Schedule (daily,
                        weekly, monthly)
                    </td>
                    <td>
                        Create a new report. The report will be generated
                        asynchronously and will be available for download when
                        completed.
                    </td>
                </tr>
                <tr>
                    <td>/reports/{id}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>id (required): Report ID</td>
                    <td>Get details of a specific report.</td>
                </tr>
                <tr>
                    <td>/reports/{id}</td>
                    <td>DELETE</td>
                    <td>Yes</td>
                    <td>id (required): Report ID</td>
                    <td>Delete a report.</td>
                </tr>
                <tr>
                    <td>/reports/{id}/download</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>id (required): Report ID</td>
                    <td>
                        Download a report file (CSV or PDF). The report must be
                        in 'completed' status.
                    </td>
                </tr>
                <tr>
                    <td>/reports/{id}/regenerate</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>id (required): Report ID</td>
                    <td>
                        Regenerate a report. This is useful if the report
                        generation failed or if you want to update the report
                        with the latest data.
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="section">Plaid Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/plaid/link-token</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>Get a Plaid link token to initialize the Plaid Link flow.</td>
                </tr>
                <tr>
                    <td>/plaid/accounts</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>public_token (required): Public token from Plaid Link
accounts (required): Array of selected accounts</td>
                    <td>Link Plaid accounts to the user's profile.</td>
                </tr>
                <tr>
                    <td>/plaid/accounts</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>Get the user's linked Plaid accounts.</td>
                </tr>
                <tr>
                    <td>/plaid/accounts/{id}/set-default</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>id (required): Plaid account ID</td>
                    <td>Set a Plaid account as the default account.</td>
                </tr>
                <tr>
                    <td>/plaid/accounts/{id}/toggle-payment</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>id (required): Plaid account ID</td>
                    <td>Enable or disable payment for a Plaid account.</td>
                </tr>
                <tr>
                    <td>/plaid/accounts/{id}</td>
                    <td>DELETE</td>
                    <td>Yes</td>
                    <td>id (required): Plaid account ID</td>
                    <td>Delete (unlink) a Plaid account.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Plaid Payment Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/plaid-payment/accounts</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>Get the user's payment-enabled Plaid accounts.</td>
                </tr>
                <tr>
                    <td>/plaid-payment/process</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>plaid_account_id (required): ID of the Plaid account to use for payment
subscription_tier (required): Subscription tier (base, premium)
billing_cycle (required): Billing cycle (monthly, yearly)</td>
                    <td>Process a payment using a Plaid account.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Plaid Webhook Endpoints</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/webhook/plaid</td>
                    <td>POST</td>
                    <td>No</td>
                    <td>Plaid-Signature (header, required): Plaid webhook signature</td>
                    <td>Handle Plaid webhook events for account updates, transactions, and payment status changes.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Crypto Wallet Endpoints (Premium Feature)</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Auth Required</th>
                    <th>Parameters</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/crypto/connect</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>wallet_address (required): Cryptocurrency wallet address (e.g., Ethereum address)
blockchain_network (required): Network type (ethereum, binance, polygon, etc.)
wallet_name (optional): Custom name for the wallet</td>
                    <td>Connect a cryptocurrency wallet to the user's account. Requires Premium subscription.</td>
                </tr>
                <tr>
                    <td>/crypto/wallets</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>None</td>
                    <td>Get all connected cryptocurrency wallets for the user. Requires Premium subscription.</td>
                </tr>
                <tr>
                    <td>/crypto/portfolio/{wallet_id}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>wallet_id (required): ID of the connected wallet</td>
                    <td>Get detailed portfolio information for a connected wallet. Requires Premium subscription.</td>
                </tr>
                <tr>
                    <td>/crypto/advice/{wallet_id}</td>
                    <td>GET</td>
                    <td>Yes</td>
                    <td>wallet_id (required): ID of the connected wallet</td>
                    <td>Get AI-generated investment advice for the wallet's portfolio. Requires Premium subscription.</td>
                </tr>
                <tr>
                    <td>/crypto/refresh/{wallet_id}</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>wallet_id (required): ID of the connected wallet</td>
                    <td>Manually trigger a refresh of the wallet's portfolio data. Requires Premium subscription.</td>
                </tr>
                <tr>
                    <td>/crypto/analyze</td>
                    <td>POST</td>
                    <td>Yes</td>
                    <td>wallet (required): Wallet address
tokens (required): JSON object containing token holdings</td>
                    <td>Send portfolio data to Binnit AI for analysis and receive investment advice. This endpoint is primarily used by the backend system. Requires Premium subscription.</td>
                </tr>
                <tr>
                    <td>/crypto/wallets/{wallet_id}</td>
                    <td>DELETE</td>
                    <td>Yes</td>
                    <td>wallet_id (required): ID of the connected wallet</td>
                    <td>Disconnect a cryptocurrency wallet from the user's account. Requires Premium subscription.</td>
                </tr>
            </tbody>
        </table>

        <div class="section">Rate Limits</div>

        <table>
            <thead>
                <tr>
                    <th>Endpoint Group</th>
                    <th>Rate Limit</th>
                    <th>Time Window</th>
                    <th>Notes</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Standard Endpoints</td>
                    <td>60 requests</td>
                    <td>Per minute</td>
                    <td>Applied per user or IP address</td>
                </tr>
                <tr>
                    <td>Crypto Endpoints</td>
                    <td>100 requests</td>
                    <td>Per hour</td>
                    <td>Applied per user to prevent API abuse</td>
                </tr>
                <tr>
                    <td>Webhook Endpoints</td>
                    <td>1000 requests</td>
                    <td>Per day</td>
                    <td>Applied per webhook source (Stripe, Plaid)</td>
                </tr>
            </tbody>
        </table>

        <div class="footer">
            <p>PocketWatch API Documentation | Last updated: September 15, 2024</p>
        </div>
    </body>
</html>
