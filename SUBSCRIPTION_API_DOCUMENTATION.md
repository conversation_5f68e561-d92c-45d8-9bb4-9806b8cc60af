# PocketWatch Subscription API Documentation

## Overview

The Subscription API allows users to manage their subscription plans, including creating new subscriptions, upgrading/downgrading plans, changing billing cycles, and viewing subscription history.

## Base URL

All API endpoints are relative to: `https://pocketwatch.nexusbridgellc.com/api`

## Authentication

All subscription endpoints (except webhook) require authentication via Bear<PERSON> token.

Include the following header in all requests:
```
Authorization: Bearer {your_token}
```

## Subscription Tiers

PocketWatch offers two subscription tiers:

1. **Base Tier**
   - $5/month or $50/year
   - Features:
     - Financial bins with thresholds
     - Transaction categorization
     - Graphical insights
     - Up to 3 levels of sub-bins

2. **Premium Tier**
   - $10/month or $100/year
   - Features:
     - All Base Tier features
     - Unlimited sub-bin levels
     - Crypto Scanner
     - Priority notifications
     - Advanced AI suggestions

## Free Trial

All new users automatically receive a 7-day free trial of the Base Tier. During the trial period, users can:
- Access all Base Tier features
- Upgrade to Premium Tier at any time
- Cancel before the trial ends to avoid charges

## API Endpoints

### Get All Subscriptions

Retrieves all subscriptions for the authenticated user.

- **URL**: `/subscriptions`
- **Method**: `GET`
- **Authentication**: Required
- **Parameters**: None

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "subscriptions": [
      {
        "id": 1,
        "user_id": 123,
        "name": "Premium Monthly",
        "stripe_id": "sub_1234567890",
        "stripe_status": "active",
        "stripe_price": "price_1234567890",
        "subscription_tier": "premium",
        "billing_cycle": "monthly",
        "price": 10.00,
        "currency": "USD",
        "trial_ends_at": "2023-06-01T00:00:00Z",
        "ends_at": null,
        "created_at": "2023-05-25T00:00:00Z",
        "updated_at": "2023-05-25T00:00:00Z",
        "features": {
          "secure_login": true,
          "bank_account_linking": true,
          "total_balance_view": true,
          "balance_toggle": true,
          "financial_bins": true,
          "auto_categorization": true,
          "manual_bin_editing": true,
          "graphical_insights": true,
          "recent_transactions": true,
          "chatbot_access": true,
          "notifications": true,
          "max_sub_bin_levels": -1,
          "crypto_scanner": true,
          "unlimited_sub_bins": true,
          "priority_notifications": true,
          "advanced_ai_suggestions": true
        }
      }
    ]
  }
}
```

### Get Subscription Plans

Retrieves available subscription plans.

- **URL**: `/subscriptions/plans`
- **Method**: `GET`
- **Authentication**: Required
- **Parameters**: None

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": "base_monthly",
        "name": "Base Monthly",
        "description": "Essential financial management tools",
        "price": 5.00,
        "currency": "USD",
        "interval": "month",
        "stripe_price_id": "price_base_monthly",
        "features": [
          "Financial bins with thresholds",
          "Transaction categorization",
          "Graphical insights",
          "Up to 3 levels of sub-bins"
        ]
      },
      {
        "id": "base_yearly",
        "name": "Base Yearly",
        "description": "Essential financial management tools (yearly)",
        "price": 50.00,
        "currency": "USD",
        "interval": "year",
        "stripe_price_id": "price_base_yearly",
        "features": [
          "Financial bins with thresholds",
          "Transaction categorization",
          "Graphical insights",
          "Up to 3 levels of sub-bins"
        ]
      },
      {
        "id": "premium_monthly",
        "name": "Premium Monthly",
        "description": "Advanced financial management with premium features",
        "price": 10.00,
        "currency": "USD",
        "interval": "month",
        "stripe_price_id": "price_premium_monthly",
        "features": [
          "All Base Tier features",
          "Unlimited sub-bin levels",
          "Crypto Scanner",
          "Priority notifications",
          "Advanced AI suggestions"
        ]
      },
      {
        "id": "premium_yearly",
        "name": "Premium Yearly",
        "description": "Advanced financial management with premium features (yearly)",
        "price": 100.00,
        "currency": "USD",
        "interval": "year",
        "stripe_price_id": "price_premium_yearly",
        "features": [
          "All Base Tier features",
          "Unlimited sub-bin levels",
          "Crypto Scanner",
          "Priority notifications",
          "Advanced AI suggestions"
        ]
      }
    ],
    "trial_days": 7
  }
}
```

### Create Subscription

Creates a new subscription for the authenticated user.

- **URL**: `/subscriptions`
- **Method**: `POST`
- **Authentication**: Required
- **Parameters**:
  - `subscription_tier` (required): Subscription tier (`base` or `premium`)
  - `billing_cycle` (required): Billing cycle (`monthly` or `yearly`)

#### Success Response

- **Code**: 201 Created
- **Content**:
```json
{
  "success": true,
  "data": {
    "checkout_url": "https://checkout.stripe.com/c/pay/cs_test_1234567890",
    "subscription": {
      "id": 1,
      "user_id": 123,
      "name": "Premium Monthly",
      "stripe_id": null,
      "stripe_status": "pending",
      "stripe_price": "price_premium_monthly",
      "subscription_tier": "premium",
      "billing_cycle": "monthly",
      "price": 10.00,
      "currency": "USD",
      "trial_ends_at": "2023-06-01T00:00:00Z",
      "ends_at": null,
      "created_at": "2023-05-25T00:00:00Z",
      "updated_at": "2023-05-25T00:00:00Z"
    }
  }
}
```

#### Error Response

- **Code**: 422 Unprocessable Entity
- **Content**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "subscription_tier": [
      "The subscription tier field is required."
    ],
    "billing_cycle": [
      "The billing cycle field is required."
    ]
  }
}
```

### Get Subscription Details

Retrieves details for a specific subscription.

- **URL**: `/subscriptions/{id}`
- **Method**: `GET`
- **Authentication**: Required
- **URL Parameters**:
  - `id` (required): Subscription ID

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "subscription": {
      "id": 1,
      "user_id": 123,
      "name": "Premium Monthly",
      "stripe_id": "sub_1234567890",
      "stripe_status": "active",
      "stripe_price": "price_premium_monthly",
      "subscription_tier": "premium",
      "billing_cycle": "monthly",
      "price": 10.00,
      "currency": "USD",
      "trial_ends_at": "2023-06-01T00:00:00Z",
      "ends_at": null,
      "created_at": "2023-05-25T00:00:00Z",
      "updated_at": "2023-05-25T00:00:00Z",
      "features": {
        "secure_login": true,
        "bank_account_linking": true,
        "total_balance_view": true,
        "balance_toggle": true,
        "financial_bins": true,
        "auto_categorization": true,
        "manual_bin_editing": true,
        "graphical_insights": true,
        "recent_transactions": true,
        "chatbot_access": true,
        "notifications": true,
        "max_sub_bin_levels": -1,
        "crypto_scanner": true,
        "unlimited_sub_bins": true,
        "priority_notifications": true,
        "advanced_ai_suggestions": true
      }
    }
  }
}
```

#### Error Response

- **Code**: 404 Not Found
- **Content**:
```json
{
  "success": false,
  "message": "Subscription not found"
}
```

### Update Subscription

Updates a subscription (e.g., upgrade/downgrade tier).

- **URL**: `/subscriptions/{id}`
- **Method**: `PUT`
- **Authentication**: Required
- **URL Parameters**:
  - `id` (required): Subscription ID
- **Parameters**:
  - `subscription_tier` (required): New subscription tier (`base` or `premium`)

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "message": "Subscription updated successfully",
  "data": {
    "subscription": {
      "id": 1,
      "user_id": 123,
      "name": "Premium Monthly",
      "stripe_id": "sub_1234567890",
      "stripe_status": "active",
      "stripe_price": "price_premium_monthly",
      "subscription_tier": "premium",
      "billing_cycle": "monthly",
      "price": 10.00,
      "currency": "USD",
      "trial_ends_at": "2023-06-01T00:00:00Z",
      "ends_at": null,
      "created_at": "2023-05-25T00:00:00Z",
      "updated_at": "2023-05-25T00:00:00Z"
    }
  }
}
```

### Change Billing Cycle

Changes the billing cycle of a subscription.

- **URL**: `/subscriptions/{id}/change-cycle`
- **Method**: `PUT`
- **Authentication**: Required
- **URL Parameters**:
  - `id` (required): Subscription ID
- **Parameters**:
  - `billing_cycle` (required): New billing cycle (`monthly` or `yearly`)

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "message": "Billing cycle updated successfully",
  "data": {
    "subscription": {
      "id": 1,
      "user_id": 123,
      "name": "Premium Yearly",
      "stripe_id": "sub_1234567890",
      "stripe_status": "active",
      "stripe_price": "price_premium_yearly",
      "subscription_tier": "premium",
      "billing_cycle": "yearly",
      "price": 100.00,
      "currency": "USD",
      "trial_ends_at": "2023-06-01T00:00:00Z",
      "ends_at": null,
      "created_at": "2023-05-25T00:00:00Z",
      "updated_at": "2023-05-25T00:00:00Z"
    }
  }
}
```

### Cancel Subscription

Cancels a subscription.

- **URL**: `/subscriptions/{id}`
- **Method**: `DELETE`
- **Authentication**: Required
- **URL Parameters**:
  - `id` (required): Subscription ID
- **Parameters**:
  - `cancel_reason` (optional): Reason for cancellation
  - `cancel_feedback` (optional): Additional feedback

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "message": "Subscription canceled successfully",
  "data": {
    "subscription": {
      "id": 1,
      "user_id": 123,
      "name": "Premium Monthly",
      "stripe_id": "sub_1234567890",
      "stripe_status": "canceled",
      "stripe_price": "price_premium_monthly",
      "subscription_tier": "premium",
      "billing_cycle": "monthly",
      "price": 10.00,
      "currency": "USD",
      "trial_ends_at": "2023-06-01T00:00:00Z",
      "ends_at": "2023-06-25T00:00:00Z",
      "created_at": "2023-05-25T00:00:00Z",
      "updated_at": "2023-05-25T00:00:00Z"
    }
  }
}
```

### Pause Subscription

Pauses a subscription.

- **URL**: `/subscriptions/{id}/pause`
- **Method**: `POST`
- **Authentication**: Required
- **URL Parameters**:
  - `id` (required): Subscription ID
- **Parameters**:
  - `pause_duration` (optional): Duration in days to pause (default: 30)

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "message": "Subscription paused successfully",
  "data": {
    "subscription": {
      "id": 1,
      "user_id": 123,
      "name": "Premium Monthly",
      "stripe_id": "sub_1234567890",
      "stripe_status": "paused",
      "stripe_price": "price_premium_monthly",
      "subscription_tier": "premium",
      "billing_cycle": "monthly",
      "price": 10.00,
      "currency": "USD",
      "trial_ends_at": "2023-06-01T00:00:00Z",
      "ends_at": null,
      "paused_at": "2023-05-25T00:00:00Z",
      "resume_at": "2023-06-24T00:00:00Z",
      "created_at": "2023-05-25T00:00:00Z",
      "updated_at": "2023-05-25T00:00:00Z"
    }
  }
}
```

### Resume Subscription

Resumes a paused subscription.

- **URL**: `/subscriptions/{id}/resume`
- **Method**: `POST`
- **Authentication**: Required
- **URL Parameters**:
  - `id` (required): Subscription ID

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "message": "Subscription resumed successfully",
  "data": {
    "subscription": {
      "id": 1,
      "user_id": 123,
      "name": "Premium Monthly",
      "stripe_id": "sub_1234567890",
      "stripe_status": "active",
      "stripe_price": "price_premium_monthly",
      "subscription_tier": "premium",
      "billing_cycle": "monthly",
      "price": 10.00,
      "currency": "USD",
      "trial_ends_at": "2023-06-01T00:00:00Z",
      "ends_at": null,
      "paused_at": null,
      "resume_at": null,
      "created_at": "2023-05-25T00:00:00Z",
      "updated_at": "2023-05-25T00:00:00Z"
    }
  }
}
```

### Get Subscription History

Retrieves the subscription history for the authenticated user.

- **URL**: `/subscriptions/history`
- **Method**: `GET`
- **Authentication**: Required
- **Parameters**:
  - `page` (optional): Page number (default: 1)
  - `per_page` (optional): Items per page (default: 15)

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "history": [
      {
        "id": 1,
        "subscription_id": 1,
        "event_type": "created",
        "previous_tier": null,
        "new_tier": "base",
        "previous_billing_cycle": null,
        "new_billing_cycle": "monthly",
        "previous_status": null,
        "new_status": "trialing",
        "created_at": "2023-05-25T00:00:00Z"
      },
      {
        "id": 2,
        "subscription_id": 1,
        "event_type": "upgraded",
        "previous_tier": "base",
        "new_tier": "premium",
        "previous_billing_cycle": "monthly",
        "new_billing_cycle": "monthly",
        "previous_status": "trialing",
        "new_status": "trialing",
        "created_at": "2023-05-26T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 2,
      "count": 2,
      "per_page": 15,
      "current_page": 1,
      "total_pages": 1
    }
  }
}
```

### Stripe Webhook

Handles Stripe webhook events.

- **URL**: `/webhook/stripe`
- **Method**: `POST`
- **Authentication**: None (uses Stripe signature verification)
- **Headers**:
  - `Stripe-Signature` (required): Stripe webhook signature

#### Success Response

- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "message": "Webhook processed successfully"
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - The request was malformed |
| 401 | Unauthorized - Authentication is required |
| 403 | Forbidden - You don't have permission to access this resource |
| 404 | Not Found - The requested resource was not found |
| 422 | Unprocessable Entity - Validation failed |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Something went wrong on our end |

## Rate Limiting

API requests are limited to 60 requests per minute per user. If you exceed this limit, you'll receive a 429 Too Many Requests response.

## Changelog

- **2023-06-01**: Initial API documentation
- **2023-07-15**: Added pause/resume endpoints
- **2023-08-10**: Added subscription history endpoint
- **2023-09-05**: Added change billing cycle endpoint
