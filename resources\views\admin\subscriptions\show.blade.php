@extends('admin.layouts.app')

@section('title', 'Subscription Details')
@section('subtitle', 'View and manage subscription information')

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('admin.subscriptions.index') }}">Subscriptions</a></li>
    <li class="breadcrumb-item">Details</li>
@endsection

@section('actions')
    <div class="btn-group">
        <a href="{{ route('admin.subscriptions.edit', $subscription->id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit
        </a>
        <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
            <span class="visually-hidden">Toggle Dropdown</span>
        </button>
        <ul class="dropdown-menu dropdown-menu-end">
            @if($subscription->stripe_status === 'active' || $subscription->stripe_status === 'trialing')
                <li>
                    <button class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#cancelModal">
                        <i class="fas fa-ban me-2 text-danger"></i> Cancel
                    </button>
                </li>
                <li>
                    <button class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#pauseModal">
                        <i class="fas fa-pause me-2 text-warning"></i> Pause
                    </button>
                </li>
            @endif
            
            @if($subscription->stripe_status === 'canceled' && $subscription->ends_at && $subscription->ends_at->isFuture())
                <li>
                    <button class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#resumeModal">
                        <i class="fas fa-play me-2 text-success"></i> Resume
                    </button>
                </li>
            @endif
            
            @if($subscription->stripe_status === 'paused')
                <li>
                    <button class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#unpauseModal">
                        <i class="fas fa-play me-2 text-success"></i> Unpause
                    </button>
                </li>
            @endif
            
            <li><hr class="dropdown-divider"></li>
            <li>
                <button class="dropdown-item text-danger" type="button" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    <i class="fas fa-trash-alt me-2"></i> Delete
                </button>
            </li>
        </ul>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-xl-4 col-lg-5">
            <!-- Subscription Info Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Information</h6>
                    <span class="badge {{ $subscription->stripe_status === 'active' ? 'bg-success' : ($subscription->stripe_status === 'trialing' ? 'bg-info' : 'bg-secondary') }}">
                        {{ ucfirst($subscription->stripe_status) }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="text-muted mb-1">Subscription ID</div>
                        <div class="fw-bold">{{ $subscription->id }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Stripe ID</div>
                        <div class="fw-bold">
                            @if($subscription->stripe_id)
                                <span class="text-monospace">{{ $subscription->stripe_id }}</span>
                                <button class="btn btn-sm btn-link p-0 ms-2" 
                                        onclick="navigator.clipboard.writeText('{{ $subscription->stripe_id }}'); alert('ID copied!');">
                                    <i class="fas fa-copy"></i>
                                </button>
                            @else
                                <span class="text-muted">N/A</span>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Plan</div>
                        <div class="fw-bold">
                            <span class="badge {{ $subscription->subscription_tier === 'premium' ? 'bg-success' : 'bg-secondary' }}">
                                {{ ucfirst($subscription->subscription_tier) }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Billing Cycle</div>
                        <div class="fw-bold">{{ ucfirst($subscription->billing_cycle) }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Price</div>
                        <div class="fw-bold">${{ number_format($subscription->price, 2) }} / {{ $subscription->billing_cycle }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Created On</div>
                        <div class="fw-bold">{{ $subscription->created_at->format('M d, Y H:i') }}</div>
                    </div>
                    
                    @if($subscription->trial_ends_at)
                        <div class="mb-3">
                            <div class="text-muted mb-1">Trial Ends</div>
                            <div class="fw-bold">
                                {{ $subscription->trial_ends_at->format('M d, Y') }}
                                @if($subscription->onTrial())
                                    <span class="badge badge-soft-info ms-1">
                                        {{ now()->diffInDays($subscription->trial_ends_at) }} days left
                                    </span>
                                @endif
                            </div>
                        </div>
                    @endif
                    
                    @if($subscription->ends_at)
                        <div class="mb-3">
                            <div class="text-muted mb-1">Ends On</div>
                            <div class="fw-bold {{ $subscription->ends_at->isPast() ? 'text-danger' : 'text-warning' }}">
                                {{ $subscription->ends_at->format('M d, Y') }}
                            </div>
                        </div>
                    @endif
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Last Updated</div>
                        <div class="fw-bold">{{ $subscription->updated_at->format('M d, Y H:i') }}</div>
                    </div>
                </div>
            </div>
            
            <!-- User Info Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="user-avatar me-3" style="width: 50px; height: 50px; font-size: 1.5rem;">
                            {{ substr($subscription->user->name, 0, 1) }}
                        </div>
                        <div>
                            <h5 class="mb-0">{{ $subscription->user->name }}</h5>
                            <div class="text-muted">{{ $subscription->user->email }}</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">User ID</div>
                        <div class="fw-bold">{{ $subscription->user->id }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Status</div>
                        <div class="fw-bold">
                            <span class="badge {{ $subscription->user->is_active ? 'bg-success' : 'bg-danger' }}">
                                {{ $subscription->user->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Stripe Customer ID</div>
                        <div class="fw-bold">
                            @if($subscription->user->stripe_id)
                                <span class="text-monospace">{{ $subscription->user->stripe_id }}</span>
                                <button class="btn btn-sm btn-link p-0 ms-2" 
                                        onclick="navigator.clipboard.writeText('{{ $subscription->user->stripe_id }}'); alert('ID copied!');">
                                    <i class="fas fa-copy"></i>
                                </button>
                            @else
                                <span class="text-muted">N/A</span>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="text-muted mb-1">Registered On</div>
                        <div class="fw-bold">{{ $subscription->user->created_at->format('M d, Y') }}</div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.users.show', $subscription->user_id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-user me-1"></i> View User Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-8 col-lg-7">
            <!-- Subscription Features Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Features</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Feature</th>
                                    <th>Status</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($subscription->features as $feature => $value)
                                    <tr>
                                        <td>{{ ucwords(str_replace('_', ' ', $feature)) }}</td>
                                        <td>
                                            @if(is_bool($value))
                                                @if($value)
                                                    <span class="badge bg-success">Enabled</span>
                                                @else
                                                    <span class="badge bg-danger">Disabled</span>
                                                @endif
                                            @elseif(is_numeric($value))
                                                @if($value < 0)
                                                    <span class="badge bg-success">Unlimited</span>
                                                @else
                                                    <span class="badge bg-info">{{ $value }}</span>
                                                @endif
                                            @else
                                                <span class="badge bg-secondary">{{ $value }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @switch($feature)
                                                @case('secure_login')
                                                    Secure authentication system
                                                    @break
                                                @case('bank_account_linking')
                                                    Connect bank accounts for automatic tracking
                                                    @break
                                                @case('total_balance_view')
                                                    View total balance across all accounts
                                                    @break
                                                @case('balance_toggle')
                                                    Toggle balance visibility for privacy
                                                    @break
                                                @case('financial_bins')
                                                    Organize finances into customizable bins
                                                    @break
                                                @case('auto_categorization')
                                                    Automatically categorize transactions
                                                    @break
                                                @case('manual_bin_editing')
                                                    Manually edit and organize bins
                                                    @break
                                                @case('graphical_insights')
                                                    Visual charts and graphs of financial data
                                                    @break
                                                @case('recent_transactions')
                                                    View recent transaction history
                                                    @break
                                                @case('chatbot_access')
                                                    Access to AI financial assistant
                                                    @break
                                                @case('notifications')
                                                    Receive financial alerts and notifications
                                                    @break
                                                @case('max_sub_bin_levels')
                                                    Maximum number of nested sub-bin levels
                                                    @break
                                                @case('crypto_scanner')
                                                    Track and analyze cryptocurrency assets
                                                    @break
                                                @case('unlimited_sub_bins')
                                                    Create unlimited sub-bins for organization
                                                    @break
                                                @case('priority_notifications')
                                                    Receive priority financial alerts
                                                    @break
                                                @case('advanced_ai_suggestions')
                                                    Advanced AI-powered financial suggestions
                                                    @break
                                                @default
                                                    {{ ucwords(str_replace('_', ' ', $feature)) }}
                                            @endswitch
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Stripe Information Card -->
            @if($stripeSubscription)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Stripe Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="text-muted mb-1">Stripe Status</div>
                                <div class="fw-bold">{{ ucfirst($stripeSubscription->status) }}</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="text-muted mb-1">Current Period</div>
                                <div class="fw-bold">
                                    {{ date('M d, Y', $stripeSubscription->current_period_start) }} - 
                                    {{ date('M d, Y', $stripeSubscription->current_period_end) }}
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="text-muted mb-1">Collection Method</div>
                                <div class="fw-bold">{{ ucfirst($stripeSubscription->collection_method) }}</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="text-muted mb-1">Cancel At Period End</div>
                                <div class="fw-bold">
                                    @if($stripeSubscription->cancel_at_period_end)
                                        <span class="text-warning">Yes</span>
                                    @else
                                        <span class="text-success">No</span>
                                    @endif
                                </div>
                            </div>
                            
                            @if($stripeSubscription->trial_end)
                                <div class="col-md-6 mb-3">
                                    <div class="text-muted mb-1">Trial End</div>
                                    <div class="fw-bold">{{ date('M d, Y', $stripeSubscription->trial_end) }}</div>
                                </div>
                            @endif
                            
                            @if($stripeSubscription->cancel_at)
                                <div class="col-md-6 mb-3">
                                    <div class="text-muted mb-1">Cancels On</div>
                                    <div class="fw-bold text-warning">{{ date('M d, Y', $stripeSubscription->cancel_at) }}</div>
                                </div>
                            @endif
                            
                            @if($stripeSubscription->ended_at)
                                <div class="col-md-6 mb-3">
                                    <div class="text-muted mb-1">Ended On</div>
                                    <div class="fw-bold text-danger">{{ date('M d, Y', $stripeSubscription->ended_at) }}</div>
                                </div>
                            @endif
                            
                            @if($stripeSubscription->default_payment_method)
                                <div class="col-md-12 mb-3">
                                    <div class="text-muted mb-1">Payment Method</div>
                                    <div class="fw-bold">
                                        {{ ucfirst($stripeSubscription->default_payment_method->type) }} - 
                                        {{ ucfirst($stripeSubscription->default_payment_method->card->brand) }} 
                                        ending in {{ $stripeSubscription->default_payment_method->card->last4 }}
                                        (Expires {{ $stripeSubscription->default_payment_method->card->exp_month }}/{{ $stripeSubscription->default_payment_method->card->exp_year }})
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif
            
            <!-- Invoices Card -->
            @if(count($invoices) > 0)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Recent Invoices</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Invoice ID</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoices as $invoice)
                                        <tr>
                                            <td>
                                                <span class="text-monospace">{{ substr($invoice->id, 3) }}</span>
                                            </td>
                                            <td>{{ date('M d, Y', $invoice->created) }}</td>
                                            <td>${{ number_format($invoice->amount_paid / 100, 2) }}</td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'paid' => 'success',
                                                        'open' => 'warning',
                                                        'void' => 'secondary',
                                                        'uncollectible' => 'danger',
                                                        'draft' => 'info'
                                                    ];
                                                    $color = $statusColors[$invoice->status] ?? 'secondary';
                                                @endphp
                                                <span class="badge bg-{{ $color }}">
                                                    {{ ucfirst($invoice->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
    
    <!-- Cancel Modal -->
    <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.subscriptions.cancel', $subscription->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="cancelModalLabel">Cancel Subscription</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to cancel this subscription?</p>
                        <p class="text-muted">The subscription will remain active until the end of the current billing period.</p>
                        
                        <div class="mb-3">
                            <label for="cancel_reason" class="form-label">Cancellation Reason</label>
                            <input type="text" class="form-control" id="cancel_reason" name="cancel_reason" placeholder="Optional">
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="send_notification" name="send_notification" value="1" checked>
                            <label class="form-check-label" for="send_notification">
                                Send notification to user
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-danger">Cancel Subscription</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Resume Modal -->
    <div class="modal fade" id="resumeModal" tabindex="-1" aria-labelledby="resumeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.subscriptions.resume', $subscription->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="resumeModalLabel">Resume Subscription</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to resume this subscription?</p>
                        <p class="text-muted">The subscription will continue with the current billing cycle.</p>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="send_notification_resume" name="send_notification" value="1" checked>
                            <label class="form-check-label" for="send_notification_resume">
                                Send notification to user
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-success">Resume Subscription</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Pause Modal -->
    <div class="modal fade" id="pauseModal" tabindex="-1" aria-labelledby="pauseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.subscriptions.pause', $subscription->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="pauseModalLabel">Pause Subscription</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to pause this subscription?</p>
                        <p class="text-muted">The subscription will be paused immediately and no further charges will be made until unpaused.</p>
                        
                        <div class="mb-3">
                            <label for="pause_reason" class="form-label">Pause Reason</label>
                            <input type="text" class="form-control" id="pause_reason" name="pause_reason" placeholder="Optional">
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="send_notification_pause" name="send_notification" value="1" checked>
                            <label class="form-check-label" for="send_notification_pause">
                                Send notification to user
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-warning">Pause Subscription</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Unpause Modal -->
    <div class="modal fade" id="unpauseModal" tabindex="-1" aria-labelledby="unpauseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.subscriptions.unpause', $subscription->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="unpauseModalLabel">Unpause Subscription</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to unpause this subscription?</p>
                        <p class="text-muted">The subscription will be reactivated immediately and billing will resume.</p>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="send_notification_unpause" name="send_notification" value="1" checked>
                            <label class="form-check-label" for="send_notification_unpause">
                                Send notification to user
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-success">Unpause Subscription</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.subscriptions.destroy', $subscription->id) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">Delete Subscription</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This action cannot be undone.
                        </div>
                        <p>Are you sure you want to permanently delete this subscription?</p>
                        <p class="text-muted">This will cancel the subscription in Stripe and remove it from the database.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-danger">Delete Permanently</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
