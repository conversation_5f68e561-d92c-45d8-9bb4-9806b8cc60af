<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('type')->comment('transaction, bin, summary');
            $table->string('format')->comment('csv, pdf');
            $table->string('period_type')->comment('daily, weekly, monthly, custom');
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->json('filters')->nullable();
            $table->string('status')->default('pending')->comment('pending, processing, completed, failed');
            $table->string('file_path')->nullable();
            $table->timestamp('last_generated_at')->nullable();
            $table->string('schedule')->nullable()->comment('daily, weekly, monthly, none');
            $table->boolean('is_recurring')->default(false);
            $table->timestamp('next_run_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
